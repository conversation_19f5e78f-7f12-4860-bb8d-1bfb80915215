<?xml version="1.0" encoding="UTF-8"?>
<project xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
    xmlns="http://maven.apache.org/POM/4.0.0"
    xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 https://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <artifactId>spring-boot-starter-parent</artifactId>
        <groupId>org.springframework.boot</groupId>
        <relativePath/>
        <version>2.7.18</version> <!-- lookup parent from repository -->
    </parent>
    <description>rj_internet_interface project for Spring Boot</description>
    <groupId>com.sunhealth</groupId>
    <modelVersion>4.0.0</modelVersion>
    <name>rj_internet_interface</name>
    <artifactId>rj_internet_interface</artifactId>
    <version>2.0.1019</version>
    <packaging>jar</packaging>

    <properties>
        <java.version>1.8</java.version>
        <maven.compiler.source>1.8</maven.compiler.source>
        <maven.compiler.target>1.8</maven.compiler.target>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
        <project.reporting.outputEncoding>UTF-8</project.reporting.outputEncoding>

        <commons-lang-version>2.6</commons-lang-version>
        <fastjson-version>1.2.83</fastjson-version>
        <jackson-datatype-jsr310-version>2.13.2</jackson-datatype-jsr310-version>
        <mybatis-plus-version>3.5.5</mybatis-plus-version>
        <slf4j-api-version>1.7.36</slf4j-api-version>
        <jedi.version>2.0.32</jedi.version>
        <snakeyaml-version>1.28</snakeyaml-version>
        <spring-boot.version>2.7.18</spring-boot.version>
        <swagger.version>1.6.5</swagger.version>
    </properties>

    <dependencyManagement>
        <dependencies>
            <dependency>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-dependencies</artifactId>
                <version>${spring-boot.version}</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>
        </dependencies>
    </dependencyManagement>
    <dependencies>
        <dependency>
            <artifactId>okhttp</artifactId>
            <groupId>com.squareup.okhttp3</groupId>
            <version>4.12.0</version>
        </dependency>
        <dependency>
            <artifactId>spring-boot-starter-aop</artifactId>
            <groupId>org.springframework.boot</groupId>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-autoconfigure</artifactId>
        </dependency>
        <!-- https://mvnrepository.com/artifact/com.auth0/java-jwt -->
        <!--        <dependency>-->
        <!--            <artifactId>java-jwt</artifactId>-->
        <!--            <groupId>com.auth0</groupId>-->
        <!--            <version>3.10.3</version>-->
        <!--        </dependency>-->
        <dependency>
            <groupId>org.apache.commons</groupId>
            <artifactId>commons-lang3</artifactId>
        </dependency>
        <dependency>
            <groupId>commons-io</groupId>
            <artifactId>commons-io</artifactId>
            <version>2.13.0</version>
        </dependency>
        <dependency>
            <groupId>io.dropwizard</groupId>
            <artifactId>dropwizard-jackson</artifactId>
            <version>1.3.1</version>
        </dependency>
        <dependency>
            <groupId>com.google.guava</groupId>
            <artifactId>guava</artifactId>
            <version>32.1.3-jre</version>
        </dependency>
        <dependency>
            <artifactId>spring-boot-devtools</artifactId>
            <groupId>org.springframework.boot</groupId>
            <optional>true</optional>
            <scope>runtime</scope>
        </dependency>
        <dependency>
            <artifactId>spring-boot-starter</artifactId>
            <groupId>org.springframework.boot</groupId>
        </dependency>

        <dependency>
            <artifactId>spring-boot-starter-web</artifactId>
            <groupId>org.springframework.boot</groupId>
        </dependency>
        <dependency>
            <groupId>org.springframework</groupId>
            <artifactId>spring-web</artifactId>
            <version>5.3.33</version>
        </dependency>
        <dependency>
            <groupId>org.springframework</groupId>
            <artifactId>spring-webmvc</artifactId>
            <version>5.3.33</version>
        </dependency>
        <dependency>
            <artifactId>spring-boot-starter-validation</artifactId>
            <groupId>org.springframework.boot</groupId>
        </dependency>
        <!--        <dependency>-->
        <!--            <artifactId>spring-security-crypto</artifactId>-->
        <!--            <groupId>org.springframework.security</groupId>-->
        <!--        </dependency>-->
        <!--SpringBoot配置处理-->
        <dependency>
            <artifactId>spring-boot-configuration-processor</artifactId>
            <groupId>org.springframework.boot</groupId>
            <optional>true</optional>
        </dependency>
        <dependency>
            <groupId>org.quartz-scheduler</groupId>
            <artifactId>quartz</artifactId>
        </dependency>
        <dependency>
            <artifactId>lombok</artifactId>
            <groupId>org.projectlombok</groupId>
            <optional>true</optional>
        </dependency>
        <dependency>
            <artifactId>spring-boot-starter-test</artifactId>
            <groupId>org.springframework.boot</groupId>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>org.springframework.data</groupId>
            <artifactId>spring-data-commons</artifactId>
        </dependency>
        <!--        <dependency>-->
        <!--            <artifactId>jjwt</artifactId>-->
        <!--            <groupId>io.jsonwebtoken</groupId>-->
        <!--            <version>${jjwt.version}</version>-->
        <!--        </dependency>-->
        <dependency>
            <artifactId>fastjson</artifactId>
            <groupId>com.alibaba</groupId>
            <version>${fastjson-version}</version>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-jdbc</artifactId>
        </dependency>
        <dependency>
            <groupId>com.microsoft.sqlserver</groupId>
            <artifactId>mssql-jdbc</artifactId>
        </dependency>
        <dependency>
            <artifactId>commons-lang</artifactId>
            <groupId>commons-lang</groupId>
            <version>${commons-lang-version}</version>
        </dependency>
        <dependency>
            <artifactId>snakeyaml</artifactId>
            <groupId>org.yaml</groupId>
            <version>${snakeyaml-version}</version>
        </dependency>
        <dependency>
            <groupId>org.mybatis.spring.boot</groupId>
            <artifactId>mybatis-spring-boot-starter</artifactId>
            <version>2.3.2</version>
        </dependency>
        <dependency>
            <groupId>com.baomidou</groupId>
            <artifactId>mybatis-plus-boot-starter</artifactId>
            <version>${mybatis-plus-version}</version>
        </dependency>
        <dependency>
            <artifactId>slf4j-api</artifactId>
            <groupId>org.slf4j</groupId>
            <version>${slf4j-api-version}</version>
        </dependency>
        <dependency>
            <artifactId>jackson-datatype-jsr310</artifactId>
            <groupId>com.fasterxml.jackson.datatype</groupId>
            <version>${jackson-datatype-jsr310-version}</version>
        </dependency>
        <dependency>
            <artifactId>swagger-annotations</artifactId>
            <groupId>io.swagger</groupId>
            <version>${swagger.version}</version>
        </dependency>

        <!-- 添加 springdoc 依赖 -->
        <dependency>
            <groupId>org.springdoc</groupId>
            <artifactId>springdoc-openapi-ui</artifactId>
            <version>1.7.0</version>
        </dependency>


        <!-- jasypt -->
        <dependency>
            <groupId>com.github.ulisesbocchio</groupId>
            <artifactId>jasypt-spring-boot-starter</artifactId>
            <version>3.0.5</version>
        </dependency>

        <!-- druid -->
        <dependency>
            <groupId>com.alibaba</groupId>
            <artifactId>druid-spring-boot-starter</artifactId>
            <version>1.2.16</version>
        </dependency>

        <!-- 多数据源所需要使用到的依赖-->
        <dependency>
            <groupId>com.baomidou</groupId>
            <artifactId>dynamic-datasource-spring-boot-starter</artifactId>
            <version>3.1.0</version>
        </dependency>

        <dependency>
            <groupId>org.zalando</groupId>
            <artifactId>problem-spring-web</artifactId>
            <version>0.26.2</version>
        </dependency>

        <!--jackson problem-->
        <dependency>
            <groupId>org.zalando</groupId>
            <artifactId>jackson-datatype-problem</artifactId>
            <version>0.26.0</version>
        </dependency>

        <dependency>
            <groupId>org.bouncycastle</groupId>
            <artifactId>bcprov-jdk15on</artifactId>
            <version>1.68</version>
        </dependency>
        <dependency>
            <groupId>com.sun.jna</groupId>
            <artifactId>jna</artifactId>
            <version>3.0.9</version>
        </dependency>

    </dependencies>
    <repositories>
        <repository>
            <id>public</id>
            <name>aliyun nexus</name>
            <url>https://maven.aliyun.com/nexus/content/groups/public/</url>
            <releases>
                <enabled>true</enabled>
            </releases>
            <snapshots>
                <enabled>true</enabled>
            </snapshots>
        </repository>
    </repositories>

    <build>
        <plugins>
            <plugin>
                <artifactId>swagger-maven-plugin</artifactId>
                <configuration>
                    <apiSources>
                        <apiSource>
                            <host>http://localhost:12082/hisapi</host>
                            <info>
                                <description>API
                                </description>
                                <title>taihealth - API - rj_internet_interface</title>
                                <version>version-${project.version}</version>
                            </info>
                            <locations>
                                <location>com.sunhealth.ihhis.controller</location>
                            </locations>
                            <outputFormats>json</outputFormats>
                            <schemes>http</schemes>
                            <springmvc>true</springmvc>
                            <swaggerDirectory>${basedir}/target/classes/com/sunhealth/ihhis/controller/
                            </swaggerDirectory>
                        </apiSource>
                    </apiSources>
                </configuration>
                <executions>
                    <execution>
                        <goals>
                            <goal>generate</goal>
                        </goals>
                        <phase>compile</phase>
                    </execution>
                </executions>
                <groupId>com.github.kongchen</groupId>
                <version>3.1.7</version>
            </plugin>

            <plugin><!--编译跳过测试文件检查的生命周期-->
                <artifactId>maven-surefire-plugin</artifactId>
                <configuration>
                    <skip>true</skip>
                </configuration>
                <groupId>org.apache.maven.plugins</groupId>
            </plugin>
            <!--            <plugin>-->
            <!--                <artifactId>git-commit-id-plugin</artifactId>-->
            <!--                <configuration>-->
            <!--                    <format>properties</format>-->
            <!--                    <generateGitPropertiesFile>true</generateGitPropertiesFile>-->
            <!--                </configuration>-->
            <!--                <executions>-->
            <!--                    <execution>-->
            <!--                        <goals>-->
            <!--                            <goal>revision</goal>-->
            <!--                        </goals>-->
            <!--                    </execution>-->
            <!--                </executions>-->
            <!--                <groupId>pl.project13.maven</groupId>-->
            <!--            </plugin>-->
<!--            <plugin>-->
<!--                <artifactId>stork-maven-plugin</artifactId>-->
<!--                <executions>-->
<!--                    <execution>-->
<!--                        <goals>-->
<!--                            <goal>launcher</goal>-->
<!--                        </goals>-->
<!--                        <id>stork-launcher</id>-->
<!--                        <phase>package</phase>-->
<!--                    </execution>-->
<!--                    <execution>-->
<!--                        <configuration>-->
<!--                            <finalName>rj_internet_interface-${project.version}</finalName>-->
<!--                        </configuration>-->
<!--                        <goals>-->
<!--                            <goal>assembly</goal>-->
<!--                        </goals>-->
<!--                        <id>stork-assembly</id>-->
<!--                        <phase>package</phase>-->
<!--                    </execution>-->
<!--                </executions>-->
<!--                <groupId>com.fizzed</groupId>-->
<!--                <version>3.0.0</version>-->
<!--            </plugin>-->
            <plugin>
                <artifactId>maven-compiler-plugin</artifactId>
                <configuration>
                    <source>1.8</source>
                    <target>1.8</target>
                </configuration>
            </plugin>
            <plugin>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-maven-plugin</artifactId>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-surefire-plugin</artifactId>
                <configuration>
                    <skipTests>true</skipTests>
                </configuration>
            </plugin>
        </plugins>

        <resources>
            <resource>
                <directory>src/main/resources</directory>
                <filtering>true</filtering>
            </resource>
            <resource>
                <directory>src/main/java</directory>
                <includes>
                    <include>**/*.xml</include>
                </includes>
            </resource>
        </resources>
    </build>

</project>
