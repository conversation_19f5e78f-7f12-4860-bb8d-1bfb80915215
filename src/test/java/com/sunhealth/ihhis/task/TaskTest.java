package com.sunhealth.ihhis.task;

import com.sunhealth.ihhis.model.vm.PushInvoiceMessageParam;
import com.sunhealth.ihhis.task.scheduler.InvoiceScheduler;
import org.quartz.Scheduler;
import org.quartz.SchedulerException;
import org.quartz.impl.StdSchedulerFactory;

public class TaskTest {

    public static void main(String[] args) {
        try {
            Scheduler scheduler = StdSchedulerFactory.getDefaultScheduler();
            scheduler.start();
            for (int i = 0; i < 10; i++) {
                InvoiceScheduler.delayTask(new PushInvoiceMessageParam());
            }
        } catch (SchedulerException ex) {
            throw new RuntimeException(ex);
        }
    }

}
