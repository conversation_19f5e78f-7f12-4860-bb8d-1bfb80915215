package com.sunhealth.ihhis.task;

import com.sunhealth.ihhis.utils.MedicalRecordRenderer;
import org.w3c.dom.Document;
import org.w3c.dom.Element;
import org.w3c.dom.NodeList;
import javax.xml.parsers.DocumentBuilderFactory;
import java.io.StringReader;
import org.xml.sax.InputSource;

public class DomBodyTextExtractor {
    public static String getBodyText(String xmlString) throws Exception {
        // 解析 XML 字符串
        Document doc = DocumentBuilderFactory.newInstance()
            .newDocumentBuilder()
            .parse(new InputSource(new StringReader(xmlString)));

        // 获取根节点
        Element root = doc.getDocumentElement();

        // 直接获取 BodyText 子节点的文本内容
        NodeList bodyTextNodes = root.getElementsByTagName("BodyText");
        if (bodyTextNodes.getLength() > 0) {
            return bodyTextNodes.item(0).getTextContent().trim();
        }
        return "";
    }

    public static void main(String[] args) throws Exception {
        String xml = "<XTextDocument xmlns:xsi=\"http://www.w3.org/2001/XMLSchema-instance\" xmlns:xsd=\"http://www.w3.org/2001/XMLSchema\" EditorVersionString=\"1.1.1111.1\">\n"
            + "  <Attributes />\n"
            + "  <XElements>\n"
            + "    <Element xsi:type=\"XTextBody\">\n"
            + "      <Attributes />\n"
            + "      <XElements>\n"
            + "        <Element xsi:type=\"XInputField\" StyleIndex=\"2\">\n"
            + "          <Expressions />\n"
            + "          <Attributes />\n"
            + "          <XElements>\n"
            + "            <Element xsi:type=\"XString\" StyleIndex=\"3\">\n"
            + "              <Text>日期时间：</Text>\n"
            + "            </Element>\n"
            + "          </XElements>\n"
            + "          <TextValue>日期时间：</TextValue>\n"
            + "          <Readonly>true</Readonly>\n"
            + "          <EventExpressions />\n"
            + "          <UserEditable>false</UserEditable>\n"
            + "          <InnerValue>日期时间：</InnerValue>\n"
            + "          <ToolTip>日期时间</ToolTip>\n"
            + "          <BackgroundText>日期时间</BackgroundText>\n"
            + "          <FieldSettings>\n"
            + "            <EditStyle>Date</EditStyle>\n"
            + "            <ListItems />\n"
            + "          </FieldSettings>\n"
            + "        </Element>\n"
            + "        <Element xsi:type=\"XInputField\" StyleIndex=\"2\">\n"
            + "          <ID>日期时间</ID>\n"
            + "          <Expressions />\n"
            + "          <Attributes />\n"
            + "          <XElements>\n"
            + "            <Element xsi:type=\"XString\" StyleIndex=\"2\">\n"
            + "              <Text>2025-04-15</Text>\n"
            + "            </Element>\n"
            + "          </XElements>\n"
            + "          <TextValue>2025-04-15</TextValue>\n"
            + "          <AcceptChildElementTypes2>Text Field InputField LineBreak PageBreak ParagraphFlag</AcceptChildElementTypes2>\n"
            + "          <EventExpressions />\n"
            + "          <Name>日期时间</Name>\n"
            + "          <InnerValue>2025-04-15</InnerValue>\n"
            + "          <ToolTip>日期时间</ToolTip>\n"
            + "          <BackgroundText>日期时间</BackgroundText>\n"
            + "          <FieldSettings>\n"
            + "            <EditStyle>Date</EditStyle>\n"
            + "            <ListItems />\n"
            + "          </FieldSettings>\n"
            + "        </Element>\n"
            + "        <Element xsi:type=\"XInputField\" StyleIndex=\"2\">\n"
            + "          <ID>挂号科室</ID>\n"
            + "          <Expressions />\n"
            + "          <Attributes />\n"
            + "          <XElements>\n"
            + "            <Element xsi:type=\"XString\" StyleIndex=\"2\">\n"
            + "              <Text>专家门诊</Text>\n"
            + "            </Element>\n"
            + "          </XElements>\n"
            + "          <TextValue>专家门诊</TextValue>\n"
            + "          <EventExpressions />\n"
            + "          <Name>挂号科室</Name>\n"
            + "          <InnerValue>专家门诊</InnerValue>\n"
            + "          <ToolTip>挂号科室</ToolTip>\n"
            + "          <BackgroundText>科室</BackgroundText>\n"
            + "        </Element>\n"
            + "        <Element xsi:type=\"XParagraphFlag\" StyleIndex=\"2\" />\n"
            + "        <Element xsi:type=\"XInputField\" StyleIndex=\"1\">\n"
            + "          <Expressions />\n"
            + "          <Attributes />\n"
            + "          <XElements>\n"
            + "            <Element xsi:type=\"XString\" StyleIndex=\"1\">\n"
            + "              <Text>代诊者</Text>\n"
            + "            </Element>\n"
            + "          </XElements>\n"
            + "          <TextValue>代诊者</TextValue>\n"
            + "          <EventExpressions>\n"
            + "            <Expression>\n"
            + "              <Expression>value&lt;&gt;'自来'</Expression>\n"
            + "              <CustomTargetName />\n"
            + "            </Expression>\n"
            + "            <Expression>\n"
            + "              <Expression>value&lt;&gt;'代诊者'</Expression>\n"
            + "              <Target>Custom</Target>\n"
            + "              <CustomTargetName>精神检查</CustomTargetName>\n"
            + "            </Expression>\n"
            + "            <Expression>\n"
            + "              <Expression>value&lt;&gt;'代诊者'</Expression>\n"
            + "              <Target>Custom</Target>\n"
            + "              <CustomTargetName>精神检查描述</CustomTargetName>\n"
            + "            </Expression>\n"
            + "            <Expression>\n"
            + "              <Expression>value&lt;&gt;'代诊者'</Expression>\n"
            + "              <Target>Custom</Target>\n"
            + "              <CustomTargetName>体格检查</CustomTargetName>\n"
            + "            </Expression>\n"
            + "            <Expression>\n"
            + "              <Expression>value&lt;&gt;'代诊者'</Expression>\n"
            + "              <Target>Custom</Target>\n"
            + "              <CustomTargetName>体格检查情况</CustomTargetName>\n"
            + "            </Expression>\n"
            + "            <Expression>\n"
            + "              <Expression>value&lt;&gt;'代诊者'</Expression>\n"
            + "              <Target>Custom</Target>\n"
            + "              <CustomTargetName>8888880245</CustomTargetName>\n"
            + "            </Expression>\n"
            + "            <Expression>\n"
            + "              <Expression>value&lt;&gt;'代诊者'</Expression>\n"
            + "              <Target>Custom</Target>\n"
            + "              <CustomTargetName>8888880258</CustomTargetName>\n"
            + "            </Expression>\n"
            + "            <Expression>\n"
            + "              <Expression>value&lt;&gt;'代诊者'</Expression>\n"
            + "              <Target>Custom</Target>\n"
            + "              <CustomTargetName>,</CustomTargetName>\n"
            + "            </Expression>\n"
            + "          </EventExpressions>\n"
            + "          <Name>陪诊者</Name>\n"
            + "          <InnerValue>代诊者</InnerValue>\n"
            + "          <ToolTip>双击选择&lt;陪诊&gt;或者&lt;自来&gt;或者&lt;代诊&gt;</ToolTip>\n"
            + "          <BackgroundText>陪诊者</BackgroundText>\n"
            + "          <FieldSettings>\n"
            + "            <EditStyle>DropdownList</EditStyle>\n"
            + "            <ListSource>\n"
            + "              <Name />\n"
            + "              <SourceName />\n"
            + "              <DisplayPath />\n"
            + "              <ValuePath />\n"
            + "              <Items>\n"
            + "                <Item>\n"
            + "                  <Text>陪诊者</Text>\n"
            + "                  <Value>陪诊者</Value>\n"
            + "                </Item>\n"
            + "                <Item>\n"
            + "                  <Text>代诊者</Text>\n"
            + "                  <Value>代诊者</Value>\n"
            + "                </Item>\n"
            + "                <Item>\n"
            + "                  <Text>自来</Text>\n"
            + "                  <Value>自来</Value>\n"
            + "                </Item>\n"
            + "              </Items>\n"
            + "            </ListSource>\n"
            + "            <ListItems />\n"
            + "          </FieldSettings>\n"
            + "        </Element>\n"
            + "        <Element xsi:type=\"XInputField\" StyleIndex=\"2\">\n"
            + "          <Expressions />\n"
            + "          <Attributes />\n"
            + "          <XElements>\n"
            + "            <Element xsi:type=\"XInputField\">\n"
            + "              <ID>关系描述</ID>\n"
            + "              <Expressions />\n"
            + "              <Attributes />\n"
            + "              <XElements>\n"
            + "                <Element xsi:type=\"XString\">\n"
            + "                  <Text>关系：</Text>\n"
            + "                </Element>\n"
            + "                <Element xsi:type=\"XInputField\">\n"
            + "                  <ID>8888880004</ID>\n"
            + "                  <Expressions />\n"
            + "                  <Attributes />\n"
            + "                  <XElements>\n"
            + "                    <Element xsi:type=\"XString\">\n"
            + "                      <Text>妻子</Text>\n"
            + "                    </Element>\n"
            + "                  </XElements>\n"
            + "                  <TextValue>妻子</TextValue>\n"
            + "                  <EventExpressions />\n"
            + "                  <Name>关系</Name>\n"
            + "                  <InnerValue>夫妻</InnerValue>\n"
            + "                  <ToolTip>双击选择人员关系</ToolTip>\n"
            + "                  <BackgroundText>关系</BackgroundText>\n"
            + "                  <FieldSettings>\n"
            + "                    <EditStyle>DropdownList</EditStyle>\n"
            + "                    <ListSource>\n"
            + "                      <SourceName>8888880004</SourceName>\n"
            + "                      <Items />\n"
            + "                    </ListSource>\n"
            + "                    <ListItems />\n"
            + "                  </FieldSettings>\n"
            + "                </Element>\n"
            + "              </XElements>\n"
            + "              <TextValue>关系：妻子</TextValue>\n"
            + "              <AcceptChildElementTypes2>All</AcceptChildElementTypes2>\n"
            + "              <EventExpressions />\n"
            + "              <Name>关系描述</Name>\n"
            + "              <InnerValue>关系：妻子</InnerValue>\n"
            + "              <ToolTip>关系描述</ToolTip>\n"
            + "            </Element>\n"
            + "          </XElements>\n"
            + "          <TextValue>关系：妻子</TextValue>\n"
            + "          <AcceptChildElementTypes2>All</AcceptChildElementTypes2>\n"
            + "          <EventExpressions />\n"
            + "          <MultiParagraph>true</MultiParagraph>\n"
            + "          <InnerValue>关系：妻子</InnerValue>\n"
            + "          <ToolTip>1</ToolTip>\n"
            + "          <BackgroundText>1</BackgroundText>\n"
            + "        </Element>\n"
            + "        <Element xsi:type=\"XParagraphFlag\" StyleIndex=\"2\" />\n"
            + "        <Element xsi:type=\"XInputField\">\n"
            + "          <ID>bqybqk</ID>\n"
            + "          <Expressions />\n"
            + "          <Attributes />\n"
            + "          <XElements>\n"
            + "            <Element xsi:type=\"XString\">\n"
            + "              <Text>配药,反映夜眠有改善，出汗减少。</Text>\n"
            + "            </Element>\n"
            + "          </XElements>\n"
            + "          <TextValue>配药,反映夜眠有改善，出汗减少。</TextValue>\n"
            + "          <EventExpressions />\n"
            + "          <Name>病情演变情况</Name>\n"
            + "          <InnerValue>配药,反映夜眠有改善，出汗减少。</InnerValue>\n"
            + "          <ToolTip>直接录入或者点击&lt;选择主诉&gt;按钮选择现病史描述部分</ToolTip>\n"
            + "          <BackgroundText>病情演变情况</BackgroundText>\n"
            + "        </Element>\n"
            + "        <Element xsi:type=\"XInputField\">\n"
            + "          <ID>精神检查</ID>\n"
            + "          <Expressions />\n"
            + "          <Attributes />\n"
            + "          <XElements>\n"
            + "            <Element xsi:type=\"XString\">\n"
            + "              <Text>精神检查：</Text>\n"
            + "            </Element>\n"
            + "          </XElements>\n"
            + "          <TextValue>精神检查：</TextValue>\n"
            + "          <Readonly>true</Readonly>\n"
            + "          <EventExpressions />\n"
            + "          <Name>精神检查：</Name>\n"
            + "          <InnerValue>精神检查：</InnerValue>\n"
            + "          <ToolTip>精神检查：</ToolTip>\n"
            + "          <BackgroundText>精神检查：</BackgroundText>\n"
            + "        </Element>\n"
            + "        <Element xsi:type=\"XInputField\">\n"
            + "          <ID>精神检查描述</ID>\n"
            + "          <Expressions />\n"
            + "          <Attributes />\n"
            + "          <XElements>\n"
            + "            <Element xsi:type=\"XString\">\n"
            + "              <Text>意识清，接触合作，称：“家里漏煤气了。人家要收很多钱，肠子不好，拉不出。”未引出幻觉妄想，</Text>\n"
            + "            </Element>\n"
            + "          </XElements>\n"
            + "          <TextValue>意识清，接触合作，称：“家里漏煤气了。人家要收很多钱，肠子不好，拉不出。”未引出幻觉妄想，</TextValue>\n"
            + "          <EventExpressions />\n"
            + "          <Name>精神检查描述</Name>\n"
            + "          <InnerValue>意识清，接触合作，称：“家里漏煤气了。人家要收很多钱，肠子不好，拉不出。”未引出幻觉妄想，</InnerValue>\n"
            + "          <ToolTip>直接输入</ToolTip>\n"
            + "          <BackgroundText>精神检查描述</BackgroundText>\n"
            + "        </Element>\n"
            + "        <Element xsi:type=\"XInputField\">\n"
            + "          <ID>8888880245</ID>\n"
            + "          <Expressions />\n"
            + "          <Attributes />\n"
            + "          <XElements>\n"
            + "            <Element xsi:type=\"XString\">\n"
            + "              <Text>情感低落,情绪焦虑不安</Text>\n"
            + "            </Element>\n"
            + "          </XElements>\n"
            + "          <TextValue>情感低落,情绪焦虑不安</TextValue>\n"
            + "          <EventExpressions />\n"
            + "          <Name>情感</Name>\n"
            + "          <InnerValue>情感低落,情绪焦虑不安</InnerValue>\n"
            + "          <BackgroundText>情感</BackgroundText>\n"
            + "          <FieldSettings>\n"
            + "            <EditStyle>DropdownList</EditStyle>\n"
            + "            <MultiSelect>true</MultiSelect>\n"
            + "            <ListSource>\n"
            + "              <SourceName>8888880245</SourceName>\n"
            + "              <Items />\n"
            + "            </ListSource>\n"
            + "            <ListItems />\n"
            + "          </FieldSettings>\n"
            + "        </Element>\n"
            + "        <Element xsi:type=\"XInputField\">\n"
            + "          <ID>,</ID>\n"
            + "          <Expressions />\n"
            + "          <Attributes />\n"
            + "          <XElements>\n"
            + "            <Element xsi:type=\"XString\">\n"
            + "              <Text>,</Text>\n"
            + "            </Element>\n"
            + "          </XElements>\n"
            + "          <TextValue>,</TextValue>\n"
            + "          <EventExpressions />\n"
            + "          <Name>,</Name>\n"
            + "          <InnerValue>,</InnerValue>\n"
            + "          <ToolTip>,</ToolTip>\n"
            + "          <BackgroundText>,</BackgroundText>\n"
            + "        </Element>\n"
            + "        <Element xsi:type=\"XInputField\">\n"
            + "          <ID>8888880258</ID>\n"
            + "          <Expressions />\n"
            + "          <Attributes />\n"
            + "          <XElements>\n"
            + "            <Element xsi:type=\"XString\">\n"
            + "              <Text>自知力部分。</Text>\n"
            + "            </Element>\n"
            + "          </XElements>\n"
            + "          <TextValue>自知力部分。</TextValue>\n"
            + "          <EventExpressions />\n"
            + "          <Name>自知力</Name>\n"
            + "          <InnerValue>自知力部分</InnerValue>\n"
            + "          <BackgroundText>自知力</BackgroundText>\n"
            + "          <FieldSettings>\n"
            + "            <EditStyle>DropdownList</EditStyle>\n"
            + "            <ListSource>\n"
            + "              <SourceName>8888880258</SourceName>\n"
            + "              <Items />\n"
            + "            </ListSource>\n"
            + "            <ListItems />\n"
            + "          </FieldSettings>\n"
            + "        </Element>\n"
            + "        <Element xsi:type=\"XParagraphFlag\" StyleIndex=\"2\" />\n"
            + "        <Element xsi:type=\"XInputField\">\n"
            + "          <Expressions />\n"
            + "          <Attributes />\n"
            + "          <XElements>\n"
            + "            <Element xsi:type=\"XString\" StyleIndex=\"1\">\n"
            + "              <Text>诊断：</Text>\n"
            + "            </Element>\n"
            + "          </XElements>\n"
            + "          <TextValue>诊断：</TextValue>\n"
            + "          <Readonly>true</Readonly>\n"
            + "          <EventExpressions />\n"
            + "          <InnerValue>诊断：</InnerValue>\n"
            + "        </Element>\n"
            + "        <Element xsi:type=\"XInputField\">\n"
            + "          <ID>诊断意见</ID>\n"
            + "          <Expressions />\n"
            + "          <Attributes />\n"
            + "          <XElements>\n"
            + "            <Element xsi:type=\"XString\">\n"
            + "              <Text>混合性焦虑和抑郁障碍</Text>\n"
            + "            </Element>\n"
            + "          </XElements>\n"
            + "          <TextValue>混合性焦虑和抑郁障碍</TextValue>\n"
            + "          <EventExpressions />\n"
            + "          <Name>诊断</Name>\n"
            + "          <ValidateStyle>\n"
            + "            <ValueName />\n"
            + "            <CustomMessage />\n"
            + "          </ValidateStyle>\n"
            + "          <InnerValue>焦虑（抑郁）状态。</InnerValue>\n"
            + "          <ToolTip>开药界面写好诊断后，点击&lt;导入处方&gt;按钮</ToolTip>\n"
            + "          <BackgroundText>诊断</BackgroundText>\n"
            + "          <FieldSettings>\n"
            + "            <EditStyle>DropdownList</EditStyle>\n"
            + "            <ListSource>\n"
            + "              <SourceName>8888880014</SourceName>\n"
            + "              <Items />\n"
            + "            </ListSource>\n"
            + "            <ListItems />\n"
            + "          </FieldSettings>\n"
            + "        </Element>\n"
            + "        <Element xsi:type=\"XParagraphFlag\" />\n"
            + "        <Element xsi:type=\"XInputField\" StyleIndex=\"2\">\n"
            + "          <Expressions />\n"
            + "          <Attributes />\n"
            + "          <XElements>\n"
            + "            <Element xsi:type=\"XString\" StyleIndex=\"1\">\n"
            + "              <Text>处理：</Text>\n"
            + "            </Element>\n"
            + "          </XElements>\n"
            + "          <TextValue>处理：</TextValue>\n"
            + "          <Readonly>true</Readonly>\n"
            + "          <EventExpressions />\n"
            + "          <InnerValue>处理：</InnerValue>\n"
            + "        </Element>\n"
            + "        <Element xsi:type=\"XParagraphFlag\" StyleIndex=\"2\" />\n"
            + "        <Element xsi:type=\"XInputField\" StyleIndex=\"2\">\n"
            + "          <ID>处方信息</ID>\n"
            + "          <Expressions />\n"
            + "          <Attributes />\n"
            + "          <XElements>\n"
            + "            <Element xsi:type=\"XString\" StyleIndex=\"2\">\n"
            + "              <Text>草酸艾司西酞普兰片10mg*14片/盒 28片 早1片 口服</Text>\n"
            + "            </Element>\n"
            + "            <Element xsi:type=\"XParagraphFlag\" />\n"
            + "            <Element xsi:type=\"XString\" StyleIndex=\"2\">\n"
            + "              <Text>盐酸曲唑酮片(美时玉)50mg*20片/盒 40片 每天晚上每次2片 口服</Text>\n"
            + "            </Element>\n"
            + "            <Element xsi:type=\"XParagraphFlag\" />\n"
            + "            <Element xsi:type=\"XString\" StyleIndex=\"2\">\n"
            + "              <Text>阿普唑仑片0.4mg*20片/盒 40片 每天晚上每次1片 口服</Text>\n"
            + "            </Element>\n"
            + "            <Element xsi:type=\"XParagraphFlag\" />\n"
            + "          </XElements>\n"
            + "          <TextValue>草酸艾司西酞普兰片10mg*14片/盒 28片 早1片 口服\n"
            + "盐酸曲唑酮片(美时玉)50mg*20片/盒 40片 每天晚上每次2片 口服\n"
            + "阿普唑仑片0.4mg*20片/盒 40片 每天晚上每次1片 口服\n"
            + "</TextValue>\n"
            + "          <AcceptChildElementTypes2>Text LineBreak PageBreak ParagraphFlag</AcceptChildElementTypes2>\n"
            + "          <EventExpressions />\n"
            + "          <Name>处方信息</Name>\n"
            + "          <MultiParagraph>true</MultiParagraph>\n"
            + "          <InnerValue>草酸艾司西酞普兰片10mg*14片/盒 28片 早1片 口服\n"
            + "盐酸曲唑酮片(美时玉)50mg*20片/盒 40片 每天晚上每次2片 口服\n"
            + "阿普唑仑片0.4mg*20片/盒 40片 每天晚上每次1片 口服\n"
            + "</InnerValue>\n"
            + "          <ToolTip>在开药界面开好药之后再点击&lt;导入处方&gt;按钮</ToolTip>\n"
            + "          <BackgroundText>处方信息</BackgroundText>\n"
            + "        </Element>\n"
            + "        <Element xsi:type=\"XParagraphFlag\">\n"
            + "          <AutoCreate>true</AutoCreate>\n"
            + "        </Element>\n"
            + "      </XElements>\n"
            + "    </Element>\n"
            + "    <Element xsi:type=\"XTextHeader\">\n"
            + "      <Attributes />\n"
            + "      <XElements>\n"
            + "        <Element xsi:type=\"XParagraphFlag\" StyleIndex=\"0\">\n"
            + "          <AutoCreate>true</AutoCreate>\n"
            + "        </Element>\n"
            + "      </XElements>\n"
            + "    </Element>\n"
            + "    <Element xsi:type=\"XTextFooter\">\n"
            + "      <Attributes />\n"
            + "      <XElements>\n"
            + "        <Element xsi:type=\"XParagraphFlag\">\n"
            + "          <AutoCreate>true</AutoCreate>\n"
            + "        </Element>\n"
            + "      </XElements>\n"
            + "    </Element>\n"
            + "  </XElements>\n"
            + "  <Info>\n"
            + "    <CreationTime>2024-12-17T07:56:09.2714003+08:00</CreationTime>\n"
            + "    <LastModifiedTime>2024-12-17T07:56:09.2714003+08:00</LastModifiedTime>\n"
            + "    <LastPrintTime>1980-01-01T00:00:00</LastPrintTime>\n"
            + "    <Operator>RJSoft.Writer Version:1.1.1111.1</Operator>\n"
            + "    <NumOfPage>1</NumOfPage>\n"
            + "  </Info>\n"
            + "  <BodyText>日期时间：2025-04-15专家门诊\n"
            + "代诊者关系：妻子\n"
            + "配药,反映夜眠有改善，出汗减少。\n"
            + "诊断：混合性焦虑和抑郁障碍\n"
            + "处理：\n"
            + "草酸艾司西酞普兰片10mg*14片/盒 28片 早1片 口服\n"
            + "盐酸曲唑酮片(美时玉)50mg*20片/盒 40片 每天晚上每次2片 口服\n"
            + "阿普唑仑片0.4mg*20片/盒 40片 每天晚上每次1片 口服\n"
            + "\n"
            + "</BodyText>\n"
            + "  <UserHistories />\n"
            + "  <ContentStyles>\n"
            + "    <Default xsi:type=\"DocumentContentStyle\">\n"
            + "      <Color>ControlText</Color>\n"
            + "      <FontName>宋体</FontName>\n"
            + "      <FontSize>12</FontSize>\n"
            + "    </Default>\n"
            + "    <Styles>\n"
            + "      <Style Index=\"0\">\n"
            + "        <Align>Center</Align>\n"
            + "      </Style>\n"
            + "      <Style Index=\"1\">\n"
            + "        <Bold>true</Bold>\n"
            + "      </Style>\n"
            + "      <Style Index=\"2\">\n"
            + "        <LineSpacingStyle>SpaceSpecify</LineSpacingStyle>\n"
            + "        <LineSpacing>74.7916641</LineSpacing>\n"
            + "        <DefaultValuePropertyNames>FirstLineIndent,LeftIndent,SpacingAfterParagraph,SpacingBeforeParagraph</DefaultValuePropertyNames>\n"
            + "      </Style>\n"
            + "      <Style Index=\"3\">\n"
            + "        <Bold>true</Bold>\n"
            + "        <LineSpacingStyle>SpaceSpecify</LineSpacingStyle>\n"
            + "        <LineSpacing>74.7916641</LineSpacing>\n"
            + "      </Style>\n"
            + "    </Styles>\n"
            + "  </ContentStyles>\n"
            + "  <DocumentGraphicsUnit>Document</DocumentGraphicsUnit>\n"
            + "  <PageSettings />\n"
            + "</XTextDocument>"; // 替换为你的 XML 字符串
        String bodyText = MedicalRecordRenderer.renderToHtml(xml);
        System.out.println(bodyText);
    }
}
