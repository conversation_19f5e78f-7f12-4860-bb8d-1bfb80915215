<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.sunhealth.ihhis.dao.his.AppointmentMapper">

    <select id="selectTodayAppointments" resultType="com.sunhealth.ihhis.model.entity.register.Appointment">
        SELECT * FROM tb_Appointment
        WHERE checkDate <![CDATA[ >= ]]> #{start}
          and checkDate <![CDATA[ < ]]> #{end}
          AND certificateNo LIKE CONCAT(#{certificateNo}, '%')
          AND deptId = #{deptId}
          AND doctorId = #{doctorId}
          AND appointmentStatus IN (0, 5)
    </select>
</mapper>