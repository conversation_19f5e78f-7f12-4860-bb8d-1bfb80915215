package com.sunhealth.ihhis.dao.his;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.sunhealth.ihhis.model.entity.charge.ChargeListTime;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.springframework.transaction.annotation.Transactional;

import java.util.Date;
import java.util.List;

public interface ChargeListTimeMapper extends BaseMapper<ChargeListTime> {
    @Transactional
    void insertChargeListTime(
        @Param("chargeNo") Long chargeNo,
        @Param("accountFlag") String accountFlag,
        @Param("recipeCount") Integer recipeCount,
        @Param("computerNo") String computerNo,
        @Param("opId") Integer opId,
        @Param("time") Date time,
        @Param("dataForm") Short dataForm,
        @Param("jsFlag") Integer jsFlag,
        @Param("cardType") Integer cardType
    );

    @Select("SELECT * FROM Reg_Tv_ChargeList WHERE ChargeNo = #{chargeNo}")
    List<ChargeListTime> selectChargeListByChargeNo(@Param("chargeNo") Long chargeNo);

}
