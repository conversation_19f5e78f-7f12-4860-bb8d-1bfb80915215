package com.sunhealth.ihhis.dao.his;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.sunhealth.ihhis.model.entity.yibao.RegOnlineGjYiBaoUploadFeeRecord;
import org.apache.ibatis.annotations.Select;

public interface RegOnlineGjYiBaoUploadFeeRecordMapper extends BaseMapper<RegOnlineGjYiBaoUploadFeeRecord> {

    @Select("select * from Reg_Online_GjYiBao_uploadFeeRecord where RegNo = #{regNo} and Flag = 0")
    RegOnlineGjYiBaoUploadFeeRecord selectByRegNo(String regNo);

    @Select("select * from Reg_Online_GjYiBao_uploadFeeRecord where ChargeNo = #{chargeNo} and Flag = 1")
    RegOnlineGjYiBaoUploadFeeRecord selectByChargeNo(String chargeNo);

    @Select("select * from Reg_Online_GjYiBao_uploadFeeRecord where PayOrdId = #{payOrdId}")
    RegOnlineGjYiBaoUploadFeeRecord selectByPayOrdId(String payOrdId);
}
