package com.sunhealth.ihhis.dao.his;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.sunhealth.ihhis.model.entity.charge.ThirdAddAccount;
import com.sunhealth.ihhis.model.entity.register.RegisterListTime;
import java.util.Map;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.springframework.stereotype.Repository;

@Repository
public interface ThirdAddAccountMapper extends BaseMapper<ThirdAddAccount> {

}
