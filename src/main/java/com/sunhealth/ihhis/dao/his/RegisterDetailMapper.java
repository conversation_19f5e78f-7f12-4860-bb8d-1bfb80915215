package com.sunhealth.ihhis.dao.his;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.sunhealth.ihhis.model.entity.register.RegisterDetail;
import com.sunhealth.ihhis.model.entity.register.RegisterDetailTime;
import java.util.List;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Select;

@Mapper
public interface RegisterDetailMapper extends BaseMapper<RegisterDetail> {
    @Select("SELECT * FROM Reg_Tb_RegisterDetail_Time WHERE RegNo = #{regNo} and IsDelete = 0")
    List<RegisterDetail> selectListByRegNo(Long regNo);
}
