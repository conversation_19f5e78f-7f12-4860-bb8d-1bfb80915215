<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.sunhealth.ihhis.dao.his.ReportTestInfoMapper">
    <select id="selectNewList" resultType="com.sunhealth.ihhis.model.entity.report.ReportTestInfo">
        SELECT
        r.*
        FROM
        Apply_Tb_List a,
        Report_Test_Info r,
        Reg_Tb_PatientList p
        WHERE
        a.id = r.ApplyId
        AND p.PatID = a.PatId
        and r.applyTime between #{req.beginTime} and #{req.endTime}
        and r.HospitalId = #{req.hospitalCode}
        <if test="req.patid != null and req.patid != ''">
            and p.PatID in (SELECT p2.patid FROM Reg_Tb_PatientList p1 JOIN Reg_Tb_PatientList p2 ON p1.CertificateNo = p2.CertificateNo WHERE p1.PatId = #{req.patid})
        </if>
        <if test="req.user_source != null and req.user_source != ''">
            and r.PatientType = #{req.user_source}
        </if>
        <if test="req.patname != null and req.patname != ''">
            and r.PatName = #{req.patname}
        </if>

    </select>

    <select id="selectOldLisReports" resultType="com.sunhealth.ihhis.model.entity.report.ReportTestInfo">
        SELECT
        -- 核心修改：所有别名都从下划线改为了驼峰，以匹配 ReportTestInfo 的属性名
        -- 核心修改：对于Date类型字段，移除了 CONVERT 和 ISNULL，直接返回数据库的 datetime 类型

        -- 以下字段在 VReportBase_rj 中可能不存在，用 NULL 占位
        NULL AS id,
        NULL AS patientId,
        NULL AS cardNo,
        NULL AS bedNo,
        NULL AS hospitalName,
        NULL AS hospitalId,
        NULL AS speimenType,
        NULL AS isMicrobes,
        NULL AS applyDept,
        NULL AS applyWard,
        NULL AS applyWardName,
        NULL AS applyDoctor,
        NULL AS inspectionItem,
        NULL AS instrumentNo,
        [报告日期] AS speimentSampleTime,       -- 采样时间，源视图中似乎没有，用NULL
        NULL AS speimentReceiveDoctor,
        NULL AS testDept,
        [报告日期] AS testTime,
        NULL AS testDoctor,
        NULL AS reportDoctor,
        NULL AS reportUrl,
        NULL AS diagCode,
        NULL AS diagName,
        NULL AS criticalFlag,
        NULL AS criricalContent,
        NULL AS reportStatus,
        NULL AS receiveTime,
        NULL AS canceller,
        NULL AS cancelTime,

        -- 以下是根据 VReportBase_rj 字段进行的映射
        CASE
        WHEN [病人类别] = '门诊' THEN '01'
        WHEN [病人类别] = '住院' THEN '02'
        ELSE NULL  -- 使用 NULL 而不是空字符串，更规范
        END AS patientType,
        ISNULL([就诊流水号], '') AS visitNo,
        ISNULL([姓名], '') AS patName,
        ISNULL([就诊流水号], '') AS hospNo,
        ISNULL([报告唯一标识], '') AS reportNo,
        ISNULL([报告编号], '') AS applyId,          -- 申请单Id，使用原SQL逻辑映射到报告编号
        ISNULL([送检科室], '') AS applyDeptName,
        ISNULL([开单医生], '') AS applyDoctorName,
        [送检日期] AS applyTime,                    -- 返回 Date 类型
        ISNULL([报告名称], '') AS inspectionItemName,
        ISNULL([检验者], '') AS testDoctorName,     -- 检验者 映射为 TestDoctorName
        ISNULL([送检科室], '') AS testDeptName,     -- 检验科室 暂用送检科室
        [报告日期] AS reportTime,                   -- 返回 Date 类型
        [审核时间] AS auditorTime,                  -- 返回 Date 类型
        ISNULL([审核者], '') AS auditorDoctorName,
        ISNULL([检验者], '') AS reportDoctorName,   -- 报告医生 暂用检验者，请根据实际业务确认
        [送检日期] AS speimentReceiveTime,          -- 送检时间，使用送检日期，返回 Date 类型
        ISNULL([标本种类], '') AS speimenTypeName,
        ISNULL([备注], '') AS remark,
        [性别]                AS sex,
        [年龄]                AS age

        FROM
        [lisdatabase].[dbo].[VReportBase_rj]
        WHERE
        1=1
        <if test="patName != null and patName != ''">
            AND [姓名] = #{patName}
        </if>
        <if test="sex != null and sex != ''">
            AND [性别] = #{sex}
        </if>
        <if test="age != null and age != ''">
            AND [年龄] = #{age}
        </if>
        <if test="startDate != null">
            AND [报告日期] <![CDATA[ >= ]]> #{startDate}
        </if>
        <if test="endDate != null">
            AND [报告日期] <![CDATA[ < ]]> #{endDate}
        </if>
        <if test="reportNo != null and reportNo != ''">
            AND [报告编号] = #{reportNo}
        </if>
        <if test="reportDate != null">
            AND [报告日期] = #{reportDate}
        </if>
        <if test="userSource != null and userSource != ''">
            AND [病人类别] = #{userSource}
        </if>
    </select>



    <select id="selectOldLisReportResults" resultType="com.sunhealth.ihhis.model.entity.report.ReportTestDetail">
        SELECT

        NULL AS id,
        #{reportNo} AS reportNo,  -- 将传入的 reportNo 直接作为结果返回，保证对象完整性
        NULL AS reportDetailNo,
        NULL AS upperValue,
        NULL AS lowerValue,
        NULL AS receiveTime,    -- 源视图无此字段，设为 NULL
        ISNULL([项目编码], '') AS itemCode,
        ISNULL([项目名称], '') AS itemName,
        ISNULL([检验结果], '') AS itemResult,
        ISNULL([单位], '') AS resultUnit,
        ISNULL([标志], '') AS abnormalFlag,
        ISNULL([参考值], '') AS refValue,
        [排列次序] AS sort  -- 直接映射为 Integer 类型，MyBatis会自动转换

        FROM
        [lisdatabase].[dbo].[VReportDetail_rj]
        <where>
            <if test="reportNo != null and reportNo != ''">
                AND [报告编号] = #{reportNo}
            </if>
            <if test="reportDate != null">
                AND [报告日期] = #{reportDate}
            </if>
        </where>
        ORDER BY [排列次序] ASC
    </select>




</mapper>