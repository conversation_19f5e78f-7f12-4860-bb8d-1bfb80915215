package com.sunhealth.ihhis.dao.his;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.sunhealth.ihhis.model.entity.register.Appointment;
import java.util.Date;
import java.util.List;

public interface AppointmentMapper extends BaseMapper<Appointment> {

    List<Appointment> selectTodayAppointments(Date start, Date end, Integer deptId,
                                              Integer doctorId, String certificateNo);

}
