package com.sunhealth.ihhis.dao.his;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.sunhealth.ihhis.model.entity.register.RegisterList;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.springframework.stereotype.Repository;

@Repository
public interface RegisterListMapper extends BaseMapper<RegisterList> {
    @Select("update Reg_Tb_SpecialSourceRelation set status= #{status} where SpecialRegNo=#{regno}")
    void updateSpecialSourceRelation(@Param("regno") Long regno, @Param("status") Integer status);

    @Select("select KeyValue from System_Tb_Config where KeyCode='regFeeForFreeGrade' and HospitalId = #{hospitalId}")
    String selectFreeGrade(@Param("hospitalId") String hospitalCode);

    @Select("select KeyValue from System_Tb_Config where KeyCode='regFeeForFreeAge' and HospitalId = #{hospitalId}")
    String selectFreeAge(@Param("hospitalId") String hospitalCode);
}
