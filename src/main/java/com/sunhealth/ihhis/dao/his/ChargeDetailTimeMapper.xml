<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.sunhealth.ihhis.dao.his.ChargeDetailTimeMapper">
    <insert id="insertChargeDetailTime">
        INSERT INTO [dbo].[Reg_Tb_ChargeDetail_Time] (
            [ChargeDetl],
            [ChargeNo],
            [RecipeNum],
            [RecipeID],
            [RecipeDetlID],
            [ItemID],
            [ItemName],
            [Quantiy],
            [ItemCategory],
            [PackageNo],
            [GroupNo],
            [ClinicUnit],
            [BasicUnit],
            [ClinicQty],
            [Dosage],
            [DosageUnit],
            [DrugGauge],
            [CheckCode],
            [ExecuteDept],
            [Times],
            [Price],
            [ExpensePrice],
            [NonExpensePrice],
            [Status],
            [TotalAmount],
            [DoctorId],
            [DeptId],
            [DoctorLevel],
            [FeeType],
            [IsDrug],
            [SpecialFlag],
            [DataFrom],
            [FromFlag],
            [DiscountAmount],
            [IsDelete],
            [CreatedBy],
            [CreatedDate],
            [InsuranceTradeAmount],
            [InsuranceCashAmount],
            [SelfAmount],
            [ClassifyTotal],
            [USAGE],
            [CreditAmt],
            [RealAmt],
            [OtherAmt],
            [JzAmount],
            [OldChargeDetl],
        [HospitalCode]
        ) SELECT
            #{detailNo}, 
            #{chargeNo},
            pre.RecipeNum,
            pre.RecipeID,
            pre.RecipeDetlID,
            pre.ItemID,
            pre.ItemName,
            pre.Quantiy,
            pre.ItemCategory,
            pre.PackageNo,
            pre.GroupNo,
            pre.ClinicUnit,
            pre.BasicUnit,
            pre.ClinicQty,
            pre.Dosage,
            pre.DosageUnit,
            pre.DrugGauge,
            pre.CheckCode,
            pre.ExecuteDept,
            pre.Times,
            pre.Price,
            pre.ExpensePrice,
            pre.NonExpensePrice,
            pre.Status,
            pre.TotalAmount,
            pre.DoctorId,
            pre.DeptId,
            pre.DoctorLevel,
            amt.FeeType,
            pre.IsDrug,
            pre.SpecialFlag,
            pre.DataFrom,
            pre.FromFlag,
            pre.DiscountAmount,
            0,
            #{opId},
            #{time},
            amt.InsuranceTradeAmount ,
            amt.InsuranceCashAmount,
            amt.SelfAmount ,
            amt.ClassifyTotal ,
            pre.Usage,
            amt.CreditAmt,
            amt.RealAmt,
            amt.OtherAmt,
            amt.JzAmount,
            CASE

            WHEN det.OldChargeDetl IS NULL THEN
            #{detailNo} ELSE det.OldChargeDetl
        END oldchargedetl,
	pre.HospitalCode
FROM
	[dbo].[Reg_Tb_PreCharge] pre ( nolock )
	INNER JOIN [dbo].[Reg_Tb_PreCharge_Amt] amt ( nolock ) ON pre.ChargeNo = amt.ChargeNo
	AND pre.RecipeDetlID = amt.RecipeDetlID
	LEFT JOIN Reg_Tb_ChargeDetail_Time det ( nolock ) ON pre.RecipeDetlID = det.RecipeDetlID
	AND pre.OldChargeNo = det.ChargeNo
WHERE
	pre.ChargeNo = #{chargeNo}
	AND pre.RecipeDetlID = #{recipeDetlID}
    </insert>

</mapper>