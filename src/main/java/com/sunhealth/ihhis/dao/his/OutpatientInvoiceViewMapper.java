package com.sunhealth.ihhis.dao.his;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.sunhealth.ihhis.model.entity.invoice.OutpatientInvoiceView;
import java.util.List;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

public interface OutpatientInvoiceViewMapper extends BaseMapper<OutpatientInvoiceView> {
    @Select("select * from Reg_TV_OutpatientInvoice where ChargeNo = #{chargeNo}" +
            " and HospitalCode = #{hospitalCode} and status in (0, 80)")
    List<OutpatientInvoiceView> selectByChargeNo(@Param("chargeNo") String chargeNo,
                                                 @Param("hospitalCode") String hospitalCode);
}
