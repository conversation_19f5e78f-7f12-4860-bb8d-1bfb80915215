<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.sunhealth.ihhis.dao.his.PatientCardMapper" >

    <insert id="savePatientCard" parameterType="com.sunhealth.ihhis.model.entity.patient.RTPatientCard">
        insert into Reg_Tb_PatientCard(PatId, CardNo, HisCardNo, CardType, ChargeType, Status, CreateBy, CreateDate, UpdateBy, UpdateDate)
        values
            (#{p.patId}, #{p.cardNo}, #{p.hisCardNo}, #{p.cardType}, #{p.chargeType}, #{p.status}, #{p.createBy}, #{p.createDate}, #{p.updateBy}, #{p.updateDate})
    </insert>

    <select id="getCardNoBySfzAndHospCode" resultType="java.lang.String">
        select top 1 b.CardNo as cardNo from Reg_Tb_PatientList a
                                                 inner join Reg_Tb_PatientCard b on a.PatID = b.PatId and b.Status = 0
        where a.CertificateNo = #{certificateNo, jdbcType=VARCHAR} and a.HospitalCode = #{hospitalCode, jdbcType=INTEGER}
        and a.IsDelete = 0 and a.IsUse = 1 and b.CardNo like 'KH%' order by a.PatId desc
    </select>

    <select id="getBySfzAndHospCode" resultType="com.sunhealth.ihhis.model.entity.patient.RTPatientCard">
        select top 1 b.* from Reg_Tb_PatientList a
                                  inner join Reg_Tb_PatientCard b on a.PatID = b.PatId and b.Status = 0
        where a.CertificateNo = #{certificateNo, jdbcType=VARCHAR} and a.HospitalCode = #{hospitalCode, jdbcType=INTEGER}
        and a.IsDelete = 0 and a.IsUse = 1 and b.CardType = 0 and b.CardNo like 'KH%' order by a.PatId desc
    </select>

    <select id="getCardNoBySfz" resultType="java.lang.String">
        select ltrim(rtrim(isnull(a.CardNo,''))) as cardNo
        from Reg_Tb_PatientCard a (nolock)
                 inner join Reg_Tb_PatientList b (nolock) on a.PatId=b.PatID
        where b.CertificateNo = #{certificateNo, jdbcType=VARCHAR}
        and b.CertificateNo >'0' and a.CardNo like 'KH%'
    </select>

    <select id="getByCardNoAndType" resultType="com.sunhealth.ihhis.model.entity.patient.RTPatientCard">
        select b.* from Reg_Tb_PatientList a
        inner join Reg_Tb_PatientCard b on a.PatID = b.PatId and b.Status = 0
        where b.CardNo = #{cardNo, jdbcType=VARCHAR}
          and b.CardType = #{cardType, jdbcType=INTEGER}
          and a.HospitalCode = #{hospitalCode, jdbcType=INTEGER}
        and a.IsDelete = 0 and a.IsUse = 1
    </select>

</mapper>