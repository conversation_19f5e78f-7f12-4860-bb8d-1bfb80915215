package com.sunhealth.ihhis.dao.his;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.sunhealth.ihhis.model.entity.SystemTBItemCategory;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.springframework.stereotype.Repository;

@Repository
public interface SystemTBItemCategoryMapper extends BaseMapper<SystemTBItemCategory> {

    @Select("select * from System_Tb_ItemCategory where ItemCode = #{code} and HospitalCode = #{hospitalCode} ")
    SystemTBItemCategory selectCategoryByIdAndHospital(@Param("code") Integer code, @Param("hospitalCode") String hospitalCode);
}
