package com.sunhealth.ihhis.dao.his;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.sunhealth.ihhis.model.entity.report.ReportTestDetail;
import com.sunhealth.ihhis.model.entity.report.ReportTestInfo;
import java.util.List;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

public interface ReportTestDetailMapper extends BaseMapper<ReportTestDetail> {
    @Select("select * from Report_Test_Detail where ReportNo = #{report}")
    List<ReportTestDetail> selectByReportNo(@Param("report") String reportNo);
}
