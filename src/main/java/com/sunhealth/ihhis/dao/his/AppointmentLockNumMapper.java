package com.sunhealth.ihhis.dao.his;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.sunhealth.ihhis.model.entity.register.Appointment;
import com.sunhealth.ihhis.model.entity.register.AppointmentLockNum;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

public interface AppointmentLockNumMapper extends BaseMapper<AppointmentLockNum> {
    void insertAppointmentLockNum(AppointmentLockNum appointment);
    @Select("select * from TB_Appointment_LockNum where SQH = #{sqh}")
    AppointmentLockNum selectAppointmentLockNumBySqh(Integer sqh);

    @Select("update TB_Appointment_LockNum set Flag = #{flag} where SQH = #{sqh}")
    void updateFlagBySqh(@Param("flag") Integer flag,  @Param("sqh") Integer sqh);
}
