<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.sunhealth.ihhis.dao.his.PatientDetlMapper" >

    <insert id="savePatientDetl" parameterType="com.sunhealth.ihhis.model.entity.patient.RTPatientDetl">
        insert into Reg_Tb_PatientDetl(PatId, Marriage, Nation, Occupation, PatPhone, HospitalCode, Address, CreateTime, IsDelete, IsUse, OpCode, CreatedBy, CreatedDate, UpdateBy, UpdateDate)
        values
            (#{pd.patId}, #{pd.marriage}, #{pd.nation}, #{pd.occupation}, #{pd.patPhone}, #{pd.hospitalCode}, #{pd.address}, #{pd.createTime}, #{pd.isDelete}, #{pd.isUse}, #{pd.opCode}, #{pd.createdBy}, #{pd.createdDate}, #{pd.updateBy}, #{pd.updateDate})
    </insert>
</mapper>