package com.sunhealth.ihhis.dao.his;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.sunhealth.ihhis.model.entity.register.PreRegisterList;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.springframework.stereotype.Repository;

import java.util.Date;
import java.util.List;

@Repository
public interface PreRegisterListMapper extends BaseMapper<PreRegisterList> {


    @Select({" SELECT ",
            " 	a.RegNo ",
            " FROM ",
            " 	Reg_Tb_PreRegisterList a ",
            " 	INNER JOIN System_TB_ChargeType c ON a.ChargeType = c.ChargeTypeCode ",
            " WHERE ",
            "     a.HospitalCode = #{hospitalCode} ",
            " 	AND c.IsInsurance = 1 AND NOT EXISTS (SELECT * FROM Reg_Tv_RegisterList b WHERE a.RegNo = b.RegNo)",
            "   AND (",
            "     EXISTS (SELECT * FROM Reg_Online_GjYiBao_uploadFeeRecord d1 WHERE d1.RegNo = a.RegNo AND d1.Flag = 0 AND d1.CreatedDate > #{startTime})",
            "       OR EXISTS (SELECT * FROM Reg_Online_SHYiBao_uploadFeeRecord d2 WHERE d2.RegNo = a.RegNo AND d2.Flag = 0 AND d2.CreatedDate > #{startTime})",
            "     )"})
    List<Long> selectUnPaidYiBaoRegNo(@Param("hospitalCode") String hospitalCode, @Param("startTime") Date startTime);

}
