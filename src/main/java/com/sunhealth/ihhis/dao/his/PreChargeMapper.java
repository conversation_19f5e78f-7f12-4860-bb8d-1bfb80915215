package com.sunhealth.ihhis.dao.his;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.sunhealth.ihhis.model.entity.charge.PreCharge;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.Date;
import java.util.List;

public interface PreChargeMapper extends BaseMapper<PreCharge> {

    int insertPreCharge(@Param("preChargeNo") Long preChargeNo, @Param("chargeType") Integer chargeType, @Param(
        "chargeNo") Long chargeNo, @Param("createdDate") Date createdDate);

    @Select("SELECT * FROM dbo.Reg_Tb_PreCharge WHERE ChargeNo = #{chargeNo} AND HospitalCode = #{hospitalCode}")
    List<PreCharge> selectPreChargeList(@Param("chargeNo") String chargeNo, @Param("hospitalCode") String hospitalCode);

    @Select({" SELECT e.ChargeNo FROM Reg_Tb_PreCharge e ",
            " 	INNER JOIN System_TB_ChargeType c ON e.ChargeType = c.ChargeTypeCode ",
            " WHERE e.HospitalCode = #{hospitalCode} AND c.IsInsurance = 1 ",
            " 	AND NOT EXISTS (SELECT * FROM Reg_Tv_ChargeList b WHERE e.ChargeNo = b.ChargeNo)",
            "   AND (",
            "     EXISTS (SELECT * FROM Reg_Online_GjYiBao_uploadFeeRecord d1 WHERE d1.ChargeNo = e.ChargeNo AND d1.Flag = 1 AND d1.CreatedDate > #{startTime})",
            "       OR EXISTS (SELECT * FROM Reg_Online_SHYiBao_uploadFeeRecord d2 WHERE d2.ChargeNo = e.ChargeNo AND d2.Flag = 1 AND d2.CreatedDate > #{startTime})",
            "     )"})
    List<Long> selectUnPaidYiBaoChargeNo(@Param("hospitalCode") String hospitalCode, @Param("startTime") Date startTime);

}
