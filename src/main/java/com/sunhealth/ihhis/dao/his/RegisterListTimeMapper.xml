<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.sunhealth.ihhis.dao.his.RegisterListTimeMapper">

    <!-- 调用存储过程查询挂号是否可以退号 -->

    <resultMap id="invoice" type="com.sunhealth.ihhis.model.dto.InvoiceDTO">
        <result property="invoiceId" column="InvoiceID"/>
        <result property="regNo" column="RegNo"/>
        <result property="chargeType" column="ChargeType"/>
        <result property="patId" column="PatID"/>
        <result property="status" column="Status"/>
    </resultMap>
    <select id="selectInvoice" resultMap="invoice">
        select InvoiceID, RegNo, ChargeType, PatID, Status
        from Reg_TV_OutpatientInvoice
        where RegNo = #{regNo}
          AND PatID = #{patId}
          and HospitalCode = #{hospitalCode}
          and isdelete = 0
    </select>

    <resultMap id="outpatientInvoice" type="com.sunhealth.ihhis.model.entity.invoice.OutpatientInvoice">
        <result property="invoiceId" column="InvoiceID"/>
        <result property="regNo" column="RegNo"/>
        <result property="chargeType" column="ChargeType"/>
        <result property="patId" column="PatID"/>
        <result property="status" column="Status"/>
    </resultMap>
    <resultMap id="canReturnResult" type="java.util.HashMap">
        <result property="success" column=""/>
        <result property="msg" column="(1)"/>
    </resultMap>

    <select id="canReturnRegister" statementType="CALLABLE" resultMap="canReturnResult,outpatientInvoice">
        exec Usp_Charge_CanReturnRegister
            #{regno,mode=IN,jdbcType=BIGINT},
            #{ip,mode=IN,jdbcType=VARCHAR}
    </select>


    <select id="checkAge" statementType="CALLABLE" resultType="java.util.Map">
        exec usp_CheckAge
        #{certificateno,mode=IN,jdbcType=VARCHAR},
        #{subjectid,mode=IN,jdbcType=INTEGER}
    </select>


    <resultMap id="registerInfo" type="com.sunhealth.ihhis.model.dto.register.PatientRegistInfo">
        <result property="regno" column="regno"/>
        <result property="patid" column="patid"/>
        <result property="patname" column="patname"/>
        <result property="sex" column="sex"/>
        <result property="birth" column="birth"/>
        <result property="visit_time" column="visit_time"/>
        <result property="visit_time_span" column="visit_time_span"/>
        <result property="dept_id" column="dept_id"/>
        <result property="dept_name" column="dept_name"/>
        <result property="doctor_id" column="doctor_id"/>
        <result property="doctor_name" column="doctor_name"/>
        <result property="regist_type" column="regist_type"/>
        <result property="settle_status" column="settle_status"/>
        <result property="total_amount" column="total_amount"/>
    </resultMap>


    <select id="selectRegisterInfos" resultMap="registerInfo" >
        select rt.regno,
               rt.patid,
               rt.patname
             , (case when patient.Sex = 1 then '男' else '女' end) as sex
             , patient.Birthday as birth
             , rt.visitTime as visit_time
             , rt.DeptID as dept_id
             , dept.DeptName as dept_name
             , rt.DoctorID as doctor_id
             , doctor.Name as doctor_name
             , (CASE
                WHEN doclevel.HisDictionaryName LIKE '%普通%' THEN '0'
                WHEN doclevel.HisDictionaryName LIKE '%主任%' THEN '2'
                WHEN doclevel.HisDictionaryName LIKE '%急诊%' THEN '1'
                ELSE '0'
               end) as regist_type
             , (case when rt.Status = 0 then 0 else 1 end) as record_status
             , '1' as settle_status
             , CAST(t.TotalAmount * 100 AS INT)  as total_amount
             , '' as visit_time_span
        from Reg_Tv_RegisterList rt
                 inner join Reg_Tb_PatientList patient on rt.PatID = patient.PatID and patient.IsUse = 1
                 inner join System_Tb_Worker doctor on rt.DoctorID =  doctor.WorkerId
                 inner join System_Tb_Department dept on rt.DeptID = dept.DeptId
                 inner join TB_Dic_HisDictionaryExt doclevel on doclevel.HisDictionaryCode = rt.DoctorLevel and doclevel.DictionaryTypeID=114 and doclevel.HospitalCode=rt.HOSPITALCODE and doclevel.lang=1
                 inner join Reg_TV_OutpatientInvoice t on rt.RegNo = t.RegNo and t.Flag = 1
        where rt.patid = #{req.patid}
        <if test="req.beginDate != null">
            and t.CreatedDate <![CDATA[ >= ]]> #{req.beginDate}
        </if>
        <if test="req.endDate != null">
            and t.CreatedDate <![CDATA[ < ]]> #{req.endDate}
        </if>
          and rt.hospitalCode = #{req.hospitalCode}
    </select>


    <resultMap id="medicalRecord" type="com.sunhealth.ihhis.model.dto.medicalrecord.PatientMedicalRecord">
        <result property="regno" column="regno"/>
        <result property="document_id" column="id"/>
        <result property="cre_doctor_id" column="ysbm"/>
        <result property="cre_doctor_name" column="ysxm"/>
        <result property="cre_dept_id" column="ksbm"/>
        <result property="cre_dept_name" column="ksmc"/>
        <result property="cre_create_date" column="cjrq"/>
        <result property="bl" column="bl"/>
        <result property="bl_html" column="blhtml"/>
    </resultMap>

    <select id="selectMedicalRecord" resultMap="medicalRecord" >
        SELECT
            c.regno,
            c.id,
            c.ysbm,
            c.ysxm,
            c.ksbm,
            c.ksmc,
            c.cjrq,
--             CASE
--                 WHEN NOT EXISTS (
--                     SELECT 1
--                     FROM MZYS_TB_DZBL_NODE n
--                     WHERE n.doc_id = c.id
--                     ) THEN c.bl
--                 ELSE NULL
--                 END AS bl
            c.blhtml,
            CASE
                WHEN c.blhtml IS NOT NULL THEN NULL  -- blhtml有值则bl为null
                ELSE c.bl                            -- blhtml为null则保留bl
                END AS bl
        FROM Reg_Tv_RegisterList a
                 INNER JOIN MZYS_TB_DZBL c ON
                    c.RegNo = a.RegNo AND
                    c.HospitalCode = a.HospitalCode
        WHERE a.Status = 0
         -- AND c.tempCode IN (1, 2)
          AND a.PatID = #{req.patid}
          AND c.cjrq <![CDATA[ >= ]]> #{req.beginDate}
          AND c.cjrq <![CDATA[ < ]]> #{req.endDate}
         AND c.tjbj = 1
        ORDER BY c.cjrq DESC;
    </select>

    <resultMap id="medicalRecordInfo" type="com.sunhealth.ihhis.model.dto.medicalrecord.PatientMedicalRecordInfo">
        <result property="document_code" column="node_name"/>
        <result property="document_name" column="node_name"/>
        <result property="document_content" column="node_value"/>
        <result property="number" column="number"/>
    </resultMap>

    <select id="selectMedicalRecordInfo" resultMap="medicalRecordInfo" >
        SELECT
            node_name,
            node_value,
            ROW_NUMBER ( ) OVER ( ORDER BY create_time ASC ) AS number
        FROM
            MZYS_TB_DZBL_NODE (nolock)
        WHERE
            doc_id = #{docId};
    </select>


    <select id="checkVisitFlag"
        statementType="CALLABLE"
        resultType="java.lang.Integer">
        { CALL dbo.Usp_Charge_CheckVisitFlag (
        @sfz = #{sfz, jdbcType=VARCHAR},
        @hospitalCode = #{hospitalCode, jdbcType=INTEGER},
        @CardNo = '',
        @OutpatientNo = ''
        ) }
    </select>

</mapper>