package com.sunhealth.ihhis.dao.his;

import com.sunhealth.ihhis.model.dto.schedule.*;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.springframework.stereotype.Repository;

import java.util.Date;
import java.util.List;

@Repository
public interface SchedulingMapper {

    List<SourceNumber> listSchedulingSourceNumber(@Param("req") SourceNumberReq req, @Param("timeSpanId") List<Integer> timeSpanId);

    /**
     * 获取号序状态
     * @param req
     * @return
     */
    List<SourceNumber> getSchedulingSourceNumber(@Param("req") SourceNumberReq req);

    List<DeptList> listSchedulingDept(@Param("req") DeptListReq req);

    List<SourceDetails> listSourceDetails(@Param("req") SourceDetailsReq req);

    List<DeptSourceDetails> listDeptSourceDetails(@Param("req") DeptSourceDetailsReq req);

    List<SourceNumber> listSourceNumberByDept(@Param("req") DeptSourceDetailsReq req);

    List<DoctorSourceDetails> listDoctorSourceDetails(@Param("req") DoctorSourceDetailsReq req);

    List<SourceNumber> listSourceNumberByDoct(@Param("req") DoctorSourceDetailsReq req);

    ScheduleDetail selectScheduleDetail(@Param("schedulingId") String schedulingId,
                                        @Param("hospitalCode") String hospitalCode);

    @Select("select SubjectId from TB_Cinfiger_Scheduling where ID = #{schedulingId} and HOSPITALCODE = "
        + "#{hospitalCode}")
    Integer selectCourseIdBySchedulingId(@Param("schedulingId") String schedulingId,
                                         @Param("hospitalCode") String hospitalCode);

    @Select("select Quatity from TB_Cinfiger_Subjectfeeitem where subjectID = #{courseId} and FEEitemCode = "
        + "#{itemCode}")
    Integer selectItemQTYByCourseIdAndItemCode(@Param("courseId") String courseId,
                                               @Param("itemCode") String itemCode);

    @Select("update TB_Config_SubjectNumDetail set Status = #{num.status} where NumId = #{num.NumId}")
    void updateSourceNumberStatus(@Param("num") SourceNumber number);

    @Select("update TB_Appointment set AppointmentStatus = #{status} where AppointmentID = #{appointmentId}")
    void updateAppointmentStatus(@Param("appointmentId") String appointmentId, @Param("status") Integer status);

    @Select("SELECT top 1.ID from TB_Cinfiger_Scheduling where Subjectid = #{subjectid} and DutyDate = #{dutyDate} "
        + "and isDelete = 0 and IsUse = 1;")
    Integer selectSchedulingIdBySubjectidAndDutyDate(@Param("subjectid") Integer subjectid, @Param("dutyDate") Date dutyDate);

    @Select("SELECT d.HisDictionaryName  "
        + "FROM TB_Dic_HisDictionaryExt d "
        + "JOIN TB_Cinfiger_SubjectItem s ON d.HisDictionaryID = s.Grade "
        + "WHERE s.SubjectID = #{courceId} ")
    List<String> selectSchedulingTypeByCourceId(@Param("courceId") Integer courceId);

    List<CurrentDayAppointment> currentDayAppointment(@Param("req") SourceDetailsReq req);

    List<CurrentDoctorSourceDetails> getCurrentDayDoctorSourceDetail(@Param("req") CurrentDoctorSourceDetailsReq req);
}
