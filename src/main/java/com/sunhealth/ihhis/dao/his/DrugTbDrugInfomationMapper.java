package com.sunhealth.ihhis.dao.his;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.sunhealth.ihhis.model.entity.charge.DrugTbDrugInfomation;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.springframework.stereotype.Repository;

@Repository
public interface DrugTbDrugInfomationMapper extends BaseMapper<DrugTbDrugInfomation> {

    @Select("select * from Drug_Tb_DrugInfomation where drugId = #{drugId} and hospitalId = #{hospitalId}")
    DrugTbDrugInfomation selectOneByDrugIdAndHospitalId(@Param("drugId") Integer drugId, @Param("hospitalId") String hospitalId);

}
