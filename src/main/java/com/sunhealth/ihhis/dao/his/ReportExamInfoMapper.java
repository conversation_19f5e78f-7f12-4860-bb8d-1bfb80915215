package com.sunhealth.ihhis.dao.his;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.sunhealth.ihhis.model.dto.report.ReportListReq;
import com.sunhealth.ihhis.model.entity.report.ReportExamDetail;
import com.sunhealth.ihhis.model.entity.report.ReportExamInfo;
import com.sunhealth.ihhis.model.entity.report.ReportTestInfo;
import java.util.Date;
import java.util.List;
import org.apache.ibatis.annotations.Param;

public interface ReportExamInfoMapper extends BaseMapper<ReportExamInfo> {
    List<ReportExamInfo> list(@Param("req") ReportListReq req);

    /**
     * 从旧RIS系统（链接服务器）查询检查报告列表
     * @param patId 患者PID (对应 patId 字段)
     * @param comeFlag 就诊来源标识 (0:门诊, 1:住院, 对应 ComeFlag 字段)
     * @param startDate 开始时间
     * @param endDate 结束时间
     * @return 检查报告列表
     */
    List<ReportExamInfo> selectOldRisReports(
        @Param("patId") String patId,
        @Param("comeFlag") Integer comeFlag,
        @Param("startDate") Date startDate,
        @Param("endDate") Date endDate,
        @Param("reportNo") String reportNo); // 新增 reportNo 以便详情查询复用

    /**
     * 从旧RIS系统（链接服务器）查询检查报告详情
     * @param reportNo 报告唯一标识 (对应 RisId)
     * @return 检查报告详情列表（通常只有一个）
     */
    List<ReportExamDetail> selectOldRisReportDetails(@Param("reportNo") String reportNo);
}
