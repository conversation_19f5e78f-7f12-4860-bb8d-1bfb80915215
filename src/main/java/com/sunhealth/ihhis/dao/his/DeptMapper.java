package com.sunhealth.ihhis.dao.his;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.sunhealth.ihhis.model.dto.DeptInfo;
import com.sunhealth.ihhis.model.entity.Dept;
import com.sunhealth.ihhis.model.entity.report.ReportExamDetail;
import org.apache.ibatis.annotations.Select;

public interface DeptMapper extends BaseMapper<Dept> {

    Page<DeptInfo> selectPageDept(Page page, Integer status, Integer hospitalId, String channelType);
    @Select("select top 1 DeptName from System_Tb_Department where DeptCode = #{deptId} and HospitalId=#{hospitalCode}")
    String selectDeptName(String deptId, String hospitalCode);
}
