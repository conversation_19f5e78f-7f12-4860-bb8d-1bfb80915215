package com.sunhealth.ihhis.dao.his;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.sunhealth.ihhis.model.dto.medicalrecord.PatientMedicalRecord;
import com.sunhealth.ihhis.model.dto.medicalrecord.PatientMedicalRecordInfo;
import com.sunhealth.ihhis.model.dto.outpatientcharge.OutpatientRecipeReq;
import com.sunhealth.ihhis.model.dto.register.PatientRegistInfo;
import com.sunhealth.ihhis.model.dto.register.PatientRegistInfoReq;
import com.sunhealth.ihhis.model.entity.register.RegisterListTime;
import java.util.List;
import java.util.Map;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Update;
import org.springframework.stereotype.Repository;

@Repository
public interface RegisterListTimeMapper extends BaseMapper<RegisterListTime> {
    @Select({"SELECT * FROM Reg_Tv_RegisterListTime WHERE RegNo = #{regNo} AND IsDelete = 0 and status = 80"})
    RegisterListTime selectByRegNoRefund(Long regNo);

    @Update("UPDATE Reg_Tb_RegisterList_Time SET RegistNum = RegistOrder WHERE RegNo = #{regNo} AND status = 0")
    int updateRegistNumToRegistOrder(@Param("regNo") Long regNo);

    Map<String, Object> checkAge(@Param("certificateno") String certificateno,
                                 @Param("subjectid") int subjectid);

    List<PatientRegistInfo> selectRegisterInfos(@Param("req") PatientRegistInfoReq req);

    List<PatientMedicalRecord> selectMedicalRecord(@Param("req") OutpatientRecipeReq req);
    List<PatientMedicalRecordInfo> selectMedicalRecordInfo(@Param("docId") String docId);

    /**
     * 调用存储过程检查就诊标志
     * @param sfz 身份证号
     * @param hospitalCode 医院编码
     * @return 存在就诊记录的数量
     */
    Integer checkVisitFlag(
        @Param("sfz") String sfz,
        @Param("hospitalCode") Integer hospitalCode
    );
}
