package com.sunhealth.ihhis.dao.his;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.sunhealth.ihhis.model.entity.patient.RTPatientDetl;
import com.sunhealth.ihhis.model.entity.patient.RTPatientList;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.springframework.stereotype.Repository;

@Repository
public interface PatientDetlMapper extends BaseMapper<RTPatientDetl> {
    @Select("select * from Reg_Tb_PatientDetl where PatId = #{id}")
    RTPatientDetl selectByPatId(@Param("id") String id);

    /**
     * 新增就诊人详情信息
     * @param patientDetl
     * @return
     */
    int savePatientDetl(@Param("pd") RTPatientDetl patientDetl);

    /**
     * 更新就诊人就诊信息
     * @param pid 就诊人ID
     * @return
     */
    // update Reg_Tb_PatientDetl set VisitFlag=isnull(VisitFlag,0)+(1) where PatID={#pid}
    @Select("update Reg_Tb_PatientDetl set VisitFlag=isnull(VisitFlag,0)+(1) where PatID=#{pid}")
    void updatePatientVisitInfo(@Param("pid") Long pid);

    @Select("update Reg_Tb_PatientDetl set VisitFlag=isnull(VisitFlag,0)+(-1), NewPatID=#{newPid}  where PatID=#{pid}")
    void updatePatientVisitInfoAndNewPatId(@Param("pid") Long pid, @Param("newPid") Long newPid);
}
