<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.sunhealth.ihhis.dao.his.YBJSPatSfzToKHMapper" >

    <select id="getKHBySfz" resultType="java.lang.String">
        select top 1 KH from dbo.Tbt_Recipe_YBJS_PatSfzToKH nolock where PatSfz = #{certificateNo, jdbcType=VARCHAR}
    </select>

</mapper>