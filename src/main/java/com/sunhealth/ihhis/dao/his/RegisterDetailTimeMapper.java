package com.sunhealth.ihhis.dao.his;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.sunhealth.ihhis.model.entity.register.RegisterDetailTime;
import java.util.List;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Select;

@Mapper
public interface RegisterDetailTimeMapper extends BaseMapper<RegisterDetailTime> {
    @Select("SELECT * FROM Reg_Tb_RegisterDetail_Time WHERE RegNo = #{regNo} and IsDelete = 0")
    List<RegisterDetailTime> selectListByRegNo(Long regNo);
}
