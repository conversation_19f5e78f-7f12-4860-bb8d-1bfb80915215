package com.sunhealth.ihhis.dao.his;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.sunhealth.ihhis.model.entity.TBDicHisDictionary;
import org.apache.ibatis.annotations.Select;

public interface DictionaryMapper extends BaseMapper<TBDicHisDictionary> {
    @Select({"SELECT * FROM TB_Dic_HisDictionary WHERE DictionaryTypeID = #{dictionaryTypeID} AND HisDictionaryName = #{name} ",
            "AND HospitalId = #{hospitalId}"})
    TBDicHisDictionary selectByDictionaryTypeIDAndHisDictionaryNameAndHospitalId(Integer dictionaryTypeID, String name,
                                                                                 String hospitalId);

}
