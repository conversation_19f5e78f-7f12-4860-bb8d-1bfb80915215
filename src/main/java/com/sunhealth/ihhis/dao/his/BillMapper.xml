<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.sunhealth.ihhis.dao.his.BillMapper">

    <resultMap id="billOrder" type="com.sunhealth.ihhis.model.dto.bill.BillOrder">
        <result property="order_type" column="order_type"/>
        <result property="pay_type" column="pay_type"/>
        <result property="transaction_time" column="transaction_time"/>
        <result property="transaction_id" column="transaction_id"/>
        <result property="serial_no" column="serial_no"/>
        <result property="settle_id" column="settle_id"/>
        <result property="transaction_status" column="transaction_status"/>
        <result property="settlement_order_amount" column="settlement_order_amount"/>
        <result property="voucher_amount" column="voucher_amount"/>
        <result property="merchant_refund_number" column="merchant_refund_number"/>
        <result property="refund_amount" column="refund_amount"/>
        <result property="self_refund_amount" column="self_refund_amount"/>
        <result property="medicare_refund_amount" column="medicare_refund_amount"/>
        <result property="self_order_amount" column="self_order_amount"/>
        <result property="medicare_order_amount" column="medicare_order_amount"/>
        <result property="pay_order_id" column="pay_order_id"/>
        <result property="recharge_voucher_refund_amount" column="recharge_voucher_refund_amount"/>
        <result property="refund_type" column="refund_type"/>
        <result property="refund_status" column="refund_status"/>
        <result property="product_name" column="product_name"/>
        <result property="order_amount" column="order_amount"/>
        <result property="refund_application_amount" column="refund_application_amount"/>
    </resultMap>

    <!--查询门诊账单-支付 -->
    <select id="getPaymentOutPatientBill"  resultMap="billOrder">
        select case i.ChargeType when 86 then 1 when 10 then 0 when 30 then 1 end order_type,
               iw.Payway pay_type,
               i.CreatedDate transaction_time,
               i.PayOrderNo serial_no,
               i.ReturnOrderNo transaction_id,
               case when i.Flag = 1 then i.RegNo when i.Flag = 2 then i.ChargeNo end settle_id,
               'SUCCESS' transaction_status,
               COALESCE(i.TotalAmount, 0) settlement_order_amount,
               0 voucher_amount,
               COALESCE(i.TotalAmount, 0) order_amount, --订单总金额
               COALESCE(i.PayFee, 0) self_order_amount, --订单支付金额-自费
               COALESCE(i.PubPay, 0) + COALESCE(i.CurrAccountPay, 0) medicare_order_amount, --订单支付金额-医保
               '' merchant_refund_number,
               0 refund_amount, --退款总金额
               0 self_refund_amount, --退款金额-自费
               0 medicare_refund_amount, --退款金额-医保
               0 refund_application_amount, --申请退款金额
               0 recharge_voucher_refund_amount,
              case i.Flag
                   when 1 then (SELECT TOP 1 PayOrderId_Or_OrderNo
                                    FROM (
                                    SELECT PayOrdId AS PayOrderId_Or_OrderNo
                                    FROM Reg_Online_GjYiBao_uploadFeeRecord
                                    WHERE RegNo = i.RegNo
                                    UNION
                                    SELECT OrderNo
                                    FROM Reg_Online_SHYiBao_uploadFeeRecord
                                    WHERE RegNo = i.RegNo) as tempYibao1
                                )
                   when 2 then (SELECT TOP 1 PayOrderId_Or_OrderNo
                                    FROM (
                                    SELECT PayOrdId AS PayOrderId_Or_OrderNo
                                    FROM Reg_Online_GjYiBao_uploadFeeRecord
                                    WHERE  ChargeNo = i.ChargeNo
                                    UNION
                                    SELECT OrderNo
                                    FROM Reg_Online_SHYiBao_uploadFeeRecord
                                    WHERE  ChargeNo = i.ChargeNo) as tempYibao2
                                )
                  end pay_order_id,
               '' refund_type,
               '' refund_status,
               case i.Flag when 1 then '预约挂号' when 2 then '门诊缴费' end product_name
        from Reg_TV_OutpatientInvoice i
        left join Reg_TV_RegisterList register on i.RegNo = register.RegNo
        left join Reg_Tv_ChargeList charge on i.ChargeNo = charge.ChargeNo
        left join Reg_Tv_OpInvoicePayway iw on i.InvoiceID = iw.InvoiceID
        where i.CreatedDate <![CDATA[ >= ]]> #{req.beginDate}
        and i.CreatedDate <![CDATA[ < ]]> #{req.endDate}
        and i.Status in (0, 80)
        and i.OpCode = #{req.opCode}  -- 和线下交易账单区分
        and i.HospitalCode = #{req.hospitalCode}
        and i.IsDelete = 0
        and not exists (SELECT 1 FROM Reg_tb_ThirdAddAcount taa  WHERE taa.regno = i.regno and taa.DataFrom = '掌上医院-护理到家') --不查护理到家的数据
        and iw.Payway in
        <foreach item="payType" collection="payTypes" open="(" separator="," close=")">
            #{payType}
        </foreach>
    </select>

    <!--查询门诊账单-退款 -->
    <select id="getRefundOutPatientBill"  resultMap="billOrder">
        select case ri.ChargeType when 86 then 1 when 10 then 0 when 30 then 1 end order_type,
        iw.Payway pay_type,
        ri.CreatedDate transaction_time,
        r.PayOrderNo serial_no,
        r.ReturnOrderNo transaction_id,
        case ri.Flag when 1 then register.ReturnRegNo when 2 then charge.ReturnChargeNo end settle_id,
        'REFUND' transaction_status,
        0 settlement_order_amount,
        0 voucher_amount,
        0 order_amount, --订单总金额
        0 self_order_amount, --订单支付金额-自费
        0 medicare_order_amount, --订单支付金额-医保
        ri.ChargeNo merchant_refund_number,
        COALESCE(ABS(ri.TotalAmount), 0) refund_amount, --退款总金额
        COALESCE(ABS(ri.PayFee), 0) self_refund_amount, --退款金额-自费
        COALESCE(ABS(ri.PubPay), 0) + COALESCE(ABS(ri.CurrAccountPay), 0) medicare_refund_amount, --退款金额-医保
        0 recharge_voucher_refund_amount,
        COALESCE(ABS(ri.TotalAmount), 0) refund_application_amount,
        case ri.Flag
            when 1 then (SELECT TOP 1 PayOrderId_Or_OrderNo
                FROM (
                SELECT PayOrdId AS PayOrderId_Or_OrderNo
                FROM Reg_Online_GjYiBao_uploadFeeRecord
                WHERE RegNo = r.RegNo
                UNION
                SELECT OrderNo
                FROM Reg_Online_SHYiBao_uploadFeeRecord
                WHERE RegNo = r.RegNo) as tempYibao1
                )
            when 2 then (SELECT TOP 1 PayOrderId_Or_OrderNo
                FROM (
                SELECT PayOrdId AS PayOrderId_Or_OrderNo
                FROM Reg_Online_GjYiBao_uploadFeeRecord
                WHERE  ChargeNo = r.ChargeNo
                UNION
                SELECT OrderNo
                FROM Reg_Online_SHYiBao_uploadFeeRecord
                WHERE  ChargeNo = r.ChargeNo) as tempYibao2
                )
            end pay_order_id,
        'ORIGINAL' refund_type,
        'SUCCESS' refund_status,
        case ri.Flag when 1 then '预约挂号' when 2 then '门诊缴费' end product_name
        from Reg_TV_OutpatientInvoice ri --退款发票
        inner join Reg_TV_OutpatientInvoice r on ri.ReturnInvoiceID = r.InvoiceID --支付发票
        left join Reg_TV_RegisterList register on r.RegNo = register.RegNo
        left join Reg_Tv_ChargeList charge on r.ChargeNo = charge.ChargeNo
        left join Reg_Tv_OpInvoicePayway iw on ri.InvoiceID = iw.InvoiceID
        where ri.CreatedDate <![CDATA[ >= ]]> #{req.beginDate}
        and ri.CreatedDate <![CDATA[ < ]]> #{req.endDate}
        and ri.Status in (120)
        and ri.OpCode = #{req.opCode}  -- 和线下交易账单区分
        and ri.HospitalCode = #{req.hospitalCode}
        and ri.IsDelete = 0
        and not exists (SELECT 1 FROM Reg_tb_ThirdAddAcount taa  WHERE taa.regno = r.regno and taa.DataFrom = '掌上医院-护理到家') --不查护理到家的数据
        and iw.Payway in
        <foreach item="payType" collection="payTypes" open="(" separator="," close=")">
            #{payType}
        </foreach>
    </select>

    <!--查询住院账单 没有退款仅有充值，住院预交金线下退款-->
    <select id="getInPatientBill" resultMap="billOrder">
        select
        0 order_type,
        a.PayType pay_type,
        a.PayTime transaction_time,
        b.ZfbTradeNo transaction_id,
        a.PayTradeNo serial_no,
        a.PreChargeId settle_id,
        case a.BillStatus when 120 then 'REFUND' else 'SUCCESS' end transaction_status,
        case a.BillStatus when 120 then 0 else COALESCE(ABS(a.Amount), 0) end settlement_order_amount,
        0 voucher_amount,
        case a.BillStatus when 120 then a.PreChargeId end merchant_refund_number, --商户退款单号
        case a.BillStatus when 120 then 0 else COALESCE(ABS(a.Amount), 0) end order_amount,--订单支付总金额
        case a.BillStatus when 120 then 0 else COALESCE(ABS(a.Amount), 0) end self_order_amount,--订单支付总金额-自费
        0 medicare_order_amount, -- 订单支付总金额-医保
        case a.BillStatus when 120 then COALESCE(ABS(a.Amount), 0) else 0 end refund_amount,--订单退款总金额
        case a.BillStatus when 120 then COALESCE(ABS(a.Amount), 0) else 0 end self_refund_amount,--订单退款总金额-自费
        0 medicare_refund_mount, --订单退款总金额-医保
        '' pay_order_id,
        0 recharge_voucher_refund_amount,
        case a.BillStatus when 120 then 'ORIGINAL' else '' end refund_type,
        case a.BillStatus when 120 then 'SUCCESS'  else '' end refund_status,
        case a.BillStatus when 120 then COALESCE(ABS(a.Amount), 0) else 0 end refund_application_amount,
        '预交金' product_name
        from IO_Tb_PatientPreCharge a
        inner join IO_Tb_ZfbJy b on (a.PreChargeId=b.PreChargeId or a.PairId = b.PreChargeId) and b.HospTradeNo like 'IH%'
        where a.PayTime <![CDATA[ >= ]]> #{req.beginDate}
        and a.PayTime <![CDATA[ < ]]> #{req.endDate}
        and a.HospitalId = #{req.hospitalCode}
        and a.IsDelete = 0
        and b.PayType in
        <foreach item="payType" collection="payTypes" open="(" separator="," close=")">
            #{payType}
        </foreach>
    </select>
</mapper>