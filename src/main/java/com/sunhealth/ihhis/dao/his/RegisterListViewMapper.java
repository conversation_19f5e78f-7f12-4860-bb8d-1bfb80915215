package com.sunhealth.ihhis.dao.his;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.sunhealth.ihhis.model.entity.view.RegisterListView;
import org.apache.ibatis.annotations.Select;

public interface RegisterListViewMapper extends BaseMapper<RegisterListView> {
    @Select("select * from Reg_Tv_RegisterList where RegNo = #{regNo} and IsDelete = 0")
    RegisterListView selectByRegNo(Long regNo);
}
