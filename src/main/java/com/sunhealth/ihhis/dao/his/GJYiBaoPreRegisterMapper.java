package com.sunhealth.ihhis.dao.his;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.sunhealth.ihhis.model.entity.register.GJYiBaoPreRegister;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.springframework.stereotype.Repository;

import java.util.Date;
import java.util.List;

@Repository
public interface GJYiBaoPreRegisterMapper extends BaseMapper<GJYiBaoPreRegister> {

    @Select({"SELECT a.RegNo FROM Reg_GjYiBao_PreRegister a ",
            "WHERE a.CreateTime > #{startTime} AND HospitalCode = #{hospitalCode} ",
            " AND NOT EXISTS (SELECT * FROM Reg_GjYiBao_Register b WHERE a.RegNo = b.RegNo)"})
    List<Long> selectUnPaidRegNo(@Param("hospitalCode") String hospitalCode, @Param("startTime") Date startTime);

}
