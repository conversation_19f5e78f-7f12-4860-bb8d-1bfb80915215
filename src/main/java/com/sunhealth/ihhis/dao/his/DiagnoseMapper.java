package com.sunhealth.ihhis.dao.his;

import com.sunhealth.ihhis.model.dto.bill.BillOrder;
import com.sunhealth.ihhis.model.dto.bill.HisBillReq;
import com.sunhealth.ihhis.model.entity.Diagnose;
import java.util.List;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.springframework.stereotype.Repository;

@Repository
public interface DiagnoseMapper {
    @Select("select * from MZYS_TB_MZYSZD where regno = #{regno} and yybm = #{yybm}")
    List<Diagnose> selectByRegno(@Param("regno") String regno, @Param("yybm")String yybm);

}
