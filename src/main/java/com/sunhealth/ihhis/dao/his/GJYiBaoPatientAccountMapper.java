package com.sunhealth.ihhis.dao.his;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.sunhealth.ihhis.model.entity.gjyibao.GjYiBaoPatientAccount;
import org.apache.ibatis.annotations.Select;

public interface GJYiBaoPatientAccountMapper extends BaseMapper<GjYiBaoPatientAccount> {
    @Select("select * from Reg_GjYiBao_PatientAccount where PsnNo = #{psnNo} and PatId = #{patId} and CardNo = "
        + "#{cardNo}")
    GjYiBaoPatientAccount selectOneByPsnNoAndPatIdAndCardNo(String psnNo, String patId, String cardNo);
}
