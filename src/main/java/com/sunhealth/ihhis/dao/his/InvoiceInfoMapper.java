package com.sunhealth.ihhis.dao.his;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.sunhealth.ihhis.model.entity.invoice.InvoiceInfo;
import org.apache.ibatis.annotations.Select;

public interface InvoiceInfoMapper extends BaseMapper<InvoiceInfo> {

    @Select("update Reg_Tb_InvoiceInfo set State=#{state} where InvoiceID=#{invoiceId}")
    void updateStateByInvoiceId(Integer state, Long invoiceId);
    // SELECT TOP
    //		1 @newInvoiceID = id
    //	FROM
    //		Reg_Tb_InvoiceInfo nolock
    //	WHERE
    //		EmployeeID =@EmployeeID
    //		AND IsDelete = 0
    //		AND IsUse = 0
    //	ORDER BY
    //		CreatedDate
    @Select("SELECT TOP 1 * FROM Reg_Tb_InvoiceInfo WHERE EmployeeID=#{employeeID} AND IsDelete=0 AND IsUse=0 ORDER BY"
        + " CreatedDate")
    InvoiceInfo selectTopId(Long employeeID);
}
