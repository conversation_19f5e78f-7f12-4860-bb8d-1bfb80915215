<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.sunhealth.ihhis.dao.his.PreChargeMapper">
    <insert id="insertPreCharge" >
        INSERT INTO [dbo].[Reg_Tb_PreCharge] (
            ChargeType,
            RegistType,
            CardNo,
            PatId,
            NewItemID,
            [RegNo],
            [ChargeNo],
            [RecipeNum],
            [RecipeID],
            [RecipeDetlID],
            [ItemID],
            [ItemName],
            [Quantiy],
            [ItemCategory],
            [ItemCategoryName],
            [PackageNo],
            [GroupNo],
            [ClinicUnit],
            [BasicUnit],
            [ClinicQty],
            [Dosage],
            [DosageUnit],
            [DrugGauge],
            [CheckCode],
            [ExecuteDept],
            [Times],
            [Price],
            [ExpensePrice],
            [NonExpensePrice],
            [Status],
            [TotalAmount],
            [DoctorId],
            [DeptId],
            [DoctorLevel],
            [FeeType],
            [IsDrug],
            [DataFrom],
            [FromFlag],
            USAGE,
            [HospitalCode],
            RecipeOn,
            DeptKind,
            PatType,
            IndustrialInjury,
            CureCode,
            DiagCode,
            SpecialFlag,
            JsFlag,
            DebitPayInvoiceID
            <!-- [CreatedDate] -->
        ) SELECT
            #{chargeType, jdbcType=INTEGER} [ChargeType],
            a.RegistType,
            a.CardNo,
            a.PatId,
            b.NewPatID,
            a.[RegNo],
            #{chargeNo, jdbcType=BIGINT} [ChargeNo],
            [RecipeNum],
            [RecipeID],
            [RecipeDetlID],
            [ItemID],
            a.[ItemName],
            Quantiy,
            [ItemCategory],
            c.ItemName [ItemCategoryName],
            [PackageNo],
            [GroupNo],
            LEFT([ClinicUnit], 7), -- 在这里对 ClinicUnit 字段进行截断
            [BasicUnit],
            [ClinicQty],
            [Dosage],
            [DosageUnit],
            [DrugGauge],
            [CheckCode],
            [ExecuteDept],
            [Times],
            [Price],
            [ExpensePrice],
            [NonExpensePrice],
            0,
            a.[TotalAmount],
            a.[DoctorId],
            a.[DeptId],
            b.[DoctorLevel],
            [FeeType],
            [IsDrug],
            [DataFrom],
            [FromFlag],
            USAGE,
            a.[HospitalCode],
            a.RecipeOn,
            b.DeptKind,
            r.PatType,
            r.IndustrialInjury,
            r.CureCode,
            '',
            r.SpecialFlag,
            b.JsFlag,
            a.DebitPayInvoiceID
           <!-- #{createdDate} -->
   FROM
       [dbo].[Reg_Tb_PreChargeDetail] a ( nolock )
       LEFT JOIN [dbo].[Reg_Tb_RegisterList_Time] b ( nolock ) ON a.RegNo = b.RegNo
       LEFT JOIN Reg_Insurance_Register r ( nolock ) ON r.RegNo = b.RegNo
       LEFT JOIN [dbo].[System_Tb_ItemCategory] c ( nolock ) ON c.ItemCode = a.ItemCategory
       AND a.HospitalCode = c.HospitalCode
   WHERE
       PreChargeNo = #{preChargeNo, jdbcType=BIGINT}
</insert>

</mapper>