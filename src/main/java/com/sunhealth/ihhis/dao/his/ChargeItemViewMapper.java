package com.sunhealth.ihhis.dao.his;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.sunhealth.ihhis.model.dto.outpatientcharge.*;
import com.sunhealth.ihhis.model.entity.view.ChargeItemView;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

public interface ChargeItemViewMapper extends BaseMapper<ChargeItemView> {

    List<OutpatientCharge> selectOutpatientChargeList(@Param("req")UnChargeReq req);
    List<OutpatientChargeRecipeInfo> selectChargeRecipeList(@Param("req") ChargeDetailReq req);
    List<ItemInfo> selectRecipeItemList(@Param("recipeNo") String recipeNo, @Param("settleId") String settleId);
    List<OutpatientUnChargeRecipeInfo> selectUnChargeRecipeList(@Param("req") UnChargeReq req);
    @Select("select count(RegNo) from Reg_Tv_ChargeList where RegNo = #{regno} and IsDelete = 0 and Status = 0")
    Long selectCountChargeList(@Param("regno") Long regno);

    List<ItemInfo> selectUnChargeRecipeItemList(@Param("recipeNo") String recipeNo, @Param("hospitalCode") String hospitalCode);
    void updateRecipeDetailStatus(@Param("detailIdList") List<Long> idList, @Param("status") Integer targetStatus);
    void updateRecipeDetailStatusWhereStatus(@Param("detailIdList") List<Long> idList,
                                         @Param("status") Integer targetStatus,
                                  @Param("decideStatus") Integer status);
    void updateApplyDetailStatus(@Param("detailIdList") List<Long> idList, @Param("status") Integer status);
    void updateApplyDetailCostStatus(@Param("detailIdList") List<Long> idList, @Param("status") Integer status);

    void updateApplyDetailStatusWhereStatus(@Param("detailIdList") List<Long> idList, @Param("status") Integer tStatus
                                            ,@Param("decideStatus") Integer status);
    void updateApplyDetailCostStatusWhereStatus(@Param("detailIdList") List<Long> idList,
                                              @Param("status") Integer tStatus, @Param("decideStatus") Integer status);

    @Select("select * from System_Tb_ChargeItem where ItemCode = #{itemCode}")
    ChargeItemView selectChargeItemByItemCode(@Param("itemCode") String itemCode);

    @Select("select count(1) from MZYS_TB_MZCFMX where cfmxid = #{detailId} and zt = 1 and hospitalCode = #{hospitalCode} ")
    int countPaidItem(@Param("detailId") String detailId, @Param("hospitalCode") String hospitalCode);

    @Select("select count(1) from MZYS_TB_MZCFMX where cfmxid = #{detailId} and scbj = 1 and hospitalCode = "
        + "#{hospitalCode} ")
    int countDeletedItem(@Param("detailId") String detailId, @Param("hospitalCode") String hospitalCode);

    @Select("exec Usp_Charge_SaveSendDrug #{chargeNo}, #{opId}, #{drugDept}, #{chargeWindowNo}")
    void uspChargeSaveSendDrug(@Param("chargeNo") Long chargeNo, @Param("opId") Integer opId,
                               @Param("drugDept") Integer drugDept, @Param("chargeWindowNo") String chargeWindowNo);

    @Select("exec Usp_NewHis_UpdateRecipe #{recipedetalid}, #{recipeid}, #{zt}")
    void uspNewHisUpdateRecipe(@Param("recipedetalid") String recipedetalid, @Param("recipeid") String recipeid,
                               @Param("zt") Integer zt);

    @Select("select ItemName from System_Tb_ItemCategory where ItemCode = #{code} and HospitalCode = #{hospitalCode} "
        + "and stopped = 0")
    String selectCategoryName(@Param("code")Integer code, @Param("hospitalCode")String hospitalCode);
}
