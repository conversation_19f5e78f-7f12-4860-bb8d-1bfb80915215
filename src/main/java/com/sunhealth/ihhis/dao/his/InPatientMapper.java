package com.sunhealth.ihhis.dao.his;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.sunhealth.ihhis.model.dto.inpatient.*;
import com.sunhealth.ihhis.model.entity.inpatient.InPatient;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface InPatientMapper extends BaseMapper<InPatient> {

    List<InpatientRecord> listInpatientRecord(@Param("req") InpatientRecordReq req);

    List<AdvanceChargeDetail> listAdvanceChargeDetail(@Param("req") AdvanceChargeDetailReq req);
}
