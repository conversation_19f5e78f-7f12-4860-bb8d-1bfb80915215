package com.sunhealth.ihhis.dao.his;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.sunhealth.ihhis.model.entity.dqyibao.RegInsurancePreCharge;
import org.apache.ibatis.annotations.Select;

public interface RegInsurancePreChargeMapper extends BaseMapper<RegInsurancePreCharge> {

    @Select("select * from Reg_Insurance_PreCharge where ChargeNo = #{chargeNo} and IsDeleted = 0")
    RegInsurancePreCharge selectByChargeNo(Long chargeNo);

}
