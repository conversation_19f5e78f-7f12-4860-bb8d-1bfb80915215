package com.sunhealth.ihhis.dao.his;

import com.sunhealth.ihhis.model.dto.bill.BillOrder;
import com.sunhealth.ihhis.model.dto.bill.HisBillReq;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface BillMapper {

    // 查询门诊支付账单
    List<BillOrder> getPaymentOutPatientBill(@Param("req") HisBillReq req, @Param("payTypes") List<Integer> payTypes);
    // 查询门诊退款账单
    List<BillOrder> getRefundOutPatientBill(@Param("req") HisBillReq req, @Param("payTypes") List<Integer> payTypes);

    List<BillOrder> getInPatientBill(@Param("req") HisBillReq req, @Param("payTypes") List<Integer> payTypes);
}
