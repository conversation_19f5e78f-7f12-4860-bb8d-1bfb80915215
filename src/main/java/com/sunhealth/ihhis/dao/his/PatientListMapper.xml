<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.sunhealth.ihhis.dao.his.PatientListMapper">

    <insert id="savePatientList" parameterType="com.sunhealth.ihhis.model.entity.patient.RTPatientList">
        insert into Reg_Tb_PatientList(PatId, PatName, HospNo, HospitalCode, CertificateType, CertificateNo, Birthday, Sex, CreateTime, IsDelete, IsUse, OpCode, CreatedBy, CreatedDate, UpdateBy, UpdateDate)
        values (#{pl.patId}, #{pl.patName}, #{pl.hospNo}, #{pl.hospitalCode}, #{pl.certificateType}, #{pl.certificateNo}, #{pl.birthday}, #{pl.sex}, #{pl.createTime}, #{pl.isDelete}, #{pl.isUse}, #{pl.opCode}, #{pl.createdBy}, #{pl.createdDate}, #{pl.updateBy}, #{pl.updateDate})
    </insert>

    <select id="getPatientInfo" resultType="com.sunhealth.ihhis.model.dto.patient.HisPatientInfo">
        select a.PatId           as patid,
               a.PatName         as patname,
               b.HisCardNo       as hiscardno,
               b.CardNo          as cardNo,
               case b.CardType when 1 then 2 else 0 end as cardtype,
               a.CertificateNo   as certificateNo,
               a.CertificateType as certificateType,
               a.Sex             as sex,
               c.Marriage        as marriage,
               c.Nation          as nation,
               c.Occupation      as occupation,
               a.Birthday        as birthday,
               c.Address         as address,
               c.PatPhone        as telephone,
               b.ChargeType      as chargeType
        from Reg_Tb_PatientList a
                 inner join Reg_Tb_PatientCard b on a.PatID = b.PatId and b.Status = 0
                 inner join Reg_Tb_PatientDetl c on a.PatId = c.PatId
        where ltrim(rtrim(a.CertificateNo)) = #{request.certificate_no, jdbcType=VARCHAR}
          and a.HospitalCode = #{request.hospitalCode, jdbcType=INTEGER}
          and a.IsDelete = 0
          and a.IsUse = 1
    </select>

    <select id="selectPatientList" resultType="com.sunhealth.ihhis.model.entity.patient.RTPatientList">
        select
        a.*
        from Reg_Tb_PatientList a
        inner join Reg_Tb_PatientCard b on a.PatID = b.PatId and b.Status = 0
        inner join Reg_Tb_PatientDetl c on a.PatID = c.PatId
        where a.HospitalCode = #{req.hospitalCode}
        and a.patName = #{req.patname}
        <if test="req.certificate_no != null and req.certificate_no != ''">
            and ltrim(rtrim(a.CertificateNo)) = #{req.certificate_no}
        </if>
        <if test="req.patid != null and req.patid != ''">
            and a.PatID = #{req.patid}
        </if>
<!--        <if test="req.cardno != null and req.cardno != ''">-->
<!--            and b.cardNo = #{req.cardno}-->
<!--        </if>-->
        and a.IsDelete = 0 and a.IsUse = 1
    </select>

    <select id="getPatientSimpleDataByExamReportNo" resultType="com.sunhealth.ihhis.model.dto.patient.HisPatientSimpleData">
        SELECT DISTINCT a.patName as name,
               a.patSex  as sex,
               a.patAge  as age
        FROM Apply_Tb_List a,
             Report_Exam_Info r,
             Reg_Tb_PatientList p
        WHERE a.id = r.ApplyId
          AND p.PatID = a.PatId
          and r.reportNo = #{reportNo}
    </select>

    <select id="getPatientSimpleDataByTestReportNo" resultType="com.sunhealth.ihhis.model.dto.patient.HisPatientSimpleData">
        SELECT DISTINCT a.patName as name,
               a.patSex  as sex,
               a.patAge  as age
        FROM Apply_Tb_List a,
             Report_Test_Info r,
             Reg_Tb_PatientList p
        WHERE a.id = r.ApplyId
          AND p.PatID = a.PatId
          and r.reportNo = #{reportNo}
    </select>

    <select id="listPatientByJzlsh" resultType="com.sunhealth.ihhis.model.entity.patient.RTPatientList">
        select
            a.*
        from Reg_Tb_PatientList a
        inner join MZYS_TB_KZJL b on a.PatID = b.hzbh and a.HospitalCode = b.hospitalCode
        where b.jzlsh = #{regNo}
    </select>

    <select id="listPatientByCertificateNo" resultType="com.sunhealth.ihhis.model.entity.patient.RTPatientList">
        select * from Reg_Tb_PatientList where CertificateNo = #{certificateNo} and HospitalCode = #{hospitalCode} and IsUse = 1  order by CreatedDate desc;
    </select>

</mapper>