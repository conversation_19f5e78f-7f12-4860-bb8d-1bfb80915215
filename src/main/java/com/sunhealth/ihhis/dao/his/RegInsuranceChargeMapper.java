package com.sunhealth.ihhis.dao.his;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.sunhealth.ihhis.model.entity.dqyibao.RegInsuranceCharge;
import org.apache.ibatis.annotations.Select;

public interface RegInsuranceChargeMapper extends BaseMapper<RegInsuranceCharge> {

    @Select("select * from Reg_Insurance_Charge where ChargeNo = #{chargeNo}")
    RegInsuranceCharge getByChargeNo(Long chargeNo);

    @Select("select paytype from MZYS_TB_MZCFMX nolock where cfmxlsh = #{recipeDetlID}")
    String selectPayType(Long recipeDetlID);

}
