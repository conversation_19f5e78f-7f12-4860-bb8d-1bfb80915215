<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.sunhealth.ihhis.dao.his.OutpatientInvoiceMapper" >

    <resultMap id="electronicInvoiceFile" type="com.sunhealth.ihhis.model.dto.electronicInvoice.ElectronicInvoiceFile">
        <result property="patid" column="PatID"/>
        <result property="elec_invoice_no" column="InvoiceID"/>
        <result property="elec_invoice_url" column="picurl"/>
        <result property="invoicing_amount" column="TotalAmount"/>
        <result property="invoicing_date" column="createtime"/>
        <result property="invoicing_code" column="pjdm"/>
        <result property="invoicing_no" column="pjhm"/>
        <result property="check_code" column="jym"/>
        <result property="invoicing_entity" column="HospitalName"/>
    </resultMap>

    <select id="getElectronicInvoiceFile" resultMap="electronicInvoiceFile">
        SELECT
            rto.PatID,
            rto.InvoiceID,
            tec.picurl,
            CAST(ISNULL(rto.TotalAmount, 0) * 100 AS INT) AS TotalAmount,
            tec.createtime,
            tec.pjdm,
            tec.pjhm,
            tec.jym,
            th.HospitalName
        FROM
            Reg_TV_OutpatientInvoice rto
        LEFT JOIN
            Tb_eInvoice te ON CAST(rto.InvoiceID AS VARCHAR) = te.InvoiceID
        LEFT JOIN
            Tb_eInvoiceCreate tec ON te.bus_id = tec.bus_id
        LEFT JOIN
            Tb_Hospital th on rto.HospitalCode = th.HOSPITALCODE
        WHERE
            rto.PatID = #{req.patid} and rto.InvoiceID = #{req.elec_invoice_no} and rto.HospitalCode = #{req.hospitalCode}
    </select>

    <resultMap id="electronicInvoice" type="com.sunhealth.ihhis.model.dto.electronicInvoice.ElectronicInvoice">
        <result property="patid" column="PatID"/>
        <result property="elec_invoice_no" column="InvoiceID"/>
        <result property="visit_time" column="VisitTime"/>
        <result property="amount" column="TotalAmount"/>
        <result property="invoicing_entity" column="HospitalName"/>
        <result property="pat_name" column="PatName"/>
        <result property="out_trade_no" column="settleId"/>
        <result property="trade_type" column="Flag"/>
        <result property="online_type" column="OpCode"/>
        <result property="operation_status" column="status"/>
    </resultMap>

    <select id="getElectronicInvoiceList" resultMap="electronicInvoice">
        SELECT
        rto.RegNo,
        rto.PatID,
        rto.InvoiceID,
        CASE
        WHEN mtk.sckzrq IS NULL THEN NULL
        ELSE FORMAT(TRY_CONVERT(datetime, mtk.sckzrq), 'yyyyMMddHHmmss')
        END AS VisitTime,
        rto.PatName,
        th.HospitalName,
        CAST(ISNULL(rto.TotalAmount, 0) * 100 AS INT) AS TotalAmount,
        CASE
        WHEN rto.Flag = 1 THEN rto.RegNo
        WHEN rto.Flag = 2 THEN rto.ChargeNo
        ELSE NULL
        END AS settleId,
        CASE
        WHEN rto.Flag = 1 THEN 0
        WHEN rto.Flag = 2 THEN 1
        ELSE NULL
        END AS Flag,
        CASE
        WHEN rto.OpCode = #{req.opCode} THEN 0
        ELSE 1
        END AS OpCode,
        CASE
        WHEN tec.picurl IS NOT NULL AND tec.picurl != '' THEN 2
        ELSE 0
        END AS status,
        mtk.sckzrq
        FROM Reg_TV_OutpatientInvoice rto
        LEFT JOIN (
        SELECT *,
        ROW_NUMBER() OVER (
        PARTITION BY ghlsh
        ORDER BY sckzrq DESC
        ) AS rn
        FROM MZYS_TB_KZJL
        ) mtk ON rto.RegNo = mtk.ghlsh AND mtk.rn = 1
        LEFT JOIN Tb_eInvoice te  ON CAST(rto.InvoiceID AS VARCHAR) = te.InvoiceID
        LEFT JOIN
        Tb_eInvoiceCreate tec ON te.bus_id = tec.bus_id
        LEFT JOIN
        Tb_Hospital th on rto.HospitalCode = th.HOSPITALCODE
        WHERE
        rto.PatID = #{req.patid} and rto.HospitalCode = #{req.hospitalCode} and rto.Status = 0
        <if test='req.online_type == "0"'>
            AND rto.OpCode = #{req.opCode}
        </if>
        <if test='req.online_type == "1"'>
            AND rto.OpCode != #{req.opCode}
        </if>
        ORDER BY mtk.sckzrq DESC
    </select>

</mapper>