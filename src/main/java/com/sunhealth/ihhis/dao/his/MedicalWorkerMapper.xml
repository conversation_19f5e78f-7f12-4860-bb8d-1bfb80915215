<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.sunhealth.ihhis.dao.his.MedicalWorkerMapper">
    <!-- 定义一个 resultMap -->
    <resultMap id="doctorInfo" type="com.sunhealth.ihhis.model.dto.DoctorInfo">
        <!-- 指定查询结果中的列与模型属性的映射关系 -->
        <result property="name" column="name"/>
        <result property="code" column="WorkerId"/>
        <result property="identity" column="IdentityCard"/>
        <result property="mobile" column="Telephone"/>
<!--        <result property="certificate" column="deptClassCode"/>-->
<!--        <result property="practising_number" column="deptClassCode"/>-->
<!--        <result property="areas_of_expertise" column="deptClassCode"/>-->
<!--        <result property="introduction" column="deptClassCode"/>-->
        <result property="dept_id" column="DeptId"/>
        <result property="dept_name" column="DeptName"/>
        <result property="channel_type" column="channel_type"/>
        <!-- 其他属性映射 -->
    </resultMap>

    <!-- 自定义 SQL 查询语句 -->
    <select id="selectPageDoctor" resultMap="doctorInfo">
        SELECT
        w.name,
        w.WorkerId,
        RTRIM(w.IdentityCard) as IdentityCard,
        w.Telephone,
        d.DeptId,
        d.DeptName,
        #{channelType} as channel_type
        FROM System_Tb_Worker w
        LEFT JOIN System_Tb_WorkerDepatmentRelation wd on w.WorkerId = wd.WorkerId
        LEFT JOIN System_Tb_Department d on wd.DeptId = d.DeptId
        WHERE w.Status = #{status}
        AND w.HospitalId = #{hospitalId}
        AND w.StaffType = 1
        AND ((w.Remark != 'HIS工程师' and w.Remark != '信息科') or w.Remark is null)
        <if test="deptId != null and deptId != '' ">
            AND d.DeptId = #{deptId}
        </if>
        ORDER BY w.WorkerId ASC
    </select>

</mapper>