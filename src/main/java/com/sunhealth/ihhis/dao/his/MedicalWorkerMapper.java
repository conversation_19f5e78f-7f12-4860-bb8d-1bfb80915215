package com.sunhealth.ihhis.dao.his;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.sunhealth.ihhis.model.dto.DeptInfo;
import com.sunhealth.ihhis.model.dto.DoctorInfo;
import com.sunhealth.ihhis.model.entity.MedicalWorker;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

public interface MedicalWorkerMapper extends BaseMapper<MedicalWorker> {

    Page<DoctorInfo> selectPageDoctor(Page page, Integer status, Integer hospitalId, String deptId, String channelType);
    @Select("SELECT * FROM System_Tb_Worker WHERE WorkerNo=#{workNo} AND HospitalId=#{hospitalCode} ")
    MedicalWorker selectByWorkNoAndHospitalCode(@Param("workNo")String workNo,@Param("hospitalCode") String hospitalCode);

    @Select("SELECT * FROM System_Tb_Worker WHERE WorkerId=#{doctorId} AND HospitalId=#{hospitalCode} ")
    MedicalWorker selectByDoctorIdAndHospitalCode(@Param("doctorId")String doctorId,
                                               @Param("hospitalCode") String hospitalCode);
}
