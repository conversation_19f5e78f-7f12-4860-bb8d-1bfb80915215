<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.sunhealth.ihhis.dao.his.ChargeItemViewMapper">
    <resultMap id="outpatientCharge" type="com.sunhealth.ihhis.model.dto.outpatientcharge.OutpatientCharge">
        <result property="settle_id" column="settle_id"/>
        <result property="channel_type" column="channel_type"/>
        <result property="regno" column="regno"/>
        <result property="invoice_no" column="invoice_no"/>
        <result property="elec_invoice_no" column="elec_invoice_no"/>
        <result property="refund_settle_id" column="refund_settle_id"/>
        <result property="record_status" column="record_status"/>
        <result property="record_flag" column="record_flag"/>
        <result property="item_name" column="item_name"/>
        <result property="end_time" column="end_time"/>
        <result property="total_amount" column="total_amount"/>
        <result property="discount_amount" column="discount_amount"/>
        <result property="cash_pay" column="cash_pay"/>
        <result property="pub_pay" column="pub_pay"/>
        <result property="pub_account_pay" column="pub_account_pay"/>
        <result property="dept_id" column="dept_id"/>
        <result property="dept_name" column="dept_name"/>
        <result property="recipe_doctor_name" column="recipe_doctor_name"/>
        <result property="drug_window" column="drug_window"/>
        <result property="drug_dept" column="drug_dept"/>
        <result property="charge_flag" column="charge_flag"/>
        <result property="out_trade_no" column="out_trade_no"/>
        <result property="elec_invoice_qrcode" column="elec_invoice_qrcode"/>
        <result property="elec_invoice_url" column="elec_invoice_url"/>
        <result property="organ_code" column="organ_code"/>
        <result property="organ_name" column="organ_name"/>
    </resultMap>

    <!-- 自定义 SQL 查询语句 -->
    <select id="selectOutpatientChargeList" resultMap="outpatientCharge">
        SELECT
        a.ChargeNo AS settle_id --结算收据号 Y HIS一次结算单据号(标记一次结算的唯一号)
        ,
        b.TerminalType AS channel_type --就诊渠道 Y 0线下就诊 1线上就诊（互联网医院） -1全部
        ,
        b.RegNo AS regno --门诊就诊流水号 Y 门诊挂号序号
        ,
        b.InvoiceInfo AS invoice_no --发票号 N
        ,
        c.bus_id AS elec_invoice_no --电子收据号 N 仅限有电子发票情况下输出
        ,
        tf.ChargeNo AS refund_settle_id --退收据号 N
        ,
        CASE b.Status WHEN 0 THEN 0 WHEN 80 THEN 1 ELSE 2 END AS record_status --收费类型 Y 0正常 1退费 2红冲
        ,
        CASE b.Flag WHEN 1 THEN 0 WHEN 2 THEN 1 END AS record_flag --记录类型 Y 0挂号1收费
        ,
        (SELECT STRING_AGG(ItemName, ',')  FROM Reg_Tv_ChargeDetail cd WHERE cd.ChargeNo = a.ChargeNo) item_name
        ,
        FORMAT ( b.OpTime, 'yyyyMMddHHmmss' ) AS end_time --结算时间 Y
        ,
        CAST ( b.TotalAmount * 100 AS INT ) AS total_amount --总金额 Y
        ,
        CAST ( b.DiscountAmount * 100 AS INT ) AS discount_amount --优惠金额 Y
        ,
        CAST ( b.CashFee * 100 AS INT ) AS cash_pay --个人现金支付金额 Y 个人支付的现金(包括现金、银联卡、微信支付、支付宝支付等)
        ,
        CAST ( b.PubPay * 100 AS INT ) AS pub_pay --医保统筹支付金额 N 如果医保支付时此字段必填
        ,
        CAST ( b.CurrAccountPay * 100 AS INT ) + CAST ( b.LastAccountPay * 100 AS INT ) AS pub_account_pay --医保个人账户字符金额 N
        ,
        r.DeptID  AS dept_id --科室代码 Y
        ,
        regdept.DeptName  AS dept_name --科室名称 Y
        ,
        regdoc.Name  AS recipe_doctor_name -- 开方医生名称 Y record_flag为1时必填
        ,
        b.DispensingWindow AS drug_window --发药窗口集合 N
        ,
        b.DrugDept AS drug_dept --发药药房集合 N
        ,
        0 AS charge_flag --欠费补交标记 N 0正常缴费 1欠费 2补缴 3补缴退费
        ,
        b.PayOrderNo AS out_trade_no --HIS订单号 N 补缴订单号
        ,
        '' AS elec_invoice_qrcode --电子票据二维码数据 N 仅限当地有电子发票方案的医院
        ,
        c.qrcodeUrl AS elec_invoice_url --电子票据URL N 仅限当地有电子发票方案的医院
        ,
        b.HospitalCode AS organ_code --院区代e名称 N

        FROM
        Reg_Tv_ChargeList a
        INNER JOIN Reg_TV_OutpatientInvoice b ON a.ChargeNo = b.ChargeNo
        LEFT JOIN Tb_eInvoice c ON b.InvoiceInfo = c.bus_id
        LEFT JOIN Reg_TV_OutpatientInvoice tf ON b.ReturnInvoiceID = tf.InvoiceID
        LEFT JOIN Reg_Tv_RegisterList r ON r.RegNo = a.RegNo
        LEFT JOIN System_Tb_Department regdept ON regdept.DeptId = r.DeptID
        LEFT JOIN System_Tb_Worker regdoc ON regdoc.WorkerId = r.DoctorID
        where b.Status in (0, 80, 120)
        and a.PatID = #{req.patid}
        and a.ChargeTime between #{req.beginDate} and #{req.endDate}
        and b.TotalAmount <![CDATA[ > ]]> 0
        <if test="req.channel_type != null and  req.channel_type!=''">
            and b.TerminalType = #{req.channel_type}
        </if>
    </select>

    <resultMap id="recipe" type="com.sunhealth.ihhis.model.dto.outpatientcharge.OutpatientChargeRecipeInfo">
        <result property="recipe_no" column="recipe_no"/>
        <result property="link_recipe_no" column="link_recipe_no"/>
        <result property="regno" column="regno"/>
        <result property="recipe_type" column="recipe_type"/>
        <result property="recipe_time" column="recipe_time"/>
        <result property="recipe_dept_id" column="recipe_dept_id"/>
        <result property="recipe_dept_name" column="recipe_dept_name"/>
        <result property="recipe_doctor_id" column="recipe_doctor_id"/>
        <result property="recipe_doctor_name" column="recipe_doctor_name"/>
        <result property="check_status" column="check_status"/>
        <result property="items" column="items"/>
        <result property="organ_code" column="organ_code"/>
        <result property="settle_status" column="settle_status"/>
        <result property="hosp_flag" column="hosp_flag"/>
        <result property="oneself_flag" column="oneself_flag"/>
        <result property="long_recipe_flag" column="long_recipe_flag"/>
        <result property="extend_recipe_flag" column="extend_recipe_flag"/>
        <result property="record_status" column="record_status"/>
    </resultMap>

    <!-- 自定义 SQL 查询语句 -->
    <select id="selectChargeRecipeList" resultMap="recipe">
        SELECT DISTINCT
            b.RecipeID recipe_no --处方序号
            ,
            b.RecipeID link_recipe_no --联动处方序号
            ,
            a.RegNo regno --挂号序号
            ,
            CASE m.mzcflx WHEN 12 THEN 1 WHEN 13 THEN 2 WHEN 14 THEN 3 WHEN 8 THEN 4 WHEN 6 THEN 5 ELSE 1 END recipe_type --处方类型
            ,
            m.sckfrq recipe_time --开方时间
            ,
            dept.DeptId recipe_dept_id --开方科室代码
            ,
            dept.DeptName AS recipe_dept_name --开方科室名称
            ,
            doc.WorkerId recipe_doctor_id --开方医生代码
            ,
            doc.Name AS recipe_doctor_name --开方医生名称
            ,
            CASE m.cfzt WHEN 0 THEN 1 WHEN 1 THEN 0 ELSE 1 END check_status --审核状态
            ,
            a.HospitalCode organ_code --院区代码
            ,
            (SELECT STRING_AGG ( ItemName, ',' ) FROM Reg_Tv_ChargeDetail cd WHERE cd.ChargeNo = a.ChargeNo ) items --药品/项目信息
            ,
            CASE a.Status WHEN 0 THEN 0 WHEN 80 THEN 1 WHEN 120 THEN 2 END record_status --记录状态
            ,
            CASE a.Status WHEN 0 THEN 2 ELSE 2 END settle_status --结算状态
            ,
            a.ChargeNo settle_id -- 结算单号
            ,
            '0' hosp_flag -- 流转处方药房标志	Y	0常规处方 1院外流转处方
            ,
            '0' oneself_flag -- 自备标识	Y	0非自备(院内取药) 1自备
            ,
            '0' long_recipe_flag --长处方标志
            ,
            '0' extend_recipe_flag --延伸处方标志
        FROM
            Reg_Tv_ChargeList a
                INNER JOIN Reg_Tv_ChargeDetail b ON a.ChargeNo = b.ChargeNo
                LEFT JOIN Drug_Tb_ClinicDispensingList drug ON drug.ChargeId = a.ChargeNo
                LEFT JOIN MZYS_TB_MZCF m ON m.cfid = b.RecipeID
                LEFT JOIN Reg_Tv_RegisterList r ON r.RegNo = a.RegNo
                LEFT JOIN System_Tb_Department dept ON dept.DeptId = r.DeptID
                LEFT JOIN System_Tb_Worker doc ON doc.WorkerId = r.DoctorID
        WHERE a.ChargeNo = #{req.settle_id}
    </select>

    <resultMap id="item" type="com.sunhealth.ihhis.model.dto.outpatientcharge.ItemInfo">
        <result property="item_code" column="item_code"/>
        <result property="item_name" column="item_name"/>
        <result property="drug_flag" column="drug_flag"/>
        <result property="unit" column="unit"/>
        <result property="insurance_code" column="insurance_code"/>
        <result property="drug_spec" column="drug_spec"/>
        <result property="dosage_form_code" column="dosage_form_code"/>
        <result property="frequency" column="frequency"/>
        <result property="frequency_code" column="frequency_code"/>
        <result property="usage_code" column="usage_code"/>
        <result property="usage" column="usage"/>
        <result property="dosage" column="dosage"/>
        <result property="dosage_unit" column="dosage_unit"/>
        <result property="use_days" column="use_days"/>
        <result property="price" column="price"/>
        <result property="quantity" column="quantity"/>
        <result property="amount" column="amount"/>
    </resultMap>

    <select id="selectRecipeItemList" resultMap="item">
        select a.ItemID     item_code      --药品、项目代码
             , a.ItemName   item_name      --药品、项目名称
             --detail_entrust_content	--明细嘱托
             , a.IsDrug     drug_flag      --药品标识
             , a.ClinicUnit unit           --药品单位
             --clinical_item_code	--临床项目代码
             --clinical_item_name	--临床项目名称
             --set_item_name	--收费大项目名称
             , b.NationCode insurance_code --医保对应代码
             , b.DrugGuage  drug_spec      --药品规格
             --dosage_form_name	--剂型名称
             , drug.DosageForm dosage_form_code	--剂型代码
             , yf.yfmc         frequency      --频次名称
             , yf.yfbm      frequency_code --频次代码
             , c.gytj       usage_code     --用法代码
             , gytj.HisDictionaryName usage	--用法名称
            ,c.jl dosage	--药品剂量
            ,c.jldw dosage_unit	--剂量单位
            --decocting_method	--中药煎法
            ,c.ts use_days	--用药天数
            --self_rate	--自付比例
            ,CAST( a.Price * 100 AS INT) price	--项目单价
            ,a.Quantiy quantity	--数量
            ,CAST( a.TotalAmount * 100 AS INT) amount	--总金额
        from Reg_Tv_ChargeDetail a
            inner join System_Tb_PubItems b
        on a.ItemID=b.ItemCode
            left join Drug_Tb_DrugInfomation drug on drug.DrugId=b.ItemCode
            left join MZYS_TB_MZCFMX c on c.cfmxid=a.RecipeDetlID
            left join MZYS_TB_YPYF yf on yf.yflsh= c.yf
            left join TB_Dic_HisDictionary gytj on gytj.HisDictionaryCode= c.gytj and gytj.HisDictionaryId=31 and gytj.HospitalId= c.hospitalCode
        where a.RecipeID = #{recipeNo}
          and a.ChargeNo = #{settleId}

    </select>

    <resultMap id="unChargeRecipe" type="com.sunhealth.ihhis.model.dto.outpatientcharge.OutpatientUnChargeRecipeInfo">
        <result property="regno" column="regno"/>
        <result property="recipe_no" column="recipe_no"/>
        <result property="recipe_type" column="recipe_type"/>
        <result property="recipe_time" column="recipe_time"/>
        <result property="recipe_dept_id" column="recipe_dept_id"/>
        <result property="recipe_dept_name" column="recipe_dept_name"/>
        <result property="recipe_doctor_id" column="recipe_doctor_id"/>
        <result property="recipe_doctor_name" column="recipe_doctor_name"/>
        <result property="check_status" column="check_status"/>
        <result property="delivery_flag" column="delivery_flag"/>
        <result property="recipe_name" column="recipe_name"/>
        <result property="chargetype_name" column="chargetype_name"/>
        <result property="chargetype_code" column="chargetype_code"/>
        <result property="organ_code" column="organ_code"/>
        <result property="organ_name" column="organ_name"/>
        <result property="delivery_type" column="delivery_type"/>
        <result property="link_recipe_no" column="link_recipe_no"/>
        <result property="appointment_id" column="appointment_id"/>
        <result property="cure_item" column="cure_item"/>
        <result property="cure_item_name" column="cure_item_name"/>
        <result property="source_name" column="source_name"/>
        <result property="record_status" column="record_status"/>
        <result property="settle_status" column="settle_status"/>
        <result property="settle_id" column="settle_id"/>
        <result property="hosp_flag" column="hosp_flag"/>
        <result property="oneself_flag" column="oneself_flag"/>
        <result property="recipe_source" column="recipe_source"/>
        <result property="application_no" column="application_no"/>
        <result property="long_recipe_flag" column="long_recipe_flag"/>
        <result property="extend_recipe_flag" column="extend_recipe_flag"/>
        <result property="entrust_content" column="entrust_content"/>
        <result property="detail_entrust_content" column="detail_entrust_content"/>
        <result property="exec_dept_id" column="exec_dept_id"/>
        <result property="exec_dept_name" column="exec_dept_name"/>
    </resultMap>
    <select id="selectUnChargeRecipeList" resultMap="unChargeRecipe">
        select distinct jl.ghlsh regno,
        cf.cfid recipe_no, --处方序号
        case cf.mzcflx when 12 then 1 when 13 then 2 when 14 then 3 when 8 then 4 when 6 then 5 else 1 end recipe_type,  --处方类型
        cf.sckfrq  recipe_time, --开方时间
        jl.ksbm recipe_dept_id, --开方科室代码
        dept.DeptName recipe_dept_name, --开方科室名称
        jl.ysbm recipe_doctor_id, --开方医生编码
        doc.Name recipe_doctor_name, --开方医生名称
        case cf.cfzt when 0 then 1 when 1 then 0 else 1 end  check_status,    --审核状态
        jl.hospitalCode organ_code, --院区编码
        '' organ_name, --院区名称
        '' delivery_flag, --外送标志
        '' recipe_name, --处方名称
        '' chargetype_name, --医保说明
        '' chargetype_code, --医保代码
        '0'  delivery_type, --配送类型
        '' link_recipe_no, --联动处方序号
        '' appointment_id, --预约序号
        '' cure_item, --特殊病种代码
        '' cure_item_name, --特殊病种名称
        '' source_name, --来源说明
        case (select top 1 Status from Reg_Tb_PreCharge pc where pc.PatId = jl.hzbh and pc.RegNo = jl.ghlsh and pc.HospitalCode = jl.hospitalCode) when 80 then 1 when 120 then 2 else 0 end record_status, --记录状态
        '0' as settle_status, --结算状态
        (select top 1 ChargeNo from Reg_Tb_PreCharge pc where pc.PatId = jl.hzbh and pc.RegNo = jl.ghlsh and pc.RecipeID  = cf.cfid and pc.Status = 0 and pc.HospitalCode = jl.hospitalCode) settle_id, --结算单号
        '0' hosp_flag, --流转处方药房标志
        '0' oneself_flag, --自备标识
        '0' recipe_source, --处方来源标志
        '' application_no, --申请单序号
        '0' long_recipe_flag, --长处方标志
        '0' extend_recipe_flag, --延伸处方标志
        '' entrust_content, --嘱托
        zxdept.DeptId exec_dept_id, --执行科室代码
        zxdept.DeptName exec_dept_name --执行科室名称
        from MZYS_TB_KZJL jl(nolock)
        left join MZYS_TB_MZCF cf(nolock) on jl.jzlsh = cf.jzlsh and jl.hospitalCode = cf.hospitalCode
        left join  MZYS_TB_MZCFMX mx(nolock) on mx.cfid = cf.cfid and mx.hospitalCode = cf.hospitalCode
        left join System_Tb_Department dept(nolock) on jl.ksbm = dept.DeptId and jl.hospitalCode = dept.HospitalId
        left join System_Tb_Department zxdept(nolock) on cf.zxks = zxdept.DeptId and jl.hospitalCode = zxdept.HospitalId
        left join System_Tb_Worker doc(nolock) on doc.WorkerId = jl.zhkzys and doc.HospitalId = jl.hospitalCode
        where  (mx.tcbj = 0 or mx.tcbj is null)
        and (mx.dj>0)
        and mx.zt in (0,-1) --未收费或者正在收费
        AND isnull(mx.QuoteDetailId,0)=0 ---引用的处方表示部分退产生的在退费时收费不在收费页面收费
        and isnull(mx.pytjbm,1) in (1,3) --非外配药
        and isnull(mx.scbj,0) = 0
        and isnull((select lt.IsDebitPay from Reg_Tb_RegisterList_Time lt where lt.RegNo=jl.ghlsh and lt.HospitalCode = jl.hospitalCode),0)=0 --- 排除记账处方 @IsDebitPay
        and mx.cfmxid not in (select isnull(ad.Cfmxid,ad.ApplyDetailId)
                              from MZYS_TB_PsychotherapyApplyList al(nolock)
                              inner join MZYS_TB_PsychotherapyApplyDetail ad(nolock) on al.ApplyId=ad.ApplyId and al.HospitalId=ad.HospitalId
                              inner join System_Tb_PubItems item(nolock) on item.ItemCode = ad.ItemId and ad.HospitalId=item.HospitalId
                              where al.RegNo= jl.ghlsh and al.HospitalId = jl.hospitalCode
                                and ISNULL(ad.ChargeStatus,0) in (0,2)        --未收费，已退费
                                and item.ClinicExpensePrice+item.ClinicNonExpensePrice>0
                                and ad.isDelete=0)
        and jl.hzbh = #{req.patid} and jl.hospitalCode = #{req.hospitalCode} and cf.sckfrq <![CDATA[ >= ]]> #{req.beginDate} and cf.sckfrq <![CDATA[ < ]]> #{req.endDate}
      AND EXISTS (SELECT 1 FROM reg_tv_registerlist reg(nolock)
                  WHERE reg.regno = jl.ghlsh
                    AND reg.hospitalCode = jl.hospitalCode
                    AND reg.status = 0) -- 只查询在 reg_tv_registerlist 里有记录且 status = 0 的数据
    </select>

    <select id="selectUnChargeRecipeItemList" resultMap="item">
        select     mx.xmbm     item_code      --药品、项目代码
             , mx.xmmc   item_name      --药品、项目名称
             , case when b.ItemCategory in (12, 13, 14 ) then 1 else 2 end drug_flag      --药品标识
             , mx.dw unit           --药品单位
             , b.NationCode insurance_code --医保对应代码
             , mx.gg  drug_spec      --药品规格
             , drug.DosageForm  dosage_form_code	--剂型代码
             , yf.yfmc         frequency      --频次名称
             , yf.yfbm      frequency_code --频次代码
             , mx.gytj       usage_code     --用法代码
             , gytj.HisDictionaryName usage	--用法名称
            ,mx.jl dosage	--药品剂量
            ,mx.jldw dosage_unit	--剂量单位
            ,mx.ts use_days	--用药天数
            ,CAST( mx.dj * 100 AS INT) price	--项目单价
            ,mx.xmsl * IsNull(mx.cffs, 1) quantity	--数量
            ,CAST( mx.cfje * 100 AS INT) * IsNull(mx.cffs, 1) amount	--总金额
        from MZYS_TB_MZCFMX mx
        inner join System_Tb_PubItems b on mx.xmbm=b.ItemCode
        left join Drug_Tb_DrugInfomation drug on drug.DrugId=b.ItemCode
        left join MZYS_TB_YPYF yf on yf.yflsh= mx.yf
        left join TB_Dic_HisDictionary gytj on gytj.HisDictionaryCode= mx.gytj and gytj.HisDictionaryId=31 and gytj.HospitalId= mx.hospitalCode
        where mx.cfid = #{recipeNo} and mx.hospitalCode = #{hospitalCode} and mx.zt in (0,-1) and mx.scbj != 1
    </select>
    <update id="updateRecipeDetailStatus">
        update MZYS_TB_MZCFMX
        set zt = #{status}
        where cfmxid in
          <foreach collection="detailIdList" item="id" open="(" close=")" separator=",">
            #{id}
          </foreach>
    </update>

    <update id="updateApplyDetailStatus">
        update Apply_Tb_Detail
        set ChargeFlag = #{status}
        where ListID in
        (
         SELECT jclsh FROM MZYS_TB_MZCFMX where Cfmxid in
        <foreach collection="detailIdList" item="id" open="(" close=")" separator=",">
            #{id}
        </foreach>
        )
    </update>

    <update id="updateApplyDetailCostStatus">
        update Apply_Tb_Detail_Cost
        set ChargeFlag = #{status}
        where ListID in
        (
         SELECT jclsh FROM MZYS_TB_MZCFMX where Cfmxid in
        <foreach collection="detailIdList" item="id" open="(" close=")" separator=",">
            #{id}
        </foreach>
        )
    </update>

    <update id="updateRecipeDetailStatusWhereStatus">
        update MZYS_TB_MZCFMX
        set zt = #{status}
        where cfmxid in
        <foreach collection="detailIdList" item="id" open="(" close=")" separator=",">
            #{id}
        </foreach>
        and zt = #{decideStatus}
    </update>

    <update id="updateApplyDetailStatusWhereStatus">
        update Apply_Tb_Detail
        set ChargeFlag = #{status}
        where ListID in
        (
        SELECT jclsh FROM MZYS_TB_MZCFMX where Cfmxid in
        <foreach collection="detailIdList" item="id" open="(" close=")" separator=",">
            #{id}
        </foreach>
        )
        and ChargeFlag = #{decideStatus}
    </update>

    <update id="updateApplyDetailCostStatusWhereStatus">
        update Apply_Tb_Detail_Cost
        set ChargeFlag = #{status}
        where ListID in
        (
        SELECT jclsh FROM MZYS_TB_MZCFMX where Cfmxid in
        <foreach collection="detailIdList" item="id" open="(" close=")" separator=",">
            #{id}
        </foreach>
        )
        and ChargeFlag = #{decideStatus}
    </update>
</mapper>