package com.sunhealth.ihhis.dao.his;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.sunhealth.ihhis.model.entity.patient.RTPatientCard;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.springframework.stereotype.Repository;

@Repository
public interface PatientCardMapper extends BaseMapper<RTPatientCard> {

    /**
     * 新增就诊卡
     * @param patientCard
     * @return
     */
    int savePatientCard(@Param("p") RTPatientCard patientCard);

    /**
     * 根据身份证,医院编码获取就诊卡号
     * @param certificateNo 身份证号
     * @param hospitalCode 医院编码
     * @return
     */
    String getCardNoBySfzAndHospCode(String certificateNo, Integer hospitalCode);

    /**
     * 根据身份证,医院编码获取自费就诊卡信息
     * @param certificateNo 身份证号
     * @param hospitalCode 医院编码
     * @return
     */
    RTPatientCard getBySfzAndHospCode(String certificateNo, Integer hospitalCode);

    /**
     * 根据身份证,获取就诊卡号
     * @param certificateNo 身份证号
     * @return
     */
    String getCardNoBySfz(String certificateNo);

    /**
     * 根据就诊卡号、医院编码查询就诊卡信息
     * @param cardNo
     * @param cardType
     * @param hospitalCode
     * @return
     */
    RTPatientCard getByCardNoAndType(String cardNo, Integer cardType, Integer hospitalCode);
    @Select("select top 1 * from Reg_Tb_PatientCard where CardNo = #{cardNo} and PatId = #{patId} and CardType = #{cardType}")
    RTPatientCard getByCardNoAndPatIdAndCardType(String cardNo, Long patId, Integer cardType);

    @Select("select top 1 * from Reg_Tb_PatientCard where PatId = #{patId} and CardType = #{cardType} order by "
        + "CreateDate desc")
    RTPatientCard getByPatIdAndCardType(Long patId, Integer cardType);

    @Select("select top 1 * from Reg_Tb_PatientCard where CardNo = #{cardNo} and PatId = #{patId}")
    RTPatientCard getByCardNoAndPatId(String cardNo, Long patId);
}
