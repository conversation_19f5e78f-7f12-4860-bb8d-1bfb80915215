package com.sunhealth.ihhis.dao.his;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.sunhealth.ihhis.model.entity.register.PreRegisterDetail;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.springframework.stereotype.Repository;

@Repository
public interface PreRegisterDetailMapper extends BaseMapper<PreRegisterDetail> {

    /**
     * 查询诊疗项目代码
     *
     * @param regNo
     * @return
     */
    @Select("exec Usp_GetZLXMDM #{regNo}")
    String getZLXMDM(@Param("regNo") long regNo);

}
