package com.sunhealth.ihhis.dao.his;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.sunhealth.ihhis.model.entity.charge.ChargeDetailTime;
import java.util.Date;
import org.apache.ibatis.annotations.Param;
import org.springframework.transaction.annotation.Transactional;

public interface ChargeDetailTimeMapper extends BaseMapper<ChargeDetailTime> {
    @Transactional
    void insertChargeDetailTime(
       @Param("detailNo") Long detailNo,
       @Param("chargeNo") Long chargeNo,
       @Param("opId") Integer opId,
       @Param("time") Date time,
       @Param("recipeDetlID") Long recipeDetlID
    );
}
