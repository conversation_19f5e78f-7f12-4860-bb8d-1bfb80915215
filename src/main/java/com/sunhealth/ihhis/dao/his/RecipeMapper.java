package com.sunhealth.ihhis.dao.his;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.sunhealth.ihhis.model.dto.outpatientcharge.ItemInfo;
import com.sunhealth.ihhis.model.dto.outpatientcharge.OutpatientRecipeInfo;
import com.sunhealth.ihhis.model.dto.outpatientcharge.OutpatientRecipeReq;
import com.sunhealth.ihhis.model.entity.view.ChargeItemView;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface RecipeMapper extends BaseMapper<ChargeItemView> {

    List<OutpatientRecipeInfo> selectOutpatientRecipeList(@Param("req") OutpatientRecipeReq req);

    List<ItemInfo> selectRecipeItemList(@Param("recipeNo") String recipeNo);
    List<ItemInfo> selectRecipeItemList1(@Param("recipeNo") String recipeNo);
}
