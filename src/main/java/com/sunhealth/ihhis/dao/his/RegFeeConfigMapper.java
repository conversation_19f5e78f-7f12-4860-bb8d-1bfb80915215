package com.sunhealth.ihhis.dao.his;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.sunhealth.ihhis.model.entity.register.RegFeeConfig;
import java.math.BigDecimal;
import java.util.Map;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.springframework.stereotype.Repository;

@Repository
public interface RegFeeConfigMapper extends BaseMapper<RegFeeConfig> {

    @Select("select top 1 * from Reg_Tb_RegFeeConfig where ItemId = #{itemId} and ConfigType = 2 and ConfigId = #{doctorLevel} "
        + "and HospitalCode = #{hospitalCode}")
    RegFeeConfig selectOneByItemId(@Param("itemId") Integer itemId, @Param("hospitalCode") String hospitalCode,
                                   @Param("doctorLevel") Integer doctorLevel);
    @Select("select sum(CAST(0.00 as decimal(18,2)) + CAST(fc.Price as decimal(18,2)))\n"
        + "        from Reg_Tb_RegFeeConfig fc\n"
        + "        where fc.ConfigType=2\n"
        + "        and fc.ConfigId= #{doctorLevel} \n"
        + "        and fc.HospitalCode=#{hospitalCode}")
    BigDecimal selectRegistrationFeeByDoctorLevel(@Param("doctorLevel") Integer doctorLevel, @Param(
        "hospitalCode") String hospitalCode);

    @Select("select sum(CAST(0.00 as decimal(18,2)) + CAST(fc.Price as decimal(18,2)))\n"
        + "        from Reg_Tb_RegFeeConfig fc\n"
        + "        left join System_Tb_ChargeItem stc on fc.ItemId=stc.ItemCode and stc.HospitalId=fc.HospitalCode\n"
        + "        where fc.ConfigType=2\n"
        + "        and fc.ConfigId= #{doctorLevel}\n"
        + "        and stc.ItemCategory = 3\n"
        + "        and fc.HospitalCode=#{hospitalCode}")
    BigDecimal selectTreatmentFeeByDoctorLevel(@Param("doctorLevel") Integer doctorLevel, @Param(
        "hospitalCode") String hospitalCode);
}
