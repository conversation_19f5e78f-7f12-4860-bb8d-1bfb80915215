package com.sunhealth.ihhis.dao.his;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.sunhealth.ihhis.model.entity.register.RegWhiteList;
import com.sunhealth.ihhis.model.entity.register.RegisterListTime;
import java.util.List;
import java.util.Map;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.springframework.stereotype.Repository;

@Repository
public interface RegWhiteListMapper extends BaseMapper<RegWhiteList> {
    @Select({"SELECT * FROM Tbt_WhiteList WHERE PatId = #{patId} AND IsUse = 1"})
    List<RegWhiteList> selectByPatId(String patId);
}
