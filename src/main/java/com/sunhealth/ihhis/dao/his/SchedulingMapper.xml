<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.sunhealth.ihhis.dao.his.SchedulingMapper" >

    <resultMap id="SourceNumberResultMap" type="com.sunhealth.ihhis.model.dto.schedule.SourceNumber">
        <result property="schedulingId" column="schedulingId"/>
        <result property="timespanId" column="timespanId"/>
        <result property="SubjectID" column="SubjectID"/>
        <result property="SeqNum" column="SeqNum"/>
        <result property="DutyDate" column="DutyDate"/>
        <result property="status" column="computedStatus"/> <!-- 将computedStatus映射到status -->
        <result property="StartTime" column="StartTime"/>
        <result property="EndTime" column="EndTime"/>
        <result property="hospitalCode" column="hospitalCode"/>
        <result property="NumId" column="NumId"/>
    </resultMap>

    <select id="listSchedulingSourceNumber" resultMap="SourceNumberResultMap">
        select b.*, c.StartTime, c.EndTime,
        case
        when b.Status = 0 then 0
        when b.Status = 1 then
        case
        when not exists (
        select *
        from TB_Appointment_LockNum d(nolock)
        where d.Subjectid = b.Subjectid
        and d.DutyDate = b.DutyDate
        and d.SeqNum = b.SeqNum
        and (d.flag = 1 or (d.flag = 0 and d.LockTime > dateadd(minute, -30, getdate())))
        )
        and not exists (
        select *
        from TB_Appointment e(nolock)
        where e.Subjectid = b.Subjectid
        and e.CheckDate = b.DutyDate
        and e.AppointmentStatus not in (2, 3)
        and e.IsDelete = 0
        and e.IsUse = 1
        and e.AppointmentNum = b.SeqNum
        )
        then 0
        else 1
        end
        else 2
        end as computedStatus
        from TB_Cinfiger_Scheduling a(nolock)
        inner join TB_Config_SubjectNumDetail b(nolock) on a.Subjectid = b.Subjectid and a.DutyDate = b.DutyDate and a.HOSPITALCODE = b.HospitalCode and b.IsDelete = 0 and b.IsUse = 1
        inner join TB_Dic_TimeSpanExt c(nolock) on b.TimeSpanID = c.ID and a.HOSPITALCODE = c.HOSPITALCODE and c.IsDelete = 0 and c.IsUse = 1
        where a.id = #{req.scheduling_id} and a.HOSPITALCODE = #{req.hospitalCode} and a.IsDelete = 0 and a.IsUse = 1 and (a.StopNumber = 0 or a.StopNumber is null)
        <if test="req.source_number != null and req.source_number != ''">
            and b.SeqNum = #{req.source_number}
        </if>
        <if test="timeSpanId != null and timeSpanId != ''">
            and b.TimeSpanID in
            <foreach item="item" index="index" collection="timeSpanId" separator="," open="(" close=")">
                #{item}
            </foreach>
        </if>
        and not exists(
            select * from [dbo].[TB_Appointment_LockNum] d(nolock)
            where d.Subjectid = b.Subjectid
            and d.DutyDate = b.DutyDate
            and (flag = 1 or (flag = 0 and [LockTime] > dateadd(minute, -30, getdate())))
            and b.NumId = d.NumId
        )
        and not exists(
            select * from [dbo].[TB_Appointment] e(nolock)
            where e.Subjectid = b.Subjectid
            and e.CheckDate = b.DutyDate
            and e.AppointmentStatus not in (2, 3)
            and e.IsDelete = 0 and e.IsUse = 1 and b.SeqNum = e.AppointmentNum
        )
        order by b.SeqNum asc
    </select>

    <select id="getSchedulingSourceNumber" resultType="com.sunhealth.ihhis.model.dto.schedule.SourceNumber">
        select case
        when b.Status = 0 then 0
        when b.Status = 1 then
        case
        when not exists (
        select *
        from TB_Appointment_LockNum d(nolock)
        where d.Subjectid = b.Subjectid
        and d.DutyDate = b.DutyDate
        and d.SeqNum = b.SeqNum
        and (d.flag = 1 or (d.flag = 0 and d.LockTime > dateadd(minute, -30, getdate())))
        )
        and not exists (
        select *
        from TB_Appointment e(nolock)
        where e.Subjectid = b.Subjectid
        and e.CheckDate = b.DutyDate
        and e.AppointmentStatus not in (2, 3)
        and e.IsDelete = 0
        and e.IsUse = 1
        and e.AppointmentNum = b.SeqNum
        )
        then 0
        else 1
        end
        else 2
        end as Status, b.*, c.StartTime, c.EndTime, e.HisDictionaryName as name

        from TB_Cinfiger_Scheduling a(nolock)
        inner join TB_Cinfiger_SubjectItem s(nolock) on a.Subjectid = s.Subjectid and a.HOSPITALCODE = s.HOSPITALCODE and s.IsUse = 1 and s.IsDelete = 0
        inner join TB_Dic_HisDictionaryExt e(nolock) on s.TimeType=e.HisDictionaryID and e.lang=1 and e.IsDelete=0 and e.IsUse=1
        inner join TB_Config_SubjectNumDetail b(nolock) on a.Subjectid = b.Subjectid and a.DutyDate = b.DutyDate and a.HOSPITALCODE = b.HospitalCode and b.IsDelete = 0 and b.IsUse = 1
        inner join TB_Dic_TimeSpanExt c(nolock) on b.TimeSpanID = c.ID and a.HOSPITALCODE = c.HOSPITALCODE and c.IsDelete = 0 and c.IsUse = 1
        where a.id = #{req.scheduling_id} and a.HOSPITALCODE = #{req.hospitalCode} and a.IsDelete = 0 and a.IsUse = 1 and (a.StopNumber = 0 or a.StopNumber is null)
        <if test="req.source_number != null and req.source_number != ''">
            and b.SeqNum = #{req.source_number}
        </if>
    </select>

    <select id="listSchedulingDept" resultType="com.sunhealth.ihhis.model.dto.schedule.DeptList">
        select distinct c.DeptId as deptId,
                        c.DeptName as deptName,
                        c.InputCode1 as inputCode1,
                        c.InputCode2 as inputCode2,
                        c.ParentId as parentId,
                        c.Level as deptLevel,
                        a.ScheType,
                        doclevel.HisDictionaryName as schedulingType
        from TB_Cinfiger_Scheduling a(nolock)
        inner join TB_Cinfiger_SubjectItem b(nolock) on a.Subjectid=b.SubjectID and a.HOSPITALCODE = b.HOSPITALCODE and b.IsUse = 1 and b.IsDelete = 0
        inner join System_Tb_Department c(nolock) on b.DeptCode = c.DeptCode and c.DeptClassCode = '1' and a.HOSPITALCODE = c.HospitalId
        inner join TB_Dic_HisDictionaryExt doclevel(nolock) on doclevel.HisDictionaryID = b.Grade and doclevel.DictionaryTypeID=114 and doclevel.HospitalCode=a.HOSPITALCODE and doclevel.lang=1
        where a.DutyDate between #{req.beginDate} and #{req.endDate}
        and a.IsUse=1
        and c.Status = 1
        and (a.StopNumber = 0 or a.StopNumber is null)
        and a.IsDelete=0
        and a.HOSPITALCODE = #{req.hospitalCode}
    </select>

    <select id="listSourceDetails" resultType="com.sunhealth.ihhis.model.dto.schedule.SourceDetails">
        select A.ID schedulingId,
        b.SubjectName schedulingName,
        c.TimespanID timespanId,
        b.DctCode doctorId,
        doc.Name doctorName,
        b.DeptCode deptId,
        dept.DeptName deptName,
        dept.ParentId parentDeptId,
        dept.Level deptLevel,
        a.DutyDate dutyDate,
        time.StartTime dutyStartTime,
        time.EndTime dutyEndTime,
        time.Name timeTypeName,
        time.StartTime beginTime,
        time.EndTime endTime,
        c.quatity+c.maxNum sourceQty,
        (select count(distinct SeqNum)
          from (
                select lock.SeqNum
                from TB_Appointment_LockNum lock(nolock)
                where lock.SubjectID = a.Subjectid
                and lock.TimeSpanID = c.TimespanID
                and lock.DutyDate = a.DutyDate
                and (lock.flag = 1 OR (lock.flag = 0 and lock.LockTime > dateadd(MINUTE, -30, getdate())))
                union all
                select app.AppointmentSeqNum AS SeqNum
                from TB_Appointment app(nolock)
                where app.SubjectID = a.Subjectid
                and app.TimeSpanID = c.TimespanID
                and app.CheckDate = a.DutyDate
                and app.AppointmentStatus not in (2, 3)
                and app.IsDelete = 0 and app.IsUse = 1
              ) results
        ) usedSourceQty,
        (select sum(CAST(0.00 as decimal(18,2)) + CAST(fc.Price as decimal(18,2)))
        from Reg_Tb_RegFeeConfig fc(nolock)
        where fc.ConfigType=2
        and fc.ConfigId= doclevel.HisDictionaryCode
        and fc.HospitalCode=a.HOSPITALCODE
        ) registrationFee,
        (select STRING_AGG(fc.ItemId, ',')
        from Reg_Tb_RegFeeConfig fc(nolock)
        where fc.ConfigType=2
        and fc.ConfigId= doclevel.HisDictionaryCode
        and fc.HospitalCode=a.HOSPITALCODE
        ) registrationFeeCode,
        (select sum(CAST(0.00 as decimal(18,2)) + CAST(fc.Price as decimal(18,2)))
        from Reg_Tb_RegFeeConfig fc(nolock)
        left join System_Tb_ChargeItem stc(nolock) on fc.ItemId=stc.ItemCode and stc.HospitalId=fc.HospitalCode
        where fc.ConfigType=2
        and fc.ConfigId= doclevel.HisDictionaryCode
        and stc.ItemCategory = 3
        and fc.HospitalCode=a.HOSPITALCODE
        ) treatmentFee,
        (select STRING_AGG(stc.ItemCode, ',')
        from Reg_Tb_RegFeeConfig fc(nolock)
        left join System_Tb_ChargeItem stc(nolock) on fc.ItemId=stc.ItemCode and stc.HospitalId=fc.HospitalCode
        where fc.ConfigType=2
        and fc.ConfigId= doclevel.HisDictionaryCode
        and stc.ItemCategory = 3
        and fc.HospitalCode=a.HOSPITALCODE
        ) treatmentFeeCode,
        doclevel.HisDictionaryName schedulingType,
        b.DeptAddRess doctorRoom
        from TB_Cinfiger_Scheduling a
        inner join TB_Cinfiger_SubjectItem b on a.Subjectid=b.SubjectID and a.HOSPITALCODE = b.HOSPITALCODE and b.IsUse = 1 and b.IsDelete = 0
        inner join TB_Cinfiger_SchedulingTimespanDetail c on a.ID=c.mainID and a.HOSPITALCODE = c.HOSPITALCODE and c.IsUse = 1 and c.IsDelete = 0
        left join TB_Dic_HisDictionaryExt doclevel on doclevel.HisDictionaryID=b.Grade and doclevel.DictionaryTypeID=114 and doclevel.HospitalCode=a.HOSPITALCODE and doclevel.lang=1
        left join System_Tb_Worker doc on doc.WorkerId=b.DctCode and a.HOSPITALCODE = doc.HospitalId
        left join System_Tb_Department dept on dept.DeptId=b.DeptCode and dept.DeptClassCode = '1' and a.HOSPITALCODE = dept.HospitalId
        left join TB_Dic_TimeSpanExt time on time.ID=c.TimespanID and a.HOSPITALCODE = time.HOSPITALCODE and time.IsUse = 1 and time.IsDelete = 0
        where a.DutyDate between #{req.beginDate} and #{req.endDate}
        and a.IsUse=1
        and (a.StopNumber = 0 or a.StopNumber is null)
        and a.IsDelete=0
        and a.HOSPITALCODE = #{req.hospitalCode}
        <if test="req.dept_id != null and req.dept_id != ''">
            and b.DeptCode = #{req.dept_id}
        </if>
        and time.StartTime is not null
        and time.EndTime is not null
        and time.StartTime is not null
        and time.EndTime is not null
        order by a.DutyDate, time.EndTime
    </select>

    <select id="listDeptSourceDetails" resultType="com.sunhealth.ihhis.model.dto.schedule.DeptSourceDetails">
        select a.ID schedulingId,
               b.DctCode doctorId,
               c.TimespanID timespanId,
               a.DutyDate dutyDate,
               time.StartTime dutyStartTime,
               time.EndTime dutyEndTime,
               time.Name timeTypeName,
               time.StartTime beginTime,
               time.EndTime endTime,
               c.quatity+c.maxNum sourceQty,
               (select count(distinct SeqNum)
                from (
                         select lock.SeqNum
                         from TB_Appointment_LockNum lock(nolock)
                         where lock.SubjectID = a.Subjectid
                           and lock.TimeSpanID = c.TimespanID
                           and lock.DutyDate = a.DutyDate
                           and (lock.flag = 1 OR (lock.flag = 0 and lock.LockTime > dateadd(MINUTE, -30, getdate())))
                         union all
                         select app.AppointmentSeqNum AS SeqNum
                         from TB_Appointment app(nolock)
                         where app.SubjectID = a.Subjectid
                           and app.TimeSpanID = c.TimespanID
                           and app.CheckDate = a.DutyDate
                           and app.AppointmentStatus not in (2, 3)
                           and app.IsDelete = 0 and app.IsUse = 1
                     ) results
               ) usedSourceQty,
               (select sum(CAST(0.00 as decimal(18,2)) + CAST(fc.Price as decimal(18,2)))
                from Reg_Tb_RegFeeConfig fc(nolock)
                where fc.ConfigType=2
                  and fc.ConfigId= doclevel.HisDictionaryCode
                  and fc.HospitalCode=a.HOSPITALCODE
               ) registrationFee,
               (select STRING_AGG(fc.ItemId, ',')
                from Reg_Tb_RegFeeConfig fc(nolock)
                where fc.ConfigType=2
                  and fc.ConfigId= doclevel.HisDictionaryCode
                  and fc.HospitalCode=a.HOSPITALCODE
               ) registrationFeeCode,
               (select sum(CAST(0.00 as decimal(18,2)) + CAST(fc.Price as decimal(18,2)))
                from Reg_Tb_RegFeeConfig fc(nolock)
                         left join System_Tb_ChargeItem stc on fc.ItemId=stc.ItemCode and stc.HospitalId=fc.HospitalCode
                where fc.ConfigType=2
                  and fc.ConfigId= doclevel.HisDictionaryCode
                  and stc.ItemCategory = 3
                  and fc.HospitalCode=a.HOSPITALCODE
               ) treatmentFee,
               (select STRING_AGG(stc.ItemCode, ',')
                from Reg_Tb_RegFeeConfig fc(nolock)
                         left join System_Tb_ChargeItem stc on fc.ItemId=stc.ItemCode and stc.HospitalId=fc.HospitalCode
                where fc.ConfigType=2
                  and fc.ConfigId= doclevel.HisDictionaryCode
                  and stc.ItemCategory = 3
                  and fc.HospitalCode=a.HOSPITALCODE
               ) treatmentFeeCode,
               b.SubjectName schedulingName,
               doclevel.HisDictionaryName schedulingType,
               b.DeptAddRess doctorRoom
        from TB_Cinfiger_Scheduling a
        inner join TB_Cinfiger_SubjectItem b on a.Subjectid=b.SubjectID and a.HOSPITALCODE = b.HOSPITALCODE and b.IsUse = 1 and b.IsDelete = 0
        inner join TB_Cinfiger_SchedulingTimespanDetail c on a.ID=c.mainID and a.HOSPITALCODE = c.HOSPITALCODE and c.IsUse = 1 and c.IsDelete = 0
        left join System_Tb_Worker doc on doc.WorkerId=b.DctCode and a.HOSPITALCODE = doc.HospitalId
        left join System_Tb_Department dept on dept.DeptId=b.DeptCode and dept.DeptClassCode = '1' and a.HOSPITALCODE = dept.HospitalId
        left join TB_Dic_TimeSpanExt time on time.ID=c.TimespanID and a.HOSPITALCODE = time.HOSPITALCODE and time.IsUse = 1 and time.IsDelete = 0
        inner join TB_Dic_HisDictionaryExt doclevel on doclevel.HisDictionaryID = b.Grade and doclevel.DictionaryTypeID=114 and doclevel.HospitalCode=a.HOSPITALCODE and doclevel.lang=1
        where a.DutyDate between #{req.beginDate} and #{req.endDate}
        and a.IsUse=1
        and (a.StopNumber = 0 or a.StopNumber is null)
        and a.IsDelete=0
        and a.HOSPITALCODE = #{req.hospitalCode} and b.DeptCode = #{req.dept_id}
        and time.StartTime is not null
        and time.EndTime is not null
        and time.StartTime is not null
        and time.EndTime is not null
    </select>

    <select id="listSourceNumberByDept" resultType="com.sunhealth.ihhis.model.dto.schedule.SourceNumber">
        select
            a.ID schedulingId,
            c.TimespanID timespanId,
            n.SeqNum seqNum,
            case n.Status
                when 0 then 0
                when 1 then
                    case
                        when not exists (
                            select *
                            from TB_Appointment_LockNum lock(nolock)
                            where lock.SubjectID = n.SubjectID
                              and lock.TimeSpanID = n.TimeSpanID
                              and lock.DutyDate = n.DutyDate
                              and lock.SeqNum = n.SeqNum
                              and (lock.flag = 1 or (lock.flag = 0 and lock.LockTime > dateadd(minute, -30, getdate())))
                            )
                            and not exists(
                                select * from TB_Appointment app(nolock)
                                where app.Subjectid =  n.SubjectID
                                  and app.CheckDate = n.DutyDate
                                  and app.AppointmentNum = n.SeqNum
                                  and app.AppointmentStatus not in (2, 3)
                                  and app.IsDelete = 0 and app.IsUse = 1
                            )
                        then 0
                        else 1
                    end
                else 2
            end as status
        from TB_Cinfiger_Scheduling a(nolock)
        inner join TB_Cinfiger_SubjectItem b(nolock) on a.Subjectid=b.SubjectID and a.HOSPITALCODE = b.HOSPITALCODE and b.IsUse = 1 and b.IsDelete = 0
        inner join TB_Cinfiger_SchedulingTimespanDetail c(nolock) on a.ID=c.mainID and a.HOSPITALCODE = c.HOSPITALCODE and c.IsUse = 1 and c.IsDelete = 0
        left join System_Tb_Department dept(nolock) on dept.DeptId=b.DeptCode and dept.DeptClassCode = '1' and a.HOSPITALCODE = dept.HospitalId
        inner join TB_Config_SubjectNumDetail n(nolock) on n.SubjectID = a.Subjectid and a.DutyDate = n.DutyDate and n.TimeSpanID = c.TimespanID and a.HOSPITALCODE = n.HospitalCode and n.IsUse = 1 and n.IsDelete = 0
        where a.DutyDate between #{req.beginDate} and #{req.endDate}
        and a.IsUse=1
        and (a.StopNumber = 0 or a.StopNumber is null)
        and a.IsDelete=0
        and a.HOSPITALCODE = #{req.hospitalCode} and b.DeptCode = #{req.dept_id}
        order by a.id, c.TimespanID, n.SeqNum
    </select>

    <select id="listDoctorSourceDetails" resultType="com.sunhealth.ihhis.model.dto.schedule.DoctorSourceDetails">
        select
            a.ID schedulingId,
            c.TimespanID timespanId,
            b.DeptCode dept_id,
            dept.DeptName dept_name,
            a.DutyDate dutyDate,
            time.StartTime dutyStartTime,
            time.EndTime dutyEndTime,
            time.Name timeTypeName,
            time.StartTime beginTime,
            time.EndTime endTime,
            c.quatity+c.maxNum sourceQty,
            (select count(distinct SeqNum)
             from (
                      select lock.SeqNum
                      from TB_Appointment_LockNum lock(nolock)
                      where lock.SubjectID = a.Subjectid
                        and lock.TimeSpanID = c.TimespanID
                        and lock.DutyDate = a.DutyDate
                        and (lock.flag = 1 OR (lock.flag = 0 and lock.LockTime > dateadd(MINUTE, -30, getdate())))
                      union all
                      select app.AppointmentSeqNum AS SeqNum
                      from TB_Appointment app(nolock)
                      where app.SubjectID = a.Subjectid
                        and app.TimeSpanID = c.TimespanID
                        and app.CheckDate = a.DutyDate
                        and app.AppointmentStatus not in (2, 3)
                        and app.IsDelete = 0 and app.IsUse = 1
                  ) results
            ) usedSourceQty,
            (select sum(CAST(0.00 as decimal(18,2)) + CAST(fc.Price as decimal(18,2)))
             from Reg_Tb_RegFeeConfig fc(nolock)
             where fc.ConfigType=2
               and fc.ConfigId= doclevel.HisDictionaryCode
               and fc.HospitalCode=a.HOSPITALCODE
            ) registrationFee,
            (select STRING_AGG(fc.ItemId, ',')
             from Reg_Tb_RegFeeConfig fc(nolock)
             where fc.ConfigType=2
               and fc.ConfigId= doclevel.HisDictionaryCode
               and fc.HospitalCode=a.HOSPITALCODE
            ) registrationFeeCode,
            (select sum(CAST(0.00 as decimal(18,2)) + CAST(fc.Price as decimal(18,2)))
             from Reg_Tb_RegFeeConfig fc(nolock)
                      left join System_Tb_ChargeItem stc(nolock) on fc.ItemId=stc.ItemCode and stc.HospitalId=fc.HospitalCode
             where fc.ConfigType=2
               and fc.ConfigId= doclevel.HisDictionaryCode
               and stc.ItemCategory = 3
               and fc.HospitalCode=a.HOSPITALCODE
            ) treatmentFee,
            (select STRING_AGG(stc.ItemCode, ',')
             from Reg_Tb_RegFeeConfig fc(nolock)
                      left join System_Tb_ChargeItem stc(nolock) on fc.ItemId=stc.ItemCode and stc.HospitalId=fc.HospitalCode
             where fc.ConfigType=2
               and fc.ConfigId= doclevel.HisDictionaryCode
               and stc.ItemCategory = 3
               and fc.HospitalCode=a.HOSPITALCODE
            ) treatmentFeeCode,
            doclevel.HisDictionaryName schedulingType,
            b.SubjectName schedulingName,
            b.DeptAddRess doctorRoom
        from TB_Cinfiger_Scheduling a
        inner join TB_Cinfiger_SubjectItem b on a.Subjectid=b.SubjectID and a.HOSPITALCODE = b.HOSPITALCODE and b.IsUse = 1 and b.IsDelete = 0
        inner join TB_Cinfiger_SchedulingTimespanDetail c on a.ID=c.mainID and a.HOSPITALCODE = c.HOSPITALCODE and c.IsUse = 1 and c.IsDelete = 0
        left join TB_Dic_HisDictionaryExt doclevel on doclevel.HisDictionaryID=b.Grade and doclevel.DictionaryTypeID=114 and doclevel.HospitalCode=a.HOSPITALCODE and doclevel.lang=1
        left join System_Tb_Worker doc on doc.WorkerId=b.DctCode and a.HOSPITALCODE = doc.HospitalId
        left join System_Tb_Department dept on dept.DeptId=b.DeptCode and dept.DeptClassCode = '1' and a.HOSPITALCODE = dept.HospitalId
        left join TB_Dic_TimeSpanExt time on time.ID=c.TimespanID and a.HOSPITALCODE = time.HOSPITALCODE and time.IsUse = 1 and time.IsDelete = 0
        where a.DutyDate between #{req.beginDate} and #{req.endDate}
        and a.IsUse=1
        and (a.StopNumber = 0 or a.StopNumber is null)
        and a.IsDelete=0
        and a.HOSPITALCODE = #{req.hospitalCode} and b.DctCode = #{req.doctor_id}
        and time.StartTime is not null
        and time.EndTime is not null
        and time.StartTime is not null
        and time.EndTime is not null
    </select>

    <select id="listSourceNumberByDoct" resultType="com.sunhealth.ihhis.model.dto.schedule.SourceNumber">
        select
            a.ID schedulingId,
            c.TimespanID timespanId,
            n.SeqNum seqNum,
            case n.Status
                when 0 then 0
                when 1 then
                    case
                        when not exists (
                            select *
                            from TB_Appointment_LockNum lock(nolock)
                            where lock.SubjectID = n.SubjectID
                              and lock.TimeSpanID = n.TimeSpanID
                              and lock.DutyDate = n.DutyDate
                              and lock.SeqNum = n.SeqNum
                              and (lock.flag = 1 or (lock.flag = 0 and lock.LockTime > dateadd(minute, -30, getdate())))
                        )
                        and not exists(
                            select * from TB_Appointment app(nolock)
                            where app.Subjectid =  n.SubjectID
                              and app.CheckDate = n.DutyDate
                              and app.AppointmentNum = n.SeqNum
                              and app.AppointmentStatus not in (2, 3)
                              and app.IsDelete = 0 and app.IsUse = 1
                        )
                        then 0
                        else 1
                    end
               else 2
            end as status
        from TB_Cinfiger_Scheduling a(nolock)
                 inner join TB_Cinfiger_SubjectItem b(nolock) on a.Subjectid=b.SubjectID and a.HOSPITALCODE = b.HOSPITALCODE and b.IsUse = 1 and b.IsDelete = 0
                 inner join TB_Cinfiger_SchedulingTimespanDetail c(nolock) on a.ID=c.mainID and a.HOSPITALCODE = c.HOSPITALCODE and c.IsUse = 1 and c.IsDelete = 0
                 left join System_Tb_Department dept(nolock) on dept.DeptId=b.DeptCode and dept.DeptClassCode = '1' and a.HOSPITALCODE = dept.HospitalId
                 inner join TB_Config_SubjectNumDetail n(nolock) on n.SubjectID = a.Subjectid and a.DutyDate = n.DutyDate and n.TimeSpanID = c.TimespanID and a.HOSPITALCODE = n.HospitalCode and n.IsUse = 1 and n.IsDelete = 0
        where a.DutyDate between #{req.beginDate} and #{req.endDate}
        and a.IsUse=1
        and (a.StopNumber = 0 or a.StopNumber is null)
        and a.IsDelete=0
        and a.HOSPITALCODE = #{req.hospitalCode} and b.DctCode = #{req.doctor_id}
        order by a.id, c.TimespanID, n.SeqNum
    </select>

    <select id="selectScheduleDetail" resultType="com.sunhealth.ihhis.model.dto.schedule.ScheduleDetail">
        SELECT
            b.Subjectid courseID,
            b.SubjectName courseName,
            b.DeptCode deptId,
            b.DctCode doctorId,
            doclevel.HisDictionaryCode doctorLevel
        FROM
            TB_Cinfiger_Scheduling a(nolock)
                INNER JOIN TB_Cinfiger_SubjectItem b(nolock) ON a.Subjectid= b.SubjectID and a.HOSPITALCODE = b.HOSPITALCODE and b.IsUse = 1 and b.IsDelete = 0
                LEFT JOIN TB_Dic_HisDictionaryExt doclevel(nolock) ON doclevel.HisDictionaryID= b.Grade
                AND doclevel.DictionaryTypeID= 114
                AND doclevel.HospitalCode= a.HOSPITALCODE
                AND doclevel.lang= 1
                LEFT JOIN System_Tb_Worker doc(nolock) ON doc.WorkerId= b.DctCode
        WHERE
            a.id = #{schedulingId}
          AND (a.StopNumber = 0 or a.StopNumber is null)
          AND a.HOSPITALCODE = #{hospitalCode}

    </select>

    <select id="currentDayAppointment" resultType="com.sunhealth.ihhis.model.dto.schedule.CurrentDayAppointment">
        SELECT
            A.ID schedulingId,
            b.SubjectName schedulingName,
            b.DctCode doctorId,
            doc.Name doctorName,
            b.DeptCode deptId,
            dept.DeptName deptName,
            dept.ParentId parentDeptId,
            dept.Level deptLevel,
            a.DutyDate dutyDate,
            ( SELECT MIN ( t.StartTime ) FROM TB_Dic_TimeSpanExt t
                    INNER JOIN TB_Cinfiger_SchedulingTimespanDetail c ON a.ID= c.mainID AND a.HOSPITALCODE = c.HOSPITALCODE AND c.IsUse = 1 AND c.IsDelete = 0
                WHERE
                    t.ID= c.TimespanID
                    AND a.HOSPITALCODE = t.HOSPITALCODE
                    AND t.IsUse = 1
                    AND t.IsDelete = 0
                    AND t.EndTime BETWEEN CONVERT(varchar(12),GETDATE(),108) AND '24:00:00'
            ) beginTime,
            ( SELECT MAX ( t.EndTime ) FROM TB_Dic_TimeSpanExt t
                    INNER JOIN TB_Cinfiger_SchedulingTimespanDetail c ON a.ID= c.mainID AND a.HOSPITALCODE = c.HOSPITALCODE AND c.IsUse = 1 AND c.IsDelete = 0
                WHERE
                    t.ID= c.TimespanID
                    AND a.HOSPITALCODE = t.HOSPITALCODE
                    AND t.IsUse = 1
                    AND t.IsDelete = 0
                    AND t.EndTime BETWEEN CONVERT(varchar(12),GETDATE(),108) AND '24:00:00'
            ) endTime,
            ( SELECT SUM( c.quatity ) + SUM( c.maxNum )
                FROM
                    TB_Cinfiger_SchedulingTimespanDetail c
                    INNER JOIN TB_Dic_TimeSpanExt t ON t.ID= c.TimespanID AND a.HOSPITALCODE = t.HOSPITALCODE AND t.IsUse = 1 AND t.IsDelete = 0
                WHERE
                    a.ID= c.mainID
                    AND a.HOSPITALCODE = c.HOSPITALCODE
                    AND c.IsUse = 1
                    AND c.IsDelete = 0
                    AND t.EndTime BETWEEN CONVERT(varchar(12),GETDATE(),108) AND '24:00:00'
            ) sourceQty,
            ( SELECT COUNT( DISTINCT SeqNum )
                FROM
                ( SELECT
                        lock.SeqNum
                    FROM
                        TB_Cinfiger_SchedulingTimespanDetail c
                        LEFT JOIN TB_Dic_TimeSpanExt t ON t.ID = c.TimespanID AND a.HOSPITALCODE = t.HOSPITALCODE AND t.IsUse = 1 AND t.IsDelete = 0,
                        TB_Appointment_LockNum lock
                    WHERE
                        a.ID= c.mainID
                        AND a.HOSPITALCODE = c.HOSPITALCODE
                        AND c.IsUse = 1
                        AND c.IsDelete = 0
                        AND lock.SubjectID = a.Subjectid
                        AND lock.TimeSpanID = c.TimespanID
                        AND lock.DutyDate = a.DutyDate
                        AND ( lock.flag = 1 OR ( lock.flag = 0 AND lock.LockTime > dateadd( MINUTE, -30, getdate( ) ) ) )
                        AND t.EndTime BETWEEN CONVERT(varchar(12),GETDATE(),108) AND '24:00:00'
                    UNION ALL
                    SELECT
                        app.AppointmentSeqNum AS SeqNum
                    FROM
                        TB_Cinfiger_SchedulingTimespanDetail c
                        LEFT JOIN TB_Dic_TimeSpanExt t ON t.ID= c.TimespanID AND a.HOSPITALCODE = t.HOSPITALCODE AND t.IsUse = 1 AND t.IsDelete = 0,
                        TB_Appointment app
                    WHERE
                        a.ID= c.mainID
                        AND a.HOSPITALCODE = c.HOSPITALCODE
                        AND c.IsUse = 1
                        AND c.IsDelete = 0
                        AND app.SubjectID = a.Subjectid
                        AND app.TimeSpanID = c.TimespanID
                        AND app.CheckDate = a.DutyDate
                        AND app.AppointmentStatus NOT IN ( 2, 3 )
                        AND app.IsDelete = 0
                        AND app.IsUse = 1
                        AND t.EndTime BETWEEN CONVERT(varchar(12),GETDATE(),108) AND '24:00:00'
                ) results
            ) usedSourceQty,
            ( SELECT SUM( CAST ( 0.00 AS DECIMAL ( 18, 2 ) ) + CAST ( fc.Price AS DECIMAL ( 18, 2 ) ) )
                FROM
                    Reg_Tb_RegFeeConfig fc
                WHERE
                    fc.ConfigType= 2
                    AND fc.ConfigId= doclevel.HisDictionaryCode
                    AND fc.HospitalCode= a.HOSPITALCODE
            ) registrationFee,
            ( SELECT STRING_AGG ( fc.ItemId, ',' )
                FROM
                    Reg_Tb_RegFeeConfig fc
                WHERE
                    fc.ConfigType= 2
                    AND fc.ConfigId= doclevel.HisDictionaryCode
                    AND fc.HospitalCode= a.HOSPITALCODE
            ) registrationFeeCode,
            ( SELECT SUM( CAST ( 0.00 AS DECIMAL ( 18, 2 ) ) + CAST ( fc.Price AS DECIMAL ( 18, 2 ) ) )
                FROM
                    Reg_Tb_RegFeeConfig fc
                    LEFT JOIN System_Tb_ChargeItem stc ON fc.ItemId= stc.ItemCode AND stc.HospitalId= fc.HospitalCode
                WHERE
                    fc.ConfigType= 2
                    AND fc.ConfigId= doclevel.HisDictionaryCode
                    AND stc.ItemCategory = 3
                    AND fc.HospitalCode= a.HOSPITALCODE
            ) treatmentFee,
            ( SELECT STRING_AGG ( stc.ItemCode, ',' )
                FROM
                    Reg_Tb_RegFeeConfig fc
                    LEFT JOIN System_Tb_ChargeItem stc ON fc.ItemId= stc.ItemCode AND stc.HospitalId= fc.HospitalCode
                WHERE
                    fc.ConfigType= 2
                    AND fc.ConfigId= doclevel.HisDictionaryCode
                    AND stc.ItemCategory = 3
                    AND fc.HospitalCode= a.HOSPITALCODE
            ) treatmentFeeCode,
            doclevel.HisDictionaryName schedulingType,
            b.DeptAddRess doctorRoom
        FROM
            TB_Cinfiger_Scheduling a
            INNER JOIN TB_Cinfiger_SubjectItem b ON a.Subjectid= b.SubjectID AND a.HOSPITALCODE = b.HOSPITALCODE AND b.IsUse = 1 AND b.IsDelete = 0
            LEFT JOIN TB_Dic_HisDictionaryExt doclevel ON doclevel.HisDictionaryID= b.Grade AND doclevel.DictionaryTypeID= 114 AND doclevel.HospitalCode= a.HOSPITALCODE AND doclevel.lang= 1
            LEFT JOIN System_Tb_Worker doc ON doc.WorkerId= b.DctCode AND a.HOSPITALCODE = doc.HospitalId
            LEFT JOIN System_Tb_Department dept ON dept.DeptId= b.DeptCode AND dept.DeptClassCode = '1' AND a.HOSPITALCODE = dept.HospitalId
        WHERE
            a.DutyDate BETWEEN #{req.beginDate} AND #{req.endDate}
            AND a.IsUse= 1
            AND ( a.StopNumber = 0 OR a.StopNumber IS NULL )
            AND a.IsDelete= 0
            AND a.HOSPITALCODE = #{req.hospitalCode}
            AND EXISTS (select * from TB_Cinfiger_SchedulingTimespanDetail c
                inner join TB_Dic_TimeSpanExt t on t.ID=c.TimespanID and a.HOSPITALCODE = t.HOSPITALCODE and t.IsUse = 1
                and t.IsDelete = 0 where a.ID=c.mainID and a.HOSPITALCODE = c.HOSPITALCODE and c.IsUse = 1 and c.IsDelete = 0
                and t.EndTime between CONVERT(varchar(12),GETDATE(),108) and '24:00:00')
        ORDER BY
            a.DutyDate
    </select>

    <select id="getCurrentDayDoctorSourceDetail" resultType="com.sunhealth.ihhis.model.dto.schedule.CurrentDoctorSourceDetails">
        select
            a.ID schedulingId,
            b.DeptCode deptId,
            dept.DeptName deptName,
            a.DutyDate dutyDate,
            ( SELECT MIN ( t.StartTime )
                FROM TB_Dic_TimeSpanExt t
                    INNER JOIN TB_Cinfiger_SchedulingTimespanDetail c ON a.ID= c.mainID AND a.HOSPITALCODE = c.HOSPITALCODE AND c.IsUse = 1 AND c.IsDelete = 0
                WHERE
                    t.ID= c.TimespanID
                    AND a.HOSPITALCODE = t.HOSPITALCODE
                    AND t.IsUse = 1
                    AND t.IsDelete = 0
                    AND t.EndTime BETWEEN CONVERT(varchar(12),GETDATE(),108) AND '24:00:00'
            ) beginTime,
            ( SELECT MAX ( t.EndTime ) FROM
                    TB_Dic_TimeSpanExt t
                    INNER JOIN TB_Cinfiger_SchedulingTimespanDetail c ON a.ID= c.mainID AND a.HOSPITALCODE = c.HOSPITALCODE AND c.IsUse = 1 AND c.IsDelete = 0
                WHERE
                    t.ID= c.TimespanID
                    AND a.HOSPITALCODE = t.HOSPITALCODE
                    AND t.IsUse = 1
                    AND t.IsDelete = 0
                    AND t.EndTime BETWEEN CONVERT(varchar(12),GETDATE(),108) AND '24:00:00'
            ) endTime,
            ( SELECT SUM( c.quatity ) + SUM( c.maxNum )
                FROM TB_Cinfiger_SchedulingTimespanDetail c
                    INNER JOIN TB_Dic_TimeSpanExt t ON t.ID= c.TimespanID AND a.HOSPITALCODE = t.HOSPITALCODE AND t.IsUse = 1 AND t.IsDelete = 0
                WHERE
                    a.ID= c.mainID
                    AND a.HOSPITALCODE = c.HOSPITALCODE
                    AND c.IsUse = 1
                    AND c.IsDelete = 0
                    AND t.EndTime BETWEEN CONVERT(varchar(12),GETDATE(),108) AND '24:00:00'
            ) sourceQty,
            (select count(distinct SeqNum)
                from (
                    select lock.SeqNum
                    from
                        TB_Cinfiger_SchedulingTimespanDetail c
                        LEFT JOIN TB_Dic_TimeSpanExt t ON t.ID = c.TimespanID AND a.HOSPITALCODE = t.HOSPITALCODE AND t.IsUse = 1 AND t.IsDelete = 0,
                        TB_Appointment_LockNum lock
                    where lock.SubjectID = a.Subjectid
                        and lock.TimeSpanID = c.TimespanID
                        and lock.DutyDate = a.DutyDate
                        and (lock.flag = 1 OR (lock.flag = 0 and lock.LockTime > dateadd(MINUTE, -30, getdate())))
                        AND t.EndTime BETWEEN CONVERT(varchar(12),GETDATE(),108) AND '24:00:00'
                    union all
                    select app.AppointmentSeqNum AS SeqNum
                    from
                        TB_Cinfiger_SchedulingTimespanDetail c
                        LEFT JOIN TB_Dic_TimeSpanExt t ON t.ID= c.TimespanID AND a.HOSPITALCODE = t.HOSPITALCODE AND t.IsUse = 1 AND t.IsDelete = 0,
                        TB_Appointment app
                    where app.SubjectID = a.Subjectid
                        and app.TimeSpanID = c.TimespanID
                        and app.CheckDate = a.DutyDate
                        and app.AppointmentStatus not in (2, 3)
                        and app.IsDelete = 0 and app.IsUse = 1
                        AND t.EndTime BETWEEN CONVERT(varchar(12),GETDATE(),108) AND '24:00:00'
                ) results
            ) usedSourceQty,
            (select sum(CAST(0.00 as decimal(18,2)) + CAST(fc.Price as decimal(18,2)))
                from Reg_Tb_RegFeeConfig fc
                    where fc.ConfigType=2
                    and fc.ConfigId= doclevel.HisDictionaryCode
                    and fc.HospitalCode=a.HOSPITALCODE
            ) registrationFee,
            (select STRING_AGG(fc.ItemId, ',')
                from Reg_Tb_RegFeeConfig fc
                    where fc.ConfigType=2
                    and fc.ConfigId= doclevel.HisDictionaryCode
                    and fc.HospitalCode=a.HOSPITALCODE
            ) registrationFeeCode,
            (select sum(CAST(0.00 as decimal(18,2)) + CAST(fc.Price as decimal(18,2)))
                from Reg_Tb_RegFeeConfig fc
                    left join System_Tb_ChargeItem stc on fc.ItemId=stc.ItemCode and stc.HospitalId=fc.HospitalCode
                where fc.ConfigType=2
                    and fc.ConfigId= doclevel.HisDictionaryCode
                    and stc.ItemCategory = 3
                    and fc.HospitalCode=a.HOSPITALCODE
            ) treatmentFee,
            (select STRING_AGG(stc.ItemCode, ',')
                from Reg_Tb_RegFeeConfig fc
                    left join System_Tb_ChargeItem stc on fc.ItemId=stc.ItemCode and stc.HospitalId=fc.HospitalCode
                where fc.ConfigType=2
                    and fc.ConfigId= doclevel.HisDictionaryCode
                    and stc.ItemCategory = 3
                    and fc.HospitalCode=a.HOSPITALCODE
            ) treatmentFeeCode,
            doclevel.HisDictionaryName schedulingType,
            b.SubjectName schedulingName,
            b.DeptAddRess doctorRoom
        from TB_Cinfiger_Scheduling a
            inner join TB_Cinfiger_SubjectItem b on a.Subjectid=b.SubjectID and a.HOSPITALCODE = b.HOSPITALCODE and b.IsUse = 1 and b.IsDelete = 0
            left join TB_Dic_HisDictionaryExt doclevel on doclevel.HisDictionaryID=b.Grade and doclevel.DictionaryTypeID=114 and doclevel.HospitalCode=a.HOSPITALCODE and doclevel.lang=1
            left join System_Tb_Worker doc on doc.WorkerId=b.DctCode and a.HOSPITALCODE = doc.HospitalId
            left join System_Tb_Department dept on dept.DeptId=b.DeptCode and dept.DeptClassCode = '1' and a.HOSPITALCODE = dept.HospitalId
        where a.DutyDate between #{req.beginDate} AND #{req.endDate}
            and a.IsUse=1
            and (a.StopNumber = 0 or a.StopNumber is null)
            and a.IsDelete=0
            and a.HOSPITALCODE = #{req.hospitalCode} and b.DctCode = #{req.doctor_id}
            AND EXISTS (select * from TB_Cinfiger_SchedulingTimespanDetail c
                inner join TB_Dic_TimeSpanExt t on t.ID=c.TimespanID and a.HOSPITALCODE = t.HOSPITALCODE and t.IsUse = 1
                and t.IsDelete = 0 where a.ID=c.mainID and a.HOSPITALCODE = c.HOSPITALCODE and c.IsUse = 1 and c.IsDelete = 0
                and t.EndTime between CONVERT(varchar(12),GETDATE(),108) and '24:00:00')
    </select>

</mapper>
