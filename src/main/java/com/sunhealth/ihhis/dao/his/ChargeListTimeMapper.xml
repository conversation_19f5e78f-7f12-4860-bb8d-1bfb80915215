<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.sunhealth.ihhis.dao.his.ChargeListTimeMapper">
    <insert id="insertChargeListTime">
        --从预收费表中根据PreChargeNo查出要收费的信息分别插入收费主表
        INSERT INTO [dbo].[Reg_Tb_ChargeList_Time] ([ChargeNo],
            [PatID],
            [NewPatID],
            [RegNo],
            [CardType],
            [CardNo],
            [CardData],
            [OutpatientNo],
            [AccountFlag],
            [RegistType],
            [RecipeCount],
            [ChargeTime],
            [ChargeType],
            [ComputerNo],
            [Status],
            [IndustrialInjury],
            [CureCode],
            [DiagCode],
            [PatType],
            [OpCode],
            [CreateTime],
            [IsDelete],
            [CreatedBy],
            [CreatedDate],
            [DataFrom],
            [JsFlag],
        [HospitalCode])
        SELECT TOP 1
                #{chargeNo},
                 reglist.PatID,
               patlist.NewPatID,
               pre.RegNo,
               #{cardType},
               pre.CardNo,
               pre.CardNo,
               reglist.OutPatientNo,
               #{accountFlag},
               reglist.RegistType,
               #{recipeCount},
               #{time},
               pre.ChargeType,
               #{computerNo},
               0,
               pre.IndustrialInjury,
               pre.CureCode,
               pre.DiagCode,
               pre.PatType,
               #{opId},
               #{time},
               0,
               #{opId},
               #{time},
               #{dataForm},
               #{jsFlag},
               pre.HospitalCode
        FROM
            [dbo].[Reg_Tb_PreCharge] pre ( nolock )
            INNER JOIN [dbo].[Reg_Tb_RegisterList_Time] reglist ( nolock )
        ON reglist.RegNo = pre.RegNo
            INNER JOIN [dbo].[Reg_Tb_PatientList] patlist ( nolock ) ON patlist.PatID = reglist.PatID
            AND pre.HospitalCode = patlist.HospitalCode
        WHERE
            pre.ChargeNo = #{chargeNo}
        UNION ALL
        SELECT TOP 1 #{chargeNo}, reglist.PatID,
               patlist.NewPatID,
               pre.RegNo,
               #{cardType},
               pre.CardNo,
               pre.CardNo,
               reglist.OutPatientNo,
               #{accountFlag},
               reglist.RegistType,
               #{recipeCount},
               #{time},
               pre.ChargeType,
               #{computerNo},
               0,--patdetl.EvidenceNo,
               pre.IndustrialInjury,
               pre.CureCode,
               pre.DiagCode,
               pre.PatType,
               #{opId},
               #{time},
               0,
               #{opId},
               #{time},
               #{dataForm},
               #{jsFlag},
               pre.HospitalCode
        FROM
            [dbo].[Reg_Tb_PreCharge] pre ( nolock )
            INNER JOIN [dbo].[Reg_Tb_RegisterList] reglist ( nolock )
        ON reglist.RegNo = pre.RegNo
            INNER JOIN [dbo].[Reg_Tb_PatientList] patlist ( nolock ) ON patlist.PatID = reglist.PatID
            AND pre.HospitalCode = patlist.HospitalCode
        WHERE
            pre.ChargeNo = #{chargeNo}
    </insert>

</mapper>