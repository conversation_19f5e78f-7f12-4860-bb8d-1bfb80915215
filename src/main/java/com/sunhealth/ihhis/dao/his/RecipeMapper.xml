<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.sunhealth.ihhis.dao.his.RecipeMapper">
    <resultMap id="recipe" type="com.sunhealth.ihhis.model.dto.outpatientcharge.OutpatientRecipeInfo">
        <result property="regno" column="regno"/>
        <result property="recipe_no" column="recipe_no"/>
        <result property="recipe_type" column="recipe_type"/>
        <result property="recipe_time" column="recipe_time"/>
        <result property="recipe_dept_id" column="recipe_dept_id"/>
        <result property="recipe_dept_name" column="recipe_dept_name"/>
        <result property="recipe_doctor_id" column="recipe_doctor_id"/>
        <result property="recipe_doctor_name" column="recipe_doctor_name"/>
        <result property="check_status" column="check_status"/>
        <result property="organ_code" column="organ_code"/>
        <result property="record_status" column="record_status"/>
        <result property="settle_status" column="settle_status"/>
        <result property="hosp_flag" column="hosp_flag"/>
        <result property="oneself_flag" column="oneself_flag"/>
        <result property="long_recipe_flag" column="long_recipe_flag"/>
        <result property="extend_recipe_flag" column="extend_recipe_flag"/>
        <result property="main_diagnosis" column="main_diagnosis"/>
        <result property="recipe_source" column="recipe_source"/>
    </resultMap>

    <!-- 自定义 SQL 查询语句 -->
    <select id="selectOutpatientRecipeList" resultMap="recipe">
        SELECT
            m.ghlsh regno --挂号序号
            ,
            m.cfid recipe_no --处方序号
            ,
            CASE m.mzcflx WHEN 12 THEN 1 WHEN 13 THEN 2 WHEN 14 THEN 3 WHEN 8 THEN 4 WHEN 6 THEN 5 ELSE 1 END recipe_type --处方类型
            ,
            FORMAT(m.sckfrq, 'yyyyMMddHHmmss') recipe_time --开方时间
            ,
            dept.DeptId recipe_dept_id --开方科室代码
            ,
            dept.DeptName AS recipe_dept_name --开方科室名称
            ,
            doc.WorkerId recipe_doctor_id --开方医生代码
            ,
            doc.Name AS recipe_doctor_name --开方医生名称
            ,
            CASE m.cfzt WHEN 0 THEN 1 WHEN 1 THEN 0 ELSE 1 END check_status --审核状态
            ,
            a.HospitalCode organ_code --院区代码
            ,
            CASE a.Status WHEN 0 THEN 0 WHEN 80 THEN 1 WHEN 120 THEN 2 END record_status --记录状态
            ,
            CASE a.Status WHEN 0 THEN 2 ELSE 2 END settle_status --结算状态
            ,
            '0' hosp_flag -- 流转处方药房标志	Y	0常规处方 1院外流转处方
            ,
            '0' oneself_flag -- 自备标识	Y	0非自备(院内取药) 1自备
            ,
            '0' long_recipe_flag --长处方标志
            ,
            '0' extend_recipe_flag --延伸处方标志
            ,
            '0' recipe_source --处方来源标志
            ,
            m.OtherContent main_diagnosis--json字符串中"code": "diagnosisList" 是关联的诊断
        FROM
            MZYS_TB_MZCF m
            INNER JOIN Reg_Tv_ChargeDetail b ON m.cfid = b.recipeId
            LEFT JOIN Reg_Tv_ChargeList a ON a.chargeNo = b.chargeNo
            LEFT JOIN System_Tb_Department dept ON dept.DeptId = b.DeptID
            LEFT JOIN System_Tb_Worker doc ON doc.WorkerId = m.sckfys
        WHERE
            a.PatID = #{req.patid}
            AND a.ChargeTime BETWEEN #{req.beginDate} AND #{req.endDate}
            AND a.Status != 120
            AND m.scbj != 1
    </select>

    <resultMap id="item" type="com.sunhealth.ihhis.model.dto.outpatientcharge.ItemInfo">
        <result property="item_code" column="item_code"/>
        <result property="item_name" column="item_name"/>
        <result property="drug_flag" column="drug_flag"/>
        <result property="unit" column="unit"/>
        <result property="insurance_code" column="insurance_code"/>
        <result property="drug_spec" column="drug_spec"/>
        <result property="dosage_form_code" column="dosage_form_code"/>
        <result property="frequency" column="frequency"/>
        <result property="frequency_code" column="frequency_code"/>
        <result property="usage_code" column="usage_code"/>
        <result property="usage" column="usage"/>
        <result property="dosage" column="dosage"/>
        <result property="dosage_unit" column="dosage_unit"/>
        <result property="use_days" column="use_days"/>
        <result property="price" column="price"/>
        <result property="quantity" column="quantity"/>
        <result property="amount" column="amount"/>
    </resultMap>

    <select id="selectRecipeItemList" resultMap="item">
        select a.ItemID     item_code      --药品、项目代码
             , a.ItemName   item_name      --药品、项目名称
             --detail_entrust_content	--明细嘱托
             , a.IsDrug     drug_flag      --药品标识
             , ltrim(rtrim(isnull(a.ClinicUnit,''))) unit           --药品单位
             --clinical_item_code	--临床项目代码
             --clinical_item_name	--临床项目名称
             --set_item_name	--收费大项目名称
             , b.NationCode insurance_code --医保对应代码
             , ltrim(rtrim(isnull(b.DrugGuage,'')))  drug_spec      --药品规格
             --dosage_form_name	--剂型名称
             , drug.DosageForm dosage_form_code	--剂型代码
             , ltrim(rtrim(isnull(yf.yfmc ,''))) frequency      --频次名称
             , ltrim(rtrim(isnull(yf.yfbm,'')))  frequency_code --频次代码
             , c.gytj       usage_code     --用法代码
             , gytj.HisDictionaryName usage	--用法名称
            ,c.jl dosage	--药品剂量
            ,c.jldw dosage_unit	--剂量单位
            --decocting_method	--中药煎法
            ,c.ts use_days	--用药天数
            --self_rate	--自付比例
            ,CAST( a.Price * 100 AS INT) price	--项目单价
            ,a.Quantiy quantity	--数量
            ,CAST( a.TotalAmount * 100 AS INT) amount	--总金额
        from Reg_Tv_ChargeDetail a
            inner join System_Tb_PubItems b
        on a.ItemID=b.ItemCode
            left join Drug_Tb_DrugInfomation drug on drug.DrugId=b.ItemCode
            left join MZYS_TB_MZCFMX c on c.cfmxid=a.RecipeDetlID
            left join MZYS_TB_YPYF yf on yf.yflsh= c.yf
            left join TB_Dic_HisDictionary gytj on gytj.HisDictionaryCode= c.gytj and gytj.HisDictionaryId=31 and gytj.HospitalId= c.hospitalCode
        where a.RecipeID = #{recipeNo} and a.Quantiy > 0
    </select>


    <select id="selectRecipeItemList1" resultMap="item">
        select a.ItemID     item_code      --药品、项目代码
             , a.ItemName   item_name      --药品、项目名称
             --detail_entrust_content	--明细嘱托
             , a.IsDrug     drug_flag      --药品标识
             , c.dw unit           --药品单位
             --clinical_item_code	--临床项目代码
             --clinical_item_name	--临床项目名称
             --set_item_name	--收费大项目名称
--              , b.NationCode insurance_code --医保对应代码
             , c.gg  drug_spec      --药品规格
             --dosage_form_name	--剂型名称
--              , drug.DosageForm dosage_form_code	--剂型代码
             , ltrim(rtrim(isnull(yf.yfmc ,''))) frequency      --频次名称
             , ltrim(rtrim(isnull(yf.yfbm,'')))  frequency_code --频次代码
             , c.gytj       usage_code     --用法代码
             , gytj.HisDictionaryName usage	--用法名称
            ,c.jl dosage	--药品剂量
            ,c.jldw dosage_unit	--剂量单位
            --decocting_method	--中药煎法
            ,c.ts use_days	--用药天数
            --self_rate	--自付比例
            ,CAST( a.Price * 100 AS INT) price	--项目单价
            ,a.Quantiy quantity	--数量
            ,CAST( a.TotalAmount * 100 AS INT) amount	--总金额
        from Reg_Tv_ChargeDetail a
            left join MZYS_TB_MZCFMX c on c.cfmxid=a.RecipeDetlID
            left join MZYS_TB_YPYF yf on yf.yflsh= c.yf
            left join TB_Dic_HisDictionary gytj on gytj.HisDictionaryCode= c.gytj and gytj.DictionaryTypeID=31 and gytj.HospitalId= c.hospitalCode
        where a.RecipeID = #{recipeNo} and a.Quantiy > 0
    </select>
</mapper>