package com.sunhealth.ihhis.dao.his;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.sunhealth.ihhis.model.dto.patient.HisPatientInfo;
import com.sunhealth.ihhis.model.dto.patient.HisPatientInfoReq;
import com.sunhealth.ihhis.model.dto.patient.HisPatientSimpleData;
import com.sunhealth.ihhis.model.dto.report.ReportListReq;
import com.sunhealth.ihhis.model.entity.patient.RTPatientList;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.springframework.stereotype.Repository;

import javax.annotation.Resource;
import java.util.List;

@Repository
public interface PatientListMapper extends BaseMapper<RTPatientList> {

    /**
     * 这个是测试方法
     * @param limit
     * @return
     */
    @Deprecated
    @Select("select * from Reg_Tb_PatientList ORDER BY PatID ASC OFFSET 0 ROWS FETCH NEXT #{limit} ROWS ONLY")
    List<RTPatientList> selectAllByLimit(int limit);

    /**
     * 新增就诊人信息
     * @param patientList
     * @return
     */
    int savePatientList(@Param("pl") RTPatientList patientList);
    @Select("select * from Reg_Tb_PatientList where IsUse = 1 and PatId = #{id}")
    RTPatientList selectByPatId(@Param("id") String id);
    /**
     * 获取就诊人信息
     * @param request
     * @return
     */
    List<HisPatientInfo> getPatientInfo(@Param("request") HisPatientInfoReq request);
    List<RTPatientList>  selectPatientList(@Param("req") ReportListReq req);
    /**
     * 获取就诊人信息
     * @param reportNo
     * @return
     */
    HisPatientSimpleData getPatientSimpleDataByTestReportNo(String reportNo);

    HisPatientSimpleData getPatientSimpleDataByExamReportNo(String reportNo);

    /**
     * 根据就诊流水号获取患者信息
     * @param regNo
     * @return
     */
    List<RTPatientList> listPatientByJzlsh(@Param("regNo") String regNo);

    List<RTPatientList> listPatientByCertificateNo(@Param("certificateNo") String certificateNo, @Param("hospitalCode") Integer hospitalCode);
    @Select("SELECT p2.* "
        + "FROM Reg_Tb_PatientList p1 "
        + "JOIN Reg_Tb_PatientList p2 ON p1.CertificateNo = p2.CertificateNo "
        + "WHERE p1.PatId = #{patId} ")
    List<RTPatientList> getAllByPatId(String patId);

}