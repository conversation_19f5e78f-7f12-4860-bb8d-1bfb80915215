package com.sunhealth.ihhis.dao.his;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.sunhealth.ihhis.model.dto.report.LisReport;
import com.sunhealth.ihhis.model.dto.report.LisReportResult;
import com.sunhealth.ihhis.model.dto.report.ReportListReq;
import com.sunhealth.ihhis.model.entity.report.ReportTestDetail;
import com.sunhealth.ihhis.model.entity.report.ReportTestInfo;
import java.util.Date;
import java.util.List;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

public interface ReportTestInfoMapper extends BaseMapper<ReportTestInfo> {

    List<ReportTestInfo> selectNewList(@Param("req") ReportListReq req);

    @Select("select * from Report_Test_Info where ReportNo = #{reportNo}")
    List<ReportTestInfo> selectByReportNo(@Param("reportNo") String reportNo);

    List<ReportTestInfo> selectOldLisReports(@Param("patName") String patName, @Param("sex") String sex,
                                        @Param("age") Integer age, @Param(
            "startDate") Date startDate, @Param("endDate") Date endDate, @Param("reportNo") String reportNo, @Param(
            "reportDate") Date reportDate, @Param("userSource") String userSource);

    /**
     * 从旧的 LIS 视图中查询检验报告结果明细
     *
     * @param reportNo   报告编号
     * @param reportDate 报告日期
     * @return 检验报告结果列表
     */
    List<ReportTestDetail> selectOldLisReportResults(@Param("reportNo") String reportNo,
                                                     @Param("reportDate") Date reportDate);

}
