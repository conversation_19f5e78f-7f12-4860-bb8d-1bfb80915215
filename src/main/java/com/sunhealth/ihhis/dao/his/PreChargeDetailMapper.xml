<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.sunhealth.ihhis.dao.his.PreChargeDetailMapper">

    <resultMap id="preChargeDetail" type="com.sunhealth.ihhis.model.entity.charge.PreChargeDetail">
        <result column="PreChargeNo" property="preChargeNo"/>
        <result column="RegistType" property="registType"/>
        <result column="CardNo" property="cardNo"/>
        <result column="PatId" property="patId"/>
        <result column="NewPatId" property="newPatId"/>
        <result column="RegNo" property="regNo"/>
        <result column="RecipeNum" property="recipeNum"/>
        <result column="RecipeID" property="recipeID"/>
        <result column="RecipeDetlID" property="recipeDetlID"/>
        <result column="ItemID" property="itemID"/>
        <result column="NewItemID" property="newItemID"/>
        <result column="ItemName" property="itemName"/>
        <result column="Quantiy" property="quantity"/>
        <result column="ItemCategory" property="itemCategory"/>
        <result column="ItemCategoryName" property="itemCategoryName"/>
        <result column="PackageNo" property="packageNo"/>
        <result column="GroupNo" property="groupNo"/>
        <result column="ClinicUnit" property="clinicUnit"/>
        <result column="BasicUnit" property="basicUnit"/>
        <result column="ClinicQty" property="clinicQty"/>
        <result column="Usage" property="usage"/>
        <result column="Dosage" property="dosage"/>
        <result column="DosageUnit" property="dosageUnit"/>
        <result column="DrugGauge" property="drugGauge"/>
        <result column="CheckCode" property="checkCode"/>
        <result column="ExecuteDept" property="executeDept"/>
        <result column="Times" property="times"/>
        <result column="Price" property="price"/>
        <result column="ExpensePrice" property="expensePrice"/>
        <result column="NonExpensePrice" property="nonExpensePrice"/>
        <result column="Status" property="status"/>
        <result column="TotalAmount" property="totalAmount"/>
        <result column="DoctorId" property="doctorId"/>
        <result column="DeptId" property="deptId"/>
        <result column="DoctorLevel" property="doctorLevel"/>
        <result column="FeeType" property="feeType"/>
        <result column="IsDrug" property="isDrug"/>
        <result column="SpecialFlag" property="specialFlag"/>
        <result column="DataFrom" property="dataFrom"/>
        <result column="FromFlag" property="fromFlag"/>
        <result column="DiscountAmount" property="discountAmount"/>
        <result column="IsDelete" property="isDelete"/>
        <result column="CreatedBy" property="createdBy"/>
        <result column="CreatedDate" property="createdDate"/>
        <result column="UpdateBy" property="updateBy"/>
        <result column="UpdateDate" property="updateDate"/>
        <result column="HospitalCode" property="hospitalCode"/>
        <result column="RecipeOn" property="recipeOn"/>
        <result column="Cfzt" property="cfzt"/>
        <result column="Cfztmc" property="cfztmc"/>
        <result column="IsDebitPay" property="isDebitPay"/>
        <result column="DebitAmt" property="debitAmt"/>
        <result column="DebitPayInvoiceID" property="debitPayInvoiceID"/>
        <result column="scbj" property="scbj"/>
    </resultMap>



    <select id="selectPreChargeDetailList" resultMap="preChargeDetail">
        SELECT
            cf.xh RecipeNum,
            mx.cfid RecipeID,
            mx.cfmxid RecipeDetlID,
            b.ItemCode ItemID,
            b.NewItemCode NewItemID,
            b.ItemName ItemName,
            b.ItemCategory ItemCategory,
            ic.ItemName ItemCategoryName,
            mx.zh GroupNo,
            case when isnull(ltrim(trim(mx.dw)),'') = '' then b.ClinicUnit else mx.dw END ClinicUnit,
            mx.jcdw BasicUnit,
            IsNull(mx.PackFactor, 1) ClinicQty,
            mx.jl Dosage,
            mx.jldw DosageUnit,
            mx.gg DrugGauge,
            b.CheckCode CheckCode,
            mx.zxks ExecuteDept,
            IsNull(mx.cffs, 1) Times,
            mx.dj  Price,	--项目单价
            b.ClinicExpensePrice * IsNull(mx.PackFactor, 1) ExpensePrice,
            b.ClinicNonExpensePrice * IsNull(mx.PackFactor, 1) NonExpensePrice,
            mx.xmsl Quantiy,	--数量
            mx.cfje  * IsNull(mx.cffs, 1) TotalAmount,
            mx.sckfys DoctorId,
            register.DeptID DeptId,
            b.ABClass FeeType,
            CASE WHEN b.ItemCategory IN (12, 13, 14 ) THEN 1 ELSE 0 END IsDrug,
            mx.hospitalCode HospitalCode,
            mx.zhkfsj RecipeOn,
            cf.cfzt Cfzt,
            register.registType RegistType,
            mx.scbj scbj
        FROM
            MZYS_TB_MZCFMX mx
                INNER JOIN System_Tb_PubItems b ON mx.xmbm= b.ItemCode
                INNER JOIN MZYS_TB_MZCF cf on cf.cfid = mx.cfid
                LEFT JOIN Drug_Tb_DrugInfomation drug ON drug.DrugId= b.ItemCode
                LEFT JOIN MZYS_TB_YPYF yf ON yf.yflsh= mx.yf
                LEFT JOIN System_Tb_ItemCategory ic on b.ItemCategory = ic.ItemCode
                LEFT join Reg_Tv_RegisterList register on register.RegNo = mx.ghlsh
        WHERE
            mx.cfid = #{recipeNo}
          AND mx.hospitalCode = #{hospitalCode}
          AND mx.zt in (0,-1)-- 预算项目必须是未收费状态 2024年08月02日10:17:10  张惟说 mzcfmx.zt 0未收费 1已收费 3退费 -1 正在收费
    </select>
</mapper>