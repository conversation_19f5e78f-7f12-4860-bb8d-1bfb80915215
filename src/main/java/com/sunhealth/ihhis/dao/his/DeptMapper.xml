<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.sunhealth.ihhis.dao.his.DeptMapper">
    <!-- 定义一个 resultMap -->
    <resultMap id="deptInfo" type="com.sunhealth.ihhis.model.dto.DeptInfo">
        <!-- 指定查询结果中的列与模型属性的映射关系 -->
        <result property="dept_id" column="DeptId"/>
        <result property="dept_name" column="DeptName"/>
        <result property="dept_type" column="dept_type"/>
        <result property="first_dept_id" column="first_dept_id"/>
        <result property="first_dept_name" column="first_dept_name"/>
        <result property="deptClassCode" column="DeptClassCode"/>
<!--        <result property="deptClassName" column="deptClassCode"/>-->
        <result property="channel_type" column="channel_type"/>
        <!-- 其他属性映射 -->
    </resultMap>

    <!-- 自定义 SQL 查询语句 -->
    <select id="selectPageDept" resultMap="deptInfo">
        SELECT
        d1.DeptId,
        d1.DeptName,
        CASE d1.DeptClassCode when 1 then '1' when 2 then '2' when 4 then '3' when 5 then '5' when 11 then '5' when 7 then '5' else '4' end dept_type,
        COALESCE ( d2.DeptId, d1.DeptId ) AS first_dept_id,
        COALESCE ( d2.DeptName, d1.DeptName ) AS first_dept_name,
        d1.DeptClassCode,
        #{channelType} AS channel_type
        FROM
        System_Tb_Department d1
        LEFT JOIN
        System_Tb_Department d2
        ON d1.ParentId = d2.DeptId
        WHERE d1.Status = #{status}
        AND d1.HospitalId = #{hospitalId}
        ORDER BY d1.DeptCode ASC
    </select>

</mapper>