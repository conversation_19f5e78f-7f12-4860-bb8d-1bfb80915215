package com.sunhealth.ihhis.dao.his;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.sunhealth.ihhis.model.entity.charge.PreChargeAmt;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

public interface PreChargeAmtMapper extends BaseMapper<PreChargeAmt> {
    @Select("select count(RecipeDetlID) from Reg_Tb_PreCharge_Amt where ChargeNo = #{chargeNo} and RecipeDetlID = #{recipeDetlID}")
    int countPreChargeAmt(@Param("chargeNo") Long chargeNo, @Param("recipeDetlID") Long recipeDetlID);

    @Select("select * from Reg_Tb_PreCharge_Amt where ChargeNo = #{chargeNo} and RecipeDetlID = #{recipeDetlID}")
    PreChargeAmt selectOneByChargeNoAndRecipeDetlID(@Param("chargeNo") Long chargeNo, @Param("recipeDetlID") Long recipeDetlID);
}
