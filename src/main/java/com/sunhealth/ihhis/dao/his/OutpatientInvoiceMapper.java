package com.sunhealth.ihhis.dao.his;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.sunhealth.ihhis.model.dto.InvoiceDTO;
import com.sunhealth.ihhis.model.dto.electronicInvoice.ElectronicInvoice;
import com.sunhealth.ihhis.model.dto.electronicInvoice.ElectronicInvoiceFileReq;
import com.sunhealth.ihhis.model.dto.electronicInvoice.ElectronicInvoiceFile;
import com.sunhealth.ihhis.model.dto.electronicInvoice.ElectronicInvoiceReq;
import com.sunhealth.ihhis.model.entity.invoice.OutpatientInvoice;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface OutpatientInvoiceMapper extends BaseMapper<OutpatientInvoice> {

    List<InvoiceDTO> selectInvoice(String regNo, String patId, String hospitalCode);

    ElectronicInvoiceFile getElectronicInvoiceFile(@Param("req") ElectronicInvoiceFileReq req);

    Page<ElectronicInvoice> getElectronicInvoiceList(Page<?> page, @Param("req") ElectronicInvoiceReq req);
}
