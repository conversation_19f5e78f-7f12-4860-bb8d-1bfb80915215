<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.sunhealth.ihhis.dao.his.AppointmentLockNumMapper">

    <insert id="insertAppointmentLockNum" parameterType="com.sunhealth.ihhis.model.entity.register.AppointmentLockNum"
    useGeneratedKeys="true" keyProperty="sqh">
        INSERT INTO TB_Appointment_LockNum (
            SubjectID,
            DutyDate,
            TimeSpanID,
            SourceId,
            SeqNum,
            CertificateNo,
            LockNumOrderID,
            LockTime,
            NumId,
            HospitalCode,
            flag,
            VisitFlag,
            IsCZF
        ) VALUES (
                     #{subjectID},
                     #{dutyDate},
                     #{timeSpanID},
                     #{sourceId},
                     #{seqNum},
                     #{certificateNo},
                     newid(),
                     #{lockTime},
                     #{numId},
                     #{hospitalCode},
                     #{flag},
                     #{visitFlag},
                     #{isCZF}
                 )
    </insert>
</mapper>