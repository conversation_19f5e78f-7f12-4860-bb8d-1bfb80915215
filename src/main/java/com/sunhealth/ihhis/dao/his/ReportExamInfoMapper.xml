<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.sunhealth.ihhis.dao.his.ReportExamInfoMapper">
    <select id="list" resultType="com.sunhealth.ihhis.model.entity.report.ReportExamInfo">
        SELECT
        r.id as id,
        p.PatID as patientId,
        r.PatientType as patientType,
        r.VisitNo as visitNo,
        r.Pat<PERSON>ame as patName,
        r.<PERSON><PERSON> as hospNo,
        r.<PERSON>o as cardNo,
        r.BedNo as bedNo,
        r.Hospital<PERSON> as hospitalName,
        r.HospitalId as hospitalId,
        r.reportNo as reportNo,
        r.reportType as reportType,
        r.reportTypeName as reportTypeName,
        r.applyId as applyId,
        r.applyDept as applyDept,
        r.applyDeptName as applyDeptName,
        r.applyWard as applyWard,
        r.applyWard<PERSON>ame as applyWardName,
        r.applyDoctor as applyDoctor,
        r.applyDoctorName as applyDoctorName,
        r.applyTime as applyTime,
        r.examSite as examSite,
        r.examMethod as examMethod,
        r.raditeNo as raditeNo,
        r.examDept as examDept,
        r.examDeptName as examDeptName,
        r.examTime as examTime,
        r.examDoctor as examDoctor,
        r.examDoctorName as examDoctorName,
        r.reportTime as reportTime,
        r.reportDoctor as reportDoctor,
        r.reportDoctorName as reportDoctorName,
        r.auditorTime as auditorTime,
        r.auditorDoctor as auditorDoctor,
        r.auditorDoctorName as auditorDoctorName,
        r.reportUrl as reportUrl,
        r.examVerdict as examVerdict,
        r.examDiag as examDiag,
        r.examDescript as examDescript,
        r.criticalFlag as criticalFlag,
        r.criricalContent as criricalContent,
        r.reportStatus as reportStatus,
        r.receiveTime as receiveTime,
        (SELECT
        STRING_AGG(itemName, ',')
        FROM
        report_exam_detail where ReportNo = r.reportNo)as itemName
        FROM
        Apply_Tb_List a,
        Report_Exam_Info r,
        Reg_Tb_PatientList p
        WHERE
        a.id = r.ApplyId
        AND p.PatID = a.PatId
        and r.applyTime between #{req.beginTime} and #{req.endTime}
        and r.HospitalId = #{req.hospitalCode}
        <if test="req.patid != null and req.patid != ''">
            and p.PatID in (SELECT p2.patid FROM Reg_Tb_PatientList p1 JOIN Reg_Tb_PatientList p2 ON p1.CertificateNo = p2.CertificateNo WHERE p1.PatId = #{req.patid})
        </if>
        <if test="req.user_source != null and req.user_source != ''">
            and r.PatientType = #{req.user_source}
        </if>
        <if test="req.patname != null and req.patname != ''">
            and r.PatName = #{req.patname}
        </if>

    </select>


    <!-- 新增：查询旧RIS报告列表 -->
    <select id="selectOldRisReports" resultType="com.sunhealth.ihhis.model.entity.report.ReportExamInfo">
        SELECT
        CAST(a.RisId AS VARCHAR(50)) AS reportNo,       -- 报告编号, 使用RisId作为唯一标识
        ISNULL(a.PatId, '') AS patientId,
        a.ComeFlag AS patientType,
        ISNULL(a.RegNo, '') AS visitNo,
        ISNULL(a.PatName, '') AS patName,
        ISNULL(a.HospNo, '') AS hospNo,
        ISNULL(a.PatCard, '') AS cardNo,
        ISNULL(a.HospBedNo, '') AS bedNo,
        NULL AS hospitalName,                           -- 源表无，设为NULL
        NULL AS hospitalId,                             -- 源表无，设为NULL

        -- 报告与申请信息
        CAST(a.RisId AS VARCHAR(50)) AS applyId,        -- 申请单Id，使用RisId
        ISNULL(a.JcTypeCode, '') AS reportType,         -- 报告类型
        ISNULL(a.JcTypeName, '') AS reportTypeName,
        ISNULL(a.SqDeptId, '') AS applyDept,
        ISNULL(a.SqDeptName, '') AS applyDeptName,
        NULL AS applyWard,                              -- 源表无，设为NULL
        NULL AS applyWardName,                          -- 源表无，设为NULL
        ISNULL(sqDoctor.WorkerId, '') AS applyDoctor,
        ISNULL(sqDoctor.Name, '') AS applyDoctorName,
        a.SqDate AS applyTime,

        -- 检查信息
        ISNULL(a.JcBwName, '') AS examSite,
        ISNULL(a.JcFf, '') AS examMethod,
        b.YyNo AS raditeNo,                             -- 影像号
        NULL AS examDept,                               -- 源表无，设为NULL
        ISNULL(a.JcTypeName, '') AS examDeptName,       -- 执行科室暂用检查类型名
        b.JcDate AS examTime,                           -- 检查时间
        b.BgDoctId AS examDoctor,                       -- 报告医生ID
        a.JcDoctName AS examDoctorName,                 -- 检查医生姓名
        b.RepDate AS reportTime,                        -- 报告时间
        bgdoctor.Name AS reportDoctor,
        bgdoctor.Name AS reportDoctorName,
        b.Repshdate AS auditorTime,                     -- 审核时间
        b.bgDoctId AS auditorDoctor,
        bgdoctor.Name AS auditorDoctorName,
        c.DigitalSignatures AS reportUrl,               -- 报告Url, 借用签名图片字段
        a.JcResult  AS examDiag,
        b.Sfjl  AS examDescript,

        a.status AS reportStatus,                       -- 报告状态

        -- 报告项目 (在实体类中为 exist=false, 用于列表展示)
        ISNULL(a.JcItemName, '') AS itemName,
        a.PatAge as age,
        a.PatSex as sex

        FROM
        [************].[zxhis].[dbo].[tbt_risrequest] a
        INNER JOIN
        [************].[zxhis].[dbo].[tbt_risrepinfo] b ON a.RisId = b.RisId
        LEFT JOIN
        [************].[zxhis].[dbo].[tbt_MdDigitalSignatures] c ON c.WorkerId = b.BgDoctId
        left join System_Tb_Worker bgdoctor on b.bgDoctId = bgdoctor.WorkerId
        left join System_Tb_Worker sqdoctor on b.sqDoctId = sqdoctor.WorkerId

        <where>
            <if test="patId != null and patId != ''">
                AND a.PatId = #{patId}
            </if>
            <if test="comeFlag != null">
                AND a.ComeFlag = #{comeFlag}
            </if>
            <if test="startDate != null">
                AND b.JcDate <![CDATA[ >= ]]> #{startDate}
            </if>
            <if test="endDate != null">
                AND b.JcDate <![CDATA[ < ]]> #{endDate}
            </if>
            <if test="reportNo != null and reportNo != ''">
                AND a.RisId = CAST(#{reportNo} AS INT)
            </if>
        </where>
        ORDER BY
        b.JcDate DESC
    </select>

    <!-- 新增：查询旧RIS报告详情 -->
    <!-- 旧系统没有独立的明细表, 我们从主报告信息中模拟生成一条详情记录, 以适配前端 -->
    <select id="selectOldRisReportDetails" resultType="com.sunhealth.ihhis.model.entity.report.ReportExamDetail">
        SELECT
        CAST(a.RisId AS VARCHAR(50)) AS reportNo
        , ISNULL(a.JcItemCode, '') AS itemCode
        , ISNULL(a.JcItemName, '') AS itemName
        -- 旧报告没有独立的子项结果, 暂时置空
        , NULL AS itemResult
        FROM
        [************].[zxhis].[dbo].[tbt_risrequest] a
        WHERE
        a.RisId = CAST(#{reportNo} AS INT)
    </select>



</mapper>