<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.sunhealth.ihhis.dao.his.InPatientMapper">

    <select id="listInpatientRecord" resultType="com.sunhealth.ihhis.model.dto.inpatient.InpatientRecord">
        select a.<PERSON><PERSON> re<PERSON>,
               a.InDiagnosisName diagnose_name,
               a.patid,
               a.<PERSON><PERSON><PERSON>o hiscardno,
               a.patname,
               a.sex,
               a.Birthday birth,
               a.CertificateNo certificate_no,
               a.<PERSON>,
               a.ContactsName contacts_name,
               case lxrgx.DictionaryCode when 1 then '03' when 5 then '01' else '00' end contacts_relationship,
               a.ContactsRelationShipPhone contacts_telephone,
               jzd.Remark address,
               a.MobilePhone telephone,
               case a.Status when 1 then '0' when 2 then '1' when 3 then '2' when 4 then '1' when 5 then '7' when 9 then '3' else '8' end status, -- <PERSON>(1,"待入区"),TWO(2,"在区"),THRE<PERSON>(3,"待出院"),FOUR(4,"中期离区"),FIVE(5,"转区"),NINE(9,"出院") ZERO(0,"无效"),
               a.InTime in_time,
               a.OutWardTime out_time,
               a.LastChargeTime end_date,
               dept.DeptName dept_name,
               ward.DeptName ward_name,
               bed.BedNo bed_no,
               doctor.Name doctor_name,
               (select COALESCE(SUM(Amount), 0) from IO_Tb_PatientPreCharge prec
                where prec.RegNo=a.RegNo
                and prec.HospitalId=a.HospitalId
                and prec.ChargeType=2
                and BillStatus=1 and prec.CreateOn <![CDATA[ >= ]]> #{req.beginDate} and prec.CreateOn <![CDATA[ < ]]> #{req.endDate}) advance_total_amount,
               (select COALESCE(SUM(Amount), 0) from IO_Tb_PatientPreCharge prec
                where prec.RegNo=a.RegNo
                and prec.HospitalId=a.HospitalId
                and prec.ChargeType=2
                and BillStatus=1 and prec.CreateOn <![CDATA[ >= ]]> #{req.beginDate} and prec.CreateOn <![CDATA[ < ]]> #{req.endDate})
                   - (select COALESCE(SUM(ReimburseAmount+UnReimburseAmount), 0) from IO_PatientFee fee
                                     where fee.RegNo=a.RegNo
                                     and fee.HospitalId=a.HospitalId and fee.FeeTime <![CDATA[ >= ]]> #{req.beginDate} and fee.FeeTime <![CDATA[ < ]]> #{req.endDate}) advance_account_amount,
               (select COALESCE(SUM(ReimburseAmount+UnReimburseAmount), 0) from IO_PatientFee fee
                where fee.RegNo=a.RegNo
                and fee.HospitalId=a.HospitalId and fee.FeeTime <![CDATA[ >= ]]> #{req.beginDate} and fee.FeeTime <![CDATA[ < ]]> #{req.endDate}) yfstotal_amount,
               0 discount_amount,
               (select COALESCE(SUM(UnReimburseAmount), 0) from IO_PatientFee fee
                where fee.RegNo=a.RegNo
                and fee.HospitalId=a.HospitalId and fee.FeeTime <![CDATA[ >= ]]> #{req.beginDate} and fee.FeeTime <![CDATA[ < ]]> #{req.endDate}) self_amount,
               (select COALESCE(SUM(ReimburseAmount), 0) from IO_PatientFee fee
                where fee.RegNo=a.RegNo
                and fee.HospitalId=a.HospitalId and fee.FeeTime <![CDATA[ >= ]]> #{req.beginDate} and fee.FeeTime <![CDATA[ < ]]> #{req.endDate}) pub_pay,
               a.HospitalId organ_code,
               hosp.HospitalName organ_name,
               a.InsuranceCode chargetype_code,
               insur.ChargeTypeName chargetype_name
        from IO_Tb_InPatient a
        left join TB_Dic_HisDictionary lxrgx on a.ContactsRelationShip=lxrgx.HisDictionaryCode and lxrgx.HospitalId=a.HospitalId and lxrgx.DictionaryTypeID=5
        left join IO_Tb_PatientAddress jzd on jzd.RegNo=a.RegNo and jzd.addressTypeCode='9' and a.HospitalId=jzd.HospitalId
        left join System_Tb_Department dept on dept.DeptId=a.DeptId and dept.HospitalId=a.HospitalId
        left join System_Tb_Department ward on ward.DeptId=a.WardId and ward.HospitalId=a.HospitalId
        left join System_Tb_Bed bed on bed.BedId=a.BedId and bed.HospitalId=a.HospitalId
        left join System_Tb_Worker doctor on doctor.WorkerId=a.ResidentDoctor and doctor.HospitalId=a.HospitalId
        left join Tb_Hospital hosp on hosp.HOSPITALCODE=a.HospitalId
        left join System_TB_ChargeType insur on insur.ChargeTypeCode=a.InsuranceCode
        where a.PatName = #{req.patname} and a.HospitalId = #{req.hospitalCode}
        <if test="req.patid != null and req.patid != ''">
            and a.PatId = #{req.patid}
        </if>
        <if test="req.hospno != null and req.hospno != ''">
            and a.HospNo = #{req.hospno}
        </if>
        <if test='req.status == "1"'>
            and hzzt.DictionaryCode in (11, 2, 3, 4, 5)
        </if>
        order by a.InTime desc
    </select>

    <select id="listAdvanceChargeDetail" resultType="com.sunhealth.ihhis.model.dto.inpatient.AdvanceChargeDetail">
        select
            a.PreChargeId advanceChargeId,
            a.PreChargeId settleId,
            a.PayTradeNo serialNo,
            a.PayTime payTime,
            a.Amount advanceAmount,
            case jy.PayType when 11 then 2 when 12 then 1 when 17 then 1 when 13 then 3 end payType,
            case a.BillStatus when 2 then '2' when 120 then '1' else '0' end record_status,
            a.Remark memo
        from IO_Tb_PatientPreCharge a
        inner join IO_Tb_ZfbJy jy on (a.PreChargeId = jy.PreChargeId or a.PairId = jy.PreChargeId) and jy.HospTradeNo like 'IH%'
        inner join IO_Tb_InPatient b on a.RegNo=b.RegNo and a.HospitalId=b.HospitalId
        where a.ChargeType in (2,6) and a.BillStatus != 0 and b.PatName = #{req.patname} and a.HospNo = #{req.regno} and a.HospitalId = #{req.hospitalCode}
    </select>
</mapper>