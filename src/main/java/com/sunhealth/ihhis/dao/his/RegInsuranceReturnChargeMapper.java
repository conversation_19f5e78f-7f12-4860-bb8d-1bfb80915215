package com.sunhealth.ihhis.dao.his;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.sunhealth.ihhis.model.entity.dqyibao.RegInsuranceReturnCharge;
import org.apache.ibatis.annotations.Select;

public interface RegInsuranceReturnChargeMapper extends BaseMapper<RegInsuranceReturnCharge> {

    @Select("select * from Reg_Insurance_ReturnCharge where SerialNo=#{serialNo} and Flag=#{flag}")
    RegInsuranceReturnCharge getBySerialNoAndFlag(Long serialNo, int flag);

}
