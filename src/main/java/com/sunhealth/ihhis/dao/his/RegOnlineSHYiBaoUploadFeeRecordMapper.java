package com.sunhealth.ihhis.dao.his;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.sunhealth.ihhis.model.entity.yibao.RegOnlineSHYiBaoUploadFeeRecord;
import org.apache.ibatis.annotations.Select;

public interface RegOnlineSHYiBaoUploadFeeRecordMapper extends BaseMapper<RegOnlineSHYiBaoUploadFeeRecord> {

    @Select("select * from Reg_Online_SHYiBao_uploadFeeRecord where RegNo = #{regNo} and Flag = 0")
    RegOnlineSHYiBaoUploadFeeRecord selectByRegNo(String regNo);

    @Select("select * from Reg_Online_SHYiBao_uploadFeeRecord where ChargeNo = #{chargeNo} and Flag = 1")
    RegOnlineSHYiBaoUploadFeeRecord selectByChargeNo(String chargeNo);

    @Select("select * from Reg_Online_SHYiBao_uploadFeeRecord where OrderNo = #{orderNo}")
    RegOnlineSHYiBaoUploadFeeRecord selectByOrderNo(String orderNo);

}
