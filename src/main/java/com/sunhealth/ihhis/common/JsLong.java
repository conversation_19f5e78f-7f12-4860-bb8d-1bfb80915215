package com.sunhealth.ihhis.common;

public class Js<PERSON>ong extends Number implements Comparable<Long> {

    /**
     * The value of the {@code Long}.
     *
     * @serial
     */
    private final long value;

    public JsLong(long value) {
        this.value = value;
    }

    @Override
    public int compareTo(Long o) {
        return Long.compare(this.value, o);
    }

    @Override
    public int intValue() {
        return (int) value;
    }

    @Override
    public long longValue() {
        return value;
    }

    @Override
    public float floatValue() {
        return (float) value;
    }

    @Override
    public double doubleValue() {
        return (double) value;
    }

    @Override
    public String toString() {
        return Long.toString(value);
    }
}
