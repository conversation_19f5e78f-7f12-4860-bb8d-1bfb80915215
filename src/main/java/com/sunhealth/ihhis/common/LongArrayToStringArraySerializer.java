package com.sunhealth.ihhis.common;

import com.fasterxml.jackson.core.JsonGenerator;
import com.fasterxml.jackson.databind.JsonSerializer;
import com.fasterxml.jackson.databind.SerializerProvider;
import com.sunhealth.ihhis.common.JsLong;

import java.io.IOException;
import java.util.Arrays;

/**
 * long[]转String[]序列化器
 */
public class LongArrayToStringArraySerializer extends JsonSerializer<long[]> {

    @Override
    public void serialize(long[] value, JsonGenerator gen, SerializerProvider serializers) throws IOException {
        if (value == null) {
            return;
        }

        Object[] stringArray = Arrays.stream(value)
                .mapToObj(u -> {
                    if (u <= Integer.MAX_VALUE) {
                        return (int) u;
                    }
                    if (u <= 827240900198400L) {
                        return new JsLong(u);
                    }
                    return String.valueOf(u);

                })
                .toArray(Object[]::new);
        gen.writeObject(stringArray);
    }

}


