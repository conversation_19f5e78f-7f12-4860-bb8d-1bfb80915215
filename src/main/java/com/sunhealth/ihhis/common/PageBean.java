package com.sunhealth.ihhis.common;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.AllArgsConstructor;
import lombok.Data;

import java.util.List;

@Data
@AllArgsConstructor
public class PageBean<T> {
    /**
     * 页码
     */
    private long page;
    /**
     * 页面大小
     */
    private long size;
    /**
     * 总数
     */
    private long total;
    /**
     * 总页数
     */
    private long totalPage;
    /**
     * 数据集
     */
    private List<T> list;

    public PageBean() {
    }

    public PageBean(Page<T> pageInfo) {
        this.page = pageInfo.getPages();
        this.size = pageInfo.getSize();
        this.total = (int) pageInfo.getTotal();
        this.totalPage = pageInfo.getPages();
        this.list = pageInfo.getRecords();
    }
}
