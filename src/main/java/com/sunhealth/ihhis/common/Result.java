package com.sunhealth.ihhis.common;

import lombok.Data;

import java.io.Serializable;

@Data
public class Result<T> implements Serializable {

    private boolean success;

    private String code;

    private String message;

    private T data;


    public Result() {
    }

    // 通用返回成功
    public static <T> Result<T> ok() {
        Result<T> r = new Result<>();
        r.setSuccess(true);
        r.setCode("0");
        r.setMessage("成功");
        return r;
    }

    // 通用返回失败，未知错误
    public static <T> Result<T> error() {
        Result<T> r = new Result<T>();
        r.setSuccess(ResultCodeEnum.UNKNOWN_ERROR.getSuccess());
        r.setCode(ResultCodeEnum.UNKNOWN_ERROR.getCode());
        r.setMessage(ResultCodeEnum.UNKNOWN_ERROR.getMessage());
        return r;
    }

    public static <T> Result<T> error(ResultCodeEnum resultCodeEnum) {
        Result<T> r = new Result<>();
        r.setSuccess(resultCodeEnum.getSuccess());
        r.setCode(resultCodeEnum.getCode());
        r.setMessage(resultCodeEnum.getMessage());
        return r;
    }

    public void setResult(ResultCodeEnum result) {
        this.success = result.getSuccess();
        this.code = result.getCode();
        this.message = result.getMessage();
    }

}
