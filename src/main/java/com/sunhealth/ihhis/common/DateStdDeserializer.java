package com.sunhealth.ihhis.common;

import com.fasterxml.jackson.core.JsonParser;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.DeserializationContext;
import com.fasterxml.jackson.databind.deser.std.StdDeserializer;
import com.sunhealth.ihhis.utils.TimeUtils;

import java.io.IOException;
import java.util.Date;

public class DateStdDeserializer extends StdDeserializer<Date>  {

    public DateStdDeserializer() {
        this(null);
    }

    public DateStdDeserializer(Class<?> vc) {
        super(vc);
    }

    @Override
    public Date deserialize(JsonParser p, DeserializationContext ctxt) throws IOException, JsonProcessingException {
        try {
            return TimeUtils.convert(p.getValueAsString());
        } catch (Exception e) {
            return null;
        }
    }

}
