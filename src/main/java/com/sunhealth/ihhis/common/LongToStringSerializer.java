package com.sunhealth.ihhis.common;

import com.fasterxml.jackson.core.JsonGenerator;
import com.fasterxml.jackson.databind.JsonSerializer;
import com.fasterxml.jackson.databind.SerializerProvider;

import java.io.IOException;

/**
 * long[]转String[]序列化器
 */
public class LongToStringSerializer extends JsonSerializer<Long> {
    /**
     * Singleton instance to use.
     */
    public final static LongToStringSerializer instance = new LongToStringSerializer();

    @Override
    public void serialize(Long value, JsonGenerator gen, SerializerProvider serializers) throws IOException {
        if (value == null) {
            return;
        }
        if (value <= Integer.MAX_VALUE) {
            gen.writeObject((int) (long) value);
            return;
        }
        if (value <= 827240900198400L) {
            gen.writeObject(new JsLong(value));
            return;
        }
        // 827240900198400
        gen.writeObject(value.toString());
    }

}


