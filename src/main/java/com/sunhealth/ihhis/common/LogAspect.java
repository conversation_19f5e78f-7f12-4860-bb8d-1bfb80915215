package com.sunhealth.ihhis.common;

import lombok.extern.slf4j.Slf4j;
import org.aspectj.lang.annotation.Aspect;
import org.springframework.stereotype.Component;

@Slf4j
@Aspect
@Component
public class LogAspect {

//    @Autowired
//    private LogService logService;

//    @Pointcut("@annotation(com.sunhealth.ihhis.annotations.HisLog)")
//    public void pointcut() {
//    }

//    @Around("pointcut()")
//    public Object around(ProceedingJoinPoint point) throws Throwable {
//
//        try {
//            Object result;
//            // 执行方法
//            result = point.proceed();
////            logService.saveLog(point, true);
//            return result;
//        } catch (Throwable throwable) {
////            logService.saveLog(point, false);
//            throw throwable;
//        }
//    }
}
