package com.sunhealth.ihhis.common;

import lombok.Getter;

@Getter
public enum ResultCodeEnum {
    SUCCESS(true, "0", "成功"),
    UNKNOWN_ERROR(false, "500", "未知错误"),
    PARAM_ERROR(false, "400", "参数错误"),
    ;

    // 响应是否成功
    private final Boolean success;
    // 响应状态码
    private final String code;
    // 响应信息
    private final String message;

    ResultCodeEnum(boolean success, String code, String message) {
        this.success = success;
        this.code = code;
        this.message = message;
    }
}
