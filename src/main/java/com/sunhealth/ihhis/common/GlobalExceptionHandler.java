package com.sunhealth.ihhis.common;


import com.sunhealth.ihhis.error.ErrorResponseException;
import com.sunhealth.ihhis.error.SystemParamException;
import com.sunhealth.ihhis.error.TokenException;
import com.sunhealth.ihhis.model.vm.BaseResponse;
import com.sunhealth.ihhis.model.vm.BaseResponseData;
import com.sunhealth.ihhis.model.vm.ResponseStatus;
import org.apache.commons.lang.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.ConversionNotSupportedException;
import org.springframework.beans.TypeMismatchException;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.http.converter.HttpMessageNotReadableException;
import org.springframework.http.converter.HttpMessageNotWritableException;
import org.springframework.validation.BindException;
import org.springframework.web.HttpMediaTypeNotAcceptableException;
import org.springframework.web.HttpRequestMethodNotSupportedException;
import org.springframework.web.bind.MethodArgumentNotValidException;
import org.springframework.web.bind.MissingServletRequestParameterException;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.RestControllerAdvice;

import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.io.PrintStream;


/**
 * 全局异常处理
 */
//@RestController  //Controller+ResponseBody
//@RestControllerAdvice ： 是对@RestController的增强，拥有@RestController的能力
//且注解了该注解的类下面的方法可以在其他controller执行前，后，做一些事情
@RestControllerAdvice
public class GlobalExceptionHandler {

    private static final String logExceptionFormat = "Capture Exception By GlobalExceptionHandler: Code: %s Detail: %s";
    private static Logger log = LoggerFactory.getLogger(GlobalExceptionHandler.class);

    private void logging(Exception e) {
        log.error(e.getMessage(), e);
    }

    //运行时异常
    @ExceptionHandler(RuntimeException.class)
    public ResponseEntity<JsonResponse<Void>> runtimeExceptionHandler(RuntimeException ex) {
        JsonResponse<Void> response = new JsonResponse<>("400", ex.getMessage());
        logging(ex);
        return ResponseEntity.status(HttpStatus.OK).body(response);
    }

    //空指针异常
    @ExceptionHandler(NullPointerException.class)
    public ResponseEntity<JsonResponse<Void>> nullPointerExceptionHandler(NullPointerException ex) {
        JsonResponse<Void> response = new JsonResponse<>("500", "空指针异常");
        logging(ex);
        return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(response);
    }

    //类型转换异常
    @ExceptionHandler(ClassCastException.class)
    public ResponseEntity<JsonResponse<Void>> classCastExceptionHandler(ClassCastException ex) {
        JsonResponse<Void> response = new JsonResponse<>("500", "类型转换异常");
        logging(ex);
        return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(response);
    }

    //IO异常
    @ExceptionHandler(IOException.class)
    public ResponseEntity<JsonResponse<Void>> iOExceptionHandler(IOException ex) {
        JsonResponse<Void> response = new JsonResponse<>("500", "IO异常");
        logging(ex);
        return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(response);
    }

    //未知方法异常
    @ExceptionHandler(NoSuchMethodException.class)
    public ResponseEntity<JsonResponse<Void>> noSuchMethodExceptionHandler(NoSuchMethodException ex) {
        JsonResponse<Void> response = new JsonResponse<>("500", "未知方法异常");
        logging(ex);
        return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(response);
    }

    //数组越界异常
    @ExceptionHandler(IndexOutOfBoundsException.class)
    public ResponseEntity<JsonResponse<Void>> indexOutOfBoundsExceptionHandler(IndexOutOfBoundsException ex) {
        JsonResponse<Void> response = new JsonResponse<>("500", "数组越界异常");
        logging(ex);
        return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(response);
    }

    //400错误
    @ExceptionHandler({HttpMessageNotReadableException.class})
    public ResponseEntity<JsonResponse<Void>> requestNotReadable(HttpMessageNotReadableException ex) {
        JsonResponse<Void> response = new JsonResponse<>("400", "400错误");
        logging(ex);
        return ResponseEntity.status(HttpStatus.BAD_REQUEST).body(response);
    }

    //400错误
    @ExceptionHandler({TypeMismatchException.class})
    public ResponseEntity<JsonResponse<Void>> requestTypeMismatch(TypeMismatchException ex) {
        JsonResponse<Void> response = new JsonResponse<>("400", "400错误");
        logging(ex);
        return ResponseEntity.status(HttpStatus.BAD_REQUEST).body(response);
    }

    //400错误
    @ExceptionHandler({MissingServletRequestParameterException.class})
    public ResponseEntity<JsonResponse<Void>> requestMissingServletRequest(MissingServletRequestParameterException ex) {
        JsonResponse<Void> response = new JsonResponse<>("400", "参数错误");
        logging(ex);
        return ResponseEntity.status(HttpStatus.BAD_REQUEST).body(response);
    }

    //405错误
    @ExceptionHandler({HttpRequestMethodNotSupportedException.class})
    public ResponseEntity<JsonResponse<Void>> request405(HttpRequestMethodNotSupportedException ex) {
        JsonResponse<Void> response = new JsonResponse<>("405", "405错误");
        logging(ex);
        return ResponseEntity.status(HttpStatus.METHOD_NOT_ALLOWED).body(response);
    }

    //406错误
    @ExceptionHandler({HttpMediaTypeNotAcceptableException.class})
    public ResponseEntity<JsonResponse<Void>> request406(HttpMediaTypeNotAcceptableException ex) {
        JsonResponse<Void> response = new JsonResponse<>("406", "406错误");
        logging(ex);
        return ResponseEntity.status(HttpStatus.NOT_ACCEPTABLE).body(response);
    }

    //500错误
    @ExceptionHandler({ConversionNotSupportedException.class, HttpMessageNotWritableException.class})
    public ResponseEntity<JsonResponse<Void>> server500(RuntimeException ex) {
        JsonResponse<Void> response = new JsonResponse<>("500", "500错误");
        logging(ex);
        return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(response);
    }

    //栈溢出
    @ExceptionHandler({StackOverflowError.class})
    public ResponseEntity<JsonResponse<Void>> requestStackOverflow(StackOverflowError ex) {
        JsonResponse<Void> response = new JsonResponse<>("500", "栈溢出");
        ByteArrayOutputStream byteArrayOutputStream = new ByteArrayOutputStream();
        ex.printStackTrace(new PrintStream(byteArrayOutputStream));
        log.error(byteArrayOutputStream.toString());
        return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(response);
    }

    //除数不能为0
    @ExceptionHandler({ArithmeticException.class})
    public ResponseEntity<JsonResponse<Void>> arithmeticException(ArithmeticException ex) {
        JsonResponse<Void> response = new JsonResponse<>("500", "除数不能为0");
        logging(ex);
        return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(response);
    }

    //validation校验失败处理
    @ExceptionHandler({MethodArgumentNotValidException.class, BindException.class})
    public ResponseEntity<JsonResponse<Void>> methodArgumentNotValidException(MethodArgumentNotValidException ex) {
        JsonResponse<Void> response = new JsonResponse<>("500", getMessage(ex));
        logging(ex);
        return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(response);
    }

    @ExceptionHandler(SystemParamException.class)
    public ResponseEntity<JsonResponse<Void>> systemParamExceptionHandler(SystemParamException exception) {
        JsonResponse<Void> response = new JsonResponse<>("401", "该系统参数已经存在");
        logging(exception);
        return ResponseEntity.status(HttpStatus.UNAUTHORIZED).body(response);
    }

    @ExceptionHandler(TokenException.class)
    public ResponseEntity<JsonResponse<Void>> tokenExceptionHandler(TokenException exception) {
        JsonResponse<Void> response = new JsonResponse<>("401", "token错误或过期");
        logging(exception);
        return ResponseEntity.status(HttpStatus.UNAUTHORIZED).body(response);
    }

    //其他错误
    @ExceptionHandler({ErrorResponseException.class})
    public ResponseEntity<BaseResponse<ResponseStatus>> exception(ErrorResponseException ex) {
        BaseResponse<ResponseStatus> response = new BaseResponse<>("400", ex.getMessage());
        response.setData(new BaseResponseData<>(new ResponseStatus(ex.getMessage())));
        logging(ex);
        return ResponseEntity.status(HttpStatus.OK).body(response);
    }

    //其他错误
    @ExceptionHandler({Exception.class})
    public ResponseEntity<BaseResponse<Void>> exception(Exception ex) {
        BaseResponse<Void> response = new BaseResponse<>("500", ex.getMessage());
        logging(ex);
        return ResponseEntity.status(HttpStatus.OK).body(response);
    }

    private String getMessage(MethodArgumentNotValidException ex) {
        String message = ex.getMessage();
        if (ex.getFieldError() != null && StringUtils.isNotBlank(ex.getFieldError().getDefaultMessage())) {
            message = ex.getFieldError().getDefaultMessage();
        }
        return message;
    }
}
