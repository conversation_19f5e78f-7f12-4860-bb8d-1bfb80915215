package com.sunhealth.ihhis.model.insurance.request;

import com.sunhealth.ihhis.model.insurance.response.Output6301;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

@Data
public class CallBack6302 implements Serializable {

    private String payOrdId; // 支付订单号 字符型 40 Y 医保结算中心订单 号
    private String callType; // 回调类型 字符型 40 Y 固定传02，目前仅支 付成功回调
    private String medOrgOrd; // 医院订单号 字符型 40 Y 医院订单号
    private String traceTime; // 交易时间 字符型 40 Y 交易成功时间
    private BigDecimal feeSumamt; // 费用总金额 数值型 12,2 Y 保留两位小数
    private BigDecimal ownpayAmt; // 个人现金自付 数值型 12,2 Y
    private BigDecimal psnAcctPay; // 个人账户支付 数值型 12,2 Y
    private BigDecimal fundPay; // 其中基金支付 数值型 12,2 Y
    private String hiChrgTime; // 医保收费时间 字符型 6 Y
    private String hiDocSn; // 医保交易流水号 字符型 40 Y
    private String hiRgstSn; // 医保挂号流水号 字符型 40 Y
    private String orgName; // 机构名称 字符型 40 Y
    private String ecCode; // 电子凭证码值 字符型 200 N
    private String setlType; // 结算类型 字符型 40 Y ALL:医保自费全部， CASH:只结现金 HI: 只结医保
    private String revsToken; // 用于院内结算失败对 医保的冲正授权 字符型 40 Y 1 小时内有效
    private Output6301.ExtData extData; // 医保扩展数据 N 根据各地方医保要 求返回不同数据内 容
    private String orgCodg; // 两定机构编号 字符型 40 Y
    private String deposit; // 住院押金 数据型 16,2 N 根据下单传入计算， 最大值不超过自费 现金需要支付金额
    private BigDecimal acctMulaidPay; //	个人账户共济支付金额
    private BigDecimal selfAcctPay; //	本人个账支出金额

}
