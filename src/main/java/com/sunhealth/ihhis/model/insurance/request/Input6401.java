package com.sunhealth.ihhis.model.insurance.request;

import java.io.Serializable;
import lombok.Data;

/**
 * 解码
 */
@Data
public class Input6401 implements Serializable {

    private String payOrdId; //	支付订单号	处方上传的出参订单号
    private String orgCodg; //	机构编号	原结算处方机构编号
    private String payToken; //	支付token	授权的出参
    private String idNo; //	证件号码
    private String userName; //	用户姓名
    private String idType; //	证件类别	字典人员证件类型(psn_cert_type)
    private String extData; //	医保扩展数据	根据各地方医保要求传不同数据内容

}
