package com.sunhealth.ihhis.model.insurance.response;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.io.Serializable;

@Data
public class GJYBBaseResponse<T> implements Serializable {
    @JsonProperty("msgId")
    private String msgId;//消息请求记录Id
    @JsonProperty("infcode")
    private Integer infcode;//交易状态码，0成功，-1失败
    @JsonProperty("inf_refmsgid")
    private String infRefmsgid;//接收方报文I
    @JsonProperty("refmsg_time")
    private String refmsgTime;//接收报文时间
    @JsonProperty("respond_time")
    private String respondTime;//响应报文时间
    @JsonProperty("err_msg")
    private String errMsg;//错误信息
    @JsonProperty("warn_msg")
    private String warnMsg;//警告信息
    @JsonProperty("signtype")
    private String signtype;//验签类型
    @JsonProperty("cainfo")
    private String cainfo;//数字签名信息
    private T output;

    /**
     * 原始数据
     */
    private String sourceString;
}
