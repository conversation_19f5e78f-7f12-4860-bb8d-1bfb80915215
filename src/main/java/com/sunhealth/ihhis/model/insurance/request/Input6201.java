package com.sunhealth.ihhis.model.insurance.request;

import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * 解码
 */
@Data
public class Input6201 implements Serializable {

    private String orgCodg; //	机构编码	医保分配
    private String orgId; //	电子凭证机构号	电子凭证中台分配，待机构电子凭证建设完毕后可获取该机构号
    private String psnNo; //	人员编号
    private String insutype; //	险种类型
    private String medOrgOrd; //	医疗机构订单号	院内产生惟一流水，可关联到一次结算记录，结算成功回调入参返回
    private String initRxOrd; //	要续方的原处方流水	rxCircFlag 为1时必传，续方时必传
    private String rxCircFlag; //	电子处方流转标志	1：电子处方 ，0不是电子处方，默认0，
    private String begntime; //	开始时间eeeee	挂号时间yyyy-MM-dd HH:mm:ss
    private String idNo; //	证件号码
    private String userName; //	用户姓名
    private String idType; //	证件类别	字典人员证件类型(psn_cert_type)
    private String ecToken; //	电子凭证授权ecToken	电子凭证解码返回
    private String insuCode; //	参保地	电子凭证解码返回
    private String iptOtpNo; //	住院/门诊号	院内唯一流水
    private String atddrNo; //	医师编码
    private String drName; //	医师姓名
    private String deptCode; //	科室编码
    private String deptName; //	科室名称
    private String caty; //	科别
    private String mdtrtId; //	就诊ID	为空时，调用医保核心做就诊登记
    private String medType; //	医疗类别
    private String feeType; //	费用类别	见字典定义
    private BigDecimal medfeeSumamt; //	医疗费总额
    private String acctUsedFlag; //	个人账户使用标志	药店上传费用时必填
    private String mainCondDscr; //	主要病情描述
    private String diseCodg; //	病种编码	按照标准编码填写：按病种结算病种目录代码(bydise_setl_list_code)、门诊慢特病病种目录代码(opsp_dise_cod)、
    private String diseName; //	病种名称
    private String psnSetlway; //	个人结算方式
    private String chrgBchno; //	收费批次号
    private String invono; //	发票号
    private String endtime; //	出院时间	yyyy-MM-dd HH:mm:ss
    private BigDecimal fulamtOwnpayAmt = new BigDecimal(0); //	全自费金额	住院结算时需要
    private BigDecimal overlmtSelfpay = new BigDecimal(0); //	超限价金额	住院结算时需要
    private BigDecimal preselfpayAmt = new BigDecimal(0); //	先行自付金额	住院结算时需要
    private BigDecimal inscpScpAmt = new BigDecimal(0); //	符合政策范围金额	住院结算时需要
    private String oprnOprtCode; //	手术操作代码	住院结算时需要
    private String oprnOprtName; //	手术操作名称	住院结算时需要
    private String fpscNo; //	计划生育服务证号	住院结算时需要
    private String latechbFlag; //	晚育标志	住院结算时需要
    private BigDecimal gesoVal = new BigDecimal(0); //	孕周数	住院结算时需要
    private BigDecimal fetts = new BigDecimal(0); //	胎次	住院结算时需要
    private BigDecimal fetusCnt = new BigDecimal(0); //	胎儿数	住院结算时需要
    private String pretFlag; //	早产标志	住院结算时需要
    private String birctrlType; //	计划生育手术类别	生育门诊按需录入
    private String birctrlMatnDate; //	计划生育手术或生育日期	生育门诊按需录入，yyyy-MM-dd
    private String copFlag; //	伴有并发症标志	住院结算时需要
    private String dscgDeptCodg; //	出院科室编码	住院结算时需要
    private String dscgDeptName; //	出院科室名称	住院结算时需要
    private String dscgDed; //	出院床位	住院结算时需要
    private String dscgWay; //	离院方式	住院结算时需要
    private String dieDate; //	死亡日期	yyyy-MM-dd
    private String matnType; //	生育类别	住院结算时需要
    private String extData; //	扩展字段	-
    private String midSetlFlag; //	中途结算标志	见字典
    private List<Diseinfo> diseinfoList; //	诊断或症状明细
    private List<Feedetail> feedetailList; //	费用明细
    private String payAuthNo; // 支付授权码
    private String uldLatlnt = "0,0"; // 用户经纬度 格式：经度,纬度
    private String mdtrtCertType; // 就诊凭证类型
    private String pubHospRfomFlag = "1"; // 公立医院改革标志
    private String opterType = "4";
    private String insuplcAdmdvs;
    private String expContent;
    /**
     * 诊断或症状明细
     */
    @Data
    public static class Diseinfo implements Serializable {
        private String diagType; //	诊断类别
        private Integer diagSrtNo; //	诊断排序号
        private String diagCode; //	诊断代码
        private String diagName; //	诊断名称
        private String diagDept; //	诊断科室
        private String diseDorNo; //	诊断医生编码
        private String diseDorName; //	诊断医生姓名
        private String diagTime; //	诊断时间yyyy-MM-dd HH:mm:ss
        private String valiFlag; //	有效标志
    }

    /**
     * 费用明细
     */
    @Data
    public static class Feedetail implements Serializable {
        private String feedetlSn; //	费用明细流水号	单次就诊内唯一
        private String mdtrtId; //	就诊ID
        private String psnNo; //	人员编号
        private String chrgBchno; //	收费批次号	同一收费批次号病种编号必须一致
        private String diseCodg; //	病种编码	按照标准编码填写：按病种结算病种目录代码(bydise_setl_list_code)、门诊慢特病病种目录代码(opsp_dise_cod)
        private String rxno; //	处方号	外购处方时，传入外购处方的处方号；非外购处方，传入医药机构处方号
        private String rxCircFlag; //	外购处方标志
        private Date feeOcurTime; //	费用发生时间	yyyy-MM-dd HH:mm:ss
        private String medListCodg; //	医疗目录编码
        private String medinsListCodg; //	医药机构目录编码
        private BigDecimal detItemFeeSumamt; //	明细项目费用总额
        private BigDecimal cnt; //	数量
        private BigDecimal pric; //	单价
        private String sinDosDscr; //	单次剂量描述
        private String usedFrquDscr; //	使用频次描述
        private Integer prdDays; //	周期天数
        private String medcWayDscr; //	用药途径描述
        private String bilgDeptCodg; //	开单科室编码
        private String bilgDeptName; //	开单科室名称
        private String bilgDrCodg; //	开单医生编码	按照标准编码填写
        private String bilgDrName; //	开单医师姓名
        private String acordDeptCodg; //	受单科室编码
        private String acordDeptName; //	受单科室名称
        private String ordersDrCode; //	受单医生编码	按照标准编码填写
        private String ordersDrName; //	受单医生姓名
        private String hospApprFlag; //	医院审批标志
        private String tcmdrugUsedWay; //	中药使用方式
        private String etipFlag; //	外检标志
        private String etipHospCode; //	外检医院编码	按照标准编码填写
        private String dscgTkdrugFlag; //	出院带药标志
        private String matnFeeFlag; //	生育费用标志
        private String initFeedetlSn; //	原费用流水号	退单时传入被退单的费用明细流水号
        private String drordNo; //	医嘱号
        private String medType; //	医疗类别
        private String memo; //	备注
        private String extData; //	扩展字段
    }

}
