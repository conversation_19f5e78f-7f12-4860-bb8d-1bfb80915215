package com.sunhealth.ihhis.model.insurance.request;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.sunhealth.ihhis.config.HisHospitalProperties;
import com.sunhealth.ihhis.config.YiBaoProperties;
import com.sunhealth.ihhis.utils.AppContext;
import com.sunhealth.ihhis.utils.SequenceUtils;
import com.sunhealth.ihhis.utils.TimeUtils;
import lombok.Data;
import org.apache.commons.lang3.StringUtils;

import java.io.Serializable;
import java.util.Date;

@Data
public class GJYBBasicRequest<T> implements Serializable {
    /**
     * 交易编号
     */
    @JsonProperty("infno")
    private String infno;
    /**
     * 发送报文Id
     */
    @JsonProperty("msgid")
    private String msgId;
    /**
     * 就医地医保区划
     */
    @JsonProperty("mdtrtarea_admvs")
    private String mdtrtareaAdmvs;
    /**
     * 参保地医保区划
     */
    @JsonProperty("insuplc_admdvs")
    private String insuplcAdmdvs = "";
    /**
     * 接收方系统代码
     */
    @JsonProperty("recer_sys_code")
    private String recerSysCode = "hcenter_pt";
    /**
     * 设备编号
     */
    @JsonProperty("dev_no")
    private String devNo = "12345";
    /**
     * 设备安全信息
     */
    @JsonProperty("dev_safe_info")
    private String devSafeInfo = "32323";
    /**
     * 数字签名信息
     */
    @JsonProperty("cainfo")
    private String caInfo = "";
    /**
     * 签名类型
     */
    @JsonProperty("signtype")
    private String signType = "";
    /**
     * 接口版本号
     */
    @JsonProperty("infver")
    private String infver = "1.1.25";
    /**
     * 经办人类别
     */
    @JsonProperty("opter_type")
    private String opterType = "1";
    /**
     * 经办人
     * 1-经办人
     * 2-自助终端
     * 3-移动终端
     * 4-线上支付
     * 5-互联网医院
     */
    @JsonProperty("opter")
    private String opter;
    /**
     * 经办人姓名
     */
    @JsonProperty("opter_name")
    private String opterName;
    /**
     * 交易时间
     */
    @JsonProperty("inf_time")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date infTime = new Date();
    /**
     * 定点医药机构编号
     */
    @JsonProperty("fixmedins_code")
    private String fixmedinsCode;
    /**
     * 定点医药机构名称
     */
    @JsonProperty("fixmedins_name")
    private String fixmedinsName;
    /**
     * 交易签到流水号
     */
    @JsonProperty("sign_no")
    private String signNo;

    @JsonProperty("app_id")
    private String appId; // 渠道id
    @JsonProperty("enc_type")
    private String encType; // 加密方式
    @JsonProperty("pw_ecToken")
    private String pwEcToken; // 电子凭证密码核验token
    /**
     * 交易输入
     */
    private T input;

    public GJYBBasicRequest(String infno) {
        this.opter = AppContext.getInstance(HisHospitalProperties.class).getOpCode() + "";
        this.opterName = AppContext.getInstance(HisHospitalProperties.class).getOpName();
        this.fixmedinsCode = AppContext.getInstance(YiBaoProperties.class).getOrgCode();
        this.fixmedinsName = AppContext.getInstance(HisHospitalProperties.class).getHospitalName();
        this.mdtrtareaAdmvs = AppContext.getInstance(YiBaoProperties.class).getMdtrtareaAdmvs();
        this.msgId = this.fixmedinsCode + TimeUtils.nowTimeNumberString() + StringUtils.right("000" + SequenceUtils.getSequence(), 4);
        this.infno = infno;
    }

    public GJYBBasicRequest(String infno, T input, String ecToken, String signNo) {
        this(infno);
        this.input = input;
        this.signNo = signNo;
        this.pwEcToken = ecToken;
    }

    public GJYBBasicRequest(String infno, T input, String ecToken, String signNo, String insuplcAdmdvs) {
        this(infno, input, ecToken, signNo);
        this.insuplcAdmdvs = insuplcAdmdvs;
    }
}
