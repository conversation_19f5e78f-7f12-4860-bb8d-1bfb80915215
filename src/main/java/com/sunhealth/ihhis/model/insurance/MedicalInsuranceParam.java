package com.sunhealth.ihhis.model.insurance;

import com.fasterxml.jackson.annotation.JsonIgnore;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import org.apache.commons.lang3.StringUtils;

import java.io.Serializable;

@Getter
@Setter
@NoArgsConstructor
public class MedicalInsuranceParam implements Serializable {

    @ApiModelProperty("微信授权码")
    private String authCode;

    @ApiModelProperty("用户姓名")
    private String userName;

    @ApiModelProperty("用户参保地代码, 仅当mi_card_type=CERITIFICATE时，才返回有效值，多地参保用户若未选择主参保地，则该字段未空，并且不可使用后续业务。")
    private String cityId;

    @ApiModelProperty("医保线上核验payAuthNo, 当使用医保线上支付功能时，返回payAuthNo")
    private String payAuthNo;

    @ApiModelProperty("用户经纬度, 当使用医保线上支付功能时，返回对应的经纬度")
    private UserLongitudeLatitude userLongitudeLatitude;

    @ApiModelProperty("医保线上核验ecQrcode, 当使用医保线上支付功能时，返回ecQrcode")
    private String ecQrcode;

    @ApiModelProperty("医保卡号")
    private String userCardNo;

    @ApiModelProperty("ecToken,在SE01后需要给这个字段赋值")
    private String ecToken;

    @ApiModelProperty("帐户标志,在调用SM01后需要给这个字段赋值")
    private String accountAttr;

    @ApiModelProperty("是否使用地区医保")
    private boolean dqYiBao;

    @ApiModelProperty("发卡地区行政规划")
    private String insuOrg;

    @JsonIgnore
    public String toHisParam() {
        // authCode授权码|username用户姓名|city_id用户参保地代码|pay_auth_no医保线上核验|user_longitude用户定位经度|user_latitude用户定位纬度|ec_qr_code 医保qrcode|user_card_no患者卡号
        if (StringUtils.isBlank(authCode)) {
            return null;
        } else {
            return authCode + "|" + userName + "|" + cityId + "|" + payAuthNo + "|" + userLongitudeLatitude.getLongitude()
                    + "|" + userLongitudeLatitude.getLatitude() + "|" + ecQrcode + "|" + userCardNo;
        }
    }

    @JsonIgnore
    public static MedicalInsuranceParam fromHisParam(String param) {
        String[] strs = param.split("\\|");
        if (strs.length < 8) {
            return null;
        }
        MedicalInsuranceParam insuranceParam = new MedicalInsuranceParam();
        insuranceParam.setAuthCode(strs[0]);
        insuranceParam.setUserName(strs[1]);
        insuranceParam.setCityId(strs[2]);
        insuranceParam.setPayAuthNo(strs[3]);
        UserLongitudeLatitude longitudeLatitude = new UserLongitudeLatitude();
        longitudeLatitude.setLongitude(strs[4]);
        longitudeLatitude.setLatitude(strs[5]);
        insuranceParam.setUserLongitudeLatitude(longitudeLatitude);
        insuranceParam.setEcQrcode(strs[6]);
        insuranceParam.setUserCardNo(strs[7]);
        return insuranceParam;
    }

}
