package com.sunhealth.ihhis.model.insurance.request;

import java.io.Serializable;
import java.math.BigDecimal;
import lombok.Data;

/**
 * 解码
 */
@Data
public class Input6203 implements Serializable {

    private String payOrdId; //	支付订单号	处方上传的出参订单号
    private String appRefdSn; //	应用退款流水号	应用退费流水号
    private String appRefdTime; //	应用退费时间	yyyyMMddHHmmss
    private BigDecimal totlRefdAmt; //	总退费金额	原交易记录总金额
    private BigDecimal psnAcctRefdAmt; //	医保个人账户支付
    private BigDecimal fundRefdAmt; //	基金支付	含商保
    private BigDecimal cashRefdAmt; //	现金退费金额
    private String ecToken; //	电子凭证授权Token
    private String refdType; //	退费类型	ALL:全部，CASH:只退现金 HI:只退医保
    private String extData; //	医保扩展数据	根据各地方医保要求传不同数据内容
    private String payAuthNo; //	支付授权号
}
