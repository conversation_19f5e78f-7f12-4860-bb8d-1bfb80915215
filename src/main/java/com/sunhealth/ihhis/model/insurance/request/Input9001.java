package com.sunhealth.ihhis.model.insurance.request;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * 解码
 */
@Data
@NoArgsConstructor
public class Input9001 implements Serializable {

    private SignIn signIn;

    public Input9001(SignIn signIn) {
        this.signIn = signIn;
    }

    @Data
    public static class SignIn implements Serializable {
        @JsonProperty("opter_no")
        private String opterNo; // 操作员编号
        private String mac; // 签到MAC地址
        private String ip; // 签到IP地址
    }

}
