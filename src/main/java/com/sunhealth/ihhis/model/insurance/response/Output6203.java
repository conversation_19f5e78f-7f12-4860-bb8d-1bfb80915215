package com.sunhealth.ihhis.model.insurance.response;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

@Data
public class Output6203 implements Serializable {

    private String refdSn; //	结算中心流水号
    private String hiFefdSn; //	医保退费流水号	现金退费不返回
    private String hiTrnsDate; //	医保退费日期	现金退费不返回
    private String hiTrnsTime; //	医保退费时间	现金退费不返回
    private String refStatus; //	退费状态	SUCC:成功,FAIL:失败,EXP:异常，存在退医保及自费时有可能出现这个状态
    private ExtData extData; //	医保扩展数据	根据各地方医保要求传不同数据内容

    @Data
    public static class ExtData implements Serializable {
        private Output6203.ExtData.Setlinfo setlInfo;

        @Data
        public static class Setlinfo implements Serializable {
            @JsonProperty("mdtrt_cert_type")
            private String mdtrtCertType;

            @JsonProperty("acct_pay")
            private BigDecimal acctPay;

            @JsonProperty("clr_type")
            private String clrType;

            @JsonProperty("act_pay_dedc")
            private BigDecimal actPayDedc;

            @JsonProperty("fund_pay_sumamt")
            private BigDecimal fundPaySumamt;

            @JsonProperty("setl_id")
            private String setlId;

            @JsonProperty("psn_cash_pay")
            private BigDecimal psnCashPay;

            @JsonProperty("psn_type")
            private String psnType;

            @JsonProperty("brdy")
            private String brdy;

            @JsonProperty("psn_no")
            private String psnNo;

            @JsonProperty("cvlserv_pay")
            private BigDecimal cvlservPay;

            @JsonProperty("hifes_pay")
            private BigDecimal hifesPay;

            @JsonProperty("clr_optins")
            private String clrOptins;

            @JsonProperty("acct_mulaid_pay")
            private BigDecimal acctMulaidPay;

            @JsonProperty("clr_way")
            private String clrWay;

            @JsonProperty("certno")
            private String certno;

            @JsonProperty("overlmt_selfpay")
            private BigDecimal overlmtSelfpay;

            @JsonProperty("balc")
            private BigDecimal balc;

            @JsonProperty("pool_prop_selfpay")
            private BigDecimal poolPropSelfpay;

            @JsonProperty("hifp_pay")
            private BigDecimal hifpPay;

            @JsonProperty("psn_name")
            private String psnName;

            @JsonProperty("psn_part_amt")
            private BigDecimal psnPartAmt;

            @JsonProperty("hifmi_pay")
            private BigDecimal hifmiPay;

            @JsonProperty("preselfpay_amt")
            private BigDecimal preselfpayAmt;

            @JsonProperty("psn_cert_type")
            private String psnCertType;

            @JsonProperty("gend")
            private String gend;

            @JsonProperty("hosp_part_amt")
            private BigDecimal hospPartAmt;

            @JsonProperty("insutype")
            private String insutype;

            @JsonProperty("med_type")
            private String medType;

            @JsonProperty("hifob_pay")
            private BigDecimal hifobPay;

            @JsonProperty("maf_pay")
            private BigDecimal mafPay;

            @JsonProperty("inscp_scp_amt")
            private BigDecimal inscpScpAmt;

            @JsonProperty("medins_setl_id")
            private String medinsSetlId;

            @JsonProperty("medfee_sumamt")
            private BigDecimal medfeeSumamt;

            @JsonProperty("cvlserv_flag")
            private String cvlservFlag;

            @JsonProperty("fulamt_ownpay_amt")
            private BigDecimal fulamtOwnpayAmt;

            @JsonProperty("oth_pay")
            private BigDecimal othPay;

            @JsonProperty("mdtrt_id")
            private String mdtrtId;

            @JsonProperty("age")
            private int age;

            @JsonProperty("setl_time")
            private String setlTime;
        }
    }
}
