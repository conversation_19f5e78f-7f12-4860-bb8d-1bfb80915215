package com.sunhealth.ihhis.model.insurance.request;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 解码
 */
@Data
public class Input2201 implements Serializable {

    @JsonProperty("psn_no")
    private String psnNo; //	人员编号	字符型	30		Y
    @JsonProperty("insutype")
    private String insutype; //	险种类型	字符型	6	Y	Y
    @JsonProperty("begntime")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date begntime; //	开始时间	日期时间型			Y	挂号时间	yyyy-MM-dd HH:mm:ss
    @JsonProperty("mdtrt_cert_type")
    private String mdtrtCertType; //	就诊凭证类型	字符型	3	Y	Y
    @JsonProperty("mdtrt_cert_no")
    private String mdtrtCertNo; //	就诊凭证编号	字符型	50		Y	就诊凭证类型为“01”时填写电子凭证令牌，为“02”时填写身份证号，为“03”时填写社会保障卡卡号
    @JsonProperty("ipt_otp_no")
    private String iptOtpNo; //	住院/门诊号	字符型	30		Y	院内唯一流水
    @JsonProperty("atddr_no")
    private String atddrNo; //	医师编码	字符型	30		Y
    @JsonProperty("dr_name")
    private String drName; //	医师姓名	字符型	50		Y
    @JsonProperty("dept_code")
    private String deptCode; //	科室编码	字符型	30		Y
    @JsonProperty("dept_name")
    private String deptName; //	科室名称	字符型	100		Y
    @JsonProperty("caty")
    private String caty; //	科别	字符型	10	Y	Y
    @JsonProperty("exp_content")
    private String expContent; //	字段扩展	字符型	4000
    @JsonProperty("med_type")
    private String medType; //	医疗类别	字符型	6			新增入参

}
