package com.sunhealth.ihhis.model.insurance.response;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.io.Serializable;

@Data
public class Output9001 implements Serializable {

    private SignInOutB signinoutb;

    /**
     * 签到日期, 接口中没有, 这个是 YibaoSignInService 中使用的
     */
    @JsonIgnore
    private String date;

    @Data
    public static class SignInOutB implements Serializable {
        @JsonProperty("sign_time")
        private String signTime; // 签到时间
        @JsonProperty("sign_no")
        private String signNo; // 签到编号
    }
}
