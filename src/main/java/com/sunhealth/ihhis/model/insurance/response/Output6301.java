package com.sunhealth.ihhis.model.insurance.response;

import com.sunhealth.ihhis.model.insurance.request.CallBack6302;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;

@Data
@NoArgsConstructor
public class Output6301 implements Serializable {

    private String ordStas; //	订单状态	见订单状态字典
    private String payOrdId; //	支付订单号	医保结算中心订单号
    private String callType; //	回调类型	见回调类型字典定义
    private String medOrgOrd; //	医院订单号	医院订单号
    private String traceTime; //	交易时间	交易成功时间
    private String orgCodg; //	两定机构编号
    private String orgName; //	两定机构名称
    private String setlType; //	结算类型	ALL:医保自费全部，CASH:只结现金 HI:只结医保
    private BigDecimal feeSumamt; //	费用总金额
    private BigDecimal ownPayAmt; //	现金支付
    private BigDecimal psnAcctPay; //	个人账户支付
    private BigDecimal fundPay; //	医保基金支付
    private String revsToken; //	用于院内结算失败对医保的冲正授权	1小时内有效
    private ExtData extData; //	医保扩展数据	根据各地方医保要求返回不同数据内容
    private String hiChrgTime; // 医保-收费时间
    private String hiDocSn; // 医保-交易流水号
    private String hiRgstSn; // 医保-挂号流水号
    private BigDecimal acctMulaidPay; //	个人账户共济支付金额
    private BigDecimal selfAcctPay; //	本人个账支出金额


    @Data
    public static class ExtData implements Serializable {
        private String insuplcAdmdvs; //	参保地医保区划
        private String acctFlag;
        private PayChnlInfo payChnlInfo;
        private String begntime;
        private Setlinfo setlinfo;
        private List<Setldetail> setldetail;
        @Data
        public static class PayChnlInfo implements Serializable {
            private String payChnlId; //	支付渠道ID
            private String payChnlName; //	支付渠道名称
        }

        @Data
        public static class Setldetail implements Serializable {
            private String fund_pay_type;
            private BigDecimal fund_payamt;
            private BigDecimal crt_payb_lmt_amt;
            private BigDecimal inscp_scp_amt;
            private String fund_pay_type_name;
        }
        @Data
        public static class Setlinfo implements Serializable {
            private String mdtrtCertType;
            private BigDecimal acctPay;
            private String clrType;
            private BigDecimal actPayDedc;
            private BigDecimal fundPaySumamt;
            private String setlId;
            private BigDecimal psnCashPay;
            private String psnType;
            private String brdy;
            private String psnNo;
            private BigDecimal cvlservPay;
            private BigDecimal hifesPay;
            private String clrOptins;
            private BigDecimal acctMulaidPay;
            private String clrWay;
            private String certno;
            private BigDecimal overlmtSelfpay;
            private BigDecimal balc;
            private BigDecimal poolPropSelfpay;
            private BigDecimal hifpPay;
            private String psnName;
            private BigDecimal psnPartAmt;
            private BigDecimal hifmiPay;
            /**
             * 先行自付金额
             */
            private BigDecimal preselfpayAmt;
            private String psnCertType;
            private String gend;
            private BigDecimal hospPartAmt;
            private String insutype;
            private String medType;
            private BigDecimal hifobPay;
            private BigDecimal mafPay;
            /**
             * 符合政策范围金额
             */
            private BigDecimal inscpScpAmt;
            private String medinsSetlId;
            private BigDecimal medfeeSumamt;
            private String cvlservFlag;
            private BigDecimal fulamtOwnpayAmt;
            private BigDecimal othPay;
            private String mdtrtId;
            private int age;
            private String setlTime;
        }
    }

    public Output6301(CallBack6302 callBack6302) {
        this.ordStas = "6"; //	订单状态	见订单状态字典
        this.payOrdId = callBack6302.getPayOrdId();
        this.callType = callBack6302.getCallType();
        this.medOrgOrd = callBack6302.getMedOrgOrd();
        this.traceTime = callBack6302.getTraceTime();
        this.orgCodg = callBack6302.getOrgCodg();
        this.orgName = callBack6302.getOrgName();
        this.setlType = callBack6302.getSetlType();
        this.feeSumamt = callBack6302.getFeeSumamt();
        this.ownPayAmt = callBack6302.getOwnpayAmt();
        this.psnAcctPay = callBack6302.getPsnAcctPay();
        this.fundPay = callBack6302.getFundPay();
        this.revsToken = callBack6302.getRevsToken();
        this.extData = callBack6302.getExtData();
        this.hiChrgTime = callBack6302.getHiChrgTime();
        this.hiDocSn = callBack6302.getHiDocSn();
        this.hiRgstSn = callBack6302.getHiRgstSn();
    }
}
