package com.sunhealth.ihhis.model.insurance.response;

import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

@Data
public class Output6202 implements Serializable {

    private String payOrdId; //	支付订单号
    private String ordStas; //	订单状态
    private BigDecimal feeSumamt; //	费用总额
    private BigDecimal ownPayAmt; //	现金支付
    private BigDecimal psnAcctPay; //	个人账户支出
    private BigDecimal fundPay; //	医保基金支付
    private BigDecimal othFeeAmt; //	其他支付金额
    private BigDecimal hospPartAmt; //	医院负担金额
    private BigDecimal acctMulaidPay; //	个人账户共济支付金额
    private BigDecimal selfAcctPay; //	本人个账支出金额
    private ExtData extData; //	扩展数据
    @Data
    public static class ExtData implements Serializable {
        private PreSetl preSetl;

        @Data
        public static class PreSetl implements Serializable {
            // {"preSetl":{"acct_mulaid_pay":0,"acct_pay":0,"act_pay_dedc":0,"age":38,"balc":400.00,"brdy":"1985-12-19","certno":"420322198512196617","clr_optins":"420102","clr_type":"9902","clr_way":"1","cvlserv_flag":"0","cvlserv_pay":0,"fulamt_ownpay_amt":1.5,"fund_pay_sumamt":1.98,"gend":"1","hifes_pay":0,"hifmi_pay":0,"hifob_pay":0,"hifp_pay":1.98,"hosp_part_amt":0,"inscp_scp_amt":4.00,"insutype":"310","maf_pay":0,"mdtrt_cert_type":"03","mdtrt_id":"420503Y0000000338682","med_type":"12","medfee_sumamt":5.50,"medins_setl_id":"420500300004202406041708341809","oth_pay":0,"overlmt_selfpay":0,"pool_prop_selfpay":0,"preselfpay_amt":0,"psn_cash_pay":3.52,"psn_cert_type":"01","psn_name":"周学东","psn_no":"42030000000000002100685221","psn_part_amt":3.52,"psn_type":"11","setl_time":"2024-06-04 17:08:54"}}
            private BigDecimal acct_mulaid_pay; //	个人账户共济支付
            private BigDecimal acct_pay; //	个人账户支付
            private BigDecimal act_pay_dedc; //	实际支付自费
            private Integer age; //	年龄
            private BigDecimal balc; //	余额
            private String brdy; //	出生日期
            private String certno; //	证件号码
            private String clr_optins; //	清算机构
            private String clr_type; //	清算类别
            private String clr_way; //	清算方式
            private String cvlserv_flag; //	公务员医疗服务标志
            private BigDecimal cvlserv_pay; //	公务员医疗服务支付
            private BigDecimal fulamt_ownpay_amt; //	全自费金额
            private BigDecimal fund_pay_sumamt; //	基金支付总额
            private String gend; //	性别
            private BigDecimal hifes_pay; //	公务员医疗补助支付
            private BigDecimal hifmi_pay; //	公务员医疗补助支付
            private BigDecimal hifob_pay; //	公务员医疗补助支付
            private BigDecimal hifp_pay; //	公务员医疗补助支付
            private BigDecimal hosp_part_amt; //	医院部分支付
            private BigDecimal inscp_scp_amt; //	符合政策范围金额
            private String insutype; //	险种类型
            private BigDecimal maf_pay; //	医疗救助支付
            private String mdtrt_cert_type; //	就诊凭证类型
            private String mdtrt_id; //	就诊ID
            private String med_type; //	医疗类别
            private BigDecimal medfee_sumamt; //	医疗费总额
            private String medins_setl_id; //	医保结算ID
            private BigDecimal oth_pay; //	其他支付
            private BigDecimal overlmt_selfpay; //	超限价自付
            private BigDecimal pool_prop_selfpay; //	统筹自付
            private BigDecimal preselfpay_amt; //	先行自付金额
            private BigDecimal psn_cash_pay; //	个人现金支付
            private String psn_cert_type; //	人员证件类型
            private String psn_name; //	人员姓名
            private String psn_no; //	人员编号
            private BigDecimal psn_part_amt; //	个人部分支付
            private String psn_type; //	人员类别
            private String setl_time; //	结算时间
        }
    }


}
