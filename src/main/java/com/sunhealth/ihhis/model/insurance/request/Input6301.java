package com.sunhealth.ihhis.model.insurance.request;

import java.io.Serializable;
import lombok.Data;

/**
 * 解码
 */
@Data
public class Input6301 implements Serializable {

    private String payOrdId; //	支付订单号
    private String orgCodg; //	定点机构编码
    private String payToken; //	支付token	与费用上传时一致
    private String idNo; //	证件号码
    private String userName; //	用户姓名
    private String idType; //	证件类别	字典人员证件类型(psn_cert_type)
    private String extData; //	扩展数据

}
