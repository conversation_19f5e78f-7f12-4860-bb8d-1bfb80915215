package com.sunhealth.ihhis.model.insurance.request;

import java.io.Serializable;
import lombok.Data;

/**
 * 解码
 */
@Data
public class Input6202 implements Serializable {

    private String payAuthNo; //	支付授权码	电子凭证线上渠道授权返回,与电子凭证三要素不可同时为空
    private String payOrdId; //	待支付订单号	费用上传返回
    private String payToken; //	支付订单对应的token	费用上传返回
    private String orgCodg; //	定点机构编码
    private String orgBizSer; //	业务流水号	每一次请求唯一
    private String ecAuthCode; //	电子凭证授权码	与payAuthNo不可同时为空
    private String ecChnlAppId; //	电子凭证渠道ID
    private String ecChnlUserId; //	电子凭证用户ID
    private String mdtrtId; //	就诊ID	医保返回唯一流水
    private String chrgBchno; //	收费批次号	与费用上传一致
    private String extData; //	扩展数据	按需扩展


}
