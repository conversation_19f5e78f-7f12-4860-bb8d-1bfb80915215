package com.sunhealth.ihhis.model.insurance.response;

import com.fasterxml.jackson.annotation.JsonProperty;
import java.io.Serializable;
import lombok.Data;

@Data
public class GJYiBaoBaseEncryptResponse<T> implements Serializable {
    @JsonProperty("code")
    private int code;//响应状态码
    @JsonProperty("appid")
    private String appid;//渠道id
    @JsonProperty("timestamp")
    private String timestamp;//时间戳
    @JsonProperty("encType")
    private String encType;//加密方式
    @JsonProperty("signType")
    private String signType;//签名方式
    @JsonProperty("signData")
    private String signData;//签名串
    @JsonProperty("encData")
    private String encData;//加密数据
    @JsonProperty("message")
    private String message;//响应异常信息
    @JsonProperty("success")
    private Boolean success;//响应标识
    @JsonProperty("version")
    private String version;//版本号
    private T data;
}
