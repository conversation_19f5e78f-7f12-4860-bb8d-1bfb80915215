package com.sunhealth.ihhis.model.insurance.response;

import java.io.Serializable;
import lombok.Data;

@Data
public class Output6201 implements Serializable {

    private String payOrdId; //	支付订单号, 医保结算中心订单号
    private String payToken; //	支付token, 下单支付使用
    private String cashierUrl; // 医保支付收银台h5地址, 使用收银台模式时，h5地址带上电子凭证线上授权支付返回payAuthNo打开即可，如：返回收银台的h5地址拼上&chnlAppId=appid的值&chnlEncData=(payAuthNo对应的加密值)
    private Object extData; //	扩展数据

}
