package com.sunhealth.ihhis.model.insurance.response;

import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

@Data
@NoArgsConstructor
public class CallBackResult6302 implements Serializable {

    private Data6302 data;

    public CallBackResult6302(Data6302 data) {
        this.data = data;
    }

    @Data
    public static class Data6302 implements Serializable {
        /**
         * 回调通知 结果
         */
        private boolean success;

        /**
         * 结果说明
         */
        private String message;
    }

}
