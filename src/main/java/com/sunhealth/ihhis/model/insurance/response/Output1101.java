package com.sunhealth.ihhis.model.insurance.response;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

@Data
public class Output1101 implements Serializable {
    private BaseInfo baseinfo;
    private List<InsuInfo> insuinfo;
    private List<IdetInfo> idetinfo;

    @Data
    public static class BaseInfo implements Serializable {
        @JsonProperty("psn_no")
        private String psnNo;//人员编号
        @JsonProperty("psn_cert_type")
        private String psnCertType;//人员证件类型
        private String certno;//证件号码
        @JsonProperty("psn_name")
        private String psnName;//人员姓名
        private String gend;//性别
        private String naty;//民族
        private Date brdy;//出生日期
        private int age;
        @JsonProperty("exp_content")
        private String expContent;//字段扩展
    }

    @Data
    public static class InsuInfo implements Serializable {
        private String balc;//余额
        private String insutype;//险种类型(310职工基本医疗保险,320公务员医疗补助,330大额医疗费用补助,340离休人员医疗保障,390城乡居民基本医疗保险,392城乡居民大病医疗保险,510	生育保险)
        @JsonProperty("psn_type")
        private String psnType;//人员类别
        @JsonProperty("psn_insu_stas")
        private String psnInsuStas;//人员参保状态0未参保1正常参保2暂停参保4终止参保
        @JsonProperty("psn_insu_date")
        private Date psnInsuDate;//个人参保日期
        @JsonProperty("paus_insu_date")
        private Date pausInsuDate;//暂停参保日期
        @JsonProperty("cvlserv_flag")
        private String cvlservFlag;//公务员标志
        @JsonProperty("insuplc_admdvs")
        private String insuplcAdmdvs;//参保地医保区划
        @JsonProperty("emp_name")
        private String empName;//单位名称
    }

    @Data
    public static class IdetInfo implements Serializable {
        @JsonProperty("psn_idet_type")
        private String psnIdetType;//人员身份类别
        @JsonProperty("psn_type_lv")
        private String psnTypeLv;//人员类别等级
        private String memo;//备注
        private Date begntime;//开始时间
        private Date endtime;//结束时间
    }
}
