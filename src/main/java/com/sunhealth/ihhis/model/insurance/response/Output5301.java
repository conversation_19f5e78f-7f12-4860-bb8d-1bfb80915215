package com.sunhealth.ihhis.model.insurance.response;

import com.fasterxml.jackson.annotation.JsonProperty;
import java.io.Serializable;
import java.util.Date;
import lombok.Data;

@Data
public class Output5301 implements Serializable {
    @JsonProperty("opsp_dise_code")
    private String opspDiseCode; //	门慢门特病种目录代码
    @JsonProperty("opsp_dise_name")
    private String opspDiseName; //	门慢门特病种名称
    @JsonProperty("begndate")
    private Date begnDate; //	开始日期
    @JsonProperty("enddate")
    private Date endDate; //	结束日期
}
