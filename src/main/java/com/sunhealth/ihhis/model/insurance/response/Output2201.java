package com.sunhealth.ihhis.model.insurance.response;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.io.Serializable;

@Data
public class Output2201 implements Serializable {

    private Data2201 data;

    @Data
    public static class Data2201 implements Serializable {

        @JsonProperty("mdtrt_id")
        private String mdtrtId; //	就诊ID	医保返回唯一流水	30
        @JsonProperty("psn_no")
        private String psnNo; //	人员编号		30
        @JsonProperty("ipt_otp_no")
        private String iptOtpNo; //	住院/门诊号	院内唯一流水	30
        @JsonProperty("exp_content")
        private String expContent; //	字段扩展		4000
    }
}
