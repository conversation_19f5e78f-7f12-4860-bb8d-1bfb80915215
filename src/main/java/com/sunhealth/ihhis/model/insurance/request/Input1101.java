package com.sunhealth.ihhis.model.insurance.request;

import com.fasterxml.jackson.annotation.JsonProperty;
import java.io.Serializable;
import java.util.Date;
import lombok.Data;

/**
 * 解码
 */
@Data
public class Input1101 implements Serializable {

    @JsonProperty("mdtrt_cert_type")
    private String mdtrtCertType; //	就诊凭证类型
    @JsonProperty("mdtrt_cert_no")
    private String mdtrtCertNo; //	就诊凭证编号	就诊凭证类型为“01”时填写电子凭证令牌，为“02”时填写身份证号，为“03”时填写社会保障卡卡号
    @JsonProperty("card_sn")
    private String cardSn; //	卡识别码	就诊凭证类型为“03”时必填
    @JsonProperty("begntime")
    private Date begnTime; //	开始时间	获取历史参保信息时传入
    @JsonProperty("psn_cert_type")
    private String psnCertType; //	人员证件类型
    @JsonProperty("certno")
    private String certNo; //	证件号码
    @JsonProperty("psn_name")
    private String psnName; //	人员姓名

}
