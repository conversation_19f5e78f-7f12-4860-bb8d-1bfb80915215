package com.sunhealth.ihhis.model.vm;

import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.io.Serializable;

@Getter
@Setter
@NoArgsConstructor
public class PushInvoiceMessageParam implements Serializable {

    private Long invoiceId;
    private Integer invoiceType;
    private Integer flag;
    private String hospitalCode;
    private String opCode;
    private String invoiceInfo;

    public PushInvoiceMessageParam(Long invoiceId, Integer invoiceType, Integer flag, String hospitalCode, String opCode, String invoiceInfo) {
        this.invoiceId = invoiceId;
        this.invoiceType = invoiceType;
        this.flag = flag;
        this.hospitalCode = hospitalCode;
        this.opCode = opCode;
        this.invoiceInfo = invoiceInfo;
    }
}
