package com.sunhealth.ihhis.model.vm;

import java.io.Serializable;
import java.util.List;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Getter
@Setter
@NoArgsConstructor
public class PushInpatientGuanKongMessageParam implements Serializable {

    private List<Long> regNoArr;
    private Long hospId;
    private Long workerId;

    public PushInpatientGuanKongMessageParam(List<Long> regNoArr, Long hospId, Long workerId) {
        this.regNoArr = regNoArr;
        this.hospId = hospId;
        this.workerId = workerId;
    }
}
