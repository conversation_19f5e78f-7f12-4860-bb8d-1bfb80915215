package com.sunhealth.ihhis.model.vm;

import lombok.Data;

@Data
public class BaseResponse<T> implements java.io.Serializable {

    //0	调用成功
    //4xx	客户端错误
    //5xx	服务器错误
    //100x	业务逻辑错误
    private String code = "0";
    private String msg = "success";
    private BaseResponseData<T> data;

    public BaseResponse() {
    }

    public BaseResponse(String code, String msg) {
        this.code = code;
        this.msg = msg;
    }

    public static <T> BaseResponse<T> success(BaseResponseData<T> data) {
        BaseResponse<T> response = new BaseResponse<>();
        response.setCode("0");
        response.setMsg("success");
        response.setData(data);
        return response;
    }

    public static <T> BaseResponse<T> success(T data) {
        return BaseResponse.success(new BaseResponseData<>(data));
    }

    public static <T> BaseResponse<T> failed(String message, T data) {
        BaseResponse<T> response = new BaseResponse<>();
        response.setCode("400");
        response.setMsg(message);
        response.setData(new BaseResponseData<>(data));
        return response;
    }
}
