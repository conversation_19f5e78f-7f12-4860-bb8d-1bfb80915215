package com.sunhealth.ihhis.model.vm;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;

@ApiModel("操作日志查询参数模型")
@Data
@NoArgsConstructor
public class SelectOperationLogParam {
    @ApiModelProperty("页码")
    private Integer page = 1;

    @ApiModelProperty("每页大小")
    private Integer size = 20;

    public SelectOperationLogParam(Integer page, Integer size) {
        this.page = page;
        this.size = size;
    }
}
