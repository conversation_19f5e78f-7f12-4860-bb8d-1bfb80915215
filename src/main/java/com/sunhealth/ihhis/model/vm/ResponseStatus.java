package com.sunhealth.ihhis.model.vm;

import lombok.Data;

import java.io.Serializable;

@Data
public class ResponseStatus implements Serializable {

    //0	调用成功
    //4xx	客户端错误
    //5xx	服务器错误
    //100x	业务逻辑错误
    private boolean success = false;
    private String message;

    public ResponseStatus() {
    }

    public ResponseStatus(String message) {
        this.message = message;
    }

    public ResponseStatus(boolean success, String message) {
        this.success = success;
        this.message = message;
    }
}
