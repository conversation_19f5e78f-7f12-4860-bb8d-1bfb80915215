package com.sunhealth.ihhis.model.vm;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
public class BaseResponseData<T> {

    private T content;

    private Integer page;

    private Integer size;

    private Integer total;

    private Boolean pageable = false;

    public BaseResponseData() {
    }

    public BaseResponseData(T content) {
        this.content = content;
    }

    public BaseResponseData(Page pageObject) {
        this.pageable = true;
        this.content = (T) pageObject.getRecords();
        this.page = Math.toIntExact(pageObject.getPages());
        this.size = Math.toIntExact(pageObject.getSize());
        this.total = Math.toIntExact(pageObject.getTotal());
    }

}
