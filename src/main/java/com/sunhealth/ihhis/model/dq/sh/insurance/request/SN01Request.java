package com.sunhealth.ihhis.model.dq.sh.insurance.request;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * 解码
 */
@Data
public class SN01Request implements Serializable {
    /**
     * 凭证类别 0：磁卡，1：保障卡，3：电子凭证
     */
    private String cardtype;
    /**
     * 磁卡：28 位，保障卡：不填
     * 电子凭证：填写令牌
     */
    private String carddata;
    /**
     * 就诊单元号
     */
    private String jzdyh;
    /**
     * 登记号, 门诊号、住院号
     */
    private String djh;
    /**
     * 明细账单号
     * 初始为空, 后续上传需要将返回的明细账单号填入
     */
    private String mxzdh;
    /**
     * 本次费用明细包的医疗费用总额
     * 数字格式A
     */
    private String bcmxylfyze;
    /**
     * 结算类型标志
     * 120：门诊结算
     * 220：急诊结算
     * 410：家床结算
     * 510：急观结算
     * 610：住院结算
     * 门诊大病前两位：32=大病结算
     * 门诊大病后一位：1=化疗；2=放疗；3=血透；4=腹透；6=肾移植抗排异；7=同位素治疗；8=介入治疗；9=中医药治疗；A=精神病；
     */
    private String jslxbz;
    /**
     * 明细项目
     */
    private List<SN01Item> mxxms;

    @Data
    public static class SN01Item implements Serializable {
        /**
         * 费用明细单体序号
         * 细分明细所属的费用明细单体序号
         * 数字格式B
         */
        private int xh;
        /**
         * 处方号
         */
        private String cfh;
        /**
         * 科室编码,见字典表
         */
        private String deptid;
        /**
         * 科室名称
         */
        private String ksmc;
        /**
         * 处方医师号
         */
        private String cfysh;
        /**
         * 处方医师姓名
         */
        private String cfysxm;
        /**
         * 费用类别,见说明
         */
        private String fylb;
        /**
         * 明细项目编码,项目代码
         */
        private String mxxmbm;
        /**
         * 明细项目名称
         */
        private String mxxmmc;
        /**
         * 明细项目单位,项目单位
         */
        private String mxxmdw;
        /**
         * 明细项目单价
         * 数字格式D
         */
        private String mxxmdj;
        /**
         * 明细项目数量
         * 数字格式D
         */
        private String mxxmsl;
        /**
         * 明细项目金额
         * 数字格式D
         */
        private String mxxmje;
        /**
         * 医疗费用交易费用
         * 数字格式D
         */
        private String mxxmjyfy;
        /**
         * 明细项目医保结算范围费用
         * 数字格式D
         */
        private String mxxmybjsfwfy;
        /**
         * 医用材料品牌/药品通用名
         * 如是药品，填写药品通用名；如是医用材料，填写医用材料品牌；其他不填
         */
        private String yyclpp = "";
        /**
         * 注册证号
         * 如是医用材料，填写医用材料注册证号；其他不填
         */
        private String zczh;
        /**
         * 明细项目规格
         * 如是药品，填写药品规格；如是医用材料，填写医用材料规格型号；其他不填
         */
        private String mxxmgg;
        /**
         * 明细项目使用日期
         * 日期格式
         */
        private String mxxmsyrq;
        /**
         * 报销标志
         * 0：可报销项目 1：不可报销项目 2：定额支付
         */
        private String bxbz;
        /**
         * 收费、退费标志
         * 1：收费 2：退费
         */
        private String sftfbz;
        /**
         * 减负标志
         * 0：普通（不减负）
         * 1：尿毒症透析医疗费用减负
         * 2：肾移植术后抗排异医疗费用减负
         * 3: 精神病住院减负
         */
        private String jfbz;
        /**
         * 是否细分明细
         * 0：否 1：是
         */
        private String sfxfmx;
        /**
         * 细分明细
         */
        @JsonProperty("XFMX")
        private List<SN01ItemDetail> xfmx;
        @Data
        public static class SN01ItemDetail implements Serializable {
            /**
             * 费用明细单体序号
             * 如明细项目有细分需要填写，此处填写明细项目的序号
             * 数字格式B
             */
            private int xh;
            /**
             * 明细项目细分流水号
             * 按细分明细顺序填写，编号从 1 开始。例如：1、2
             */
            private String mxxflsh;
            /**
             * 明细项目子类编码
             * 结算项目库中项目代码，和明细项目库中的明细项目编码对应
             */
            private String mxxmbmzl;
            /**
             * 明细项目名称（子类）
             * 项目名称
             */
            private String mxxmmczl;
            /**
             * 明细项目单位（子类）
             * 项目单位
             */
            private String mxxmdwzl;
            /**
             * 明细项目单价（子类）
             * 数字格式D
             */
            private String mxxmdjzl;
            /**
             * 明细项目数量（子类）
             * 数字格式E
             */
            private String mxxmslzl;
            /**
             * 明细项目金额（子类）
             * 数字格式D
             */
            private String mxxmjezl;
            /**
             * 报销标志（子类）
             * 0：可报销项目 1：不可报销项目
             */
            private String bxbzzl;
            /**
             * 医用材料品牌/药品通用名（子类）
             * 如是药品，填写药品通用名；如是医用材料，填写医用材料品牌；其他不填
             * 数字
             */
            private String yyclppzl;
            /**
             * 注册证号（子类）
             * 如是医用材料，填写医用材料注册证号；其他不填
             * 数字
             */
            private String zczhzl;
            /**
             * 明细项目规格（子类）
             * 如是药品，填写药品规格；如是医用材料，填写医用材料规格型号；其他不填
             */
            private String mxxmggzl;
            /**
             * 收费、退费标志
             * 1：收费 2：退费
             */
            private String sftfbzzl;
            /**
             * 明细项目使用日期
             * 格式：YYYYMMDD，是指本条明细项目在本次住院结算期间的使用日期，应介于“出入院（观）结算记录库”中住院结算开始日期和住院结算结束日期之间。
             */
            private String mxxmsyrqzl;
        }

    }
}