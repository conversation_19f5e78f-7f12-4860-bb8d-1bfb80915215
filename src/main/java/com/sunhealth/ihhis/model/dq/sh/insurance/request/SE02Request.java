package com.sunhealth.ihhis.model.dq.sh.insurance.request;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * 解码
 */
@Data
public class SE02Request implements Serializable {
    /**
     * 订单号
     * 医院自行生成的唯一ID，生成规则：医疗机构代码（11 位）+yyyyMMddHHmmssSSS+6位随机数字
     */
    private String orderNo;
    /**
     * 令牌
     */
    private String ecToken;
    /**
     * 医保结算确认请求包（仅 xxnr部分）
     * 按线下结算的格式和长\度为准
     * SH02数据
     */
    private Object payRequest;
    /**
     * 姓名
     */
    @JsonProperty("pName")
    private String pName;

    /**
     * 身份证号
     */
    @JsonProperty("pIdNo")
    private String pIdNo;

}
