package com.sunhealth.ihhis.model.dq.sh.insurance;


import lombok.Data;

import java.io.Serializable;

/**
 * 挂号主表
 */
@Data
public class EcTokenDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    private String msgId;
    private String ecToken;
    /**
     * 证件类型
     */
    private String idType;
    /**
     * 医保用户编号
     */
    private String userName;
    /**
     * 证件号码
     */
    private String idNo;
    /**
     * 医疗机构代码
     */
    private String insuOrg;
    private String ecQrCode;
    private String payAuthNo;

}