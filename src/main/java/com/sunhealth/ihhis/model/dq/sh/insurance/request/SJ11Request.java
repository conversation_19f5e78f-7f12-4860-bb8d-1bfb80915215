package com.sunhealth.ihhis.model.dq.sh.insurance.request;

import lombok.Data;

import java.io.Serializable;

/**
 * 解码
 */
@Data
public class SJ11Request implements Serializable {

    /**
     * 凭证类别
     * 0：磁卡，1：保障卡，3：电子凭证
     */
    private String cardtype;
    /**
     * 凭证码
     * 磁卡：28 位，保障卡：不填
     * 电子凭证：填写令牌
     */
    private String carddata;
    /**
     * 科室编码
     */
    private String deptid;
    /**
     * 登记类别
     * 1：家床建床 2：急观入观 3：入院登记 4：大病登记 6：保健对象急观 7：保健对象入院 0：门诊登记
     */
    private String djtype;
    /**
     * 登记号
     * 急观：填急观号，住院：填住院号，家床：填空格，大病：填空格
     */
    private String djno;
    /**
     * 开始日期
     * 格式：“YYYYMMDD”
     * 家床：家床开始日期
     * 急观：急观开始日期
     * 住院：住院开始日期
     * 大病：大病开始日期
     */
    private String startdate;
    /**
     * 结束日期
     * 格式：“YYYYMMDD”
     * 家床：家床结束日期
     * 急观：空格
     * 住院：空格
     * 大病：空格（默认 6 个月）
     */
    private String enddate;
    /**
     * 诊断编码
     */
    private Zdnos Zdnos;
    /**
     * 大病项目
     */
    private String dbxm = "";
    /**
     * 门诊大病登记疾病诊断分类
     */
    private String zd = "";
    /**
     * 大病登记委托人姓名
     */
    private String wtrxm = "";
    /**
     * 大病登记委托人身份证
     */
    private String wtrsfzh = "";
    /**
     * 大病登记原因
     * 1：医疗原因；2：房屋动迁；3：购买新房；4：暂时与子女或其他亲属同住；5：其他；
     */
    private String yy = "";
    /**
     * 大病登记描述
     */
    private String des = "";
    /**
     * 大病登记子类
     * 1：特指内分泌特异抗肿瘤治疗；0：其它
     */
    private String dbzl = "";
    /**
     * 大病登记医师姓名
     */
    private String ysxm = "";
    /**
     * 大病登记医师工号
     */
    private String ysgh = "";

    @Data
    public static class Zdnos implements Serializable {
        /**
         * 诊断编码
         */
        private String zdno;
        /**
         * 诊断名称
         */
        private String zdmc;

        public Zdnos(String zdno, String zdmc) {
            this.zdno = zdno;
            this.zdmc = zdmc;
        }
    }

}