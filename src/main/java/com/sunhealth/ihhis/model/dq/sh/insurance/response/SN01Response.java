package com.sunhealth.ihhis.model.dq.sh.insurance.response;

import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;

/**
 * 解码
 */
@Data
public class SN01Response implements Serializable {
    /**
     * 明细账单号
     * 所有明细处理成功则返回明细账单号
     */
    private String mxzdh;
    /**
     * 明细项目计算
     */
    private List<SN01Item> mxxms;

    @Data
    public static class SN01Item implements Serializable {
        /**
         * 费用明细单体序号
         * 数字格式B
         */
        private int xh;
        /**
         * 处理标志
         * 0:成功 1：失败
         */
        private String clbz;
        /**
         * 处理返回信息
         */
        private String fhxx;
        /**
         * 报销标志
         */
        private String bxbz;

        /**
         * 明细项目金额
         * 数字格式D
         */
        private BigDecimal mxxmje;

        /**
         * 明细项目交易费用
         * 数字格式D
         */
        private BigDecimal mxxmjyfy;

        /**
         * 明细项目医保结算范围费用
         * 数字格式D
         */
        private BigDecimal mxxmybjsfwfy;

    }
}
