package com.sunhealth.ihhis.model.dq.sh.insurance.response;

import com.sunhealth.ihhis.utils.TimeUtils;
import lombok.Data;

/**
 * 医保交易返回结果
 */
@Data
public class SHYBBasicResponse<T> {

    /**
     * 交易时间,时间格式
     * 最长19
     */
    private String jysj = TimeUtils.nowTimeLongString();
    /**
     * 消息类型码,填具体的消息类型码，如 SH01，SH02
     * 最长7
     */
    private String xxlxm;
    /**
     * 交易返回码,P001 代表成功，具体见 3.4 交易返回码章节说明
     * 最长4
     */
    private String xxfhm;
    /**
     * 交易返回信息,交易返回的具体详细信息。
     * 最长200
     */
    private String fhxx;
    private String zdsbsbm;
    /**
     * 交易版本号,由医保控件读取，如 0001
     * 最长4
     */
    private String bbh;
    /**
     * 报文 ID,医疗机构代码（11 位）+日期（8 位）+流水号（9 位）
     * 最长30
     */
    private String msgid;
    /**
     * 发卡地行政区划代码
     * 由医保控件读取，参照 GB/T
     * 2260-2007 中华人民共和国行政区
     * 划代码（字典代码 AAB301）, 地市
     * 编码（ 例如： 310000 上海市）,
     * 异地就医时为参保地行政区划
     * 最长6
     */
    private String xzqhdm;
    /**
     * 医疗机构代码
     * 最长20
     */
    private String jgdm;
    /**
     * 操作员编码
     * 医疗机构操作人员编码
     * 最长20
     */
    private String czybm;
    // "1234567887654321MVv6Vv6MMvV6v6MV6VMvMMV6vM66VVv6vMV6c0a8018c"
    private String sysResv;
    // "0004fefcfebca284"
    private String recvTime;
    /**
     * 操作员姓名
     * 最长100
     */
    private String czyxm;
    /**
     * 交易渠道
     * 10=线下交易
     * 20=线上交易
     * 最长2
     */
    private String jyqd = "20";
    /**
     * 交易验证码
     * 由医保控件读取,具体格式：
     * PSAM 卡芯片号码|加密因子
     * 最长100
     */
    private String jyyzm;
    /**
     * 消息内容
     */
    private T xxnr;

    /**
     * 原始数据
     */
    private String sourceString;
}
