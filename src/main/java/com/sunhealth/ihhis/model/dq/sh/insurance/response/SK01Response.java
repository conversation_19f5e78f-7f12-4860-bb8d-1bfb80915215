package com.sunhealth.ihhis.model.dq.sh.insurance.response;

import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * 解码
 */
@Data
public class SK01Response implements Serializable {
    /**
     * 凭证类别 0：磁卡，1：保障卡，3：电子凭证
     */
    private String cardtype;
    /**
     * 卡号
     */
    private String cardid;
    /**
     * 姓名
     */
    private String personname;
    /**
     * 帐户标志
     */
    private String accountattr;
    /**
     * 中心流水号
     */
    private String translsh;
    /**
     * 当年帐户退回数
     */
    private BigDecimal curaccount;
    /**
     * 历年帐户退回数
     */
    private BigDecimal hisaccount;
    /**
     * 自负段现金退回数
     */
    private BigDecimal zfcash;
    /**
     * 统筹段帐户退回数
     */
    private BigDecimal tchisaccount;
    /**
     * 统筹段现金退回数
     */
    private BigDecimal tccash;
    /**
     * 统筹退回数
     */
    private BigDecimal tc;
    /**
     * 附加段帐户退回数
     */
    private BigDecimal dffjhisaccount;
    /**
     * 附加段现金退回数
     */
    private BigDecimal dffjcash;
    /**
     * 附加退回数
     */
    private BigDecimal dffj;
    /**
     * 当年帐户余额
     */
    private BigDecimal curaccountamt;
    /**
     * 历年帐户余额
     */
    private BigDecimal hisaccountamt;

    /**
     * 共济帐户退回数
     */
    private BigDecimal gjaccount;
}
