package com.sunhealth.ihhis.model.dq.sh.insurance.response;

import lombok.Data;

import java.io.Serializable;

/**
 * 解码
 */
@Data
public class SJ11Response implements Serializable {

    /**
     * 凭证类别
     * 0：磁卡，1：保障卡，3：电子凭证
     */
    private String cardtype;
    /**
     * 卡号
     */
    private String cardid;
    /**
     * 就诊单元号
     */
    private String jzdyh;
    /**
     * 姓名
     */
    private String personname;
    /**
     * 身份证号
     */
    private String sfzh;
    /**
     * 人员属性
     * 0：城保；
     */
    private String rysx;
    /**
     * 职退情况
     * 1：在职；2：退休；0：其他
     */
    private String gzqk;
    /**
     * 大病登记转出医疗机构名称
     */
    private String zcyymc;
    /**
     * 开始日期 yyyymmdd
     */
    private String startdate;
    /**
     * 结束日期 yyyymmdd
     */
    private String enddate;
    /**
     * 登记流水号
     */
    private String lsh;
    /**
     * 当年帐户余额
     */
    private String curaccountamt;
    /**
     * 历年帐户余额
     */
    private String hisaccountamt;

}
