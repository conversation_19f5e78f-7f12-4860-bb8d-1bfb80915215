## 上海市医疗保障信息系统
### 3.3 数据项格式说明
#### 3.3.1 字符串格式
格式：字符串 例如："操作人"，表示字符“操作人”  
#### 3.3.2 日期格式
格式：8 位数字 例如："20200101"，表示 2020 年 1 月 1 日  
#### 3.3.3 年月格式
格式：6 位数字 例如："202001"，表示 2020 年 1 月  
#### 3.3.4 时间格式
格式：yyyy-MM-dd HH:mm:ss（24 小时进制）  
例如："2020-01-01 12:00:00"，表示 2020 年 1 月 1 日 12 点 0 分 0 秒  
#### 3.3.5 数字格式 A
用途：主要用于带小数的金额数据  
格式：整数部分 + 小数点 + 小数部分，按字符串格式上传。  
小数部分 2 位。一般指金额信息。  
例如： "456734.35"； "456734.20"； "456734.00"；"0.00"  
#### 3.3.6 数字格式 B
用途：主要用于次数、数量等不含小数的整数  
格式：整数，按字符串格式上传。  
例如："1"；"10"；"234"  
#### 3.3.7 数字格式 C
用途：主要用于带 1 位小数的数字，且小数部分只能为 0 或 5，如住院天数。  
格式：整数部分 + 小数点 + 小数部分，按字符串格式上传。  
小数部分 1 位。  
例如： "0.5"； "2.5"；"15"；"265.0"  
#### 3.3.8 数字格式 D
用途：主要用于带 3 位小数的数字，如明细金额和数量。  
格式：整数部分 + 小数点 + 小数部分，按字符串格式上传。  
小数部分 3 位。  
例如： "0.501"； "2.500"；"15.000"；"265.123"  
#### 3.3.9 数字格式 E
用途：主要用于带 5 位小数的数字，如明细金额和数量。  
格式：整数部分 + 小数点 + 小数部分，按字符串格式上传。  
小数部分 5 位。  
例如： "0.50111"； "2.50011"；"15.00001"；"265.12311"  