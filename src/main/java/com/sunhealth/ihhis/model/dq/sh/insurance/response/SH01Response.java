package com.sunhealth.ihhis.model.dq.sh.insurance.response;

import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * 解码
 */
@Data
public class SH01Response implements Serializable {
    /**
     * 凭证类别
     * 0：磁卡，1：保障卡，3：电子凭证
     */
    private String cardtype;

    /**
     * 卡号
     */
    private String cardid;

    /**
     * 姓名
     */
    private String personname;

    /**
     * 特殊人员标识
     * 0：普通 1：离休 2：伤残 3：干部保健定点
     */
    private String personspectag;

    /**
     * 帐户标志,见字典表
     */
    private String accountattr;

    /**
     * 减免结算标志
     * 0：正常结算，1：医保减免结算，2：财政减免结算
     */
    private String jmjsbz;

    /**
     * 交易费用总额
     * 数字格式 A
     */
    private BigDecimal totalexpense;

    /**
     * 当年帐户支付数
     * 数字格式 A
     */
    private BigDecimal curaccountpay;

    /**
     * 历年帐户支付数
     * 数字格式 A
     */
    private BigDecimal hisaccountpay;

    /**
     * 自负段现金支付数
     * 数字格式 A
     */
    private BigDecimal zfdxjzfs;

    /**
     * 自负段历年帐户支付数
     * 数字格式 A
     */
    private BigDecimal zfdlnzhzfs;

    /**
     * 统筹段帐户支付数
     * 数字格式 A
     */
    private BigDecimal tcdzhzfs;

    /**
     * 统筹段现金支付数
     * 数字格式 A
     */
    private BigDecimal tcdxjzfs;

    /**
     * 统筹支付数
     * 工伤病人返回工伤基金支付数
     * 数字格式 A
     */
    private BigDecimal tczfs;

    /**
     * 附加段帐户支付数
     * 数字格式 A
     */
    private BigDecimal fjdzhzfs;

    /**
     * 附加段现金支付数
     * 数字格式 A
     */
    private BigDecimal fjdxjzfs;

    /**
     * 附加支付数
     * 数字格式 A
     */
    private BigDecimal fjzfs;

    /**
     * 当年帐户余额
     * 数字格式 A
     */
    private BigDecimal curaccountamt;

    /**
     * 历年帐户余额
     * 数字格式 A
     */
    private BigDecimal hisaccountamt;

    /**
     * 医保结算范围费用总额
     * 工伤病人返回工伤结算范围费用总额
     * 数字格式 A
     */
    private BigDecimal ybjsfwfyze;
    /**
     * 非医保结算范围费用总额
     * 工伤病人返回非工伤结算范围费用总额
     * 数字格式 A
     */
    private BigDecimal fybjsfwfyze;
    /**
     * 计算申请序号
     */
    private String jssqxh;
    /**
     * 记录册号
     */
    private String jlc;
    /**
     * 其中：减负金额
     * 数字格式 A
     */
    private String jfje;
    /**
     * 共济账户支付数
     */
    private BigDecimal gjzhzfs;
    /**
     * 自负段共济帐户支付数
     */
    private BigDecimal zfdgjzhzfs;
    /**
     * 统筹段共济帐户支付数
     */
    private BigDecimal tcdgjzhzfs;
    /**
     * 附加段共济帐户支付数
     */
    private BigDecimal fjdgjzhzfs;

}
