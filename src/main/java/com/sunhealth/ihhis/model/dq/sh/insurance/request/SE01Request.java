package com.sunhealth.ihhis.model.dq.sh.insurance.request;

import lombok.Data;

import java.io.Serializable;

/**
 * 解码
 */
@Data
public class SE01Request implements Serializable {
    /**
     * 机构 id 字符串 10 非空
     */
    private String orgId;
    /**
     * 电子凭证二维码值 字符串 80 非空
     */
    private String ecQrCode;

    /**
     * 获取二维码渠道 字符串 1 非空 1：支付宝 2：微信 3：随申办
     */
    private String ecQrChannel;

    /**
     * 用码业务类型 字符串 10 非空 见字典表
     * 挂号支付退款都用01101,缴费支付退款都用01301,互联网医院和患服用的一样
     */
    private String businessType;

    /**
     * 终端 Id 字符串 64 非空
     */
    private String termId;

    /**
     * ip 信息 字符串 20 非空
     */
    private String termIp;

    /**
     * 操作员工号 字符串 10 非空
     */
    private String operatorId;

    /**
     * 操作员姓名 字符串 50 非空
     */
    private String operatorName;

    /**
     * 医保科室编号 字符串 50 非空
     */
    private String officeId;

    /**
     * 科室名称 字符串 50 非空
     */
    private String officeName;

}
