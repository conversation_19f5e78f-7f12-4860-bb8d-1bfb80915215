package com.sunhealth.ihhis.model.dq.sh.insurance.response;

import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * 解码
 */
@Data
public class SM01Response implements Serializable {
    /**
     * 凭证类别
     * 0：磁卡，1：保障卡，3：电子凭证
     */
    private String cardtype;

    /**
     * 卡号
     */
    private String cardid;

    /**
     * 姓名
     */
    private String personname;

    /**
     * 帐户标志 见字典表
     */
    private String accountattr;

    /**
     * 当年帐户余额
     */
    private BigDecimal curaccountamt;

    /**
     * 历年帐户余额
     */
    private BigDecimal hisaccountamt;

    /**
     * 门诊自负段现金支付累计数
     */
    private BigDecimal totalmzzfdpay;

    /**
     * 住院起付线下支付累计数
     */
    private BigDecimal qfxxpay;

    /**
     * 门诊自负段定额
     */
    private BigDecimal rationpay;

    /**
     * 住院起付线定额
     */
    private BigDecimal beinqf;

    /**
     * 统筹支付封顶线定额
     */
    private BigDecimal tcfdx;

    /**
     * 起付线上封顶线费用累计
     */
    private BigDecimal qfxsfdxxfylj;

    /**
     * 记录册号 中心返回的记录册号
     */
    private String jlch;

    /**
     * 可享受医保社区减免标志 0：不享受 1：享受
     */
    private String ybsqjmbz;

    /**
     * 身份证号码第 17 位 1：奇数 2：偶数
     */
    private String xb;

}
