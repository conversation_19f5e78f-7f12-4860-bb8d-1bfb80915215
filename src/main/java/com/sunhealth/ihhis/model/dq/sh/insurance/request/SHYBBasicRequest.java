package com.sunhealth.ihhis.model.dq.sh.insurance.request;

import com.sunhealth.ihhis.config.HisHospitalProperties;
import com.sunhealth.ihhis.config.YiBaoProperties;
import com.sunhealth.ihhis.utils.AppContext;
import com.sunhealth.ihhis.utils.SequenceUtils;
import com.sunhealth.ihhis.utils.TimeUtils;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.apache.commons.lang3.StringUtils;

import java.io.Serializable;

@Data
@NoArgsConstructor
public class SHYBBasicRequest<T> implements Serializable {
    /**
     * 交易时间,时间格式
     * 最长19
     */
    private String jysj = TimeUtils.nowTimeLongString();
    /**
     * 消息类型码,填具体的消息类型码，如 SH01，SH02
     * 最长7
     */
    private String xxlxm;
    /**
     * 交易返回码,P001 代表成功，具体见 3.4 交易返回码章节说明
     * 最长4
     */
    private String xxfhm;
    /**
     * 交易返回信息,交易返回的具体详细信息。
     * 最长200
     */
    private String fhxx;
    /**
     * 交易版本号,由医保控件读取，如 0001
     * 最长4
     */
    private String bbh;
    /**
     * 报文 ID,医疗机构代码（11 位）+日期（8 位）+流水号（9 位）
     * 最长30
     */
    private String msgid;
    /**
     * 发卡地行政区划代码
     * 由医保控件读取，参照 GB/T
     * 2260-2007 中华人民共和国行政区
     * 划代码（字典代码 AAB301）, 地市
     * 编码（ 例如： 310000 上海市）,
     * 异地就医时为参保地行政区划
     * 最长6
     */
    private String xzqhdm;
    /**
     * 医疗机构代码
     * 最长20
     */
    private String jgdm;
    /**
     * 操作员编码
     * 医疗机构操作人员编码
     * 最长20
     */
    private String czybm;
    /**
     * 操作员姓名
     * 最长100
     */
    private String czyxm;
    /**
     * 交易渠道
     * 10=线下交易
     * 20=线上交易
     * 最长2
     */
    private String jyqd = "20";
    /**
     * 交易验证码
     * 由医保控件读取,具体格式：
     * PSAM 卡芯片号码|加密因子
     * 最长100
     */
    private String jyyzm;
    /**
     * 终端设备识别码
     * 若交易渠道非 HIS 刷卡，填写终端设备识别码（如移动设备 IMEI）；
     * 最长20
     */
    private String zdjbhs;
    /**
     * 消息内容
     */
    private T xxnr;

    public SHYBBasicRequest(String xxlxm) {
        this.xxlxm = xxlxm.toUpperCase();
        // 这个要用5期的
        this.jgdm = AppContext.getInstance(YiBaoProperties.class).getDqOrgCode();
        this.msgid = this.jgdm + TimeUtils.nowTimeNumberString() + StringUtils.right("00" + SequenceUtils.getSequence(999), 3);
//        this.xzqhdm = AppContext.getInstance(YiBaoProperties.class).getXzqhdm();
        this.czybm = AppContext.getInstance(HisHospitalProperties.class).getOpCode() + "";
        this.czyxm = AppContext.getInstance(HisHospitalProperties.class).getOpName();
//        this.czybm = "9999123";
//        this.czyxm = "互联网医院测试";
    }

    public SHYBBasicRequest(String xxlxm, T xxnr, String xzqhdm) {
        this(xxlxm);
        this.xxnr = xxnr;
        this.xzqhdm = xzqhdm;
    }
}
