package com.sunhealth.ihhis.model.dq.sh.insurance.request;

import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;

/**
 * 解码
 */
@Data
public class SI11Request implements Serializable {
    /**
     * 凭证类别
     * 0：磁卡，1：保障卡，3：电子凭证
     */
    private String cardtype;
    /**
     * 凭证码
     * 磁卡：28 位，保障卡：不填
     * 电子凭证：填写令牌
     */
    private String carddata;
    /**
     * 科室编码
     * 见字典表
     */
    private String deptid;
    /**
     * 特殊人员标识
     * 0：普通 1：离休 2：伤残 3：干部保健定点
     */
    private String personspectag;
    /**
     * 医疗类别
     * 见字典表
     */
    private String yllb;
    /**
     * 病人类型
     * 0：一般病人；
     * 1：工伤
     */
    private String persontype;
    /**
     * 工伤认定号
     */
    private String gsrdh;
    /**
     * 诊断编码循环体开始
     */
    private List<Disease> zdnos;
    /**
     * 大病项目代码
     * 见字典表
     */
    private String dbtype;
    /**
     * 家床结算开始日期
     * 家床结算时填写
     * 日期格式
     */
    private String jsksrq;
    /**
     * 家床结算结束日期
     * 家床结算时填写
     * 日期格式
     */
    private String jsjsrq;
    /**
     * 家床就诊次数
     * 家床结算时填写
     * 数字格式 B
     */
    private Integer jzcs;
    /**
     * 就诊单元号
     */
    private String jzdyh;
    /**
     * 线上业务类型
     * 1：一般线上交易
     * 2：互联网医院诊疗
     * 线上交易必填
     */
    private String xsywlx;
    /**
     * 明细账单号
     */
    private List<Detail> mxzdhs;

    @Data
    public static class Disease implements Serializable {
        /**
         * 诊断编码
         */
        private String zdno;
        /**
         * 诊断名称
         */
        private String zdmc;
    }

    @Data
    public static class Detail implements Serializable {
        /**
         * 明细账单号
         */
        private String mxzdh;
        /**
         * 交易费用总额
         * 数字格式 A
         */
        private BigDecimal totalexpense;
        /**
         * 医保结算范围费用总额
         * 工伤病人填写工伤结算范围费用总额
         * 数字格式 A
         */
        private BigDecimal ybjsfwfyze;
        /**
         * 非医保结算范围费用总额
         * 工伤病人填写非工伤结算范围费用总额
         * 数字格式 A
         */
        private BigDecimal fybjsfwfyze;
    }
}