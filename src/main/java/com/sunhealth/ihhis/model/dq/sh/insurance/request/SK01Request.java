package com.sunhealth.ihhis.model.dq.sh.insurance.request;

import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * 解码
 */
@Data
public class SK01Request implements Serializable {
    /**
     * 凭证类别 0：磁卡，1：保障卡，3：电子凭证
     */
    private String cardtype;
    /**
     * 凭证码 磁卡：28 位，保障卡：不填, 电子凭证：填写令牌
     */
    private String carddata;
    /**
     * 中心流水号
     */
    private String translsh;
    /**
     * 交易费用总额
     */
    private BigDecimal totalexpense;
    /**
     * 线上业务类型
     * 1：一般线上交易
     * 2：互联网医院诊疗
     * 线上交易必填
     */
    private String xsywlx;

}
