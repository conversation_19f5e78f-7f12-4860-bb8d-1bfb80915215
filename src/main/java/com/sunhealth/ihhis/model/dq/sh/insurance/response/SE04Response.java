package com.sunhealth.ihhis.model.dq.sh.insurance.response;

import com.fasterxml.jackson.core.type.TypeReference;
import com.sunhealth.ihhis.utils.StandardObjectMapper;
import lombok.Data;
import org.apache.commons.lang3.StringUtils;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 解码
 */
@Data
public class SE04Response implements Serializable {

    private String ecToken; // 令牌 字符串 63 非空
    private String payResponse; // SH02Response结构,医保结算返回包（仅 xxnr 部分）按线下结算的格式返回长度按线下返回的交易包长度为准
    private String gmt; // 支付完成时间 时间格式 非空
    private String orderNo; // 第三方支付订单号 字符串 40 可空 Channel 不等于“ybpay”时必传
    private String tradeNo; // 第三方支付平台的支付交易流水号 字符串 64 可空 Channel 不等于 “ybpay”时必传
    private BigDecimal amount; // 第三方支付金额 数字格式 A 10 可空 Channel 不等于 “ybpay”时必传
    private String channel; // 支付渠道 字符串 10 非空 wechat：微信支付 alipay：支付宝支付 unionpay：银联 ybpay：0 现金的付款
    private Date billTime; // 第三方支付平台清算日期 日期格式 40 可空 Channel 不等于“ybpay”时必传

    private SH02Response sh02Response = null;
    private SI12Response si12Response = null;

    public SH02Response getSh02Response() {
        if (sh02Response == null && StringUtils.isNotBlank(payResponse)) {
            sh02Response = StandardObjectMapper.readValue(payResponse, new TypeReference<SH02Response>() {});
        }
        return sh02Response;
    }

    public SI12Response getSi12Response() {
        if (si12Response == null && StringUtils.isNotBlank(payResponse)) {
            si12Response = StandardObjectMapper.readValue(payResponse, new TypeReference<SI12Response>() {});
        }
        return si12Response;
    }

}
