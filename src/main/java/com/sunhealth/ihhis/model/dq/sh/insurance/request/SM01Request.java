package com.sunhealth.ihhis.model.dq.sh.insurance.request;

import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * 解码
 */
@Data
@NoArgsConstructor
public class SM01Request implements Serializable {
    /**
     * 凭证类别
     * 0：磁卡，1：保障卡，3：电子凭证
     */
    private String cardtype;
    /**
     * 凭证码
     * 磁卡：28 位，保障卡：不填，电子凭证：填写令牌
     */
    private String carddata;

    public SM01Request(String cardtype, String carddata) {
        this.cardtype = cardtype;
        this.carddata = carddata;
    }
}
