package com.sunhealth.ihhis.model.dq.sh.insurance.request;

import lombok.Data;

import java.io.Serializable;

/**
 * 解码
 */
@Data
public class SH01Request implements Serializable {
    /**
     * 凭证类别, 0：磁卡，1：保障卡，3：电子凭证
     */
    private String cardtype;
    /**
     * 凭证码, 磁卡：28 位，保障卡：不填, 电子凭证：填写令牌
     */
    private String carddata;
    /**
     * 科室编码,见字典表
     */
    private String deptid;
    /**
     * 诊疗项目代码
     */
    private String zlxmdm;

    /**
     * 特殊人员标识
     * 0：普通 1：离休 2：伤残 3：干部保健定点
     */
    private String personspectag;

    /**
     * 医疗类别,见字典表
     */
    private String yllb;

    /**
     * 大病项目代码,见字典表
     */
    private String dbtype;

    /**
     * 病人类型
     * 0：一般病人；
     * 1：工伤；
     */
    private String persontype;

    /**
     * 工伤认定号
     */
    private String gsrdh;

    /**
     * 交易费用总额
     * 数字格式 A
     */
    private String totalexpense;

    /**
     * 医保结算范围费用总额,工伤病人填写工伤结算范围费用总额
     * 数字格式 A
     */
    private String ybjsfwfyze;

    /**
     * 诊疗费
     * 数字格式 A
     */
    private String zhenlf;

    /**
     * 门（急）诊诊疗费自费
     * 数字格式 A
     */
    private String ghf;

    /**
     * 非医保结算范围费用总额,工伤病人填写非工伤结算范围费用总额
     * 数字格式 A
     */
    private String fybjsfwfyze;

    /**
     * 享受社区减免标志
     * 0：不享受 1：享受非减免机构无需填写
     */
    private String jmbz;

    /**
     * 线上业务类型
     * 1：一般线上交易
     * 2：互联网医院诊疗
     * 线上交易必填
     */
    private String xsywlx;

}
