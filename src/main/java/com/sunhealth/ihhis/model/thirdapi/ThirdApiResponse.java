package com.sunhealth.ihhis.model.thirdapi;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class ThirdApiResponse<T> implements java.io.Serializable {

    private static final String SUCCESS_CODE = "0";

    private String code;
    private String msg;
    private ThirdApiResponseData<T> data;

    public boolean isSuccess() {
        return SUCCESS_CODE.equals(code);
    }

    public ThirdApiResponse(String code, String msg) {
        this.code = code;
        this.msg = msg;
    }
}
