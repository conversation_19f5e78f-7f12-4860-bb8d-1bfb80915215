package com.sunhealth.ihhis.model.dto.inpatient;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;

@Data
public class HospCardPreChargeRes {

    @ApiModelProperty(value = "成功状态")
    private String success;

    @ApiModelProperty(value = "提示信息")
    private String message;

    @ApiModelProperty(value = "押金流水号")
    private String advance_charge_id;

    @ApiModelProperty(value = "HIS订单号")
    private String out_trade_no;

    public static HospCardPreChargeRes success(String chargeId, String tradeNo) {
        HospCardPreChargeRes res = new HospCardPreChargeRes();
        res.success = "true";
        res.message = "预充值成功";
        res.advance_charge_id = chargeId;
        res.out_trade_no = tradeNo;
        return res;
    }
}
