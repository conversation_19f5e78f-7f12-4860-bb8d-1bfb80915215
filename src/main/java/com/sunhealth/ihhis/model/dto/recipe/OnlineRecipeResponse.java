package com.sunhealth.ihhis.model.dto.recipe;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
public class OnlineRecipeResponse {

    @ApiModelProperty("是否成功	Y")
    private boolean success;

    @ApiModelProperty("提示信息	N")
    private String message;

    @ApiModelProperty("错误代码	N")
    private String code;

    @ApiModelProperty("HIS处方序号	Y HIS内划价序号")
    private String hj_recipe_no;

    @ApiModelProperty("申请单序号	N")
    private String application_no;

    @ApiModelProperty("挂号序号	Y")
    private String regno;

    @ApiModelProperty("备注	N")
    private String memo;
}
