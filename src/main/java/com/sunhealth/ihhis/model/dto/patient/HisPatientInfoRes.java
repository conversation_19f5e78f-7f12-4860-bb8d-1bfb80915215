package com.sunhealth.ihhis.model.dto.patient;

import lombok.Data;

/**
 * 就诊人信息数据传输模型
 */
@Data
public class HisPatientInfoRes implements java.io.Serializable {

    // 门诊patid	Y	门诊患者唯一号
    private String patid;
    // 患者姓名	Y
    private String patname;
    // 病历号	N
    private String hiscardno;
    // 卡号	Y
    private String cardno;
    // 卡类型	Y	[0]自费卡 [1]医保卡 [2]社保卡 [3]身份证
    private String cardtype;
    // 证件号码	Y
    private String certificate_no;
    // 证件类型	Y 参考卫生信息数据元值域代码中CV02.01.101规范：
    //01身份证
    //02户口簿
    //03护照
    //04军官证
    //05驾驶证
    //06港澳居民通行证
    //07台湾居民通行证
    //99其他有效法定证件
    //20外国人永久居留证
    //21入出境通行证
    //22 旅行证
    private String certificate_type;
    // 性别	Y	男、女、未知
    private String sex;
    // 婚姻状况	N	0未婚,1已婚,2离独,3丧偶
    private String marriage;
    // 民族	N	民族中文名称
    private String nation;
    // 职业	N	职业中文名称
    private String career;
    // 出生日期	Y	格式yyyyMMdd
    private String birth;
    // 联系地址	N
    private String address;
    // 联系电话	N
    private String telephone;
    // 账户余额	N	院内就诊卡账户余额，如医院没有院内账户则为空
    private String account_balance;
    // 医保说明	N	医保类型说明
    private String chargetype_name;
    // 医保代码	N	医保类型代码(根据各家医院实际情况反馈)
    private String chargetype_code;
    // 外部电子卡号	N	需以当地医院实际规范为准
    private String virtual_card;
}
