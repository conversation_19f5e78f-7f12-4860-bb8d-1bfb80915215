package com.sunhealth.ihhis.model.dto.schedule;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

@Data
public class CurrentDoctorSourceDetails {

    @ApiModelProperty(value = "排班序号")
    private String schedulingId;

    @ApiModelProperty(value = "预约日期")
    private Date dutyDate;

    @ApiModelProperty(value = "坐诊时间类型")
    private String timeType;

    @ApiModelProperty(value = "开始时间")
    private String beginTime;

    @ApiModelProperty(value = "结束时间")
    private String endTime;

    @ApiModelProperty(value = "号源总数")
    private String sourceQty;

    @ApiModelProperty(value = "已预约总数")
    private String registedQty;

    @ApiModelProperty(value = "可预约号源总数")
    private String canRegistQty;

    @ApiModelProperty(value = "挂号费")
    private BigDecimal registrationFee;

    @ApiModelProperty(value = "诊疗费")
    private BigDecimal treatmentFee;

    @ApiModelProperty(value = "挂号费编号")
    private String registrationFeeCode;

    @ApiModelProperty(value = "诊疗费编号")
    private String treatmentFeeCode;

    @ApiModelProperty(value = "科室代码")
    private String deptId;

    @ApiModelProperty(value = "科室名称")
    private String deptName;

    @ApiModelProperty(value = "出诊类型")
    private String schedulingType;

    @ApiModelProperty(value = "就诊地址")
    private String doctorRoom;

}
