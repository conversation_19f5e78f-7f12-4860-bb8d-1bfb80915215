package com.sunhealth.ihhis.model.dto;

import java.io.Serializable;
import lombok.Data;

@Data
public class DoctorInfo implements Serializable {
    // 医生姓名	Y
    private String name;
    // 医生代码	Y	his中唯一代码
    private String code;
    // 就诊渠道	Y	0线下就诊 1线上就诊 -1全部 默认值为0，为空默认为0
    private String channel_type = "0";
    // 身份证号	Y
    private String identity;
    // 手机号	Y
    private String mobile;
    // 资格证书编号	Y
    private String certificate;
    // 执业证书编号	Y
    private String practising_number;
    // 专业特长	Y
    private String areas_of_expertise;
    // 个人简介	Y
    private String introduction;
    // 科室代码	Y
    private String dept_id;
    // 科室名称	Y
    private String dept_name;
    // 性别	N
    private String gender;
    // 民族	N
    private String nation;
    // 通信地址	N
    private String address;
    // 学历	N
    private String education;
    // 行政职务	N
    private String position;
    // 专业职称	N
    private String title;
    // 编制单位	N
    private String org_unit;
    // 编制单位编号	N
    private String org_unit_no;
    // 编制科室	N
    private String org_dept;
    // 照片	N	base64编码
    private String picture;
    // 签名	N	base64编码
    private String signature;
    // 身份证人像面	N	base64编码
    private String portrait;
    // 身份证国徽面	N	base64编码
    private String national_emblem;
    // 所属实体医院	N
    private String offline_hospital;
    // 执业证照片	N	base64编码
    private String practising_image;
    // 资格证照片	N	base64编码
    private String certification_image;

}
