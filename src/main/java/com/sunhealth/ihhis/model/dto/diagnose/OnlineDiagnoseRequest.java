package com.sunhealth.ihhis.model.dto.diagnose;

import com.sunhealth.ihhis.model.dto.recipe.Diagnose;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * 在线诊断保存
 */
@Data
public class OnlineDiagnoseRequest {

    @ApiModelProperty("门诊挂号唯一号，已挂号时必填")
    private String regno;

    @ApiModelProperty("住院号，住院时此参数必填")
    private String inpno;

    @ApiModelProperty("患者姓名")
    private String patname;

    @ApiModelProperty("患者唯一号")
    private String patid;

    @ApiModelProperty("诊断科室代码")
    private String dept_id;

    @ApiModelProperty("诊断科室名称")
    private String dept_name;

    @ApiModelProperty("诊断医生代码")
    private String doctor_id;

    @ApiModelProperty("诊断医生名称")
    private String doctor_name;

    @ApiModelProperty("完成时间")
    private String emr_done_time;

    @ApiModelProperty("类别：2线上诊疗（医生填写）")
    private String type;

    @ApiModelProperty("来源说明，与院内系统约定内容，用于显示病历来源中文说明")
    private String source_name;

    @ApiModelProperty("诊断信息")
    private List<Diagnose> zds;

    private String hospitalCode;
}
