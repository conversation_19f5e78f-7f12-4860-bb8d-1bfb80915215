package com.sunhealth.ihhis.model.dto.schedule;

import com.sunhealth.ihhis.utils.DecimalUtil;
import com.sunhealth.ihhis.utils.TimeUtils;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;
import java.util.Objects;

@Data
public class CurrentDoctorSourceDetailsRes {

    @ApiModelProperty(value = "排班序号")
    private String scheduling_id;

    @ApiModelProperty(value = "预约日期")
    private String duty_date;

    @ApiModelProperty(value = "坐诊时间类型")
    private String time_type;

    @ApiModelProperty(value = "开始时间")
    private String begin_time;

    @ApiModelProperty(value = "结束时间")
    private String end_time;

    @ApiModelProperty(value = "号源总数")
    private String source_qty;

    @ApiModelProperty(value = "已预约总数")
    private String registed_qty;

    @ApiModelProperty(value = "未预约总数")
    private String unvisit_qty;

    @ApiModelProperty(value = "可预约号源总数")
    private String can_regist_qty;

    @ApiModelProperty(value = "挂号费")
    private String registration_fee;

    @ApiModelProperty(value = "诊疗费")
    private String treatment_fee;

    @ApiModelProperty(value = "挂号费编号")
    private String registration_fee_code;

    @ApiModelProperty(value = "诊疗费编号")
    private String treatment_fee_code;

    @ApiModelProperty(value = "总金额")
    private String total_amount;

    @ApiModelProperty(value = "科室代码")
    private String dept_id;

    @ApiModelProperty(value = "科室名称")
    private String dept_name;

    @ApiModelProperty(value = "出诊类型")
    private String scheduling_type;

    @ApiModelProperty(value = "就诊地址")
    private String doctor_room;

    @ApiModelProperty(value = "儿童诊疗费")
    private String children_treatment_fee;
    @ApiModelProperty(value = "儿童诊疗费编码")
    private String children_treatment_fee_code;

    @ApiModelProperty(value = "备注")
    private String memo;

    @ApiModelProperty(value = "院区代码")
    private String organ_code;

    @ApiModelProperty(value = "院区名称")
    private String organ_name;

    @ApiModelProperty(value = "号序信息合集")
    private List<SourceNumberInfos> source_number_infos;

    public CurrentDoctorSourceDetailsRes(CurrentDoctorSourceDetails doctorSourceDetails) {
        this.scheduling_id = doctorSourceDetails.getSchedulingId();
        this.duty_date = TimeUtils.dateStringFormat(doctorSourceDetails.getDutyDate(), "yyyyMMdd");
        this.time_type = TimeUtils.getTimeType(doctorSourceDetails.getBeginTime(), doctorSourceDetails.getEndTime());
        this.begin_time = TimeUtils.joinDateAndTime(doctorSourceDetails.getDutyDate(), doctorSourceDetails.getBeginTime());
        this.end_time = TimeUtils.joinDateAndTime(doctorSourceDetails.getDutyDate(), doctorSourceDetails.getEndTime());
        this.source_qty = Objects.toString(doctorSourceDetails.getSourceQty(), "0");
        this.registed_qty = Objects.toString(doctorSourceDetails.getRegistedQty(), "0");
        int unusd_count = Integer.valueOf(this.source_qty) - Integer.valueOf(this.registed_qty);
        this.unvisit_qty = unusd_count < 0 ? "0" : String.valueOf(unusd_count);
        this.can_regist_qty = this.source_qty;
        this.registration_fee = DecimalUtil.defaultString(doctorSourceDetails.getRegistrationFee());
        this.registration_fee_code = doctorSourceDetails.getRegistrationFeeCode() != null ? doctorSourceDetails.getRegistrationFeeCode() : "";;
        this.treatment_fee = DecimalUtil.defaultString(doctorSourceDetails.getTreatmentFee());
        this.treatment_fee_code = doctorSourceDetails.getTreatmentFeeCode() != null ? doctorSourceDetails.getTreatmentFeeCode() : "";;
        this.total_amount = String.valueOf(Integer.valueOf(this.registration_fee) + Integer.valueOf(this.treatment_fee));
        this.scheduling_type = doctorSourceDetails.getSchedulingType();
        this.dept_id = Objects.toString(doctorSourceDetails.getDeptId(), "");
        this.dept_name = Objects.toString(doctorSourceDetails.getDeptName(), "");
        this.doctor_room = doctorSourceDetails.getDoctorRoom();
        this.children_treatment_fee = "0";
        this.children_treatment_fee_code = "";
    }
}
