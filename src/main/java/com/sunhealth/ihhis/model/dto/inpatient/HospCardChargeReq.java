package com.sunhealth.ihhis.model.dto.inpatient;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;

@Data
public class HospCardChargeReq {

    @NotBlank
    @ApiModelProperty(value = "患者姓名", required = true)
    private String patname;

    @NotBlank
    @ApiModelProperty(value = "住院号", required = true)
    private String regno;

    @ApiModelProperty(value = "预充值流水号")
    private String advance_charge_id;

    @ApiModelProperty(value = "HIS订单号", required = true)
    private String out_trade_no;

    @ApiModelProperty(value = "平台流水号")
    private String serial_no;

    @ApiModelProperty(value = "支付方式", required = true)
    private String pay_type;

    @ApiModelProperty(value = "支付金额", required = true)
    private String self_amount;

    @ApiModelProperty(value = "支付流水号", required = true)
    private String trade_no;

    @ApiModelProperty(value = "账户标识")
    private String account_id;

    @ApiModelProperty(value = "支付时间", required = true)
    private String pay_time;

    @ApiModelProperty(value = "支付渠道", required = true)
    private String port;

    @ApiModelProperty(value = "微信用户标识")
    private String open_id;

    @ApiModelProperty(value = "微信用户联系方式")
    private String open_phone;

    @ApiModelProperty(value = "充值人联系电话")
    private String telephone;

    @ApiModelProperty(value = "充值联系人姓名")
    private String contacts_name;

    @ApiModelProperty(value = "充值联系人证件号")
    private String contacts_certificate_no;

    private String hospitalCode;
}
