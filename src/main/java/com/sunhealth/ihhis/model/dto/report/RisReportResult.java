package com.sunhealth.ihhis.model.dto.report;

import com.sunhealth.ihhis.model.entity.report.ReportExamDetail;
import com.sunhealth.ihhis.model.entity.report.ReportExamInfo;
import com.sunhealth.ihhis.utils.TimeUtils;
import io.swagger.annotations.ApiModelProperty;
import java.io.Serializable;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.apache.commons.lang3.StringUtils;

/**
 * @Author: jzs
 * @Date: 2023-09-07
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class RisReportResult implements Serializable {
    @ApiModelProperty("检查项目	Y")
    private String item_name;	
    @ApiModelProperty("检查客观结果	Y  检查所示")
    private String report_result;
    @ApiModelProperty("检查影像报告详情链接	N ")
    private String film_report_detail_url;
    @ApiModelProperty("检查结论	Y")
    private String report_conclusion;
    @ApiModelProperty("检查部位	N")
    private String checkpoint;
    @ApiModelProperty("申请单号	N")
    private String application_no;
    @ApiModelProperty("检查时间	Y")
    private String exec_time;
    @ApiModelProperty("报告发布时间	Y	当报告出具时，报告发布时间必填")
    private String report_time;
    @ApiModelProperty("审核医生名称	N")
    private String check_doctor_name;
    @ApiModelProperty("执行医生姓名	Y")
    private String exec_doctor_name;
    @ApiModelProperty("申请医生名称	N")
    private String apply_doctor_name;
    @ApiModelProperty("患者姓名	Y")
    private String patname;
    @ApiModelProperty("患者性别	Y")
    private String sex;
    @ApiModelProperty("年龄	Y")
    private String age;


    public RisReportResult(ReportExamInfo info, ReportExamDetail detail) {
        this.item_name = detail.getItemName();
        this.report_result = info.getExamDescript();
        this.report_conclusion = info.getExamDiag();
        this.application_no = info.getApplyId();
        this.checkpoint = info.getExamSite();
        this.report_time = TimeUtils.getHisDateStr(info.getReportTime());
        this.apply_doctor_name = info.getApplyDoctorName();
        this.exec_doctor_name = info.getExamDoctorName() == null ? info.getReportDoctorName() : info.getExamDoctorName();
        this.exec_time = info.getExamTime() == null ? TimeUtils.getHisDateStr(info.getReportTime()) :
            TimeUtils.getHisDateStr(info.getExamTime());
//        if (info.getReportUrl() != null && !info.getReportUrl().startsWith("http")) {
//            this.film_report_detail_url =
//                "http://*************:82/xds/outIndex.php?appuser=1&hiscode2=" + info.getApplyId();
//        } else {
//            this.film_report_detail_url = info.getReportUrl();
//        }
    }
}
