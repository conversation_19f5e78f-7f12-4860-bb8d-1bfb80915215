package com.sunhealth.ihhis.model.dto.outpatientcharge;

import java.io.Serializable;
import lombok.Data;

@Data
public class PreChargeReq implements Serializable {

    /**
     * 门诊patid  门诊患者唯一号  Y
     */
    private String patid;

    /**
     * 卡号  N
     */
    private String card_no;

    /**
     * 卡类型  [0]自费卡 [1]医保卡 [2]社保卡  N
     */
    private String card_type;

    /**
     * 门诊挂号序号  通过门诊待缴费处方查询获取，调用门诊待缴费处方，获取未收费处方所关联的挂号序号(regno)  Y
     */
    private String regno;

    /**
     * 划价序号合集  收费项目序号合集。（格式：109928,1090899,109089）调用门诊待缴费处方，获取未收费处方的处方序号  Y
     */
    private String recipe_no_list;

    /**
     * 是否扣院内账户  0不从院内账户走1走院内账户  Y
     */
    private String hosp_account_flag;

    /**
     * 是否自费结算  0根据病人医保代码结算1自费结算  N
     */
    private String self_flag;

    /**
     * 支付方式默认是1，0 现金 1 微信小程序 2支付宝 3 微信公众号
     */
    private String pay_type;

    /**
     * 医保交易入参  中间用竖线隔开，没有传空
     * 微信医保：authCode授权码|username用户姓名|city_id用户参保地代码|pay_auth_no医保线上核验|user_longitude用户定位经度|user_latitude用户定位纬度|ec_token医保token|user_card_no患者卡号
     * N
     */
    private String insurance_param;

    private String hospitalCode;

}
