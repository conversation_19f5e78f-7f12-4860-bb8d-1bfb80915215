package com.sunhealth.ihhis.model.dto.inpatient;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
public class HospCardChargeRes {

    @ApiModelProperty(value = "成功状态")
    private boolean success;

    @ApiModelProperty(value = "提示信息")
    private String message;

    @ApiModelProperty(value = "预交金序号")
    private String advance_charge_id;

    @ApiModelProperty(value = "当前余额")
    private String advance_account_amount;

    @ApiModelProperty(value = "HIS订单号")
    private String out_trade_no;

    public static HospCardChargeRes success(String chargeId, String amount, String tradeNo) {
        HospCardChargeRes chargeRes = new HospCardChargeRes();
        chargeRes.success = true;
        chargeRes.message = "充值成功";
        chargeRes.advance_charge_id = chargeId;
        chargeRes.advance_account_amount = amount;
        chargeRes.out_trade_no = tradeNo;
        return chargeRes;
    }

    public static HospCardChargeRes fail(String chargeId, String tradeNo) {
        HospCardChargeRes chargeRes = new HospCardChargeRes();
        chargeRes.success = false;
        chargeRes.message = "充值失败";
        chargeRes.advance_charge_id = chargeId;
        chargeRes.out_trade_no = tradeNo;
        return chargeRes;
    }
}
