package com.sunhealth.ihhis.model.dto.register;

import lombok.Data;

@Data
public class PreRegistrationResult implements java.io.Serializable {

    // 挂号序号	Y
    private String regno;
    // HIS结算单号 Y
    private String settle_id;
    // 总金额	Y	本次结算发生的总金额
    private String total_amount;
    // 自负金额	Y	患者应该自己承担的费用（=总金额-优惠金额-医保支付-商保直赔金额+舍入金额-账户支付）
    private String self_pay;
    // 应付金额	Y	应该支付的金额（=自负金额-账户支付），应该使用支付宝或微信等支付的金额
    private String should_pay_amount;
    // 医保统筹账户	N	医保统筹账户
    private String pub_pay;
    // 优惠金额	N	医院给予病人的优惠金额
    private String discount_amount;
    // 医保个人账户支付	N
    private String pub_account_pay;
    // 商保直赔金额	N	符合商保直赔患者报销金额
    private String commercial_insurance_pay;
    // 舍入金额	N
    private String error_cents;
    // 账户余额	N	医院内预付费账户的余额
    private String account_balance;
    // 账户支付	N	医院内预付费账户支付
    private String hosp_account_pay;
    // 备注	N
    private String memo;
    // 医保交易出参	N	占位，具体参数格式内容需要根据当地医保确定
    private String insurance_param;
    // 医院下单时间	N	当使用医保支付时，这个字段必须yyyyMMddHHmmss
    private String gmt_out_create;
    // 医院HIS系统订单号（医疗机构订单号）	N	当使用医保支付时，这个字段必须
    private String medical_order_no;
    // 医保对应费用明细上传的出参单号	N	当使用医保支付时，这个字段必传，对应[6201]中的payOrdId
    private String pay_order_id;
    // 5期医保对应, 生成的唯一ID, 对应微信医保支付的bill_no
    private String bill_no;
}
