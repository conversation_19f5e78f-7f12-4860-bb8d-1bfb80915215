package com.sunhealth.ihhis.model.dto.outpatientcharge;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
public class RefundResponse {

        @ApiModelProperty(value = "成功状态 Y 成功true 失败false", example = "true")
        private Boolean success;

        @ApiModelProperty(value = "提示信息 Y", example = "退款成功")
        private String message;

        @ApiModelProperty(value = "退收据号 Y", example = "TRN123456789")
        private String refund_settle_id;

        @ApiModelProperty(value = "医保出参 N 占位，具体参数格式内容需要根据当地医保确定", example = "param1=value1|param2=value2")
        private String insurance_param;

        @ApiModelProperty(value = "退款时间 Y 如果是医保订单，并且医保同步取消，应该返回医保确认退款时间，格式yyyyMMddHHmmss", example = "20250529103000")
        private String refund_time;
}
