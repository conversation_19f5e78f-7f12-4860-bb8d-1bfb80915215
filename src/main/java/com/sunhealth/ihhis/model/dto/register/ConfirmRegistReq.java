package com.sunhealth.ihhis.model.dto.register;


import com.sunhealth.ihhis.model.dq.sh.insurance.response.SE04Response;
import com.sunhealth.ihhis.model.insurance.response.Output6301;
import lombok.Data;

import java.io.Serializable;

@Data
public class ConfirmRegistReq implements Serializable {
    private String patid;                   // 门诊 patid   Y  门诊患者唯一号
    private String regno;                   // 挂号序号   Y  本次挂号的HIS唯一码，门诊就诊流水号。
    private String settle_id;               // HIS结算单号 Y  预算时HIS反馈的HIS结算收据号
    private String serial_no;               // 平台流水号 N  第三方平台流水号，标记唯一一次业务请求，(挂号预算HIS返回收据号为空时，此参数必填)
    private String appointment_id;          // 预约序号 N  如果已经预约了，则必须传预约序号
    private String lock_number_id;          // 锁号序号 N  由《4.3.3.查询指定排班的号序信息》返回， 当班挂号未预约时传入。如已经预约则此字段不传，预约序号必填。
    private String scheduling_id;           // 排班明细序号 N  和预算时一致
    private String source_number;           // 排班号序   N  和预算时一致
    private String total_amount;            // 总金额    Y  预算时返回
    private String should_pay_amount;       // 应付金额   Y  预算时返回
    private String pay_type;                   // 支付方式   Y  0 现金 1 微信支付 2支付宝支付
    private String pay_amount;              // 支付金额   Y  支付金额
    private String trade_no;                // 支付流水号 N  应为支付的唯一流水号，用于对账。支付方式不为0时，则必填。
    private String receipt_account;         // 账户标识 N  医院收款账户标识，默认为空，医院使用多个支付账户情况下非空，此时HIS账单接口原样传出，以便于多账户对账
    private String hosp_account_flag;          // 是否扣院内账户 N  与预算保持一致
    private String self_flag;                  // 是否自费结算 N  与预算保持一致，0根据病人医保代码结算1自费结算
    private String port;                    // 支付渠道   Y  特别约定时必填
    private String pay_card_no;             // 代币卡序号  N  多张卡支付时以|分隔
    private String pay_card_amount;         // 代币卡支付金额 N  多张卡支付时以|分隔
    private String pay_card_flag;              // 代币卡使用标志 N  使用代币卡支付时传入1，默认为空
    private String insurance_param;         // 医保交易入参 N  占位，具体参数格式内容需要根据当地医保确定
    private String commercial_insurance_param; // 商保结算信息 N  存在商保结算时需传入，具体以MAP JSON格式字符串传入
    private String extra_content;           // 扩展信息   N  个性化化字段扩展信息，在医院项目里单独处理
    /**
     * 就诊渠道 Y 0：线下就诊；1：线上就诊；
     */
    private String channel_type;
    /**
     * 支付时间	Y	支付方扣款时间，用于处理对账跨天问题。格式yyyyMMddHHmmss
     */
    private String pay_time;
    private String hospitalCode;

    private Output6301 output6301;
    private SE04Response se04Response;

    /**
     * 2 国家医保
     * 其他值 上海医保
     */
    private int jsFlag;
}


