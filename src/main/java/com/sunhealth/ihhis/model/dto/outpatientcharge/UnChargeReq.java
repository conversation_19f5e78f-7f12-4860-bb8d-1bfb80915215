package com.sunhealth.ihhis.model.dto.outpatientcharge;

import java.io.Serializable;
import java.util.Date;
import lombok.Data;

@Data
public class UnChargeReq implements Serializable {
    private String patname;
    private String patid;
    private String begin_date;
    private String end_date;
    private String insurance_param;
    private String channel_type;
    private String card_no;
    private String card_type;
    private String hospitalCode;
    private Date beginDate;
    private Date endDate;
}
