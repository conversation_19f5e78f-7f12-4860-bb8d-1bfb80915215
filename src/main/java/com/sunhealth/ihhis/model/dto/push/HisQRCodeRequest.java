package com.sunhealth.ihhis.model.dto.push;

import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
public class HisQRCodeRequest {

    @ApiModelProperty("二维码类型	Y	1微信小程序 2支付宝小程序")
    private String qr_code_type;

    @ApiModelProperty("业务类型	Y	1门诊待缴费项目页面 2住院催款单页面 3静态住院预交页面")
    private String type;

    @ApiModelProperty("挂号序号或住院号 N	门诊待缴费必传、住院催款单必传、静态住院预交非必传")
    private String regno;

    @ApiModelProperty("患者唯一号	N 门诊待缴费必传、住院催款单必传、静态住院预交非必传")
    private String patid;

    @ApiModelProperty("金额	N 住院催收单金额 单位：分")
    private String amount;
}
