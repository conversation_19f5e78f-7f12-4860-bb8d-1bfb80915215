package com.sunhealth.ihhis.model.dto.register;

import java.io.Serializable;
import lombok.Data;

/**
 * @Author: jzs
 * @Date: 2023-09-05
 * 门诊预约登记出参
 */
@Data
public class SaveAppointmentReq implements Serializable {

    private String patid;             // HIS患者ID y
    private String scheduling_id;      // 排班ID y
    private String source_number;      // 号源 n
    private String telephone;         // 电话号码 n
    private String serial_no;          // 流水号 n
    private String op_certificate_no;   // 操作凭证号 n
    private String memo;              // 备注
    private String hospitalCode;
}
