package com.sunhealth.ihhis.model.dto.schedule;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
public class DeptListRes {

    @ApiModelProperty(value = "科室代码")
    private String dept_id;

    @ApiModelProperty(value = "科室名称")
    private String dept_name;

    @ApiModelProperty(value = "科室简介")
    private String dept_introduction;

    @ApiModelProperty(value = "一级科室代码")
    private String first_dept_id;

    @ApiModelProperty(value = "一级科室名称")
    private String first_dept_name;

    @ApiModelProperty(value = "二级科室代码")
    private String second_dept_id;

    @ApiModelProperty(value = "二级科室名称")
    private String second_dept_name;

    @ApiModelProperty(value = "拼音")
    private String inputcode1;

    @ApiModelProperty(value = "五笔")
    private String inputcode2;

    @ApiModelProperty(value = "出诊类型")
    private String scheduling_type;
}
