package com.sunhealth.ihhis.model.dto.outpatientcharge;

import io.swagger.annotations.ApiModelProperty;
import java.io.Serializable;
import java.util.List;
import lombok.Data;

/**
 * @Author: jzs
 * @Date: 2023-09-30
 * 缴费记录处方
 */
@Data
public class OutpatientChargeRecipeInfo implements Serializable {
    @ApiModelProperty("处方序号	Y")
    private String recipe_no;
    @ApiModelProperty("联动处方序号	N")
    private String link_recipe_no;
    @ApiModelProperty("挂号序号	Y	门诊挂号序号")
    private String regno;
    @ApiModelProperty("处方类型	Y	1:西药处方,2:中药处方,3:草药处方,4:治疗处方,5:检查处方,6体检处方,7:自动挂号产生挂号收费信息，8:检验处方")
    private String recipe_type;
    @ApiModelProperty("开方时间	Y	格式yyyyMMddHHmmss")
    private String recipe_time;
    @ApiModelProperty("开方科室代码	Y")
    private String recipe_dept_id;
    @ApiModelProperty("开方科室名称	Y")
    private String recipe_dept_name;
    @ApiModelProperty("开方医生代码	Y")
    private String recipe_doctor_id;
    @ApiModelProperty("开方医生名称	Y")
    private String recipe_doctor_name;
    @ApiModelProperty("审核状态	Y	0未审核 1已审核")
    private String check_status;
    @ApiModelProperty("处方名称	N	处方中文显示名称，默认为空根据医院约定部分特殊处方显示此名称")
    private String recipe_name;
    @ApiModelProperty("药品/项目信息	Y")
    private String items;
    @ApiModelProperty("医保说明	N	该处方对应的挂号记录所登记的医保类型说明")
    private String chargetype_name;
    @ApiModelProperty("医保代码	N	该处方对应的挂号记录所登记的医保类型说明(根据各家医院实际情况反馈)")
    private String chargetype_code;
    @ApiModelProperty("特殊病种代码	N")
    private String cure_item;
    @ApiModelProperty("特殊病种名称	N")
    private String cure_item_name;
    @ApiModelProperty("记录状态	Y	0正常 1已退费 2红冲记录")
    private String record_status;
    @ApiModelProperty("结算状态	Y	0未结算 1预算完成 2已正常结算 -1无需结算(仅限自备标识=1时)")
    private String settle_status;
    @ApiModelProperty("就诊渠道	N	0线下就诊 1线上就诊 -1全部")
    private String channel_type;
    @ApiModelProperty("申请单序号	N")
    private String application_no;
    @ApiModelProperty("长处方标志	Y	0普通 1长处方")
    private String long_recipe_flag;
    @ApiModelProperty("延伸处方标志	Y	0普通 1延伸处方")
    private String extend_recipe_flag;
    @ApiModelProperty("嘱托	N	处方主数据嘱托")
    private String entrust_content;
    @ApiModelProperty("明细嘱托	N	处方明细数据嘱托")
    private String detail_entrust_content;
    @ApiModelProperty("执行科室代码	N")
    private String exec_dept_id;
    @ApiModelProperty("执行科室名称	N")
    private String exec_dept_name;
    @ApiModelProperty("执行地址	N")
    private String exec_address;
    @ApiModelProperty("扩展信息	N")
    private String extra_content;
    @ApiModelProperty("流转处方药房标志	Y	0常规处方 1院外流转处方")
    private String hosp_flag;
    @ApiModelProperty("自备标识	Y	0非自备(院内取药) 1自备")
    private String oneself_flag;
    @ApiModelProperty("外送标志	N")
    private String delivery_flag;
    @ApiModelProperty("配送类型	N	0院内现场取药不配送1 医院药房负责配送2 第三方平台配送")
    private String delivery_type;
    @ApiModelProperty("发药窗口	N")
    private String drug_window;
    @ApiModelProperty("配发药状态	N	0未发药，1已发药")
    private String drug_status;
    @ApiModelProperty("发药时间	N")
    private String drug_time;
    @ApiModelProperty("发药号	N	药房发药流水号(具体参考医院发药模式)")
    private String drug_dispensing_id;
    @ApiModelProperty("院区代码	N")
    private String organ_code;
    @ApiModelProperty("院区名称	N")
    private String organ_name;
    @ApiModelProperty("药品/项目数据   Y")
    private List<ItemInfo> item_infos;
}
