package com.sunhealth.ihhis.model.dto.inpatient;

import com.sunhealth.ihhis.utils.DecimalUtil;
import com.sunhealth.ihhis.utils.TimeUtils;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
public class AdvanceChargeDetailRes {

    @ApiModelProperty(value = "预交金序号")
    private String advance_charge_id;

    @ApiModelProperty(value = "收据号")
    private String settle_id;

    @ApiModelProperty(value = "平台流水号")
    private String serial_no;

    @ApiModelProperty(value = "发票号")
    private String invoice_no;

    @ApiModelProperty(value = "药费日期")
    private String pay_time;

    @ApiModelProperty(value = "预交金额")
    private String advance_amount;

    @ApiModelProperty(value = "支付方式")
    private String pay_type;

    @ApiModelProperty(value = "操作类型")
    private String op_type;

    @ApiModelProperty(value = "记录状态")
    private String record_status;

    @ApiModelProperty(value = "备注")
    private String memo;

    public AdvanceChargeDetailRes(AdvanceChargeDetail advanceChargeDetail) {
        this.advance_charge_id = advanceChargeDetail.getAdvanceChargeId();
        this.settle_id = advanceChargeDetail.getSettleId();
        this.serial_no = advanceChargeDetail.getSerialNo();
        this.pay_time = TimeUtils.dateStringFormat(advanceChargeDetail.getPayTime(), "yyyyMMddHHmmss");
        this.advance_amount = DecimalUtil.defaultString(advanceChargeDetail.getAdvanceAmount());
        this.pay_type = advanceChargeDetail.getPayType();
        this.record_status = advanceChargeDetail.getRecordStatus();
        this.memo = advanceChargeDetail.getMemo();
    }
}
