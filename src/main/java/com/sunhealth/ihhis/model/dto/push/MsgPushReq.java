package com.sunhealth.ihhis.model.dto.push;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Map;

@Data
public class MsgPushReq {

    @ApiModelProperty("挂号流水号")
    private String regno;

    @ApiModelProperty("就诊类别 1门诊 2住院")
    private String user_source;

    @ApiModelProperty("消息类型")
    private String msg_type;

    @ApiModelProperty("业务流水号")
    private String operation_sn;

    @ApiModelProperty("消息详情")
    private Map<String, Object> msg_details;
}
