package com.sunhealth.ihhis.model.dto.report;

import io.swagger.annotations.ApiModelProperty;
import java.io.Serializable;
import java.util.Date;
import lombok.Data;

/**
 * 检验报告查询请求参数
 */
@Data
public class ReportListReq implements Serializable {
    @ApiModelProperty("就诊人姓名	Y")
    private String patname;
    @ApiModelProperty("就诊人id	Y  cardno/certificate_no/patid三选一")
    private String patid;
    @ApiModelProperty("就诊卡号   Y	cardno/certificate_no/patid三选一")
    private String cardno;
    @ApiModelProperty("身份证号   Y	cardno/certificate_no/patid三选一")
    private String certificate_no;
    @ApiModelProperty("1门诊 2住院	Y")
    private String user_source;
    @ApiModelProperty("开始时间	Y")
    private String begin_date;
    @ApiModelProperty("结束时间	Y")
    private String end_date;

    private Date beginTime;
    private Date endTime;

    private String hospitalCode;

}
