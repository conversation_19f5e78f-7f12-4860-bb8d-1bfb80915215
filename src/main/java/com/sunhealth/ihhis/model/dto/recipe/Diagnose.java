package com.sunhealth.ihhis.model.dto.recipe;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 诊断
 */
@Data
public class Diagnose {
    @ApiModelProperty("诊断代码	N")
    private String diagnose_code;

    @ApiModelProperty("诊断名称	Y")
    private String diagnose_name;

    @ApiModelProperty("医保诊断代码	N")
    private String icd_code;

    @ApiModelProperty("医保诊断名称	N")
    private String icd_name;

    @ApiModelProperty("诊断序号	N 0：主要诊断，1：第一辅诊，2：第二辅诊")
    private Integer zdxh;

    @ApiModelProperty("中西医诊断标志	N 0西医诊断 1中医诊断")
    private Integer diag_flag;
}
