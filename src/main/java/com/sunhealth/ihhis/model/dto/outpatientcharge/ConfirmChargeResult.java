package com.sunhealth.ihhis.model.dto.outpatientcharge;

import java.io.Serializable;
import lombok.Data;

@Data
public class ConfirmChargeResult implements Serializable {

    /**
     * 成功状态  true  false  Y
     */
    private String success;

    /**
     * 提示信息  Y
     */
    private String message;

    /**
     * HIS结算单号  HIS一次结算单据号(标记一次结算的唯一号)。本地门诊费用结算对应的HIS唯一流水号，请妥善保存，用于对账  Y
     */
    private String settle_id;

    /**
     * 收费日期  格式yyyyMMddHHmmss  Y
     */
    private String charge_time;

    /**
     * 电子收据号  仅限有电子发票情况下输出  N
     */
    private String elec_invoice_no;

    /**
     * 电子票据二维码数据  仅限当地有电子发票方案的医院  N
     */
    private String elec_invoice_qrcode;

    /**
     * 电子票据URL  仅限当地有电子发票方案的医院  N
     */
    private String elec_invoice_url;

    /**
     * 备注  N
     */
    private String memo;

    /**
     * 配药窗口集合  N
     */
    private String drug_window_list;

    /**
     * 发药窗口集合  N
     */
    private String dispensing_window_list;

    /**
     * 发药药房集合  N
     */
    private String drug_dept_list;

    /**
     * 发药号  药房发药流水号(具体参考医院发药模式)  N
     */
    private String drug_dispensing_id;


}
