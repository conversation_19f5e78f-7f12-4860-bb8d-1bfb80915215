package com.sunhealth.ihhis.model.dto.report;

import com.sunhealth.ihhis.model.entity.report.ReportTestDetail;
import com.sunhealth.ihhis.model.entity.report.ReportTestInfo;
import com.sunhealth.ihhis.utils.TimeUtils;
import io.swagger.annotations.ApiModelProperty;
import java.io.Serializable;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * @Author: jzs
 * @Date: 2023-09-07
 * 实验室检验报告
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class LisReportResult implements Serializable {
    @ApiModelProperty("检验指标名称	Y")
    private String item_name;
    @ApiModelProperty("检验指标代码	Y")
    private String item_code;
    @ApiModelProperty("检验结果	N")
    private String result;
    @ApiModelProperty("结果单位	N   如果检验结果需要单位，此字段必传")
    private String unit;
    @ApiModelProperty("异常标记	N	如果检验结果有，则此字段必传 H偏高，L偏低，P异常，NM正常")
    private String abnormal_flag;
    @ApiModelProperty("危急值标记 N   如果出现危急值，则此字段必传")
    private String critical_flag;
    @ApiModelProperty("结果参考值范围	N")
    private String reference_value;
    @ApiModelProperty("检验项目名称	Y")
    private String report_item_name;
    @ApiModelProperty("标本类型	Y")
    private String specimen;
    @ApiModelProperty("采样日期	Y")
    private String spec_time;
    @ApiModelProperty("检验日期	Y")
    private String exec_time;
    @ApiModelProperty("报告时间	Y")
    private String report_time;
    @ApiModelProperty("申请时间	Y")
    private String apply_time;
    @ApiModelProperty("检验医生	Y")
    private String exec_doctor_name;
    @ApiModelProperty("送检时间	Y")
    private String censorship_time;
    @ApiModelProperty("审核医生	Y")
    private String check_doctor_name;
    @ApiModelProperty("患者姓名	Y")
    private String patname;
    @ApiModelProperty("患者性别	Y")
    private String sex;
    @ApiModelProperty("年龄	Y")
    private String age;

    public LisReportResult(ReportTestInfo info, ReportTestDetail detail) {
        this.item_name = detail.getItemName();
        this.item_code = detail.getItemCode();
        this.result = detail.getItemResult();
        this.unit = detail.getResultUnit();
        this.reference_value = detail.getRefValue();
        this.specimen = info.getSpeimenTypeName();
        this.spec_time = TimeUtils.getHisDateStr(info.getSpeimentSampleTime());
        this.exec_time = TimeUtils.getHisDateStr(info.getTestTime());
        this.report_time = TimeUtils.getHisDateStr(info.getReportTime());
        this.apply_time = TimeUtils.getHisDateStr(info.getApplyTime());
        this.censorship_time = TimeUtils.getHisDateStr(info.getSpeimentReceiveTime());
        this.exec_doctor_name = info.getTestDoctorName();
        this.check_doctor_name = info.getAuditorDoctorName();
        this.patname = info.getPatName();
        this.abnormal_flag = detail.getAbnormalFlag();
//        this.critical_flag = detail();
        this.report_item_name = info.getInspectionItemName();
    }
}
