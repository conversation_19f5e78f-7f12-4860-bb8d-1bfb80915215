package com.sunhealth.ihhis.model.dto.inpatient;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;

@Data
public class HospCardPreChargeReq {

    @NotBlank
    @ApiModelProperty(value = "患者姓名", required = true)
    private String patname;

    @NotBlank
    @ApiModelProperty(value = "住院号", required = true)
    private String regno;

    private String hospitalCode;
}
