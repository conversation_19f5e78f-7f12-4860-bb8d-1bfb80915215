package com.sunhealth.ihhis.model.dto.patient;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;

@Data
public class HisPatientInfoReq {

    @NotBlank
    @ApiModelProperty(value = "患者姓名", required = true)
    private String patname;

    @NotBlank
    @ApiModelProperty(value = "患者身份证号", required = true)
    private String certificate_no;

    @ApiModelProperty(value = "就诊卡号码")
    private String cardno;

    @ApiModelProperty(value = "联系电话")
    private String telephone;

    @ApiModelProperty(value = "患者ID")
    private Long patid;

    private Integer hospitalCode;
}
