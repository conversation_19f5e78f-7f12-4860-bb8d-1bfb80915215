package com.sunhealth.ihhis.model.dto.outpatientcharge;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

@Data
public class RefundRequest {
        @ApiModelProperty("病人唯一码 Y")
        private String patid;

        @ApiModelProperty("挂号序号 Y")
        private String regno;

        @ApiModelProperty("收据号 Y")
        private String settle_id;

        @ApiModelProperty("退处方合集 N 待退费的处方数据集")
        private List<String> tcflist;

        @ApiModelProperty("退支付方式 Y 默认是1，0 现金 1 微信小程序支付 2支付宝支付 3 微信公众号支付 4 北京移动医保支付")
        private String paytype;

        @ApiModelProperty("退支付金额 Y 自费：自费支付金额 医保：医保部分金额，因为his负责退医保部分，平台负责退现金部分，注意：B_TradeHisBill接口中退款总金额需要把现金+医保部分都给到")
        private String paymoney;

        @ApiModelProperty("原支付流水号 Y 应为支付的唯一流水号，用于对账")
        private String paylsh;

        @ApiModelProperty("退流水号 N 退费流水号")
        private String canclelsh;

        @ApiModelProperty("账户标识 Y 标识支付账户，区分医院多个账户，供对账时使用")
        private String receipt_account;

        @ApiModelProperty("退款时间 Y 线上退款时间，格式yyyyMMddHHmmss")
        private String refund_time;

        @ApiModelProperty("医保入参 N 中间用竖线隔开，没有传空 微信医保：authCode授权码|username用户姓名|city_id用户参保地代码|pay_auth_no医保线上核验|user_longitude用户定位经度|user_latitude用户定位纬度|ec_qr_code 医保qrcode|user_card_no患者卡号")
        private String insurance_param;

        private String hospitalCode;
}
