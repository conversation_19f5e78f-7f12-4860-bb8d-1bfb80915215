package com.sunhealth.ihhis.model.dto.schedule;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;

@Data
public class SourceNumberReq {

    @NotBlank
    @ApiModelProperty(value = "排班序号", required = true)
    private String scheduling_id;

    @ApiModelProperty(value = "挂号序号")
    private String source_number;

    private String hospitalCode;
}
