package com.sunhealth.ihhis.model.dto.bill;

import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

@Data
public class BillOrder {

    // 自费或医保订单类型 Y
    private String order_type;

    // 支付类型 Y
    private String pay_type;

    // 交易时间 Y
    private Date transaction_time;

    // 交易流水号 N
    private String transaction_id;

    // 平台流水号 N
    private String serial_no;

    // 收据号 N
    private String settle_id;

    // 交易状态 Y
    private String transaction_status;

    // 应结订单金额 N
    private BigDecimal settlement_order_amount;

    // 代金券金额 N
    private BigDecimal voucher_amount;

    // 商户退款单号 Y
    private String merchant_refund_number;

    // 退款总金额 Y
    private BigDecimal refund_amount;

    // 退款金额（自费） N
    private BigDecimal self_refund_amount;

    // 退款金额（医保） N
    private BigDecimal medicare_refund_amount;

    // 订单支付金额（自费）N
    private BigDecimal self_order_amount;

    // 订单支付金额（医保）N
    private BigDecimal medicare_order_amount;

    // 医保支付订单号 N
    private String pay_order_id;

    // 充值券退款金额 N
    private BigDecimal recharge_voucher_refund_amount;

    // 退款类型 Y
    private String refund_type;

    // 退款状态 Y
    private String refund_status;

    // 商品名称 Y
    private String product_name;

    // 订单总金额 Y
    private BigDecimal order_amount;

    // 申请退款金额 N
    private BigDecimal refund_application_amount;
}
