package com.sunhealth.ihhis.model.dto.recipe;

import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import java.util.List;

/**
 * 在线处方
 */
@Getter
@Setter
public class OnlineRecipeDeleteRequest {

    @ApiModelProperty("患者姓名	Y")
    private String patname;

    @ApiModelProperty("患者唯一标识	Y")
    private String patid;

    @ApiModelProperty("挂号序号	Y 门诊挂号序号")
    private String regno;

    @ApiModelProperty("处方序号 N")
    private String recipe_no;

    private String hospitalCode;
}