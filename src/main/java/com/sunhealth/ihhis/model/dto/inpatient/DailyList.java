package com.sunhealth.ihhis.model.dto.inpatient;

import lombok.Data;

import java.util.Date;

@Data
public class DailyList {

    // 大项目名称
    private String itemCategoryName;

    // "费用生成时间"
    private Date feeTime;

    // "费用项目代码"
    private String itemCode;

    // "费用项目名称"
    private String itemName;

    // "药品标识"
    private String drugFlag;

    // "项目单位"
    private String unit;

    // "项目单价"
    private String price;

    // "项目数量"
    private String quantity;

    // "规格"
    private String gauge;

    // "总金额"
    private String amount;
}
