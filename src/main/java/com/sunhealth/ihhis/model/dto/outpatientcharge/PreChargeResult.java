package com.sunhealth.ihhis.model.dto.outpatientcharge;

import lombok.Data;

import java.io.Serializable;

@Data
public class PreChargeResult implements Serializable {

    /**
     * 门诊挂号序号  Y
     */
    private String regno;

    /**
     * HIS结算单号  预算时HIS反馈的HIS结算收据号，用于结算时传参  Y
     */
    private String settle_id;

    /**
     * 总金额  Y
     */
    private String total_amount;

    /**
     * 自负金额  患者应该自己承担的费用（=总金额-优惠金额-医保支付-商保直赔金额+舍入金额）  Y
     */
    private String self_amount;

    /**
     * 医保支付金额  医保统筹基金支付金额（单位：分）  N
     */
    private String pub_pay;

    /**
     * 优惠金额  Y
     */
    private String discount_amount;

    /**
     * 医保个人账户支付金额  医保个人账户支付金额（单位：分）  N
     */
    private String pub_account_pay;

    /**
     * 商保直赔金额  符合商保直赔患者报销金额  N
     */
    private String commercial_insurance_pay;

    /**
     * 舍入金额  N
     */
    private String error_cents;

    /**
     * 账户支付  院内预付费账户支付  N
     */
    private String hosp_account_pay;

    /**
     * 账户余额  院内预付费账户的余额  N
     */
    private String hosp_account_balance;

    /**
     * 应付金额  本次最终应该支付的金额（=自负金额-账户支付），应该使用支付宝或微信等支付的金额  Y
     */
    private String should_pay_amount;

    /**
     * 备注  N
     */
    private String memo;

    /**
     * 医保交易出参  医保端所需参数，自费病人为空，此参数仅限支持医保脱卡支付医院(统一采用BASE64编码转换)，具体参数格式根据各地医保参照附件说明  N
     */
    private String insurance_param;

    /**
     * 医院下单时间  医院调用医保6202接口下单的时间，当使用医保支付时，这个字段必传，平台给微信接口使用yyyyMMddHHmmss  N
     */
    private String gmt_out_create;

    /**
     * 医院HIS系统订单号（医疗机构订单号）  当使用医保支付时，这个字段必传  N
     */
    private String medical_order_no;

    /**
     * 医保对应费用明细上传的出参单号  当使用医保支付时，这个字段必传，对应[6201]中的payOrdId  N
     */
    private String pay_order_id;
    // 5期医保对应, 生成的唯一ID, 对应微信医保支付的bill_no
    private String bill_no;

}
