package com.sunhealth.ihhis.model.dto.electronicInvoice;

import lombok.Data;

/**
 * 电子发票列表查询请求参数
 */
@Data
public class ElectronicInvoiceReq {

    // 页码	Y	数值型，要取第几页数据。
    private int page;
    // 每页条数	Y	数值型，每页返回多少条记录。
    private int size;
    // 病人唯一码	Y
    private String patid;
    // 在线类型	N	-1全部 0线上 1线下
    private String online_type;

    private String hospitalCode;
    private int opCode;
}
