package com.sunhealth.ihhis.model.dto.medicalrecord;

import io.swagger.annotations.ApiModelProperty;
import java.io.Serializable;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
public class PatientMedicalRecordInfo implements Serializable {

    @ApiModelProperty("病历文档段代码")
    private String document_code; // 病历文档段代码 Y S主观病历（主诉、病史等） O客观病历（体征、检验检查结果等） A 诊断 P治疗处置
    @ApiModelProperty("病历文档段名称")
    private String document_name; // 病历文档段名称 Y
    @ApiModelProperty("病历文本内容")
    private String document_content; // 病历文本内容 N
    @ApiModelProperty("显示序号")
    private Integer number;

    public PatientMedicalRecordInfo(String name, String content, Integer number) {
        this.document_code = name;
        this.document_name = name;
        this.document_content = content;
        this.number = number;
    }
}
