package com.sunhealth.ihhis.model.dto.bill;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import java.util.Date;

@Data
public class HisBillReq {

    @NotBlank
    @ApiModelProperty(value = "日期", required = true)
    private String bill_date;

    @ApiModelProperty(value = "支付方式 0-全部 1-微信小程序支付 2-支付宝支付 3-微信公众号支付 默认0")
    private String pay_type = "0";

    private Date beginDate;

    private Date endDate;

    private String hospitalCode;

    private int opCode;
}
