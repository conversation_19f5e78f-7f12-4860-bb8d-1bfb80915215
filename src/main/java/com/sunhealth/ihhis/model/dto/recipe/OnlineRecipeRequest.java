package com.sunhealth.ihhis.model.dto.recipe;

import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import java.util.List;

/**
 * 在线处方
 */
@Getter
@Setter
public class OnlineRecipeRequest {

    @ApiModelProperty("患者唯一标识	Y")
    private String patid;

    @ApiModelProperty("就诊渠道	N 0线下就诊 1线上就诊 -1全部，默认0")
    private Integer channel_type;

    @ApiModelProperty("挂号序号	Y 门诊挂号序号")
    private String regno;

    @ApiModelProperty("处方类型	Y 1:西药, 2:中药, 3:草药, 4:治疗, 5:医技, 6:体检, 7:自动挂号, 8:检验")
    private Integer recipe_type;

    @ApiModelProperty("开方时间	Y 格式yyyyMMddHHmmss")
    private String recipe_time;

    @ApiModelProperty("开方科室代码	Y")
    private String recipe_dept_id;

    @ApiModelProperty("开方科室名称	Y")
    private String recipe_dept_name;

    @ApiModelProperty("开方医生代码	Y")
    private String recipe_doctor_id;

    @ApiModelProperty("开方医生名称	Y")
    private String recipe_doctor_name;

    @ApiModelProperty("审核状态	Y 0未审核 1已审核")
    private Integer check_status;

    @ApiModelProperty("处方名称	N 默认空，根据医院约定部分特殊处方显示此名称")
    private String recipe_name;

    @ApiModelProperty("嘱托	N 处方主数据嘱托")
    private String entrust_content;

    @ApiModelProperty("执行科室代码	N")
    private String exec_dept_id;

    @ApiModelProperty("执行科室名称	N")
    private String exec_dept_name;

    @ApiModelProperty("执行地址	N")
    private String exec_address;

    @ApiModelProperty("扩展信息	N")
    private String extra_content;

    @ApiModelProperty("医保标识	N 0医保内 1医保外")
    private Integer insur_flag;

    @ApiModelProperty("规则校验标志	Y 0保存 1仅校验 2判断并保存（默认0）")
    private Integer verify_flag;

    @ApiModelProperty("自备标识	Y 0非自备(院内取药) 1自备")
    private Integer oneself_flag;

    @ApiModelProperty("配送类型	N 0院内现场取药 1医院药房配送 2第三方平台配送")
    private Integer delivery_type;

    @ApiModelProperty("诊断信息")
    private List<Diagnose> diagnose_list;

    @ApiModelProperty("药品/项目数据")
    private List<RecipeItem> item_infos;

    private String hospitalCode;
}