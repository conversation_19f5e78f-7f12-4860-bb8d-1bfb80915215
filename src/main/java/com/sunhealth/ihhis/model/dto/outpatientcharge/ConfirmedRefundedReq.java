package com.sunhealth.ihhis.model.dto.outpatientcharge;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;

@Data
public class ConfirmedRefundedReq {

    @NotBlank
    @ApiModelProperty("门诊patid")
    private String patid;

    @NotBlank
    @ApiModelProperty("退款业务类型")
    private String refund_type;

    @NotBlank
    @ApiModelProperty("退款settle_id")
    private String settle_id;

    @NotBlank
    @ApiModelProperty("自费部分退款金额")
    private String refund_amount;

    @NotBlank
    @ApiModelProperty("退款时间")
    private String refund_time;

    @NotBlank
    @ApiModelProperty("成功状态")
    private String status;

    private String hospitalCode;
}
