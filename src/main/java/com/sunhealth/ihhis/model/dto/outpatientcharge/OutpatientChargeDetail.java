package com.sunhealth.ihhis.model.dto.outpatientcharge;

import io.swagger.annotations.ApiModelProperty;
import java.io.Serializable;
import java.util.List;
import lombok.Data;

/**
 * @Author: jzs
 * @Date: 2023-09-29
 */
@Data
public class OutpatientChargeDetail implements Serializable {
    
    @ApiModelProperty("结算收据号	Y	HIS一次结算单据号(标记一次结算的唯一号)")
    private String settle_id;

    private String total_fee;
    private String self_fee;
    private String insurance_fee;
    @ApiModelProperty("处方信息")
    private List<OutpatientChargeRecipeInfo> recipe_infos;

}
