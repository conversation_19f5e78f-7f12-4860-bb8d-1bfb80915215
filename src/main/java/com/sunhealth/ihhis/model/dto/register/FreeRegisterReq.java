package com.sunhealth.ihhis.model.dto.register;


import com.sunhealth.ihhis.model.dto.outpatientcharge.CreateHuLiDaoJiaChargeReq;
import lombok.Data;

/**
 * 免费挂号请求参数
 */
@Data
public class FreeRegisterReq {

    /**
     * 门诊patid - 门诊患者唯一号 Y
     */
    private String patid;
    private String cardno; //卡号	Y
    private String cardtype; //	卡类型	Y	[0]自费卡 [1]医保卡 [2]社保卡 [3]身份证
    /**
     * 患者姓名 Y
     */
    private String patname;

    /**
     * 科室id
     */
    private String deptid;
    /**
     * 医生id
     */
    private String workid;

    private String begin_time;

    private String hospitalCode;

    public FreeRegisterReq(CreateHuLiDaoJiaChargeReq req) {
        this.begin_time = req.getPay_time();
        this.patid = req.getPatid();
        this.deptid = req.getRecipe_dept_id();
        this.workid = req.getRecipe_doctor_id();
        this.hospitalCode = req.getHospitalCode();
        this.patname = req.getPatname();
        this.cardno = req.getCardno();
        this.cardtype = req.getCardtype();
    }

}

