package com.sunhealth.ihhis.model.dto.outpatientcharge;

import java.util.List;
import lombok.Data;

@Data
public class CreateHuLiDaoJiaChargeReq {
    private String patid; //	患者唯一标识	Y
    private String patname; //	患者姓名	Y
    private String cardno; //卡号	Y
    private String cardtype; //	卡类型	Y	[0]自费卡 [1]医保卡 [2]社保卡 [3]身份证
    private String recipe_time; //	开方时间	Y	格式yyyyMMddHHmmss
    private String pay_time; //	支付时间	Y	格式yyyyMMddHHmmss
    private String recipe_dept_id; //开方科室代码	Y
    private String recipe_dept_name; //开方科室名称	Y
    private String recipe_doctor_id; //开方医生代码	Y
    private String recipe_doctor_name; //	开方医生名称	Y
    private String serial_no; //平台流水号	Y	平台方业务订单id，标记唯一一次业务请求，如互联网医院平台订单号
    private String trade_no; //支付流水号	Y	应为支付的唯一流水号，用于对账。支付方式不为0时，则必填。
    private String pay_type; //支付方式	Y	默认是1，0 现金 1 微信小程序 2支付宝 3 微信公众号
    private int pay_amount; //支付金额	Y	实际支付的金额，包括交通费 单位：人民币分
    private int traffic_amount; //交通费	Y	单位：人民币分
    private int item_amount; //收费项目总金额	Y	不包括交通费，单位：人民币分
    private List<ThirdAddAccountItem> item_infos; //	收费项目数据	Y	JSONArray格式
    private String hospitalCode;
}
