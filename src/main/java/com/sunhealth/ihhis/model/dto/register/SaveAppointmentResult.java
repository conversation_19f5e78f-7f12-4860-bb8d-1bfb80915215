package com.sunhealth.ihhis.model.dto.register;

import io.swagger.annotations.ApiModelProperty;
import java.io.Serializable;
import lombok.Data;

/**
 * @Author: jzs
 * @Date: 2023-09-05
 * 门诊预约登记出参
 */
@Data
public class SaveAppointmentResult implements Serializable {

    @ApiModelProperty("成功状态   Y true  false")
    private String success;
    @ApiModelProperty("提示信息	Y")
    private String message;
    @ApiModelProperty("预约序号	Y	标记唯一一条预约记录的唯一号")
    private String appointment_id;
    @ApiModelProperty("科室代码	Y")
    private String dept_id;
    @ApiModelProperty("科室名称	Y")
    private String dept_name;
    @ApiModelProperty("医生代码	N	科室挂号时为空")
    private String doctor_id;
    @ApiModelProperty("医生名称	N	科室挂号时为空")
    private String doctor_name;
    @ApiModelProperty("挂号日期	Y	预约就诊日期")
    private String visit_date;
    @ApiModelProperty("预约日期	Y	预约录入日期")
    private String appoint_date;
    @ApiModelProperty("预约号序	Y")
    private String source_number;
    @ApiModelProperty("挂号费	Y")
    private String registration_fee;
    @ApiModelProperty("诊疗费	Y")
    private String treatment_fee;
    @ApiModelProperty("应收金额	Y")
    private String should_pay_amount;
    @ApiModelProperty("总金额	Y")
    private String total_amount;
    @ApiModelProperty("备注	    N")
    private String memo;
    @ApiModelProperty("时间点	N")
    private String visit_time;
    @ApiModelProperty("预约时段	    N")
    private String time_span;
    @ApiModelProperty("预约就诊地址	N")
    private String doctor_room;
}
