package com.sunhealth.ihhis.model.dto.recipe;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 药品/项目明细
 */
@Data
public class RecipeItem {
    @ApiModelProperty("药品、项目代码	Y")
    private String item_code;

    @ApiModelProperty("药品、项目名称	Y")
    private String item_name;

    @ApiModelProperty("明细嘱托	N")
    private String detail_entrust_content;

    @ApiModelProperty("药品标识	Y")
    private Integer drug_flag;

    @ApiModelProperty("药品单位	N")
    private String unit;

    @ApiModelProperty("临床项目代码	N")
    private String clinical_item_code;

    @ApiModelProperty("临床项目名称	N")
    private String clinical_item_name;

    @ApiModelProperty("收费大项目名称	N")
    private String set_item_name;

    @ApiModelProperty("医保对应代码	N")
    private String insurance_code;

    @ApiModelProperty("药品规格	N")
    private String drug_spec;

    @ApiModelProperty("剂型名称	N")
    private String dosage_form_name;

    @ApiModelProperty("剂型代码	N")
    private String dosage_form_code;

    @ApiModelProperty("频次名称	N")
    private String frequency;

    @ApiModelProperty("频次代码	Y")
    private String frequency_code;

    @ApiModelProperty("用法代码	N")
    private String usage_code;

    @ApiModelProperty("用法名称	N")
    private String usage;

    @ApiModelProperty("药品剂量	N")
    private String dosage;

    @ApiModelProperty("剂量单位	N")
    private String dosage_unit;

    @ApiModelProperty("中药煎法	N")
    private String decocting_method;

    @ApiModelProperty("用药天数	N")
    private String use_days;

    @ApiModelProperty("自付比例	N")
    private String self_rate;

    @ApiModelProperty("项目单价	Y")
    private String price;

    @ApiModelProperty("数量	Y")
    private String quantity;

    @ApiModelProperty("总金额	Y")
    private String amount;
}
