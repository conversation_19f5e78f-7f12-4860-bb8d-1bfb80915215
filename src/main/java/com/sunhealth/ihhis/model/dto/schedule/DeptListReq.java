package com.sunhealth.ihhis.model.dto.schedule;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import java.util.Date;

@Data
public class DeptListReq {

    @NotBlank
    @ApiModelProperty(value = "开始日期 格式yyyyMMdd", required = true)
    private String begin_date;

    @NotBlank
    @ApiModelProperty(value = "结束日期 格式yyyyMMdd", required = true)
    private String end_date;

    @ApiModelProperty(value = "就诊渠道")
    private String channel_type;

    private String hospitalCode;

    private Date beginDate;

    private Date endDate;
}
