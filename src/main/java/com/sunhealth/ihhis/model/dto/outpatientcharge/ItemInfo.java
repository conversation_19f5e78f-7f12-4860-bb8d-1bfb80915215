package com.sunhealth.ihhis.model.dto.outpatientcharge;

import io.swagger.annotations.ApiModelProperty;
import java.io.Serializable;
import lombok.Data;

/**
 * @Author: jzs
 * @Date: 2023-09-27
 * 药品/项目数据
 */
@Data
public class ItemInfo implements Serializable {
    @ApiModelProperty("药品、项目代码	Y	医院内唯一码(drug_id)")
    private String item_code;
    @ApiModelProperty("药品、项目名称	Y")
    private String item_name;
    @ApiModelProperty("药品标识	Y	1药品 2项目")
    private String drug_flag;
    @ApiModelProperty("临床项目代码	N")
    private String clinical_item_code;
    @ApiModelProperty("临床项目名称	N")
    private String clinical_item_name;
    @ApiModelProperty("收费大项目名称	N")
    private String set_item_name;
    @ApiModelProperty("医保对应代码	N	医保脱卡支付项目时该字段不为空")
    private String insurance_code;
    @ApiModelProperty("药品规格	N")
    private String drug_spec;
    @ApiModelProperty("剂型代码	N")
    private String dosage_form_code;
    @ApiModelProperty("剂型名称	N")
    private String dosage_form_name;
    @ApiModelProperty("频次代码	N")
    private String frequency_code;
    @ApiModelProperty("频次名称	N")
    private String frequency;
    @ApiModelProperty("用法名称	N")
    private String usage;
    @ApiModelProperty("用法代码	N")
    private String usage_code;
    @ApiModelProperty("药品剂量	N")
    private String dosage;
    @ApiModelProperty("剂量单位	N")
    private String dosage_unit;
    @ApiModelProperty("药品单位	N")
    private String unit;
    @ApiModelProperty("中药煎法	N")
    private String decocting_method;
    @ApiModelProperty("药天数	N")
    private String use_days;
    @ApiModelProperty("自付比例	N	医保脱卡支付项目时该字段不为空")
    private String self_rate;
    @ApiModelProperty("项目单价	Y")
    private String price;
    @ApiModelProperty("数量	Y")
    private String quantity;
    @ApiModelProperty("总金额	Y	该单个项目或药品的总金额")
    private String amount;
}
