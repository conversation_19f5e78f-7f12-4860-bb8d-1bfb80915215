package com.sunhealth.ihhis.model.dto.electronicInvoice;

import lombok.Data;

/**
 * 电子发票响应值
 */
@Data
public class ElectronicInvoice {
    // 病人唯一码	Y
    private String patid;
    // 电子收据号	Y
    private String elec_invoice_no;
    // 发票抬头	N
    private String invoice_title;
    // 开票机构	Y
    private String invoicing_entity;
    // HIS订单号	Y
    private String out_trade_no;
    // 就诊时间	Y	对应订单关联的就诊时间，如果就诊类型为住院，则就诊时间为入院时间点
    private String visit_time;
    // 订单类型	Y	0挂号、1门诊、2住院
    private String trade_type;
    // 在线类型	Y	0 线上 1线下
    private String online_type;
    // 患者名字	Y
    private String pat_name;
    // 	缴费金额	Y	单位：分
    private String amount;
    // 开具状态	Y	0未开具（未开具的电子发票的订单）1开具中2已开具
    private String operation_status;
}
