package com.sunhealth.ihhis.model.dto.schedule;

import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

@Data
public class DeptSourceDetails {

    //排版序号
    private String schedulingId;

    //医生代码
    private String doctorId;

    //坐诊时间id
    private String timespanId;
    
    //预约日期
    private Date dutyDate;

    //坐诊开始时间
    private String dutyStartTime;

    //坐诊结束时间
    private String dutyEndTime;

    //坐诊时间类型名称
    private String timeTypeName;

    //开始时间
    private String beginTime;

    //结束时间
    private String endTime;

    //号源总数
    private Integer sourceQty;

    //已预约总数
    private Integer usedSourceQty;

    //挂号费
    private BigDecimal registrationFee;

    //诊疗费
    private BigDecimal treatmentFee;

//    //儿童诊疗费
//    private String childrenTreatmentFee;

    //挂号费编号
    private String registrationFeeCode;

    //诊疗费编号
    private String treatmentFeeCode;

//    //儿童诊疗费编号
//    private String children_treatment_fee_code;

    //排班名称
    private String schedulingName;

    //出诊类型
    private String schedulingType;

    //就诊地址
    private String doctorRoom;

//    //备注
//    private String memo;
//
//    //院区代码
//    private String organ_code;
//
//    //院区名称
//    private String organ_name;
}
