package com.sunhealth.ihhis.model.dto.register;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
public class GetOutpatientPayReq {

    @ApiModelProperty("HIS收据号 Y 预算时HIS反馈的HIS结算收据号")
    private String settle_id; // HIS收据号 Y 预算时HIS反馈的HIS结算收据号

    @ApiModelProperty("支付类别 Y 0门诊预约挂号 1门诊缴费结算 2住院预交金充值 3住院结算 4互联网医院挂号 5互联网医院处方")
    private String trade_type; // 支付类别 Y 0门诊预约挂号 1门诊缴费结算 2住院预交金充值 3住院结算 4互联网医院挂号 5互联网医院处方

    @ApiModelProperty("支付流水号 N 第三方支付机构流水号，对应结算接口的trade_no传入的入参")
    private String trade_no; // 支付流水号 N 第三方支付机构流水号，对应结算接口的trade_no传入的入参

}
