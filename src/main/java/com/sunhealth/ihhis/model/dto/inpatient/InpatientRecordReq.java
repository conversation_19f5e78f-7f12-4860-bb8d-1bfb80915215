package com.sunhealth.ihhis.model.dto.inpatient;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import java.util.Date;

@Data
public class InpatientRecordReq {

    @NotBlank
    @ApiModelProperty(value = "患者姓名", required = true)
    private String patname;

    @ApiModelProperty(value = "患者唯一号", required = true)
    private String patid;

    @ApiModelProperty(value = "住院号")
    private String hospno;

    @NotBlank
    @ApiModelProperty(value = "在院状态")
    private String status;


    @ApiModelProperty(value = "开始日期 格式yyyyMMdd")
    private String begin_date;

    @ApiModelProperty(value = "开始日期 格式yyyyMMdd")
    private String end_date;

    private Date beginDate;

    private Date endDate;

    private String hospitalCode;
}
