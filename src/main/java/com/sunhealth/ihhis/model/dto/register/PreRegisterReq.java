package com.sunhealth.ihhis.model.dto.register;


import javax.validation.constraints.NotBlank;
import lombok.Data;

/**
 * 挂号预算请求参数
 */
@Data
public class PreRegisterReq {

    /**
     * 门诊patid - 门诊患者唯一号 Y
     */
    private String patid;

    /**
     * 患者姓名 Y
     */
    private String patname;

    /**
     * 排班明细序号 Y
     */
    private String scheduling_id;

    /**
     * 卡号
     */
    private String cardno;

    /**
     * 卡类型 [0]自费卡 [1]医保卡 [2]社保卡 [3]身份证
     */
    private String cardtype;

    /**
     * 预约序号 如果已经预约了，则必须传预约序号(标记一条预约记录的唯一号，由《门诊预约接口》反馈此字段)
     */
    private String appointment_id;

    /**
     * 排班号序 由《查询指定排班的号序信息》反馈此字段
     */
    private String source_number;

    /**
     * 开始时间 由《查询指定排班的号序信息》反馈此字段
     */
    private String begin_time;

    /**
     * 挂号费编号 号源信息中返回的数据 Y
     */
    @NotBlank(message = "挂号费编号不能为空")
    private String registration_fee_code;

    /**
     * 诊疗费编号 号源信息中返回的数据 Y
     */
    private String treatment_fee_code;

    /**
     * 儿童诊疗费编号 号源信息中返回的数据
     */
    private String children_treatment_fee_code;

    /**
     * 是否扣院内账户 0不从院内账户走1走院内账户 总是为0
     */
    private String hosp_account_flag;

    /**
     * 是否自费结算 0根据病人医保代码结算1自费结算
     */
    private String self_flag;

    /**
     * 医保交易入参 占位，具体参数格式内容需要根据当地医保确定
     */
    private String insurance_param;

    /**
     * 来源说明
     */
    private String source_name;

    /**
     * 就诊渠道 0：线下就诊；1：线上就诊； Y
     */
    private String channel_type;

    /**
     * 执行挂号操作人员的证件号 终端用户身份证号(执行预约操作的者的身份证号或其他证件号，可能与患者并非同一人)，作为统一防黄牛入口记录，根据医院防黄牛机制，动态设定黑名单机制
     */
    private String czrcertificate_no;

    /**
     * 绿色通道挂号序号 绿色通道补缴时必传，需调用绿通挂号接口获取
     */
    private String green_channel_number;

    private String lock_number_id;

    private String hospitalCode;

    /**
     * 默认是1，0 现金 1 微信小程序 2支付宝 3 微信公众号
     */
    private String pay_type;
}

