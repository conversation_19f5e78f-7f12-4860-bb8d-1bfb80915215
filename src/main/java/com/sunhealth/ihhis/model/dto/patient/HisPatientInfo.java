package com.sunhealth.ihhis.model.dto.patient;

import lombok.Data;

import java.util.Date;

/**
 * 就诊人信息数据传输模型
 */
@Data
public class HisPatientInfo implements java.io.Serializable {

    // 门诊patid	Y	门诊患者唯一号
    private Long patid;
    // 患者姓名	Y
    private String patname;
    // 病历号	N
    private String hisCardNo;
    // 卡号	Y
    private String cardNo;
    // 卡类型	Y	[0]自费卡 [1]医保卡 [2]社保卡 [3]身份证
    private String cardType;
    // 证件号码	Y
    private String certificateNo;
    // 证件类型	Y 参考卫生信息数据元值域代码中CV02.01.101规范：
    private String certificateType;
    // 性别	Y	男、女、未知
    private Integer sex;
    // 婚姻状况	N	0未婚,1已婚,2离独,3丧偶
    private Integer marriage;
    // 民族
    private Integer nation;
    // 职业
    private Integer occupation;
    // 出生日期	Y	格式yyyyMMdd
    private Date birthday;
    // 联系地址	N
    private String address;
    // 联系电话	N
    private String telephone;
    // 账户余额	N	院内就诊卡账户余额，如医院没有院内账户则为空
    private String accountBalance;
    // 医保代码	N	医保类型代码(根据各家医院实际情况反馈)
    private Integer chargeType;
    // 外部电子卡号	N	需以当地医院实际规范为准
    private String virtualCard;
}
