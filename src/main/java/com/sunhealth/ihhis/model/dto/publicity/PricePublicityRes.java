package com.sunhealth.ihhis.model.dto.publicity;

import com.sunhealth.ihhis.model.entity.PubItems;
import com.sunhealth.ihhis.utils.DecimalUtil;
import lombok.Data;

@Data
public class PricePublicityRes {

    private String name;

    private String code;

    private String price;

    private String description;

    private String pinyin;

    public PricePublicityRes(PubItems pubItems) {
        this.name = pubItems.getItemName();
        this.code = pubItems.getItemCode();
        this.price = DecimalUtil.defaultString(pubItems.getRetailPrice(), 2);
        this.pinyin = pubItems.getInputCode1();
    }
}
