package com.sunhealth.ihhis.model.dto.outpatientcharge;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
public class ConfirmedRefundedRes {

    @ApiModelProperty("成功状态")
    private String success;

    @ApiModelProperty("提示消息")
    private String message;

    public static ConfirmedRefundedRes success() {
        ConfirmedRefundedRes res = new ConfirmedRefundedRes();
        res.setSuccess("true");
        return res;
    }

    public static ConfirmedRefundedRes fail(String message) {
        ConfirmedRefundedRes res = new ConfirmedRefundedRes();
        res.setSuccess("true");
        res.setMessage(message);
        return res;
    }
}
