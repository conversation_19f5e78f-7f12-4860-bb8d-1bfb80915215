package com.sunhealth.ihhis.model.dto.push;

import lombok.Data;

import java.util.Map;

@Data
public class Msg {

    // 身份证号码 N
    private String certificate_no;

    // 患者姓名 Y
    private String pat_name;

    // 患者唯一号 Y
    private String patid;

    // 卡号 N
    private String card_no;

    // 医院代码 Y
    private String organ_code;

    // 医院名称 Y
    private String organ_name;

    // 就诊流水号 N
    private String regno;

    // 就诊类别 Y
    private String user_source;

    // 消息数据类型 Y
    private String msg_type;

    // 业务流水号 N
    private String operation_sn;

    // 消息推送时间 Y
    private String timestamp;

    // 推送内容详情 N
    private Map<String, Object> msg_details;

    // 推送请求ID Y
    private String request_id;

    private String hospitalCode;
}
