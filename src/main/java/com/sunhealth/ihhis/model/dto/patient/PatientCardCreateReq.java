package com.sunhealth.ihhis.model.dto.patient;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;

@Data
public class PatientCardCreateReq {
        // 患者姓名	Y
        @NotBlank
        @ApiModelProperty(value = "患者姓名", required = true)
        private String patname;

        // 证件号码	Y
        @NotBlank
        @ApiModelProperty(value = "证件号码", required = true)
        private String certificate_no;
        // 证件类型	Y	参考卫生信息数据元值域代码中CV02.01.101规范：
        // 01身份证
        // 02户口簿
        // 03护照
        // 04军官证
        // 05驾驶证
        // 06港澳居民通行证
        // 07台湾居民通行证
        // 99其他有效法定证件
        // 20外国人永久居留证
        // 21入出境通行证
        // 22旅行证
        // 为空的情况下默认为01身份证
        @NotBlank
        @ApiModelProperty(value = "证件号码", required = true)
        private String certificate_type;

        // 性别	Y	男、女、未知
        @NotBlank
        @ApiModelProperty(value = "性别", required = true)
        private String sex;

        // 婚姻状况	N	0未婚,1已婚,2离独,3丧偶
        @ApiModelProperty(value = "婚姻状况 0未婚,1已婚,2离独,3丧偶")
        private Integer marriage;

        // 民族	N	民族中文名称
        @ApiModelProperty(value = "民族")
        private String nation;

        // 职业	N	职业中文名称
        @ApiModelProperty(value = "职业")
        private String career;

        // 出生日期	Y	格式yyyyMMdd
        @ApiModelProperty(value = "出生日期")
        private String birth;

        // 联系地址	N
        @ApiModelProperty(value = "联系地址")
        private String address;

        // 联系电话	N
        @ApiModelProperty(value = "联系电话")
        private String telephone;

        private Integer hospitalCode;
}
