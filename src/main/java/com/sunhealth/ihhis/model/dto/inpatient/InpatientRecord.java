package com.sunhealth.ihhis.model.dto.inpatient;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

@Data
public class InpatientRecord {

    @ApiModelProperty(value = "住院号")
    private String regno;

    @ApiModelProperty(value = "入院诊断")
    private String diagnoseName;

    @ApiModelProperty(value = "患者唯一号")
    private String patid;

    @ApiModelProperty(value = "病历号")
    private String hiscardno;

    @ApiModelProperty(value = "患者姓名")
    private String patname;

    @ApiModelProperty(value = "性别")
    private String sex;

    @ApiModelProperty(value = "出生日期")
    private Date birth;

    @ApiModelProperty(value = "证件号")
    private String certificateNo;

    @ApiModelProperty(value = "卡号")
    private String cardno;

    @ApiModelProperty(value = "联系人姓名")
    private String contactsName;

    @ApiModelProperty(value = "联系人关系")
    private String contactsRelationship;

    @ApiModelProperty(value = "联系人电话")
    private String contactsTelephone;

    @ApiModelProperty(value = "联系地址")
    private String address;

    @ApiModelProperty(value = "联系电话")
    private String telephone;

    @ApiModelProperty(value = "病人状态")
    private String status;

    @ApiModelProperty(value = "入院日期")
    private Date inTime;

    @ApiModelProperty(value = "出院日期")
    private Date outTime;

    @ApiModelProperty(value = "结算日期")
    private Date endDate;

    @ApiModelProperty(value = "科室名称")
    private String deptName;

    @ApiModelProperty(value = "病区名称")
    private String wardName;

    @ApiModelProperty(value = "床位号")
    private String bedNo;

    @ApiModelProperty(value = "医生名称")
    private String doctorName;

    @ApiModelProperty(value = "预交金累计充值")
    private BigDecimal advanceTotalAmount;

    @ApiModelProperty(value = "预交金余额")
    private BigDecimal advanceAccountAmount;

    @ApiModelProperty(value = "已发生费用总金额")
    private BigDecimal yfstotalAmount;

    @ApiModelProperty(value = "优惠金额")
    private BigDecimal discountAmount;

    @ApiModelProperty(value = "已发生费用自付金额")
    private BigDecimal selfAmount;

    @ApiModelProperty(value = "医保报销金额")
    private BigDecimal pubPay;

    @ApiModelProperty(value = "院区代码")
    private String organCode;

    @ApiModelProperty(value = "院区名称")
    private String organName;

    @ApiModelProperty(value = "医保代码")
    private String chargetypeCode;

    @ApiModelProperty(value = "医保说明")
    private String chargetypeName;
}
