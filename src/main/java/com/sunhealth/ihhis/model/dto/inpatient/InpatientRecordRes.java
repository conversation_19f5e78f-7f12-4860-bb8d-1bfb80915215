package com.sunhealth.ihhis.model.dto.inpatient;

import com.sunhealth.ihhis.utils.DecimalUtil;
import com.sunhealth.ihhis.utils.TimeUtils;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import java.math.BigDecimal;

@Data
public class InpatientRecordRes {

    @ApiModelProperty(value = "住院号")
    private String regno;

    @ApiModelProperty(value = "入院诊断")
    private String diagnose_name;

    @ApiModelProperty(value = "患者唯一号")
    private String patid;

    @ApiModelProperty(value = "病历号")
    private String hiscardno;

    @ApiModelProperty(value = "患者姓名")
    private String patname;

    @ApiModelProperty(value = "性别")
    private String sex;

    @ApiModelProperty(value = "出生日期")
    private String birth;

    @ApiModelProperty(value = "证件号")
    private String certificate_no;

    @ApiModelProperty(value = "卡号")
    private String cardno;

    @ApiModelProperty(value = "联系人姓名")
    private String contacts_name;

    @ApiModelProperty(value = "联系人关系")
    private String contacts_relationship;

    @ApiModelProperty(value = "联系人电话")
    private String contacts_telephone;

    @ApiModelProperty(value = "联系地址")
    private String address;

    @ApiModelProperty(value = "联系电话")
    private String telephone;

    @ApiModelProperty(value = "病人状态")
    private String status;

    @ApiModelProperty(value = "入院日期")
    private String in_time;

    @ApiModelProperty(value = "出院日期")
    private String out_time;

    @ApiModelProperty(value = "结算日期")
    private String end_date;

    @ApiModelProperty(value = "科室名称")
    private String dept_name;

    @ApiModelProperty(value = "病区名称")
    private String ward_name;

    @ApiModelProperty(value = "床位号")
    private String bed_no;

    @ApiModelProperty(value = "医生名称")
    private String doctor_name;

    @ApiModelProperty(value = "预交金累计充值")
    private String advance_total_amount;

    @ApiModelProperty(value = "预交金余额")
    private String advance_account_amount;

    @ApiModelProperty(value = "已发生费用总金额")
    private String yfstotal_amount;

    @ApiModelProperty(value = "优惠金额")
    private String discount_amount;

    @ApiModelProperty(value = "已发生费用自付金额")
    private String self_amount;

    @ApiModelProperty(value = "医保报销金额")
    private String pub_pay;

    @ApiModelProperty(value = "院区代码")
    private String organ_code;

    @ApiModelProperty(value = "院区名称")
    private String organ_name;

    @ApiModelProperty(value = "医保代码")
    private String chargetype_code;

    @ApiModelProperty(value = "医保说明")
    private String chargetype_name;

    public InpatientRecordRes(InpatientRecord inpatientRecord) {
        this.regno = inpatientRecord.getRegno();
        this.diagnose_name = inpatientRecord.getDiagnoseName();
        this.patid = inpatientRecord.getPatid();
        this.hiscardno = inpatientRecord.getHiscardno();
        this.patname = inpatientRecord.getPatname();
        this.sex = inpatientRecord.getSex();
        this.birth = TimeUtils.dateStringFormat(inpatientRecord.getBirth(),"yyyyMMddHHmmss");
        this.certificate_no = inpatientRecord.getCertificateNo();
        this.cardno = inpatientRecord.getCardno();
        this.contacts_name = inpatientRecord.getContactsName();
        this.contacts_relationship = inpatientRecord.getContactsRelationship();
        this.contacts_telephone = inpatientRecord.getContactsTelephone();
        this.address = inpatientRecord.getAddress();
        this.telephone = inpatientRecord.getTelephone();
        this.status = inpatientRecord.getStatus();
        this.in_time = TimeUtils.dateStringFormat(inpatientRecord.getInTime(),"yyyyMMddHHmmss");
        this.out_time = TimeUtils.dateStringFormat(inpatientRecord.getOutTime(),"yyyyMMddHHmmss");
        this.end_date = TimeUtils.dateStringFormat(inpatientRecord.getEndDate(),"yyyyMMddHHmmss");
        this.dept_name = inpatientRecord.getDeptName();
        this.ward_name = inpatientRecord.getWardName();
        this.bed_no = inpatientRecord.getBedNo();
        this.doctor_name = inpatientRecord.getDoctorName();
        this.advance_total_amount = DecimalUtil.defaultString(inpatientRecord.getAdvanceTotalAmount());
        this.advance_account_amount = DecimalUtil.defaultString(inpatientRecord.getAdvanceAccountAmount());
        this.yfstotal_amount = DecimalUtil.defaultString(inpatientRecord.getYfstotalAmount());
        this.discount_amount = DecimalUtil.defaultString(inpatientRecord.getDiscountAmount());
        this.self_amount = DecimalUtil.defaultString(inpatientRecord.getSelfAmount());
        this.pub_pay = DecimalUtil.defaultString(inpatientRecord.getPubPay());
        this.organ_code = inpatientRecord.getOrganCode();
        this.organ_name = inpatientRecord.getOrganName();
        this.chargetype_code = inpatientRecord.getChargetypeCode();
        this.chargetype_name = inpatientRecord.getChargetypeName();
    }
}
