package com.sunhealth.ihhis.model.dto;

import com.sunhealth.ihhis.model.entity.Dept;
import java.io.Serializable;
import lombok.Data;

@Data
public class DeptInfo implements Serializable {
    // 科室代码	Y
    private String dept_id;
    // 科室名称	Y
    private String dept_name;
    // 科室分类编码 N
    // 1-门诊临床科室 2-住院临床科室 3-医技科室 4-医辅科室 5-行政后勤
    private String dept_type;
    // 一级科室代码	Y
    private String first_dept_id;
    // 一级科室名称	Y
    private String first_dept_name;
    // 科目代码 Y
    // 诊疗科目代码，channel_type为1时必传。
    // 使用诊疗科目代码表中的标准代码，
    // 根据《辽宁省互联网医疗服务监管系统接入规范1.50》必填，
    // 否则上报出错
    private String deptClassCode;
    // 科目名称 Y
    // 诊疗科目名称，channel_type为1时必传。
    // 使用诊疗科目代码表中的标准名称，
    // 根据《辽宁省互联网医疗服务监管系统接入规范1.50》必填，
    // 否则上报出错
    private String deptClassName;
    // 科室简介	N
    private String dept_introduction;
    // 二级科室代码	N
    private String second_dept_id;
    // 二级科室名称	N
    private String second_dept_name;
    // 就诊渠道	N	0线下就 1线上就诊 -1全部
    private String channel_type;
    // 拼音
    private String inputcode1;
    // 五笔
    private String inputcode2;

}
