package com.sunhealth.ihhis.model.dto.register;


import java.io.Serializable;
import lombok.Data;

@Data
public class CancelPreRegistReq implements Serializable {
    private String regno;               // 挂号序号，门诊就诊流水号
    private String settle_id;            // Y 收据号，门诊挂号预算返回不为空时必填
    private String patid;               // 门诊patid，门诊患者唯一号
    private String insuranceParam;      // 医保交易入参，医保端所需参数，自费病人为空，此参数仅限支持医保脱卡支付医院
    // (统一采用BASE64编码转换)，具体参数格式根据各地医保参照附件说明
    private String hospitalCode;
}


