package com.sunhealth.ihhis.model.dto.inpatient;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import java.util.Date;

@Data
public class DailyListReq {

    @NotBlank
    @ApiModelProperty(value = "患者姓名", required = true)
    private String patname;

    @NotBlank
    @ApiModelProperty(value = "住院号", required = true)
    private String regno;

    @NotBlank
    @ApiModelProperty(value = "查询日期 格式yyyyMMdd", required = true)
    private String date;

    @NotBlank
    @ApiModelProperty(value = "是否按数量汇总 0不汇总1数量汇总", required = true)
    private String summary_flag;

    private String hospitalCode;

    private Date beginDate;

    private Date endDate;
}
