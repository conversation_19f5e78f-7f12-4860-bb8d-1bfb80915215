package com.sunhealth.ihhis.model.dto.schedule;

import com.sunhealth.ihhis.utils.DecimalUtil;
import com.sunhealth.ihhis.utils.TimeUtils;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.text.SimpleDateFormat;
import java.util.List;
import java.util.Objects;

@Data
public class DeptSourceDetailsRes {

    @ApiModelProperty(value = "排班序号")
    private String scheduling_id;

    @ApiModelProperty(value = "坐诊时间id")
    private String timespan_id;

    @ApiModelProperty(value = "预约日期")
    private String duty_date;

    @ApiModelProperty(value = "坐诊时间类型")
    private String time_type;

    @ApiModelProperty(value = "坐诊时间类型名称")
    private String time_type_name;

    @ApiModelProperty(value = "开始时间")
    private String begin_time;

    @ApiModelProperty(value = "结束时间")
    private String end_time;

    @ApiModelProperty(value = "号源总数")
    private String source_qty;

    @ApiModelProperty(value = "已预约总数")
    private String used_source_qty;

    @ApiModelProperty(value = "未预约总数")
    private String unused_source_qty;

    @ApiModelProperty(value = "可预约号源总数")
    private String can_use_source_qty;

    @ApiModelProperty(value = "挂号费")
    private String registration_fee;

    @ApiModelProperty(value = "诊疗费")
    private String treatment_fee;

    @ApiModelProperty(value = "挂号费编号")
    private String registration_fee_code;

    @ApiModelProperty(value = "诊疗费编号")
    private String treatment_fee_code;

    @ApiModelProperty(value = "总金额")
    private String total_amount;

    @ApiModelProperty(value = "排班名称")
    private String scheduling_name;

    @ApiModelProperty(value = "就诊地址")
    private String doctor_room;

    @ApiModelProperty(value = "儿童诊疗费")
    private String children_treatment_fee;

    @ApiModelProperty(value = "儿童诊疗费编号")
    private String children_treatment_fee_code;

    @ApiModelProperty(value = "备注")
    private String memo;

    @ApiModelProperty(value = "院区代码")
    private String organ_code;

    @ApiModelProperty(value = "院区名称")
    private String organ_name;

    @ApiModelProperty(value = "号序信息合集")
    private List<SourceNumberInfos> source_number_infos;

    public DeptSourceDetailsRes(DeptSourceDetails deptSourceDetails) {
        this.scheduling_id = deptSourceDetails.getSchedulingId();
        this.timespan_id = deptSourceDetails.getTimespanId();
        this.duty_date = TimeUtils.dateStringFormat(deptSourceDetails.getDutyDate(), "yyyyMMdd");
        this.time_type = TimeUtils.getTimeType(deptSourceDetails.getDutyStartTime(), deptSourceDetails.getDutyEndTime());
        this.time_type_name = deptSourceDetails.getTimeTypeName();
        this.begin_time = TimeUtils.joinDateAndTime(deptSourceDetails.getDutyDate(), deptSourceDetails.getBeginTime());
        this.end_time = TimeUtils.joinDateAndTime(deptSourceDetails.getDutyDate(), deptSourceDetails.getEndTime());
        this.source_qty = Objects.toString(deptSourceDetails.getSourceQty(), "0");
        this.used_source_qty = Objects.toString(deptSourceDetails.getUsedSourceQty(), "0");
        int unusd_count = Integer.valueOf(this.source_qty) - Integer.valueOf(this.used_source_qty);
        this.unused_source_qty = unusd_count < 0 ? "0" : String.valueOf(unusd_count);
        this.can_use_source_qty = this.unused_source_qty;
        this.registration_fee = DecimalUtil.defaultString(deptSourceDetails.getRegistrationFee());
        this.registration_fee_code = deptSourceDetails.getRegistrationFeeCode() != null ? deptSourceDetails.getRegistrationFeeCode() : "";;
        this.treatment_fee = DecimalUtil.defaultString(deptSourceDetails.getTreatmentFee());
        this.treatment_fee_code = deptSourceDetails.getTreatmentFeeCode() != null ? deptSourceDetails.getTreatmentFeeCode() : "";;
        this.total_amount = String.valueOf(Integer.valueOf(this.registration_fee) + Integer.valueOf(this.treatment_fee));
        this.scheduling_name = deptSourceDetails.getSchedulingName();
        this.doctor_room = deptSourceDetails.getDoctorRoom();
    }
}
