package com.sunhealth.ihhis.model.dto.schedule;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

@Data
public class CurrentDayAppointment implements java.io.Serializable {
    @ApiModelProperty("排班序号")
    private String schedulingId;
    
    @ApiModelProperty("医生代码")
    private String doctorId;
    
    @ApiModelProperty("医生名称")
    private String doctorName;
    
    @ApiModelProperty("科室代码")
    private Integer deptId;
    
    @ApiModelProperty("科室名称")
    private String deptName;

    //上级科室
    private Integer parentDeptId;

    // 科室级别
    private Integer deptLevel;

    @ApiModelProperty("坐诊时间类型")
    private String timeType;

    //预约日期
    private Date dutyDate;

    @ApiModelProperty("开始时间")
    private String beginTime;
    
    @ApiModelProperty("结束时间")
    private String endTime;
    
    @ApiModelProperty("号源总数")
    private Integer sourceQty;
    
    @ApiModelProperty("已预约总数")
    private Integer usedSourceQty;
    
    @ApiModelProperty("未预约总数")
    private Integer unusedSourceQty;
    
    @ApiModelProperty("可预约号源总数")
    private Integer canUseSourceQty;
    
    @ApiModelProperty("挂号费")
    private BigDecimal registrationFee;
    
    @ApiModelProperty("诊疗费")
    private BigDecimal treatmentFee;
    
    @ApiModelProperty("儿童诊疗费")
    private BigDecimal childrenTreatmentFee;
    
    @ApiModelProperty("挂号费编号")
    private String registrationFeeCode;
    
    @ApiModelProperty("诊疗费编号")
    private String treatmentFeeCode;
    
    @ApiModelProperty("儿童诊疗费编号")
    private String childrenTreatmentFeeCode;
    
    @ApiModelProperty("总金额")
    private String totalAmount;
    
    @ApiModelProperty("排班名称")
    private String schedulingName;
    
    @ApiModelProperty("出诊类型")
    private String schedulingType;
    
    @ApiModelProperty("排班类型")
    private String sourceType;
    
    @ApiModelProperty("就诊地址")
    private String doctorRoom;
    
    @ApiModelProperty("备注")
    private String memo;
    
    @ApiModelProperty("院区代码")
    private String organCode;
    
    @ApiModelProperty("院区名称")
    private String organName;

}
