package com.sunhealth.ihhis.model.dto.medicalrecord;

import com.google.common.collect.Lists;
import io.swagger.annotations.ApiModelProperty;
import java.io.Serializable;
import java.util.List;
import lombok.Data;

@Data
public class PatientMedicalRecord implements Serializable {

    @ApiModelProperty("挂号序号")
    private String regno; // 挂号序号 Y
    @ApiModelProperty("病历文档编号")
    private String document_id; // 病历文档编号 Y 病历文档医院内唯一号
    @ApiModelProperty("病历创建医生代码")
    private String cre_doctor_id; // 病历创建医生代码 Y
    @ApiModelProperty("病历创建医生名称")
    private String cre_doctor_name; // 病历创建医生名称 Y
    @ApiModelProperty("病历创建科室代码")
    private String cre_dept_id; // 病历创建科室代码 Y
    @ApiModelProperty("病历创建科室名称")
    private String cre_dept_name; // 病历创建科室名称 Y
    @ApiModelProperty("病历创建时间")
    private String cre_create_date;

    private String bl;

    private String bl_html;

    @ApiModelProperty("病历文档详情")
    private List<PatientMedicalRecordInfo> document_infos = Lists.newArrayList();
}
