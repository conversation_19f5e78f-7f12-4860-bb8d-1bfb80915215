package com.sunhealth.ihhis.model.dto.register;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
public class GetOutpatientPayResult {

    @ApiModelProperty("支付类别 Y 0门诊预约挂号 1门诊缴费结算 2住院预交金充值 3住院结算 4互联网医院挂号 5互联网医院处方")
    private String trade_type; // 支付类别 Y 0门诊预约挂号 1门诊缴费结算 2住院预交金充值 3住院结算 4互联网医院挂号 5互联网医院处方

    @ApiModelProperty("状态 Y 0 HIS成功  1 HIS不成功 2 HIS确认中")
    private String status; // 状态 Y 0 HIS成功  1 HIS不成功 2 HIS确认中

    @ApiModelProperty("HIS单号 Y 医院HIS系统订单号（医疗机构订单号），对应于3.2.1和3.3.1里的medical_order_no")
    private String out_trade_no; // HIS单号 Y 医院HIS系统订单号（医疗机构订单号），对应于3.2.1和3.3.1里的medical_order_no

    @ApiModelProperty("医保支付订单号 N 医保必传，医保结算中心订单号,对应[6301][6301]中的payOrdId")
    private String pay_order_id; // 医保支付订单号 N 医保必传，医保结算中心订单号,对应[6301][6301]中的payOrdId

    @ApiModelProperty("收费日期 Y 格式yyyyMMddHHmmss")
    private String charge_time; // 收费日期 Y 格式yyyyMMddHHmmss

    @ApiModelProperty("医保出参 N 占位，具体参数格式内容需要根据当地医保确定")
    private String insurance_param; // 医保出参 N 占位，具体参数格式内容需要根据当地医保确定

    @ApiModelProperty("扩展信息 N 以json格式返回")
    private String extra_content; // 扩展信息 N 以json格式返回

    @ApiModelProperty("对应5期医保se02中的订单号 N 5期医保对应, 生成的唯一ID, 对应微信医保支付的bill_no")
    private String bill_no;
}
