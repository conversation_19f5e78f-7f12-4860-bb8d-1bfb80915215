package com.sunhealth.ihhis.model.dto.bill;

import lombok.Data;

import java.util.List;

@Data
public class HisBillRes {

    // 总交易单数 Y
    private String order_count;

    // 应结订单总金额 Y
    private String settlement_order_amount_count;

    // 退款总金额 Y
    private String refund_amount_count;

    // 充值券退款总金额 Y
    private String recharge_voucher_refund_amount_count;

    // 手续费总金额
    private String handling_fee_count;

    //订单总金额
    private String order_amount_count;

    // 申请退款总金额
    private String refund_application_amount_count;

    // 订单
    private List<BillOrderDTO> orders;
}
