package com.sunhealth.ihhis.model.dto.schedule;

import com.sunhealth.ihhis.utils.TimeUtils;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
public class SourceNumberRes {

    @ApiModelProperty(value = "挂号号序")
    private String source_number;

    @ApiModelProperty(value = "号序状态")
    private String status;

    @ApiModelProperty(value = "开始时间")
    private String begin_time;

    @ApiModelProperty(value = "结束时间")
    private String end_time;

    @ApiModelProperty(value = "号序时间点")
    private String number_time;

    @ApiModelProperty(value = "号序类型")
    private String number_type;

    @ApiModelProperty(value = "备注")
    private String memo;

    public SourceNumberRes(SourceNumber sourceNumber) {
        this.source_number = sourceNumber.getSeqNum();
        this.status = sourceNumber.getStatus().toString();
        this.begin_time = TimeUtils.joinDateAndTime(sourceNumber.getDutyDate(), sourceNumber.getStartTime());
        this.end_time = TimeUtils.joinDateAndTime(sourceNumber.getDutyDate(), sourceNumber.getEndTime());
    }
}
