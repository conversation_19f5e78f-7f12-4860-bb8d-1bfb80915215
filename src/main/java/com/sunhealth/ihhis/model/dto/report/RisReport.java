package com.sunhealth.ihhis.model.dto.report;

import com.sunhealth.ihhis.model.entity.report.ReportExamInfo;
import com.sunhealth.ihhis.model.entity.report.ReportTestInfo;
import com.sunhealth.ihhis.utils.TimeUtils;
import io.swagger.annotations.ApiModelProperty;
import java.io.Serializable;
import java.text.SimpleDateFormat;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.apache.commons.lang3.StringUtils;

/**
 * @Author: jzs
 * @Date: 2023-09-07
 * 检查报告列表
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class RisReport implements Serializable {
    @ApiModelProperty("报告单号	Y  如果报告未出，此处传申请单号报告已出，此字段必传")
    private String report_no;
    @ApiModelProperty("就诊流水号	Y	门诊挂号序号、住院号")
    private String regno;
    @ApiModelProperty("就诊类别	Y	1门诊 2住院")
    private String user_source;
    @ApiModelProperty("检查项目名称 Y")
    private String report_item_name;
    @ApiModelProperty("报告类别代码	N")
    private String report_type_code;
    @ApiModelProperty("报告类别名称	N")
    private String report_type_name;
    @ApiModelProperty("检查部位	N")
    private String checkpoint;
    @ApiModelProperty("申请单号	N")
    private String application_no;
    @ApiModelProperty("申请时间	Y")
    private String apply_time;
    @ApiModelProperty("报告发布时间	Y  当报告出具时，报告发布时间必填")
    private String report_time;
    @ApiModelProperty("申请科室名称	N")
    private String apply_dept_name;
    @ApiModelProperty("申请医生名称	N")
    private String apply_doctor_name;
    @ApiModelProperty("审核医生名称	N")
    private String check_doctor_name;
    @ApiModelProperty("执行科室名称	N")
    private String exec_dept_name;
    @ApiModelProperty("执行医生名称	N")
    private String exec_doctor_name;
    @ApiModelProperty("备注	N")
    private String memo;

    public RisReport(ReportExamInfo info) {
        if (StringUtils.isBlank(info.getReportNo())) {
            this.report_no = info.getApplyId();
        } else {
            this.report_no = info.getReportNo();
        }
        this.regno = info.getVisitNo();
        switch (info.getPatientType()) {
            case "02":
                this.user_source = "2";
                break;
            case "01":
            default:
                this.user_source = "1";
        }
        this.report_item_name = info.getItemName();
        this.report_type_code = info.getReportType();
        this.report_type_name = info.getReportTypeName();
        this.application_no = info.getApplyId();
        this.checkpoint = info.getExamSite();
        this.apply_time = TimeUtils.getHisDateStr(info.getApplyTime());
        this.report_time = TimeUtils.getHisDateStr(info.getReportTime());
        this.apply_dept_name = info.getApplyDeptName();
        this.apply_doctor_name = info.getApplyDoctorName();
        this.exec_dept_name = info.getExamDeptName();
        this.exec_doctor_name = info.getExamDoctorName();
        this.check_doctor_name = info.getAuditorDoctorName();
    }
}
