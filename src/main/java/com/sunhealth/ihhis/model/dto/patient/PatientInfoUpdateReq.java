package com.sunhealth.ihhis.model.dto.patient;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;

@Data
public class PatientInfoUpdateReq {

        @NotBlank
        @ApiModelProperty(value = "患者姓名", required = true)
        private String patname;

        @NotBlank
        @ApiModelProperty(value = "患者ID", required = true)
        private String patid;

        @ApiModelProperty(value = "仅限首诊患者更新（卡号为身份证号或卡号为空的情况下），为空则不更新")
        private String cardno;

        @ApiModelProperty(value = "联系地址，为空则不更新")
        private String address;

        @ApiModelProperty(value = "联系电话，为空则不更新")
        private String telephone;

        // 备注	N	为空则不更新
        @ApiModelProperty(value = "备注，为空则不更新")
        private String memo;

        @ApiModelProperty(value = "联系人姓名，为空则不更新")
        private String contacts_name;

        @ApiModelProperty(value = "联系人电话，为空则不更新")
        private String contacts_telephone;

        @ApiModelProperty(value = "联系人证件号，为空则不更新")
        private String contacts_certificate_no;

        @ApiModelProperty(value = "联系人关系，以HIS字典为准")
        private String contacts_relationship;

        @ApiModelProperty(value = "仅限该patid所对应的身份证为空的情况下允许更新(兼容其他证件号码)")
        private String certificate_no;
        // 证件类型	N	参考卫生信息数据元值域代码中CV02.01.101规范：
        // 01身份证
        // 02户口簿
        // 03护照
        // 04军官证
        // 05驾驶证
        // 06港澳居民通行证
        // 07台湾居民通行证
        // 99其他有效法定证件
        // 20外国人永久居留证
        // 21入出境通行证
        // 22旅行证
        // 为空的情况下默认为01身份证
        @ApiModelProperty(value = "证件类型，为空的情况下默认为01身份证")
        private String certificate_type;

        @ApiModelProperty(value = "民族代码，为空则不更新")
        private Integer nation;

        @ApiModelProperty(value = "职业代码，为空则不更新")
        private Integer career;

        @ApiModelProperty(value = "国籍代码，HIS国籍代码(具体以当地HIS为准)")
        private Integer nationality;

        @ApiModelProperty(value = "工作单位名称，为空则不更新")
        private String work_unit;

        @ApiModelProperty(value = "居住地详细地址")
        private String residence_county_address;

        @ApiModelProperty(value = "医保入参，占位，具体参数格式内容需要根据当地医保确定")
        private String insurance_param;

        @ApiModelProperty(value = "外部电子卡号，需以当地医院实际规范为准")
        private String virtual_card;

        private Integer hospitalCode;

        private String requestId;
}
