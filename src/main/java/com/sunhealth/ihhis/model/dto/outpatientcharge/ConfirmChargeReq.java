package com.sunhealth.ihhis.model.dto.outpatientcharge;

import com.sunhealth.ihhis.model.dq.sh.insurance.response.SE04Response;
import com.sunhealth.ihhis.model.insurance.response.Output6301;
import lombok.Data;

import java.io.Serializable;

@Data
public class ConfirmChargeReq implements Serializable {

    /**
     * 门诊patid  门诊患者唯一号  Y
     */
    private String patid;

    /**
     * 挂号序号  Y
     */
    private String regno;

    /**
     * HIS结算单号  预算时HIS反馈的HIS结算收据号  Y
     */
    private String settle_id;

    /**
     * 平台流水号  平台方业务订单id，标记唯一一次业务请求，如互联网医院平台订单号  Y
     */
    private String serial_no;

    /**
     * 划价序号合集  处方序号合集。（格式：109928,1090899,109089）调用门诊待缴费处方，获取未收费处方的处方序号  Y
     */
    private String recipe_no_list;

    /**
     * 总金额  Y
     */
    private String total_amount;

    /**
     * 应付金额  Y
     */
    private String should_pay_amount;

    /**
     * 支付方式  0 现金 1 微信支付 2支付宝支付  Y
     */
    private String pay_type;

    /**
     * 支付金额  Y
     */
    private String pay_amount;

    /**
     * 支付流水号  应为支付的唯一流水号，用于对账。支付方式不为0时，则必填。  Y
     */
    private String trade_no;

    /**
     * 支付时间  支付方扣款时间，用于处理对账跨天问题。格式yyyyMMddHHmmss  Y
     */
    private String pay_time;

    /**
     * 是否扣院内账户  0不从院内账户走1走院内账户  Y
     */
    private String hosp_account_flag;

    /**
     * 账户标识  医院收款账户标识，默认为空，医院使用多个支付账户情况下非空，此时HIS账单接口原样传出，以便于多账户对账  N
     */
    private String receipt_account;

    /**
     * 是否自费结算  0根据病人医保代码结算1自费结算  N
     */
    private String self_flag;

    /**
     * 支付渠道  特别约定时必填  N
     */
    private String port;

    /**
     * 代币卡序号  多张卡支付时以|分隔  N
     */
    private String pay_card_no;

    /**
     * 代币卡支付金额  多张卡支付时以|分隔  N
     */
    private String pay_card_amount;

    /**
     * 代币卡使用标志  使用代币卡支付时传入1，默认为空  N
     */
    private String pay_card_flag;

    /**
     * 医保交易入参  占位，具体参数格式内容需要根据当地医保确定  N
     */
    private String insurance_param;

    /**
     * 商保结算信息  存在商保结算时需传入，具体以MAP JSON格式字符串传入  N
     */
    private String commercial_insurance_param;

    /**
     * 扩展信息  个性化字段扩展信息，根据实际项目进行处理（统一采用BASE64编码转换）  N
     */
    private String extra_content;

    private String hospitalCode;

    private Output6301 output6301;
    private SE04Response se04Response;
    /**
     * 2 国家医保
     * 其他值 上海医保
     */
    private int jsFlag;
}
