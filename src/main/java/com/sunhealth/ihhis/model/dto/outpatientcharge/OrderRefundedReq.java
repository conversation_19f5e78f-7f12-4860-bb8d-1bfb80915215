package com.sunhealth.ihhis.model.dto.outpatientcharge;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;

@Data
public class OrderRefundedReq {

    @NotBlank
    @ApiModelProperty("门诊patid")
    private String patid;

    @NotBlank
    @ApiModelProperty("退款业务类型")
    private String refund_type;

    @NotBlank
    @ApiModelProperty("退款settle_id")
    private String settle_id;
    // 不是请求参数 仅临时传参使用
    private String hospitalCode;
}
