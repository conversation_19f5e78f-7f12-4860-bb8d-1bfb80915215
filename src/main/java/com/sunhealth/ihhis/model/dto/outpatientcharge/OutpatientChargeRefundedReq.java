package com.sunhealth.ihhis.model.dto.outpatientcharge;

import com.sunhealth.ihhis.model.insurance.MedicalInsuranceParam;
import io.swagger.annotations.ApiModelProperty;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import lombok.Data;

@Data
public class OutpatientChargeRefundedReq {

    @NotBlank
    @ApiModelProperty("退款settle_id")
    private String settle_id;
    @NotNull(message = "医保退款医保参数不能为空")
    private MedicalInsuranceParam insuranceParam ;

    private String hospitalCode;




}
