package com.sunhealth.ihhis.model.dto.schedule;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import java.util.Date;

@Data
public class CurrentDoctorSourceDetailsReq {

    @NotBlank
    @ApiModelProperty(value = "医生代码", required = true)
    private String doctor_id;

    @ApiModelProperty(value = "就诊渠道")
    private String scheduling_type;

    private String hospitalCode;

    private Date beginDate;

    private Date endDate;
}
