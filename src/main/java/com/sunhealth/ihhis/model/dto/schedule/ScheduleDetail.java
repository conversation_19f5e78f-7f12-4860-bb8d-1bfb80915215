package com.sunhealth.ihhis.model.dto.schedule;

import lombok.Data;

@Data
public class ScheduleDetail implements java.io.Serializable {

    private static final long serialVersionUID = 1L;

    private Integer courseID;
    private String courseName;
    private Integer deptID;
    private Integer doctorID;
    private Integer doctorLevel;
    private Integer chargeType;
    private Integer blanceWay;
    private Integer registType;
    private Integer appointmentWay;
    private String appointmentNo;
    private Integer registOrder;
    private Integer registMode;
    private String visitTime;
    private Integer status;
    private String registTime;
    private String computerNo;
    private Integer opCode;
    private String createTime;
    private String cureCode;
    private Integer visitFlag;
    private Boolean isDelete;
    private Integer createdBy;
    private String createdDate;
    private String updateDate;
    private Integer updateby;
    private Integer referralFlag;
    private Short deptKind;
    private Integer fzFlag;
    private Integer hospitalCode;
    private Integer jsFlag;
    private String acctUsedFlag;
    private String reservationType;
    private String appointmentFlag;
    private Boolean isGreenChannel;
    private Boolean isDebitPay;


}
