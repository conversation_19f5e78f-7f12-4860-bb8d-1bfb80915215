package com.sunhealth.ihhis.model.dto.bill;

import com.sunhealth.ihhis.utils.DecimalUtil;
import com.sunhealth.ihhis.utils.TimeUtils;
import lombok.Data;

@Data
public class BillOrderDTO {

    // 自费或医保订单类型 Y
    private String order_type;

    // 支付类型 Y
    private String pay_type;

    // 交易时间 Y
    private String transaction_time;

    // 交易流水号 N
    private String transaction_id;

    // 平台流水号 N
    private String serial_no;

    // 收据号 N
    private String settle_id;

    // 交易状态 Y
    private String transaction_status;

    // 应结订单金额 N
    private String settlement_order_amount;

    // 代金券金额 N
    private String voucher_amount;

    // 商户退款单号 Y
    private String merchant_refund_number;

    // 退款总金额 Y
    private String refund_amount;

    // 退款金额（自费） N
    private String self_refund_amount;

    // 退款金额（医保） N
    private String medicare_refund_amount;

    // 订单支付金额（自费）N
    private String self_order_amount;

    // 订单支付金额（医保）N
    private String medicare_order_amount;

    // 医保支付订单号 N
    private String pay_order_id;

    // 充值券退款金额 N
    private String recharge_voucher_refund_amount;

    // 退款类型 Y
    private String refund_type;

    // 退款状态 Y
    private String refund_status;

    // 商品名称 Y
    private String product_name;

    // 订单总金额 Y
    private String order_amount;

    // 申请退款金额 N
    private String refund_application_amount;

    public BillOrderDTO(BillOrder billOrder) {
        this.order_type = billOrder.getOrder_type();
        this.pay_type = billOrder.getPay_type();
        this.transaction_time = TimeUtils.dateStringFormat(billOrder.getTransaction_time(),"yyyyMMddHHmmss");
        this.transaction_id = billOrder.getTransaction_id();
        this.serial_no = billOrder.getSerial_no();
        this.settle_id = billOrder.getSettle_id();
        this.transaction_status = billOrder.getTransaction_status();
        this.settlement_order_amount = DecimalUtil.defaultString(billOrder.getSettlement_order_amount(), 2);
        this.voucher_amount = DecimalUtil.defaultString(billOrder.getVoucher_amount(), 2);
        this.merchant_refund_number = billOrder.getMerchant_refund_number();
        this.refund_amount = DecimalUtil.defaultString(billOrder.getRefund_amount(), 2);
        this.self_refund_amount = DecimalUtil.defaultString(billOrder.getSelf_refund_amount(), 2);
        this.medicare_refund_amount = DecimalUtil.defaultString(billOrder.getMedicare_refund_amount(), 2);
        this.self_order_amount = DecimalUtil.defaultString(billOrder.getSelf_order_amount(), 2);
        this.medicare_order_amount = DecimalUtil.defaultString(billOrder.getMedicare_order_amount(),2);
        this.pay_order_id = billOrder.getPay_order_id();
        this.recharge_voucher_refund_amount = DecimalUtil.defaultString(billOrder.getRecharge_voucher_refund_amount(), 2);
        this.refund_type = billOrder.getRefund_type();
        this.refund_status = billOrder.getRefund_status();
        this.product_name = billOrder.getProduct_name();
        this.order_amount = DecimalUtil.defaultString(billOrder.getOrder_amount(), 2);
        this.refund_application_amount = DecimalUtil.defaultString(billOrder.getRefund_application_amount(), 2);
    }
}
