package com.sunhealth.ihhis.model.dto.push;

import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

/**
 *
 * HIS接口服务调用IH获取二维码请求
 */
@Getter
@Setter
public class IHQRCodeRequest {

    @ApiModelProperty("二维码类型	Y	1微信小程序")
    private String qr_code_type;

    @ApiModelProperty("业务类型	Y	1门诊待缴费项目页面")
    private String type;

    @ApiModelProperty("挂号序号	N	如果要展示单次挂号的要传入此字段")
    private String regno;

    @ApiModelProperty("患者唯一号	Y	门诊patid")
    private String patid;

    @ApiModelProperty("患者姓名	Y	")
    private String patName;

    private String organ_code;

    private String organ_name;

    private String timestamp;

    private String request_id;

    public IHQRCodeRequest(HisQRCodeRequest hisQRCodeRequest) {
        this.qr_code_type = hisQRCodeRequest.getQr_code_type();
        this.type = hisQRCodeRequest.getType();
        this.regno = hisQRCodeRequest.getRegno();
        this.patid = hisQRCodeRequest.getPatid();
    }
}
