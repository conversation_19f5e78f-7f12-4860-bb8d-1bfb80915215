package com.sunhealth.ihhis.model.dto.register;

import lombok.Data;

@Data
public class ConfirmRegistResult implements java.io.Serializable {

    // 成功状态	Y	true  false
    private boolean success;
    // 提示信息	Y
    private String message;
    // HIS收据号	Y	HIS结算流水号
    private String settle_id;
    // 收费日期	Y
    private String charge_time;
    // 备注	Y
    private String memo;
    // 就诊地址	N
    private String doctor_room;
    // 挂号序号	N	本次挂号的HIS唯一码，门诊就诊流水号
    private String regno;
    // 挂号号序	N
    private String source_number;
    // 电子收据号	N	仅限有电子发票情况下输出
    private String elec_invoice_no;
    // 电子票据二维码数据	N	仅限当地有电子发票方案的医院
    private String elec_invoice_qrcode;
    // 电子票据URL	N	仅限当地有电子发票方案的医院
    private String elec_invoice_url;
    // 医保出参	N	占位，具体参数格式内容需要根据当地医保确定
    private String insurance_param;
}
