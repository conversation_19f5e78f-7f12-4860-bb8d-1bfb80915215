package com.sunhealth.ihhis.model.dto.report;

import com.sunhealth.ihhis.model.entity.report.ReportTestInfo;
import com.sunhealth.ihhis.utils.TimeUtils;
import io.swagger.annotations.ApiModelProperty;
import java.io.Serializable;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.apache.commons.lang3.StringUtils;

/**
 * @Author: jzs
 * @Date: 2023-09-07
 * 检验报告
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class LisReport implements Serializable {
    @ApiModelProperty("报告单号	Y  如果报告未出，此处传申请单号报告已出，此字段必传")
    private String report_no;
    @ApiModelProperty("就诊流水号	Y	门诊挂号序号、住院号")
    private String regno;
    @ApiModelProperty("就诊类别	Y	1门诊 2住院")
    private String user_source;
    @ApiModelProperty("检验项目名称 Y")
    private String report_item_name;
    @ApiModelProperty("报告类别代码	N")
    private String report_type_code;
    @ApiModelProperty("报告类别名称	N")
    private String report_type_name;
    @ApiModelProperty("标本类型	Y")
    private String specimen;
    @ApiModelProperty("申请单号	N")
    private String application_no;
    @ApiModelProperty("申请时间	N")
    private String apply_time;
    @ApiModelProperty("送检时间	Y")
    private String censorship_time;
    @ApiModelProperty("报告发布时间	Y")
    private String report_time;
    @ApiModelProperty("申请科室名称	N")
    private String apply_dept_name;
    @ApiModelProperty("申请医生名称	N")
    private String apply_doctor_name;
    @ApiModelProperty("审核医生名称	N")
    private String check_doctor_name;
    @ApiModelProperty("执行科室名称	N")
    private String exec_dept_name;
    @ApiModelProperty("执行医生名称	N")
    private String exec_doctor_name;
    @ApiModelProperty("备注	N")
    private String memo;
    @ApiModelProperty("患者姓名	Y")
    private String patname;
    @ApiModelProperty("患者性别	Y")
    private String sex;
    @ApiModelProperty("年龄	Y")
    private String age;

    public LisReport(ReportTestInfo reportTestInfo) {
        this.report_no = reportTestInfo.getReportNo();
        this.regno = reportTestInfo.getHospNo();
        this.user_source = "门诊".equals(reportTestInfo.getPatientType()) ? "1" : "2";
        this.report_item_name = reportTestInfo.getInspectionItemName();
        this.report_type_code = reportTestInfo.getInspectionItem();
        this.report_type_name = reportTestInfo.getInspectionItemName();
        this.specimen = reportTestInfo.getSpeimenTypeName();
        this.application_no = reportTestInfo.getApplyId();
        this.apply_time = TimeUtils.getHisDateStr(reportTestInfo.getApplyTime());
        this.censorship_time = TimeUtils.getHisDateStr(reportTestInfo.getSpeimentReceiveTime());
        this.report_time = TimeUtils.getHisDateStr(reportTestInfo.getReportTime());
        this.apply_dept_name = reportTestInfo.getApplyDeptName();
        this.apply_doctor_name = reportTestInfo.getApplyDoctorName();
        this.check_doctor_name = reportTestInfo.getTestDoctorName();
        this.exec_dept_name = reportTestInfo.getTestDeptName();
        this.exec_doctor_name = reportTestInfo.getReportDoctorName();
        this.memo = reportTestInfo.getRemark();
    }
}
