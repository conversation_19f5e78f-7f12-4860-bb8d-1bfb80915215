package com.sunhealth.ihhis.model.dto.schedule;

import com.sunhealth.ihhis.utils.DecimalUtil;
import com.sunhealth.ihhis.utils.TimeUtils;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.text.SimpleDateFormat;
import java.util.Objects;

@Data
public class SourceDetailsRes {

    @ApiModelProperty(value = "排班序号")
    private String scheduling_id;

    @ApiModelProperty(value = "坐诊时间id")
    private String timespan_id;

    @ApiModelProperty(value = "医生代码")
    private String doctor_id;

    @ApiModelProperty(value = "医生名称")
    private String doctor_name;

    @ApiModelProperty(value = "科室代码")
    private String dept_id;

    @ApiModelProperty(value = "科室名称")
    private String dept_name;

    @ApiModelProperty(value = "预约日期")
    private String duty_date;

    @ApiModelProperty(value = "坐诊时间类型")
    private String time_type;

    @ApiModelProperty(value = "坐诊时间类型名称")
    private String time_type_name;

    @ApiModelProperty(value = "开始时间")
    private String begin_time;

    @ApiModelProperty(value = "结束时间")
    private String end_time;

    @ApiModelProperty(value = "号源总数")
    private String source_qty;

    @ApiModelProperty(value = "已预约总数")
    private String used_source_qty;

    @ApiModelProperty(value = "未预约总数")
    private String unused_source_qty;

    @ApiModelProperty(value = "可预约号源总数")
    private String can_use_source_qty;

    @ApiModelProperty(value = "挂号费")
    private String registration_fee;

    @ApiModelProperty(value = "诊疗费")
    private String treatment_fee;

    @ApiModelProperty(value = "挂号费编号")
    private String registration_fee_code;

    @ApiModelProperty(value = "诊疗费编号")
    private String treatment_fee_code;

    @ApiModelProperty(value = "总金额")
    private String total_amount;

    @ApiModelProperty(value = "排班名称")
    private String scheduling_name;

    @ApiModelProperty(value = "出诊类型")
    private String scheduling_type;

    @ApiModelProperty(value = "排班类型")
    private String source_type;

    @ApiModelProperty(value = "就诊地址")
    private String doctor_room;

    @ApiModelProperty(value = "一级科室代码")
    private String first_dept_id;

    @ApiModelProperty(value = "一级科室名称")
    private String first_dept_name;

    @ApiModelProperty(value = "二级科室代码")
    private String second_dept_id;

    @ApiModelProperty(value = "二级科室名称")
    private String second_dept_name;

    @ApiModelProperty(value = "儿童诊疗费")
    private String children_treatment_fee;

    @ApiModelProperty(value = "儿童诊疗费编号")
    private String children_treatment_fee_code;

    @ApiModelProperty(value = "备注")
    private String memo;

    @ApiModelProperty(value = "院区代码")
    private String organ_code;

    @ApiModelProperty(value = "院区名称")
    private String organ_name;

    public SourceDetailsRes(SourceDetails sourceDetails) {
        this.scheduling_id = sourceDetails.getSchedulingId();
        this.timespan_id = sourceDetails.getTimespanId();
        this.doctor_id = sourceDetails.getDoctorId();
        this.doctor_name = sourceDetails.getDoctorName();
        this.dept_id = Objects.toString(sourceDetails.getDeptId(), "");
        this.dept_name =sourceDetails.getDeptName();
        this.duty_date = TimeUtils.dateStringFormat(sourceDetails.getDutyDate(), "yyyyMMdd");
        this.time_type = TimeUtils.getTimeType(sourceDetails.getDutyStartTime(), sourceDetails.getDutyEndTime());
        this.time_type_name = sourceDetails.getTimeTypeName();
        this.begin_time = TimeUtils.joinDateAndTime(sourceDetails.getDutyDate(), sourceDetails.getBeginTime());
        this.end_time = TimeUtils.joinDateAndTime(sourceDetails.getDutyDate(), sourceDetails.getEndTime());
        this.source_qty = Objects.toString(sourceDetails.getSourceQty(), "0");
        this.used_source_qty = Objects.toString(sourceDetails.getUsedSourceQty(), "0");
        int unusd_count = Integer.valueOf(this.source_qty) - Integer.valueOf(this.used_source_qty);
        this.unused_source_qty = unusd_count < 0 ? "0" : String.valueOf(unusd_count);
        this.can_use_source_qty = this.unused_source_qty;
        this.registration_fee = DecimalUtil.defaultString(sourceDetails.getRegistrationFee());
        this.registration_fee_code = sourceDetails.getRegistrationFeeCode() != null ? sourceDetails.getRegistrationFeeCode() : "";;
        this.treatment_fee = DecimalUtil.defaultString(sourceDetails.getTreatmentFee());
        this.treatment_fee_code = sourceDetails.getTreatmentFeeCode() != null ? sourceDetails.getTreatmentFeeCode() : "";;
        this.total_amount = String.valueOf(Integer.valueOf(this.registration_fee) + Integer.valueOf(this.treatment_fee));
        this.scheduling_name = sourceDetails.getSchedulingName();
        this.scheduling_type = sourceDetails.getSchedulingType();
        this.source_type = "2";
        this.doctor_room = sourceDetails.getDoctorRoom();
    }
}
