package com.sunhealth.ihhis.model.dto.push;

import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Getter
@Setter
@NoArgsConstructor
public class HisQRCodeResponse {

    @ApiModelProperty("二维码图片的base64数据 Y png格式，不包含base64前缀")
    private String qrcode_img;

    public HisQRCodeResponse(String qrcode_img) {
        this.qrcode_img = qrcode_img;
    }
}
