package com.sunhealth.ihhis.model.dto.inpatient;

import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

@Data
public class AdvanceChargeDetail {

    //预交金序号
    private String advanceChargeId;

    //收据号
    private String settleId;

    //平台流水号
    private String serialNo;

    //缴费日期
    private Date payTime;

    //预交金额
    private BigDecimal advanceAmount;

    //支付方式
    private String payType;

    //记录状态
    private String recordStatus;

    //备注
    private String memo;

    // 发票号
//    private String invoiceNo;

    // 操作类型
//    private String op_type;
}
