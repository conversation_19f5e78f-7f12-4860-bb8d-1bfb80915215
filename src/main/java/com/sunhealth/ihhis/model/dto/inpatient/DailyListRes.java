package com.sunhealth.ihhis.model.dto.inpatient;

import com.sunhealth.ihhis.utils.TimeUtils;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
public class DailyListRes {

    @ApiModelProperty(value = "明细序号")
    private String recipe_detail_id;

    @ApiModelProperty(value = "大项目名称")
    private String set_item_name;

    @ApiModelProperty(value = "费用生成时间")
    private String fee_time;

    @ApiModelProperty(value = "费用项目代码")
    private String item_code;

    @ApiModelProperty(value = "费用项目名称")
    private String item_name;

    @ApiModelProperty(value = "药品标识")
    private String drug_flag;

    @ApiModelProperty(value = "项目单位")
    private String item_unit;

    @ApiModelProperty(value = "项目单价")
    private String price;

    @ApiModelProperty(value = "项目数量")
    private String quantity;

    @ApiModelProperty(value = "规格")
    private String gauge;

    @ApiModelProperty(value = "总金额")
    private String total_amount;

    public DailyListRes(DailyList dailyList) {
        this.set_item_name = dailyList.getItemCategoryName();
        this.fee_time = TimeUtils.dateStringFormat(dailyList.getFeeTime(),"yyyyMMddHHmmss");
        this.item_code = dailyList.getItemCode();
        this.item_name = dailyList.getItemName();
        this.drug_flag = dailyList.getDrugFlag();
        this.item_unit = dailyList.getUnit();
        this.price = dailyList.getPrice();
        this.quantity = dailyList.getQuantity();
        this.gauge = dailyList.getGauge();
        this.total_amount = dailyList.getAmount();
    }
}
