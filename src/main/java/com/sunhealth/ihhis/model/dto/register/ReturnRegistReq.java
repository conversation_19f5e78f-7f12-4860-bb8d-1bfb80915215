package com.sunhealth.ihhis.model.dto.register;

import lombok.Data;

@Data
public class ReturnRegistReq implements java.io.Serializable {

    private String patid;               // 门诊patid，门诊患者唯一号
    private String regno;               // 挂号序号，门诊就诊流水号
    private String settleId;            // 收据号，HIS一次结算单据号(标记一次结算的唯一号)，挂号预算时收据号返回不为空的情况下，此字段必填
    private String payType;             // 退支付方式
    private String payAmount;           // 退支付金额，支付金额
    private String tradeNo;             // 原支付流水号，应为支付的唯一流水号，用于对账
    private String refundTradeNo;       // 退流水号，退费流水号
    private String refundTime;          // 退款时间
    private String insuranceParam;      // 医保交易入参，医保端所需参数，自费病人为空，此参数仅限支持医保脱卡支付医院
    // (统一采用BASE64编码转换)，具体参数格式根据各地医保参照附件说明

    private String hospitalCode;        // 医院编码
}
