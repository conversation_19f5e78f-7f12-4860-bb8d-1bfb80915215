package com.sunhealth.ihhis.model.dto.schedule;

import com.sunhealth.ihhis.utils.DecimalUtil;
import com.sunhealth.ihhis.utils.TimeUtils;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Objects;

@Data
public class CurrentDayAppointmentRes implements java.io.Serializable {
    @ApiModelProperty("排班序号")
    private String scheduling_id;
    
    @ApiModelProperty("医生代码")
    private String doctor_id;
    
    @ApiModelProperty("医生名称")
    private String doctor_name;
    
    @ApiModelProperty("科室代码")
    private String dept_id;
    
    @ApiModelProperty("科室名称")
    private String dept_name;
    
    @ApiModelProperty("坐诊时间类型")
    private String time_type;
    
    @ApiModelProperty("开始时间")
    private String begin_time;
    
    @ApiModelProperty("结束时间")
    private String end_time;
    
    @ApiModelProperty("号源总数")
    private String source_qty;
    
    @ApiModelProperty("已预约总数")
    private String used_source_qty;
    
    @ApiModelProperty("未预约总数")
    private String unused_source_qty;
    
    @ApiModelProperty("可预约号源总数")
    private String can_use_source_qty;
    
    @ApiModelProperty("挂号费")
    private String registration_fee;
    
    @ApiModelProperty("诊疗费")
    private String treatment_fee;
    
    @ApiModelProperty("儿童诊疗费")
    private String children_treatment_fee;
    
    @ApiModelProperty("挂号费编号")
    private String registration_fee_code;
    
    @ApiModelProperty("诊疗费编号")
    private String treatment_fee_code;
    
    @ApiModelProperty("儿童诊疗费编号")
    private String children_treatment_fee_code;
    
    @ApiModelProperty("总金额")
    private String total_amount;
    
    @ApiModelProperty("排班名称")
    private String scheduling_name;
    
    @ApiModelProperty("出诊类型")
    private String scheduling_type;
    
    @ApiModelProperty("排班类型")
    private String source_type;
    
    @ApiModelProperty("就诊地址")
    private String doctor_room;
    
    @ApiModelProperty("备注")
    private String memo;
    
    @ApiModelProperty("院区代码")
    private String organ_code;
    
    @ApiModelProperty("院区名称")
    private String organ_name;

    public CurrentDayAppointmentRes(CurrentDayAppointment appointment) {
        this.scheduling_id = appointment.getSchedulingId();
        this.doctor_id = appointment.getDoctorId();
        this.doctor_name = appointment.getDoctorName();
        this.dept_id = Objects.toString(appointment.getDeptId(), "");
        this.dept_name =appointment.getDeptName();
        this.time_type = TimeUtils.getTimeType(appointment.getBeginTime(), appointment.getEndTime());
        this.begin_time = TimeUtils.joinDateAndTime(appointment.getDutyDate(), appointment.getBeginTime());
        this.end_time = TimeUtils.joinDateAndTime(appointment.getDutyDate(), appointment.getEndTime());
        this.source_qty = Objects.toString(appointment.getSourceQty(), "0");
        this.used_source_qty = Objects.toString(appointment.getUsedSourceQty(), "0");
        this.can_use_source_qty = source_qty;
        int unusd_count = Integer.valueOf(this.source_qty) - Integer.valueOf(this.used_source_qty);
        this.unused_source_qty = unusd_count < 0 ? "0" : String.valueOf(unusd_count);
        this.can_use_source_qty = this.unused_source_qty;
        this.registration_fee = DecimalUtil.defaultString(appointment.getRegistrationFee());
        this.registration_fee_code = appointment.getRegistrationFeeCode() != null ? appointment.getRegistrationFeeCode() : "";;
        this.treatment_fee = DecimalUtil.defaultString(appointment.getTreatmentFee());
        this.treatment_fee_code = appointment.getTreatmentFeeCode() != null ? appointment.getTreatmentFeeCode() : "";;
        this.total_amount = String.valueOf(Integer.valueOf(this.registration_fee) + Integer.valueOf(this.treatment_fee));
        this.scheduling_name = appointment.getSchedulingName();
        this.scheduling_type = appointment.getSchedulingType();
        this.source_type = "2";
        this.doctor_room = appointment.getDoctorRoom();
    }
}
