package com.sunhealth.ihhis.model.dto.register;


import com.sunhealth.ihhis.enums.BusinessType;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

@Data
public class CancelRegisterReq implements Serializable {

    // 门诊患者唯一号 Y
    private String patid;

    // 门诊就诊流水号 Y
    private String regno;

    private String deptId;
    private String lsh;
    private BigDecimal totalexpense;

    //  结算单号 Y
    private String settle_id;

    // 退支付方式 0 现金 1 微信支付 2支付宝支付 3 医保
    private String pay_type;

    // 支付金额 Y
    private String pay_amount;

    // 应为支付的唯一流水号，用于对账 Y
    private String trade_no;


    // 退费流水号
    private String refund_trade_no;

    // 退费时间 Y
    private String refund_time;

    // 保险参数
    private String insurance_param;
    private String hospitalCode;

    /**
     * 医保业务类型
     */
    private BusinessType businessType;
}


