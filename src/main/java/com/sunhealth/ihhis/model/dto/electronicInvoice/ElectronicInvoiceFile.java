package com.sunhealth.ihhis.model.dto.electronicInvoice;

import lombok.Data;

/**
 * 电子发票文件响应值
 */
@Data
public class ElectronicInvoiceFile {
    // 病人唯一码	Y
    private String patid;
    // 	电子收据号	Y	如果此时没有电子收据号，可以不传
    private String elec_invoice_no;
    // 电子发票	N	base64 发票原件pdf格式，如果无法提供，此字段不要返回数据
    private String elec_invoice_file;
    // 电子票据URL	N	电子票据URL
    private String elec_invoice_url;
    // 电子发票查看方式 1 H5查看，2 小程序查看
    private String elec_invoice_show_type = "1";
    // 电子发票查看小程序appid
    private String elec_invoice_mp_appid;
    // 电子发票查看小程序路径
    private String elec_invoice_mp_path;
    // 开票机构	Y
    private String invoicing_entity;
    // 票据金额	Y
    private String invoicing_amount;
    // 开票日期	Y
    private String invoicing_date;
    // 票据代码	Y
    private String invoicing_code;
    // 票据号码	Y
    private String invoicing_no;
    // 校验码	Y
    private String check_code;
}
