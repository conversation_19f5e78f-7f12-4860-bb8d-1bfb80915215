package com.sunhealth.ihhis.model.entity.invoice;

import com.baomidou.mybatisplus.annotation.IdType;
import java.io.Serializable;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableField;
import java.math.BigDecimal;
import java.util.Date;
import lombok.Data;

@Data
@TableName("Reg_Tb_OpInvoicePayway_Time")
public class OpInvoicePaywayTime implements Serializable {
    private static final long serialVersionUID = 1L;

    @TableId(value = "SerialNo", type = IdType.AUTO)
    private Integer serialNo; // 主键字段，不能为NULL

    @TableField("InvoiceID")
    private Long invoiceID; // 不能为NULL

    @TableField("ChargeNo")
    private Long chargeNo;

    @TableField("Payway")
    private Integer payway;

    @TableField("PaywayName")
    private String paywayName;

    @TableField("PayAmount")
    private BigDecimal payAmount;

    @TableField("OpCode")
    private Integer opCode;

    @TableField("OpTime")
    private Date opTime;

    @TableField("Status")
    private Integer status;

    @TableField("IsDelete")
    private Boolean isDelete = false; // 不能为NULL，默认值为 false

    @TableField("CreatedBy")
    private Integer createdBy;

    @TableField("CreatedDate")
    private Date createdDate;

    @TableField("UpdateBy")
    private Integer updateBy;

    @TableField("UpdateDate")
    private Date updateDate;

    @TableField("HospitalCode")
    private Integer hospitalCode = 3; // 默认值为 3，不能为NULL
}
