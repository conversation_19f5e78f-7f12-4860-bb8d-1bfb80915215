package com.sunhealth.ihhis.model.entity.report;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.util.Date;
import lombok.Data;

/**
 * 检验报告详情
 */
@Data
@TableName("Report_Test_Detail")
public class ReportTestDetail implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @TableId("Id")
    private Long id;

    /**
     * 报告编号
     */
    @TableField("ReportNo")
    private String reportNo;

    /**
     * 报告明细编号
     */
    @TableField("ReportDetailNo")
    private String reportDetailNo;

    /**
     * 检验项目编码
     */
    @TableField("ItemCode")
    private String itemCode;

    /**
     * 检验项目名称
     */
    @TableField("ItemName")
    private String itemName;

    /**
     * 检验结果
     */
    @TableField("ItemResult")
    private String itemResult;

    /**
     * 结果单位
     */
    @TableField("ResultUnit")
    private String resultUnit;

    /**
     * 异常值标志;(N:阴性、P:阳性、H:高值、L:低值、M:正常值)
     */
    @TableField("AbnormalFlag")
    private String abnormalFlag;

    /**
     * 参考值
     */
    @TableField("RefValue")
    private String refValue;

    /**
     * 上限
     */
    @TableField("UpperValue")
    private String upperValue;

    /**
     * 下限
     */
    @TableField("LowerValue")
    private String lowerValue;

    /**
     * 项目排序
     */
    @TableField("Sort")
    private Integer sort;

    /**
     * 接收时间戳
     */
    @TableField("ReceiveTime")
    private Date receiveTime;
}
