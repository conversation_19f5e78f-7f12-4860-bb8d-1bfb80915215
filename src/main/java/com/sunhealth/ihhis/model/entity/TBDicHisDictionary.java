package com.sunhealth.ihhis.model.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

@Data
@TableName("TB_Dic_HisDictionary")
public class TBDicHisDictionary {

    @TableField("HisDictionaryCode")
    private Integer hisDictionaryCode;

    @TableField("HisDictionaryName")
    private String hisDictionaryName;

    @TableField("DictionaryTypeID")
    private Integer dictionaryTypeID;

    @TableField("HospitalId")
    private Integer hospitalId;

    @TableField("IsUse")
    private Integer isUse;
}
