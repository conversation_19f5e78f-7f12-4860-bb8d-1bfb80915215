package com.sunhealth.ihhis.model.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.util.Date;

@Data
@TableName("System_Tb_ItemCategory")
public class SystemTBItemCategory {

    private static final long serialVersionUID = 1L;

    /**
     * ItemCode
     */
    @TableId(value = "ItemCode", type = IdType.NONE)
    private Integer itemCode;

    /**
     * ItemName
     */
    @TableField("ItemName")
    private String itemName;

    /**
     * MzUse
     */
    @TableField("MzUse")
    private Boolean mzUse;

    /**
     * WardUse
     */
    @TableField("WardUse")
    private Boolean wardUse;

    /**
     * DrugUse
     */
    @TableField("DrugUse")
    private Boolean drugUse;

    /**
     * GhUse
     */
    @TableField("GhUse")
    private Boolean ghUse;

    /**
     * MzPrintOrder
     */
    @TableField("MzPrintOrder")
    private Integer mzPrintOrder;

    /**
     * WardPrintOrder
     */
    @TableField("WardPrintOrder")
    private Integer wardPrintOrder;

    /**
     * YbMzCode
     */
    @TableField("YbMzCode")
    private String ybMzCode;

    /**
     * YbZyCode
     */
    @TableField("YbZyCode")
    private String ybZyCode;

    /**
     * HbCategory
     */
    @TableField("HbCategory")
    private Integer hbCategory;

    /**
     * ItemNameEng
     */
    @TableField("ItemNameEng")
    private String itemNameEng;

    /**
     * Stopped
     */
    @TableField("Stopped")
    private Integer stopped;

    /**
     * HospitalCode
     */
    @TableField("HospitalCode")
    private String hospitalCode;

    /**
     * CreatorId
     */
    @TableField("CreatorId")
    private Integer creatorId;

    /**
     * CreateDate
     */
    @TableField("CreateDate")
    private Date createDate;

    /**
     * UpdaterId
     */
    @TableField("UpdaterId")
    private Integer updaterId;

    /**
     * UpdateDate
     */
    @TableField("UpdateDate")
    private Date updateDate;

    /**
     * ClassCode
     */
    @TableField("ClassCode")
    private String classCode;
}
