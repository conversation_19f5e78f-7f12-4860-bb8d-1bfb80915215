package com.sunhealth.ihhis.model.entity.register;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

@Data
@TableName("Reg_GjYiBao_PreRegister")
public class GJYiBaoPreRegister implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId(value = "RegNo")
    private Long regNo;

    @TableField(value = "MdtrtId")
    private String mdtrtId;

    @TableField(value = "PsnNo")
    private String psnNo;

    @TableField(value = "IptOtpNo")
    private String iptOtpNo;

    @TableField(value = "SetlId")
    private String setlId;

    @TableField(value = "MdtrtCertType")
    private String mdtrtCertType;

    @TableField(value = "MdtrtCertNo")
    private String mdtrtCertNo;

    @TableField(value = "MedType")
    private String medType;

    @TableField(value = "MedfeeSumamt")
    private BigDecimal medfeeSumamt;

    @TableField(value = "PsnSetlway")
    private String psnSetlway;

    @TableField(value = "ChrgBchno")
    private String chrgBchno;

    @TableField(value = "AcctUsedFlag")
    private String acctUsedFlag;

    @TableField(value = "Insutype")
    private String insutype;

    @TableField(value = "Invono")
    private String invono;

    @TableField(value = "FulamtOwnpayAmt")
    private BigDecimal fulamtOwnpayAmt;

    @TableField(value = "OverlmtSelfpay")
    private BigDecimal overlmtSelfpay;

    @TableField(value = "PreselfpayAmt")
    private BigDecimal preselfpay_amt;

    @TableField(value = "InscpScpAmt")
    private BigDecimal inscpScpAmt;

    @TableField(value = "PubHospRfomFlag")
    private String pubHospRfomFlag;

    @TableField(value = "PsnName")
    private String psnName;

    @TableField(value = "PsnCertType")
    private String psnCertType;

    @TableField(value = "Certno")
    private String certno;

    @TableField(value = "Gend")
    private String gend;

    @TableField(value = "Naty")
    private String naty;

    @TableField(value = "Brdy")
    private Date brdy;

    @TableField(value = "Age")
    private BigDecimal age;

    @TableField(value = "PsnType")
    private String psnType;

    @TableField(value = "CvlservFlag")
    private String cvlservFlag;

    @TableField(value = "SetlTime")
    private Date setlTime;

    @TableField(value = "ActPayDedc")
    private BigDecimal actPayDedc;

    @TableField(value = "HifpPay")
    private BigDecimal hifpPay;

    @TableField(value = "PoolPropSelfpay")
    private BigDecimal poolPropSelfpay;

    @TableField(value = "CvlservPay")
    private BigDecimal cvlservPay;

    @TableField(value = "HifesPay")
    private BigDecimal hifesPay;

    @TableField(value = "HifmiPay")
    private BigDecimal hifmiPay;

    @TableField(value = "HifobPay")
    private BigDecimal hifobPay;

    @TableField(value = "MafPay")
    private BigDecimal mafPay;

    @TableField(value = "OthPay")
    private BigDecimal othPay;

    @TableField(value = "FundPaySumamt")
    private BigDecimal fundPaySumamt;

    @TableField(value = "PsnPartAmt")
    private BigDecimal psnPartAmt;

    @TableField(value = "AcctPay")
    private BigDecimal acctPay;

    @TableField(value = "PsnCashPay")
    private BigDecimal psnCashPay;

    @TableField(value = "HospPartAmt")
    private BigDecimal hospPartAmt;

    @TableField(value = "Balc")
    private BigDecimal balc;

    @TableField(value = "AcctMulaidPay")
    private BigDecimal acctMulaidPay;

    @TableField(value = "MedinsSetlId")
    private String medinsSetlId;

    @TableField(value = "ClrOptins")
    private String clrOptins;

    @TableField(value = "ClrWay")
    private String clrWay;

    @TableField(value = "ClrType")
    private String clrType;

    @TableField(value = "FundPayType")
    private String fundPayType;

    @TableField(value = "CrtPaybLmtAmt")
    private BigDecimal crtPaybLmtAmt;

    @TableField(value = "FundPayamt")
    private BigDecimal fundPayamt;

    @TableField(value = "FundPayTypeName")
    private String fundPayTypeName;

    @TableField(value = "SetlProcInfo")
    private String setlProcInfo;

    @TableField(value = "HospitalCode")
    private Integer hospitalCode;

    @TableField(value = "Flag")
    private Integer flag;

    @TableField(value = "UpdateTime")
    private Date updateTime;

    @TableField(value = "CreateTime")
    private Date createTime;

    @TableField(value = "MsgID")
    private String msgID;

    @TableField(value = "InsuplcAdmdvs")
    private String insuplcAdmdvs;

    @TableField(value = "exp_content")
    private String expContent;

}

