package com.sunhealth.ihhis.model.entity.report;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModelProperty;
import java.io.Serializable;
import java.util.Date;
import lombok.Data;

/**
 * 检验报告
 */
@Data
@TableName("Report_Test_Info")
public class ReportTestInfo implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @TableId("Id")
    private Long id;

    /**
     * 患者ID
     */
    @TableField("PatientId")
    private String patientId;

    /**
     * 患者类型：01 门诊 02 住院 03 急诊 04 体检
     */
    @TableField("PatientType")
    private String patientType;

    /**
     * 就诊流水号：门诊挂号流水、住院流水号
     */
    @TableField("VisitNo")
    private String visitNo;

    /**
     * 患者姓名
     */
    @TableField("PatName")
    private String patName;

    /**
     * 住院号(门诊号)
     */
    @TableField("HospNo")
    private String hospNo;

    /**
     * 卡号
     */
    @TableField("CardNo")
    private String cardNo;

    /**
     * 床号
     */
    @TableField("BedNo")
    private String bedNo;

    /**
     * 医院名称
     */
    @TableField("HospitalName")
    private String hospitalName;

    /**
     * 院区ID：医院ID
     */
    @TableField("HospitalId")
    private Integer hospitalId;

    /**
     * 报告编号
     */
    @TableField("ReportNo")
    private String reportNo;

    /**
     * 样本类型
     */
    @TableField("SpeimenType")
    private String speimenType;

    /**
     * 样本名称：报告名称
     */
    @TableField("SpeimenTypeName")
    private String speimenTypeName;

    /**
     * 是否微生物(0否，1是)
     */
    @TableField("IsMicrobes")
    private Integer isMicrobes;

    /**
     * 申请单Id
     */
    @TableField("ApplyId")
    private String applyId;

    /**
     * 申请科室
     */
    @TableField("ApplyDept")
    private String applyDept;

    /**
     * 申请科室名称
     */
    @TableField("ApplyDeptName")
    private String applyDeptName;

    /**
     * 申请病区
     */
    @TableField("ApplyWard")
    private String applyWard;

    /**
     * 申请病区名称
     */
    @TableField("ApplyWardName")
    private String applyWardName;

    /**
     * 申请医生ID
     */
    @TableField("ApplyDoctor")
    private String applyDoctor;

    /**
     * 申请医生名称
     */
    @TableField("ApplyDoctorName")
    private String applyDoctorName;

    /**
     * 申请时间
     */
    @TableField("ApplyTime")
    private Date applyTime;

    /**
     * 检验编码
     */
    @TableField("InspectionItem")
    private String inspectionItem;

    /**
     * 检验项目
     */
    @TableField("InspectionItemName")
    private String inspectionItemName;

    /**
     * 检验设备
     */
    @TableField("InstrumentNo")
    private String instrumentNo;

    /**
     * 采样时间
     */
    @TableField("SpeimentSampleTime")
    private Date speimentSampleTime;

    /**
     * 送检时间
     */
    @TableField("SpeimentReceiveTime")
    private Date speimentReceiveTime;

    /**
     * 送检医生名称
     */
    @TableField("SpeimentReceiveDoctor")
    private String speimentReceiveDoctor;

    /**
     * 检验科室
     */
    @TableField("TestDept")
    private String testDept;

    /**
     * 检验科室名称
     */
    @TableField("TestDeptName")
    private String testDeptName;

    /**
     * 检验时间
     */
    @TableField("TestTime")
    private Date testTime;

    /**
     * 检查医生ID
     */
    @TableField("TestDoctor")
    private String testDoctor;

    /**
     * 检查医生名称
     */
    @TableField("TestDoctorName")
    private String testDoctorName;

    /**
     * 报告时间
     */
    @TableField("ReportTime")
    private Date reportTime;

    /**
     * 报告医生
     */
    @TableField("ReportDoctor")
    private String reportDoctor;

    /**
     * 报告医生名称
     */
    @TableField("ReportDoctorName")
    private String reportDoctorName;

    /**
     * 报告审核时间
     */
    @TableField("AuditorTime")
    private Date auditorTime;

    /**
     * 报告审核医生
     */
    @TableField("AuditorDoctor")
    private String auditorDoctor;

    /**
     * 报告审核医生名称
     */
    @TableField("AuditorDoctorName")
    private String auditorDoctorName;

    /**
     * 报告Url地址
     */
    @TableField("ReportUrl")
    private String reportUrl;

    /**
     * 诊断编码
     */
    @TableField("DiagCode")
    private String diagCode;

    /**
     * 诊断名称
     */
    @TableField("DiagName")
    private String diagName;

    /**
     * 备注
     */
    @TableField("Remark")
    private String remark;

    /**
     * 危机值标志
     */
    @TableField("CriticalFlag")
    private Integer criticalFlag;

    /**
     * 危机值内容
     */
    @TableField("CriricalContent")
    private String criricalContent;

    /**
     * 报告状态(0 作废 1审核 )
     */
    @TableField("ReportStatus")
    private Integer reportStatus;

    /**
     * 接收时间戳
     */
    @TableField("ReceiveTime")
    private Date receiveTime;

    /**
     * 作废者
     */
    @TableField("Canceller")
    private String canceller;

    /**
     * 作废时间
     */
    @TableField("CancelTime")
    private Date cancelTime;


    @TableField(exist = false)
    private String sex;
    @TableField(exist = false)
    private String age;
}
