package com.sunhealth.ihhis.model.entity.yibao;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

@Data
@TableName("Reg_Online_GjYiBao_Interface_Log")
public class RegOnlineGjYiBaoInterfaceLog implements Serializable {

    @TableId("ID")
    private Long id; // 不能为NULL

    @TableField("Name")
    private String name; // 接口名或编码

    @TableField("Url")
    private String url; // 接口请求地址

    @TableField("Input")
    private String input; // 入参

    @TableField("Output")
    private String output; // 出参

    @TableField("HospitalCode")
    private String hospitalCode; // 医院编码

    @TableField("CreatedDate")
    private Date createdDate; // 创建时间
}
