package com.sunhealth.ihhis.model.entity.charge;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import lombok.Data;
@Data
@TableName("Reg_Tb_PreChargeDetail")
public class PreChargeDetail implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId(value = "ID", type = IdType.AUTO)
    private Integer id;

    @TableField("PreChargeNo")
    private Long preChargeNo;

    @TableField("RegistType")
    private Integer registType;

    @TableField("CardNo")
    private String cardNo;

    @TableField("PatId")
    private Long patId;

    @TableField("NewPatId")
    private Long newPatId;

    @TableField("RegNo")
    private Long regNo;

    @TableField("RecipeNum")
    private Integer recipeNum;

    @TableField("RecipeID")
    private Long recipeID;

    @TableField("RecipeDetlID")
    private Long recipeDetlID;

    @TableField("ItemID")
    private Integer itemID;

    @TableField("NewItemID")
    private Integer newItemID;

    @TableField("ItemName")
    private String itemName;

    @TableField("Quantiy")
    private BigDecimal quantity;

    @TableField("ItemCategory")
    private Integer itemCategory;

    @TableField("ItemCategoryName")
    private String itemCategoryName;

    @TableField("PackageNo")
    private Integer packageNo;

    @TableField("GroupNo")
    private Integer groupNo;

    @TableField("ClinicUnit")
    private String clinicUnit;

    @TableField("BasicUnit")
    private String basicUnit;

    @TableField("ClinicQty")
    private Integer clinicQty;

    @TableField("Usage")
    private String usage;

    @TableField("Dosage")
    private String dosage;

    @TableField("DosageUnit")
    private String dosageUnit;

    @TableField("DrugGauge")
    private String drugGauge;

    @TableField("CheckCode")
    private String checkCode;

    @TableField("ExecuteDept")
    private Integer executeDept;

    @TableField("Times")
    private Integer times;

    @TableField("Price")
    private BigDecimal price;

    @TableField("ExpensePrice")
    private BigDecimal expensePrice;

    @TableField("NonExpensePrice")
    private BigDecimal nonExpensePrice;

    @TableField("Status")
    private Integer status;

    @TableField("TotalAmount")
    private BigDecimal totalAmount;

    @TableField("DoctorId")
    private Integer doctorId;

    @TableField("DeptId")
    private Integer deptId;

    @TableField("DoctorLevel")
    private Integer doctorLevel;

    @TableField("FeeType")
    private Integer feeType;

    @TableField("IsDrug")
    private Integer isDrug;

    @TableField("SpecialFlag")
    private Integer specialFlag;

    @TableField("DataFrom")
    private Integer dataFrom;

    @TableField("FromFlag")
    private Integer fromFlag;

    @TableField("DiscountAmount")
    private BigDecimal discountAmount;

    @TableField("IsDelete")
    private Boolean isDelete;

    @TableField("CreatedBy")
    private Integer createdBy;

    @TableField("CreatedDate")
    private Date createdDate;

    @TableField("UpdateBy")
    private Integer updateBy;

    @TableField("UpdateDate")
    private Date updateDate;

    @TableField("HospitalCode")
    private Integer hospitalCode;

    @TableField("RecipeOn")
    private Date recipeOn;

    @TableField("Cfzt")
    private String cfzt;

    @TableField("Cfztmc")
    private String cfztmc;

    @TableField("IsDebitPay")
    private Boolean isDebitPay;

    @TableField("DebitAmt")
    private BigDecimal debitAmt;

    @TableField("DebitPayInvoiceID")
    private Long debitPayInvoiceID;

    @TableField(exist = false)
    private Integer scbj;
}

