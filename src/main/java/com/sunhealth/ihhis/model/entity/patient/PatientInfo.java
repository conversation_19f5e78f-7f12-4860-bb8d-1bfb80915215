package com.sunhealth.ihhis.model.entity.patient;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.util.Date;
import lombok.Data;

@Data
@TableName("Tbt_Interface_PatientInfo")
public class PatientInfo implements Serializable {

    @TableId(value = "id", type = IdType.AUTO) // 不能为NULL
    private Long id;

    @TableField("flag")
    private Integer flag;

    @TableField("regno")
    private Long regno;

    @TableField("create_time")
    private Date createTime;
}
