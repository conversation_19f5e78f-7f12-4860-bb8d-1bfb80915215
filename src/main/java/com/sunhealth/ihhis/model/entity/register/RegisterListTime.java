package com.sunhealth.ihhis.model.entity.register;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import java.io.Serializable;
import java.util.Date;
import org.springframework.beans.BeanUtils;

@Data
@TableName("Reg_Tb_RegisterList_Time")
public class RegisterListTime implements Serializable {
    private static final long serialVersionUID = 1L;

    @TableId(value = "RegNo")
    private Long regNo;

    @TableField("ReturnRegNo")
    private Long returnRegNo;

    @TableField(value = "PatID")
    private Long patID; // 不能为NULL

    @TableField("NewPatID")
    private Long newPatID;

    @TableField("CardNo")
    private String cardNo;

    @TableField("OutPatientNo")
    private String outPatientNo;

    @TableField("HospNo")
    private String hospNo;

    @TableField("PatName")
    private String patName;

    @TableField("CourseID")
    private Integer courseID;

    @TableField("CourseName")
    private String courseName;

    @TableField("DeptID")
    private Integer deptID;

    @TableField("DoctorID")
    private Integer doctorID;

    @TableField("DoctorLevel")
    private Integer doctorLevel;

    @TableField("ChargeType")
    private Integer chargeType;

    @TableField("BlanceWay")
    private Integer blanceWay;

    @TableField("RegistType")
    private Integer registType;

    @TableField("AppointmentWay")
    private Integer appointmentWay;

    @TableField("AppointmentNo")
    private String appointmentNo;

    @TableField("RegistOrder")
    private Integer registOrder;

    @TableField("RegistNum")
    private Integer registNum;

    @TableField("RegistMode")
    private Integer registMode;

    @TableField("VisitTime")
    private String visitTime;

    @TableField(value = "Status")
    private Integer status; // 不能为NULL

    @TableField("RegistTime")
    private Date registTime;

    @TableField("ComputerNo")
    private String computerNo;

    @TableField("OpCode")
    private Integer opCode;

    @TableField("CreateTime")
    private Date createTime;

    @TableField("CureCode")
    private String cureCode;

    @TableField("VisitFlag")
    private Integer visitFlag;

    @TableField("IsDelete")
    private Boolean isDelete; // 不能为NULL

    @TableField("CreatedBy")
    private Integer createdBy; // 不能为NULL

    @TableField("CreatedDate")
    private Date createdDate; // 不能为NULL

    @TableField("UpdateDate")
    private Date updateDate; // 不能为NULL

    @TableField("updateby")
    private Integer updateby; // 不能为NULL

    @TableField("ReferralFlag")
    private Integer referralFlag;

    @TableField("FlowNo")
    private Integer flowNo;

    @TableField("DeptKind")
    private Short deptKind;

    @TableField("FzFlag")
    private Integer fzFlag;

    @TableField(value = "HospitalCode")
    private Integer hospitalCode; // 不能为NULL

    @TableField("UnitNo")
    private String unitNo;

    @TableField("GhDoctor")
    private Integer ghDoctor;

    @TableField("JsFlag")
    private Integer jsFlag;

    @TableField("VipFlag")
    private Integer vipFlag;

    @TableField("ReservationType")
    private String reservationType;

    @TableField("AppointmentFlag")
    private String appointmentFlag;

    @TableField("IsGreenChannel")
    private Boolean isGreenChannel;

    @TableField("IsDebitPay")
    private Boolean isDebitPay;

    public RegisterListTime() {}
    public RegisterListTime(PreRegisterList register) {
        BeanUtils.copyProperties(register, this);
//        this.regNo = register.getRegNo();
//        this.patID = register.getPatID();
//        this.newPatID = register.getNewPatID();
//        this.cardNo = register.getCardNo();
//        this.outPatientNo = register.getOutPatientNo();
//        this.hospNo = register.getHospNo();
//        this.patName = register.getPatName();
//        this.courseID = register.getCourseID();
//        this.courseName = register.getCourseName();
//        this.deptID = register.getDeptID();
//        this.doctorID = register.getDoctorID();
//        this.doctorLevel = register.getDoctorLevel();
//        this.chargeType = register.getChargeType();
//        this.blanceWay = register.getBlanceWay();
//        this.registType = register.getRegistType();
//        this.appointmentWay = register.getAppointmentWay();
//        this.appointmentNo = register.getAppointmentNo();
//        this.registOrder = register.getRegistOrder();
//        this.registMode = register.getRegistMode();
//        this.visitTime = register.getVisitTime();
//        this.status = register.getStatus();
//        this.registTime = register.getRegistTime();
//        this.computerNo = register.getComputerNo();
//        this.cureCode = register.getCureCode();
//        this.visitFlag = register.getVisitFlag();
//        this.isDelete = register.getIsDelete();
//        this.createdBy = register.getCreatedBy();
//        this.updateby = register.getUpdateby();
//        this.referralFlag = register.getReferralFlag();
//        this.deptKind = register.getDeptKind();
//        this.fzFlag = register.getFzFlag();
//        this.hospitalCode = register.getHospitalCode();
//        this.jsFlag = register.getJsFlag();
//        this.reservationType = register.getReservationType();
//        this.appointmentFlag = register.getAppointmentFlag();
//        this.isGreenChannel = register.getIsGreenChannel();
//        this.isDebitPay = register.getIsDebitPay();
//        this.ghDoctor = register.getDoctorID();
//        this.vipFlag = register.getVipFlag();
//        this.flowNo = register.getFlowNo();
//        this.unitNo = register.getUnitNo();




    }
}
