package com.sunhealth.ihhis.model.entity.gjyibao;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import lombok.Data;

@Data
@TableName("Reg_GjYiBao_RefundRegister")
public class GJYiBaoRefundRegister implements Serializable {
    private static final long serialVersionUID = 1L;

    @TableField("RegNo")
    private Long regNo;

    @TableField("MdtrtId")
    private String mdtrtId;

    @TableField("PsnNo")
    private String psnNo;

    @TableField("IptOtpNo")
    private String iptOtpNo;

    @TableField("SetlId")
    private String setlId;

    @TableField("MdtrtCertType")
    private String mdtrtCertType;

    @TableField("MdtrtCertNo")
    private String mdtrtCertNo;

    @TableField("MedType")
    private String medType;

    @TableField("MedfeeSumamt")
    private BigDecimal medfeeSumamt;

    @TableField("PsnSetlway")
    private String psnSetlway;

    @TableField("ChrgBchno")
    private String chrgBchno;

    @TableField("AcctUsedFlag")
    private String acctUsedFlag;

    @TableField("Insutype")
    private String insutype;

    @TableField("Invono")
    private String invono;

    @TableField("FulamtOwnpayAmt")
    private BigDecimal fulamtOwnpayAmt;

    @TableField("OverlmtSelfpay")
    private BigDecimal overlmtSelfpay;

    @TableField("PreselfpayAmt")
    private BigDecimal preselfpayAmt;

    @TableField("InscpScpAmt")
    private BigDecimal inscpScpAmt;

    @TableField("PubHospRfomFlag")
    private String pubHospRfomFlag;

    @TableField("PsnName")
    private String psnName;

    @TableField("PsnCertType")
    private String psnCertType;

    @TableField("Certno")
    private String certno;

    @TableField("Gend")
    private String gend;

    @TableField("Naty")
    private String naty;

    @TableField("Brdy")
    private Date brdy;

    @TableField("Age")
    private BigDecimal age;

    @TableField("PsnType")
    private String psnType;

    @TableField("CvlservFlag")
    private String cvlservFlag;

    @TableField("SetlTime")
    private Date setlTime;

    @TableField("ActPayDedc")
    private BigDecimal actPayDedc;

    @TableField("HifpPay")
    private BigDecimal hifpPay;

    @TableField("PoolPropSelfpay")
    private BigDecimal poolPropSelfpay;

    @TableField("CvlservPay")
    private BigDecimal cvlservPay;

    @TableField("HifesPay")
    private BigDecimal hifesPay;

    @TableField("HifmiPay")
    private BigDecimal hifmiPay;

    @TableField("HifobPay")
    private BigDecimal hifobPay;

    @TableField("MafPay")
    private BigDecimal mafPay;

    @TableField("OthPay")
    private BigDecimal othPay;

    @TableField("FundPaySumamt")
    private BigDecimal fundPaySumamt;

    @TableField("PsnPartAmt")
    private BigDecimal psnPartAmt;

    @TableField("AcctPay")
    private BigDecimal acctPay;

    @TableField("PsnCashPay")
    private BigDecimal psnCashPay;

    @TableField("HospPartAmt")
    private BigDecimal hospPartAmt;

    @TableField("Balc")
    private BigDecimal balc;

    @TableField("AcctMulaidPay")
    private BigDecimal acctMulaidPay;

    @TableField("MedinsSetlId")
    private String medinsSetlId;

    @TableField("ClrOptins")
    private String clrOptins;

    @TableField("ClrWay")
    private String clrWay;

    @TableField("ClrType")
    private String clrType;

    @TableField("FundPayType")
    private String fundPayType;

    @TableField("CrtPaybLmtAmt")
    private BigDecimal crtPaybLmtAmt;

    @TableField("FundPayamt")
    private BigDecimal fundPayamt;

    @TableField("FundPayTypeName")
    private String fundPayTypeName;

    @TableField("SetlProcInfo")
    private String setlProcInfo;

    @TableField("HospitalCode")
    private Integer hospitalCode;

    @TableField("Flag")
    private Integer flag;

    @TableField("UpdateTime")
    private Date updateTime;

    @TableField("CreateTime")
    private Date createTime;

    @TableField("CurrentAccountPay")
    private BigDecimal currentAccountPay;

    @TableField("LastAccountPay")
    private BigDecimal lastAccountPay;

    @TableField("LastAccountAmt")
    private BigDecimal lastAccountAmt;

    @TableField("CurrentAccountAmt")
    private BigDecimal currentAccountAmt;

    @TableField("InsuplcAdmdvs")
    private String insuplcAdmdvs;

    @TableField("MsgID")
    private String msgID;

    @TableField("operationCode")
    private String operationCode;
}


