package com.sunhealth.ihhis.model.entity.charge;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

@Data
@TableName("Reg_Tb_ChargeList_Time")
public class ChargeListTime implements Serializable {
    private static final long serialVersionUID = 1L;

    @TableId("ChargeNo")
    private Long chargeNo; // 主键字段 ChargeNo

    @TableField("ReturnChargeNo")
    private Long returnChargeNo;

    @TableField("PatID")
    private Long patID;

    @TableField("NewPatID")
    private Long newPatID;

    @TableField("RegNo")
    private Long regNo;

    @TableField("CardType")
    private Integer cardType;

    @TableField("CardNo")
    private String cardNo;

    @TableField("CardData")
    private String cardData;

    @TableField("OutpatientNo")
    private String outpatientNo;

    @TableField("AccountFlag")
    private String accountFlag;

    @TableField("RegistType")
    private Integer registType;

    @TableField("RecipeCount")
    private Integer recipeCount;

    @TableField("ChargeTime")
    private Date chargeTime;

    @TableField("ChargeType")
    private Integer chargeType;

    @TableField("ComputerNo")
    private String computerNo;

    @TableField("Status")
    private Integer status;

    @TableField("EvidenceNo")
    private String evidenceNo;

    @TableField("IndustrialInjury")
    private String industrialInjury;

    @TableField("CureCode")
    private String cureCode;

    @TableField("DiagnoseCode")
    private String diagnoseCode;

    @TableField("OpCode")
    private Integer opCode;

    @TableField("CreateTime")
    private Date createTime;

    @TableField("IsDelete")
    private Boolean isDelete = false; // 不能为NULL

    @TableField("CreatedBy")
    private Integer createdBy;

    @TableField("CreatedDate")
    private Date createdDate;

    @TableField("UpdateBy")
    private Integer updateBy;

    @TableField("UpdateDate")
    private Date updateDate;

    @TableField("DataFrom")
    private Integer dataFrom;

    @TableField("HospitalCode")
    private Integer hospitalCode;

    @TableField("DiagCode")
    private String diagCode;

    @TableField("PatType")
    private Short patType;

    @TableField("JsFlag")
    private Integer jsFlag;

    @TableField("IsDebitPay")
    private Boolean isDebitPay;
}

