package com.sunhealth.ihhis.model.entity.report;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import java.io.Serializable;
import java.util.Date;

@Data
@TableName("Apply_Tb_List")
public class ApplyTbList implements Serializable {
    private static final long serialVersionUID = 1L;

    @TableId(type = IdType.AUTO)
    private Integer id; // 主键(Apply_Key_List)

    @TableField("PatSource")
    private Integer patSource; // 不能为NULL 病人来源 1 门诊 2 急诊 3 住院 9 体检

    @TableField("VisitId")
    private Integer visitId; // 门急诊挂号序号 住院流水号 体检序号

    @TableField("PatId")
    private Integer patId; // 不能为NULL 病人ID

    @TableField("BigformId")
    private Integer bigformId; // 申请单类型(取申请单大类ID)

    @TableField("ApplyUserId")
    private Integer applyUserId; // 开单医生

    @TableField("ApplyDeptId")
    private Integer applyDeptId; // 开单科室

    @TableField("ChiefComplaint")
    private String chiefComplaint; // 主诉

    @TableField("MedHistory")
    private String medHistory; // 现病史

    @TableField("Diagnosis")
    private String diagnosis; // 诊断

    @TableField("InspTarget")
    private String inspTarget; // 检查目的

    @TableField("GravidaFlag")
    private Boolean gravidaFlag; // 孕妇标识

    @TableField("Status")
    private Integer status; // 申请单状态 1 有效 0 作废

    @TableField("OrderFlag")
    private Boolean orderFlag; // 可预约标识

    @TableField("ExeDeptId")
    private String exeDeptId; // 可执行科室

    @TableField("OrderStatus")
    private Integer orderStatus; // 预约状态 0 未预约 1 已预约

    @TableField("OrderOprDate")
    private Date orderOprDate; // 预约操作时间

    @TableField("OrderDate")
    private Date orderDate; // 预约日期

    @TableField("OrderTime")
    private String orderTime; // 预约时间点

    @TableField("OrderOprUserId")
    private Integer orderOprUserId; // 预约操作员ID

    @TableField("OrderRemDate")
    private Date orderRemDate; // 预约取消时间

    @TableField("OrderRemUserId")
    private Integer orderRemUserId; // 预约取消操作员ID

    @TableField("ExeFlag")
    private Integer exeFlag; // 执行标识 0 未执行 1 已执行

    @TableField("RealExeDeptId")
    private Integer realExeDeptId; // 执行科室

    @TableField("ExeDate")
    private Date exeDate; // 执行时间

    @TableField("RelStatus")
    private Integer relStatus; // 报告发布状态 0 未发布 1 已发布

    @TableField("RelDate")
    private Date relDate; // 报告发布时间

    @TableField("RelUserId")
    private Integer relUserId; // 报告发布人ID

    @TableField("RelUserName")
    private String relUserName; // 报告发布人姓名

    @TableField("CreatorId")
    private Integer creatorId; // 创建人

    @TableField("CreateDate")
    private Date createDate; // 创建时间

    @TableField("UpdatorId")
    private Integer updatorId; // 修改人

    @TableField("UpdateDate")
    private Date updateDate; // 修改时间

    @TableField("DelFlag")
    private Byte delFlag; // 删除标记 0否1是

    @TableField("HospitalId")
    private Byte hospitalId; // 医院编码

    @TableField("GroupId")
    private Integer groupId; // 所属开单批次ID

    @TableField("InspectionId")
    private String inspectionId; // 医技申请单

    @TableField("patName")
    private String patName; // 患者姓名

    @TableField("patSex")
    private String patSex; // 患者性别

    @TableField("patAge")
    private Integer patAge; // 患者年龄

    @TableField("BloodPressure")
    private String bloodPressure; // 血压

    @TableField("ImplantedFlag")
    private Integer implantedFlag; // 是否植入

    @TableField("BloodFlag")
    private Integer bloodFlag; // 是否出血

    @TableField("HospNo")
    private String hospNo; // 院号

    @TableField("ApplyUserName")
    private String applyUserName; // 医生名称

    @TableField("MainMentalSymptoms")
    private String mainMentalSymptoms; // 主要精神症状

    @TableField("InspectionFlag")
    private Integer inspectionFlag; // 医技申请状态

    @TableField("Operator")
    private Integer operator; // 申请单执行操作人

    @TableField("OpTime")
    private Date opTime; // 申请单执行操作时间

    @TableField("CancelOperator")
    private Integer cancelOperator; // 申请单撤销操作人

    @TableField("CancelTime")
    private Date cancelTime; // 申请单撤销时间

    @TableField("SymptomId")
    private Integer symptomId; // 症状id

    @TableField("Symptom")
    private String symptom; // 症状

    @TableField("CardioId")
    private Integer cardioId; // 心血管id

    @TableField("Cardiovascular")
    private String cardiovascular; // 心血管病史

    @TableField("UseMedical")
    private String useMedical; // 用药

    @TableField("ClinicalRecords")
    private String clinicalRecords; // 临床记录

    @TableField("SpecialSigns")
    private String specialSigns; // 体征录入

    @TableField("SpecialInspection")
    private String specialInspection; // 特殊检查

    @TableField("ScientificUsage")
    private String scientificUsage; // 科学用法

    @TableField("systolicPressure")
    private String systolicPressure; // 收缩压

    @TableField("wardName")
    private String wardName; // 病区名

    @TableField("bedNo")
    private String bedNo; // 床号

    @TableField("EducationName")
    private String educationName; // 文化程度

    @TableField("weight")
    private Double weight; // 体重

    @TableField("Height")
    private Double height; // 身高

    @TableField("InHospNum")
    private Integer inHospNum; // 住院次数

    @TableField("Education")
    private Integer education; // 文化程度id

    @TableField("ExeHospitalId")
    private Integer exeHospitalId; // 执行科室院区

    @TableField("CheckSubParts")
    private String checkSubParts; // 申请单信息概要表

    @TableField("InternalFlag")
    private Boolean internalFlag; // 内部标志

    @TableField("CirStatus")
    private String cirStatus; // CirStatus

    @TableField("ApplyClass")
    private String applyClass; // ApplyClass

    @TableField("ApplyType")
    private Integer applyType; // ApplyType

    @TableField("FormId")
    private Integer formId; // FormId

    @TableField("Remark")
    private String remark; // Remark
}

