package com.sunhealth.ihhis.model.entity.charge;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import java.io.Serializable;
import java.math.BigDecimal;

@Data
@TableName("Reg_Tb_PreCharge_Amt")
public class PreChargeAmt implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId(value = "ChargeNo", type = IdType.NONE)
    private Long chargeNo;

    @TableField("RegNo")
    private Long regNo;

    @TableField("RecipeID")
    private Long recipeID;

    @TableField("RecipeDetlID")
    private Long recipeDetlID;

    @TableField("DiscountAmount")
    private BigDecimal discountAmount;

    @TableField("InsuranceTradeAmount")
    private BigDecimal insuranceTradeAmount;

    @TableField("SelfAmount")
    private BigDecimal selfAmount;

    @TableField("ClassifyTotal")
    private BigDecimal classifyTotal;

    @TableField("InsuranceCashAmount")
    private BigDecimal insuranceCashAmount;

    @TableField("CreditAmt")
    private BigDecimal creditAmt;

    @TableField("RealAmt")
    private BigDecimal realAmt;

    @TableField("OtherAmt")
    private BigDecimal otherAmt;

    @TableField("JzAmount")
    private BigDecimal jzAmount;

    @TableField("FeeType")
    private Integer feeType;
}
