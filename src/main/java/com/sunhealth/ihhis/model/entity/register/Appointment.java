package com.sunhealth.ihhis.model.entity.register;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import lombok.Data;

@Data
@TableName("TB_Appointment")
public class Appointment  implements Serializable {
    private static final long serialVersionUID = 1L;

    @TableId(value = "AppointmentID", type = IdType.AUTO) // 预约编号
    private Integer appointmentID;

    @TableField("PatName") // 患者姓名
    private String patName;

    @TableField("CertificateNo") // 身份证号
    private String certificateNo;

    @TableField("Telephone") // 电话号码
    private String telephone;

    @TableField("SubjectID") // 科目编号
    private Integer subjectID;

    @TableField("VisitDate") // 预约日期
    private String visitDate;

    @TableField("AppointmentNum") // 预约号
    private Integer appointmentNum;

    @TableField("AppointmentSeqNum") //
    private Integer appointmentSeqNum;

    @TableField("AppointmentType") // 预约类型
    private Integer appointmentType;

    @TableField("AppointmentOrderID") // 预约排序id
    private String appointmentOrderID;

    @TableField("TimeSpanID") // 时段id
    private Integer timeSpanID;

    @TableField("SourceID") // 号源id
    private Integer sourceID;

    @TableField("AppointmentStatus") // 预约状态
    private Integer appointmentStatus;

    @TableField("IsDelete") // 是否删除
    private Integer isDelete;

    @TableField("IsUse") // 是否启用
    private Integer isUse;

    @TableField("CreatedBy") // 创建人
    private Integer createdBy;

    @TableField("CreatedDate") // 创建时间
    private Date createdDate;

    @TableField("UpdatedBy") // 更新人
    private Integer updatedBy;

    @TableField("UpdatedDate") // 更新时间
    private Date updatedDate;

    @TableField("HospitalCode") // 医院编码
    private String hospitalCode;

    @TableField("DeptId") // 部门id
    private Integer deptId;

    @TableField("DoctorId") // 医生id
    private Integer doctorId;

    @TableField("PatNo") // 患者编号
    private Integer patNo;

    @TableField("CardNo") // 患者卡号
    private String cardNo;

    @TableField("PatSfz") // 身份证号
    private String patSfz;

    @TableField("MovePhone") // 移动电话
    private String movePhone;

    @TableField("YyH") //
    private String yyH;

    @TableField("Sxw") //
    private Short sxw;

    @TableField("OpTime") // 操作时间
    private Date opTime;

    @TableField("Cfz") //
    private Short cfz;

    @TableField("TotAmt") //
    private BigDecimal totAmt;

    @TableField("Zhanghao") // 账号
    private String zhanghao;

    @TableField("BeginTime") // 开始时间
    private String beginTime;

    @TableField("EndTime") // 结束时间
    private String endTime;

    @TableField("ZtStatus") //
    private Integer ztStatus;

    @TableField("CheckDate") // 确认时间
    private Date checkDate;

    @TableField("Operator") // 操作员
    private Integer operator;

    @TableField("Rcs") //
    private Short rcs;

    @TableField("BmTradeNo") //
    private String bmTradeNo;

    @TableField("ParentDeptid") // 上级部门id
    private Integer parentDeptid;

    @TableField("Sex") // 性别
    private String sex;

    @TableField("Zbmz") //
    private Short zbmz;

    @TableField("RemarkA1") //  备注A1
    private String remarkA1;

    @TableField("TimsMs") //
    private String timsMs;

    @TableField("MzorJz") // 时段
    private Short mzorJz;

    @TableField("ComputerNo") // 电脑编号
    private String computerNo;

    @TableField("Yybh") //
    private String yybh;

    @TableField("KsFl") //
    private Integer ksFl;

    @TableField("Bmbh") //
    private String bmbh;

    @TableField("RemarkA2") //  备注A2
    private String remarkA2;

    @TableField("RemarkA3") //  备注A3
    private String remarkA3;

    @TableField("RemarkA4") //  备注A4
    private String remarkA4;

    @TableField("RemarkA5") //  备注A5
    private String remarkA5;

    @TableField("RemarkA_MS") //  备注A_MS
    private String remarkAMS;

    @TableField("YyFlag") //
    private Short yyFlag;

    @TableField("DyTime") //
    private Date dyTime;

    @TableField("SQH") //
    private Integer sqh;

    @TableField("OldOrderNumber") //
    private String oldOrderNumber;

    @TableField("HisCardNo") //
    private String hisCardNo;

    @TableField("Cxr") //
    private String cxr;

    @TableField("Cxsj") //
    private String cxsj;

    @TableField("PatType") //
    private String patType;

    @TableField("SpecialSourceId") //
    private Integer specialSourceId;

    @TableField("VipAppointmentNum") //
    private String vipAppointmentNum;
}


