package com.sunhealth.ihhis.model.entity.invoice;
import java.io.Serializable;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableField;
import lombok.Data;

@Data
@TableName("Reg_Tb_InvoiceInfo")
public class InvoiceInfo implements Serializable {
    private static final long serialVersionUID = 1L;

    @TableId("Id")
    private Long id;

    @TableField("EmployeeID")
    private Long employeeID;

    @TableField("UseTime")
    private java.util.Date useTime;

    @TableField("InvoicePrefix")
    private String invoicePrefix;

    @TableField("InvoiceBegin")
    private Integer invoiceBegin;

    @TableField("InvoiceEnd")
    private Integer invoiceEnd;

    @TableField("Total")
    private Integer total;

    @TableField("CurrentNo")
    private Integer currentNo;

    @TableField("SerialNo")
    private Integer serialNo;

    @TableField("State")
    private Integer state;

    @TableField("Order")
    private Integer order;

    @TableField("UpdateBy")
    private Integer updateBy;

    @TableField("UpdateDate")
    private java.util.Date updateDate;

    @TableField(value = "IsDelete")
    private Integer isDelete; // 不能为NULL

    @TableField("IsUse")
    private Integer isUse;

    @TableField("InvoiceType")
    private Integer invoiceType;

    @TableField("CreatedBy")
    private Integer createdBy;

    @TableField("CreatedDate")
    private java.util.Date createdDate;

    @TableField("GroupId")
    private Long groupId;

    @TableField("HospitalCode")
    private Integer hospitalCode;

    @TableField("IP")
    private String iP; // 不能为NULL
}
