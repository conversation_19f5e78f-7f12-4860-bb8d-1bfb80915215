package com.sunhealth.ihhis.model.entity.charge;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;

/**
 * 这个表没把全部字段放进来
 */
@Data
@TableName( "Drug_Tb_DrugInfomation")
public class DrugTbDrugInfomation implements Serializable {
    private static final long serialVersionUID = 1L;

    @TableId(value = "GlobalId", type = IdType.NONE)
    private Integer globalId;

    @TableField("DrugId")
    private Integer drugId;

    @TableField("HospitalId")
    private Integer hospitalId;

    @TableField("CommonName")
    private String commonName;

    @TableField("Remark")
    private String remark;

    @TableField("StoreNo")
    private String storeNo;

    @TableField("ApprovalNumber")
    private String approvalNumber;

    @TableField("PharmaCode")
    private String pharmaCode;

    @TableField("SkuCode")
    private String skuCode;
}
