package com.sunhealth.ihhis.model.entity.register;

import java.io.Serializable;
import java.util.Date;
import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;

@Data
@TableName("Tbt_Recipe_eInvoiceNo")
public class RecipeEInvoiceNo implements Serializable {
    private static final long serialVersionUID = 1L;

    @TableId(value = "Lsh", type = IdType.AUTO)
    private Integer lsh; // 主键，不能为NULL

    @TableField("WorkerId")
    private Integer workerId; // 不能为NULL

    @TableField("HopitalCode")
    private Integer hospitalCode; // 不能为NULL

    @TableField("InvoId")
    private Integer invoId; // 不能为NULL

    @TableField("CurrentKey")
    private Integer currentKey; // 默认值为0，不能为NULL

    @TableField("FpNum")
    private Integer fpNum; // 默认值为1，不能为NULL

    @TableField(value = "OpTime", fill = FieldFill.INSERT)
    private Date opTime; // 默认值为当前时间，可以为NULL
}
