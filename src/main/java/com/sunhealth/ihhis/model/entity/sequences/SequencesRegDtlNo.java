package com.sunhealth.ihhis.model.entity.sequences;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

/**
 * 挂号明细序号
 */
@Data
@TableName("Reg_Sequences_RegDtlNo")
public class SequencesRegDtlNo {

    @TableId(type = IdType.AUTO)
    private Long id;

    private Integer value;

    public SequencesRegDtlNo(Integer value) {
        this.value = value;
    }
}
