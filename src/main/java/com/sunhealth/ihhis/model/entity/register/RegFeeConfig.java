package com.sunhealth.ihhis.model.entity.register;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

@Data
@TableName("Reg_Tb_RegFeeConfig")
public class RegFeeConfig implements Serializable {
    private static final long serialVersionUID = 1L;

    @TableId(value = "Id", type = IdType.AUTO)
    private Integer Id;

    @TableField("ConfigType")
    private Integer configType;

    @TableField("ConfigId")
    private Integer configId;

    @TableField("ItemId")
    private Integer itemId;

    @TableField("HospitalCode")
    private String hospitalCode;

    @TableField("Price")
    private BigDecimal price;

}


