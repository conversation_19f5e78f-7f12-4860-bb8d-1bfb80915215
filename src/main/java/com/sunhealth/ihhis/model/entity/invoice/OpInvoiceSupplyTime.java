package com.sunhealth.ihhis.model.entity.invoice;
import com.baomidou.mybatisplus.annotation.IdType;
import java.io.Serializable;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableField;
import java.util.Date;
import lombok.Data;

@Data
@TableName("Reg_Tb_OpInvoiceSupply_Time")
public class OpInvoiceSupplyTime implements Serializable {
    private static final long serialVersionUID = 1L;

    @TableId(value = "Id", type = IdType.AUTO)
    private Long id; // 主键字段，不能为 NULL

    @TableField("InvoiceID")
    private Long invoiceID; // 不能为 NULL

    @TableField("InvoicePrefix")
    private String invoicePrefix;

    @TableField("InvoiceNo")
    private Long invoiceNo;

    @TableField("OutpatientNo")
    private String outpatientNo;

    @TableField("Flag")
    private Integer flag;

    @TableField("OpTime")
    private Date opTime;

    @TableField("OpCode")
    private Integer opCode;

    @TableField("IsDelete")
    private Boolean isDelete = false; // 不能为 NULL，默认值为 false

    @TableField("CreatedBy")
    private Integer createdBy;

    @TableField("CreatedDate")
    private Date createdDate;

    @TableField("UpdateBy")
    private Integer updateBy;

    @TableField("UpdateDate")
    private Date updateDate;

    @TableField("HospitalCode")
    private Integer hospitalCode = 3; // 默认值为 3，不能为 NULL
}
