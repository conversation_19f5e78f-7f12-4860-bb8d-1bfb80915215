package com.sunhealth.ihhis.model.entity.patient;


import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.util.Date;

/**
 * 就诊卡信息表
 */
@Data
@TableName("Reg_Tb_PatientCard")
public class RTPatientCard {

    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    @TableField("CardType")
    private Integer cardType;

    @TableField("CardNo")
    private String cardNo;

    @TableField("CardData")
    private String cardData;

    @TableField("ChargeType")
    private Integer chargeType;

    @TableField("PatID")
    private Long patId;

    @TableField("HisCardNo")
    private String hisCardNo;

    @TableField("AreaCode")
    private String areaCode;

    @TableField("Status")
    private Integer status;

    @TableField("PatFlag")
    private Integer patFlag;

    @TableField("CreateBy")
    private Integer createBy;

    @TableField("CreateDate")
    private Date createDate;

    @TableField("UpdateBy")
    private Integer updateBy;

    @TableField("UpdateDate")
    private Date updateDate;

}
