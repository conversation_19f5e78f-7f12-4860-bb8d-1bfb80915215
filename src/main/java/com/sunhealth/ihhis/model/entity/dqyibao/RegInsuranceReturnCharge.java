package com.sunhealth.ihhis.model.entity.dqyibao;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 退款的,上海医保挂号和门诊缴费都用这个表
 */
@Data
@TableName("Reg_Insurance_ReturnCharge")
public class RegInsuranceReturnCharge implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableField(value = "Flag")
    private Integer flag;

    @TableField(value = "SerialNo")
    private Long serialNo;

    @TableField("MzNo")
    private Long mzNo;

    @TableField("CurrentAccountRefund")
    private BigDecimal currentAccountRefund;

    @TableField("CalendarYearAccountRefund")
    private BigDecimal calendarYearAccountRefund;

    @TableField("SelfCashRefund")
    private BigDecimal selfCashRefund;

    @TableField("OverallPlanningAccountRefund")
    private BigDecimal overallPlanningAccountRefund;

    @TableField("OverallPlanningCashRefund")
    private BigDecimal overallPlanningCashRefund;

    @TableField("OverallPlanningRefund")
    private BigDecimal overallPlanningRefund;

    @TableField("AdditionalAccountRefund")
    private BigDecimal additionalAccountRefund;

    @TableField("AdditionalCashRefund")
    private BigDecimal additionalCashRefund;

    @TableField("AdditionalRefund")
    private BigDecimal additionalRefund;

    @TableField("CurrentAccountBalance")
    private BigDecimal currentAccountBalance;

    @TableField("CalendarYearAccountBalance")
    private BigDecimal calendarYearAccountBalance;

    @TableField("CenterFlowNumber")
    private String centerFlowNumber;

    @TableField("UploadTime")
    private Date uploadTime;

    @TableField("ReturnTime")
    private Date returnTime;

    @TableField("CreateTime")
    private Date createTime;

    @TableField("ReturnCenterFlowNumber")
    private String returnCenterFlowNumber;

    @TableField("RecordStatus")
    private Integer recordStatus;

    @TableField("MulaidAccount")
    private BigDecimal mulaidAccount;

    @TableField("AcctMulaidPay")
    private BigDecimal acctMulaidPay;

    @TableField("SelfMulaidPay")
    private BigDecimal selfMulaidPay;

    @TableField("PubMulaidPay")
    private BigDecimal pubMulaidPay;

    @TableField("AppendMulaidPay")
    private BigDecimal appendMulaidPay;

    @TableField("InsuranceType")
    private Integer insuranceType;

    @TableField("MulaidPayTotal")
    private BigDecimal mulaidPayTotal;

    @TableField("QfdMulaidPay")
    private BigDecimal qfdMulaidPay;

    @TableField("GyMulaidPay")
    private BigDecimal gyMulaidPay;

}
