package com.sunhealth.ihhis.model.entity.dqyibao;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

@Data
@TableName("Reg_Insurance_Register")
public class RegInsuranceRegister implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableField("SerialNo")
    private Long serialNo;

    @TableField("PatID")
    private Long patID;

    @TableField("NewPatID")
    private Long newPatId;

    @TableField("ChargeType")
    private String chargeType;

    @TableField("BlanceWay")
    private Integer blanceWay;

    @TableField("RegNo")
    private Long regNo;

    @TableField("PatName")
    private String patName;

    @TableField("CardNo")
    private String cardNo;

    @TableField("OutpatientNo")
    private String outpatientNo;

    @TableField("CardType")
    private String cardType;

    @TableField("CardData")
    private String cardData;

    @TableField("DeptId")
    private String deptId;

    @TableField("DeptNum")
    private Integer deptNum;

    @TableField("RegistType")
    private Integer registType;

    @TableField("SpecialFlag")
    private Integer specialFlag;

    @TableField("AccountFlag")
    private String accountFlag;

    @TableField("PatType")
    private Integer patType;

    @TableField("TotalAmount")
    private BigDecimal totalAmount;

    @TableField("TradeTotal")
    private BigDecimal tradeTotal;

    @TableField("InsuranceTotal")
    private BigDecimal insuranceTotal;

    @TableField("NonInsuranceTotal")
    private BigDecimal nonInsuranceTotal;

    @TableField("CalculationSerial")
    private String calculationSerial;

    @TableField("RegistAmount")
    private BigDecimal registAmount;

    @TableField("DiagAmount")
    private BigDecimal diagAmount;

    @TableField("RecordNo")
    private String recordNo;

    @TableField("JyFlag")
    private Integer jyFlag;

    @TableField("WFlag")
    private Integer wflag;

    @TableField("RFlag")
    private Integer rflag;

    /**
     * 对应5期医保中心流水号 lsh
     */
    @TableField("TradeSerialNo")
    private String tradeSerialNo;

    @TableField("CurrentAccountPay")
    private BigDecimal currentAccountPay;

    @TableField("LastAccountPay")
    private BigDecimal lastAccountPay;

    @TableField("CurrentAccountMoney")
    private BigDecimal currentAccountMoney;

    @TableField("LastAccountMoney")
    private BigDecimal lastAccountMoney;

    @TableField("CashPayTotal")
    private BigDecimal cashPayTotal;

    @TableField("ClassifyPayTotal")
    private BigDecimal classifyPayTotal;

    @TableField("PubPay")
    private BigDecimal pubPay;

    @TableField("PubAccountPay")
    private BigDecimal pubAccountPay;

    @TableField("PubCashPay")
    private BigDecimal pubCashPay;

    @TableField("AppendAccountPay")
    private BigDecimal appendAccountPay;

    @TableField("AppendCashPay")
    private BigDecimal appendCashPay;

    @TableField("AppendPay")
    private BigDecimal appendPay;

    @TableField("UploadTime")
    private Date uploadTime;

    @TableField("ReturnTime")
    private Date returnTime;

    @TableField("OpTime")
    private Date opTime;

    @TableField("CureCode")
    private String cureCode;

    @TableField("DiagCode")
    private String diagCode;

    @TableField("OpCode")
    private Integer opCode;

    @TableField("ComputerNo")
    private String computerNo;

    @TableField("MsgValue")
    private String msgValue;

    @TableField("IsDeleted")
    private Boolean isDeleted;

    @TableField("OutpatientLabel")
    private String outpatientLabel;

    @TableField("IsInsureCard")
    private Integer isInsureCard;

    @TableField("JzAmt")
    private BigDecimal jzAmt;

    @TableField("CreateTime")
    private Date createTime;

    @TableField("UnitNo")
    private String unitNo;

    @TableField("IndustrialInjury")
    private String industrialInjury;

    @TableField("DiagnosisItem")
    private String diagnosisItem;

    @TableField("IsRemission")
    private String isRemission;

    @TableField("RemissionFlag")
    private String remissionFlag;

    @TableField("OnLineType")
    private String onLineType;

    @TableField("OrderId")
    private String orderId;

    @TableField("OrderNo")
    private String orderNo;

    @TableField("EcToken")
    private String ecToken;

    @TableField("JyWay")
    private String jyWay;

    @TableField("MedicareType")
    private String medicareType;

    @TableField("CashLastAccountPay")
    private BigDecimal cashLastAccountPay;

    @TableField("HospitalCode")
    private Integer hospitalCode;

    @TableField("AcctMulaidPay")
    private BigDecimal acctMulaidPay;

    @TableField("SelfMulaidPay")
    private BigDecimal selfMulaidPay;

    @TableField("PubMulaidPay")
    private BigDecimal pubMulaidPay;

    @TableField("AppendMulaidPay")
    private BigDecimal appendMulaidPay;

    @TableField("InsuranceType")
    private Integer insuranceType;

    @TableField("MulaidPayTotal")
    private BigDecimal mulaidPayTotal;

    @TableField("QfdMulaidPay")
    private BigDecimal qfdMulaidPay;

    @TableField("GyMulaidPay")
    private BigDecimal gyMulaidPay;

}
