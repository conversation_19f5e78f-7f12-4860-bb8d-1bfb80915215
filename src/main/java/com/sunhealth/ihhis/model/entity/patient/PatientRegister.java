package com.sunhealth.ihhis.model.entity.patient;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

@Data
@TableName("Tbt_Interface_PatientRegister")
public class PatientRegister implements Serializable {

    @TableId(value = "id", type = IdType.AUTO) // 不能为NULL
    private Long id;

    @TableField("flag")
    private Integer flag;

    @TableField("regno")
    private Long regno;

    @TableField("patinfo_id")
    private Long patinfoId;

    @TableField("orgCode")
    private String orgCode;

    @TableField("busType")
    private String busType;

    @TableField("clinicNo")
    private String clinicNo;

    @TableField("patfeeType")
    private String patfeeType;

    @TableField("name")
    private String name;

    @TableField("sex")
    private String sex;

    @TableField("birthday")
    private String birthday;

    @TableField("age")
    private String age;

    @TableField("ageUnit")
    private String ageUnit;

    @TableField("idType")
    private String idType;

    @TableField("idCard")
    private String idCard;

    @TableField("address")
    private String address;

    @TableField("nation")
    private String nation;

    @TableField("phone")
    private String phone;

    @TableField("clinicDate")
    private String clinicDate;

    @TableField("deptCode")
    private String deptCode;

    @TableField("deptName")
    private String deptName;

    @TableField("wardCode")
    private String wardCode;

    @TableField("wardName")
    private String wardName;

    @TableField("doctorCode")
    private String doctorCode;

    @TableField("doctorName")
    private String doctorName;

    @TableField("category")
    private String category;

    @TableField("upload_time")
    private Date uploadTime;

    @TableField("return_time")
    private Date returnTime;

    @TableField("state")
    private Integer state;

    @TableField("errCode")
    private String errCode;

    @TableField("message")
    private String message;
}
