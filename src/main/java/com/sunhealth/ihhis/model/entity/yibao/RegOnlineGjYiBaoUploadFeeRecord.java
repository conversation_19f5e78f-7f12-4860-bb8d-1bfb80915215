package com.sunhealth.ihhis.model.entity.yibao;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.sunhealth.ihhis.model.insurance.UserLongitudeLatitude;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

@Data
@TableName("Reg_Online_GjYiBao_uploadFeeRecord")
public class RegOnlineGjYiBaoUploadFeeRecord implements Serializable {

    @TableId("ID")
    private Long id; // 不能为NULL

    @TableField("PatName")
    private String patName; // 就诊人姓名

    @TableField("CardType")
    private String cardType; // 证件类型

    @TableField("CardNo")
    private String cardNo; // 证件号码

    @TableField("ChargeNo")
    private Long chargeNo; // 结算单号

    @TableField("RegNo")
    private Long regNo; // 门诊序号

    @TableField("Flag")
    private Integer flag; // 0 挂号 1 缴费

    @TableField("HospitalCode")
    private String hospitalCode; // 医院编码

    @TableField("PayOrdId")
    private String payOrdId; // 医保-支付订单号

    @TableField("PayToken")
    private String payToken; // 医保-支付Token

    @TableField("OrdStas")
    private String ordStas; // 医保-订单状态

    @TableField("FeeSumamt")
    private BigDecimal feeSumamt; // 医保-费用总额

    @TableField("OwnPayAmt")
    private BigDecimal ownPayAmt; // 医保-现金支付

    @TableField("PsnAcctPay")
    private BigDecimal psnAcctPay; // 医保-个人账户支出

    @TableField("FundPay")
    private BigDecimal fundPay; // 医保-医保基金支付

    @TableField("Deposit")
    private BigDecimal deposit; // 医保-住院押金

    @TableField("CreatedDate")
    private Date createdDate; // 创建时间

    @TableField("UpdatedDate")
    private Date updatedDate; // 更新时间

    @TableField("Payway")
    private Integer payway; // 支付渠道 11-支付宝小程序 17-微信小程序 13-微信公众号

    @TableField("AuthCode")
    @ApiModelProperty("微信授权码")
    private String authCode;

    @ApiModelProperty("用户姓名")
    @TableField("UserName")
    private String userName;

    @ApiModelProperty("用户参保地代码, 仅当mi_card_type=CERITIFICATE时，才返回有效值，多地参保用户若未选择主参保地，则该字段未空，并且不可使用后续业务。")
    @TableField("CityId")
    private String cityId;

    @ApiModelProperty("医保线上核验payAuthNo, 当使用医保线上支付功能时，返回payAuthNo")
    @TableField("PayAuthNo")
    private String payAuthNo;

    @TableField("Longitude")
    @ApiModelProperty("经度")
    private String longitude;

    @ApiModelProperty("纬度")
    @TableField("Latitude")
    private String latitude;

    @ApiModelProperty("医保线上核验ecQrcode, 当使用医保线上支付功能时，返回ecQrcode")
    @TableField("EcQrcode")
    private String ecQrcode;

    @ApiModelProperty("医保卡号")
    @TableField("UserCardNo")
    private String userCardNo;
}

