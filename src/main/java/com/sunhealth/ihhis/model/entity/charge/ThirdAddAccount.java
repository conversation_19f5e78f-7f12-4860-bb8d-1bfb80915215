package com.sunhealth.ihhis.model.entity.charge;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.sunhealth.ihhis.model.entity.view.ChargeItemView;
import lombok.Data;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

@Data
@TableName("Reg_tb_ThirdAddAcount")
public class ThirdAddAccount implements Serializable {

    @TableId("id")
    // 不能为NULL
    private Long id;

    @TableField("cardno")
    private String cardno;

    @TableField("patid")
    private Long patid;

    @TableField("RecipeId")
    private Long recipeId;

    @TableField("RecipeDetlID")
    private Long recipeDetlID;

    @TableField("ItemID")
    private Long itemID;

    @TableField("ItemName")
    private String itemName;

    @TableField("Quantiy")
    private BigDecimal quantiy;

    @TableField("ItemCategory")
    private Integer itemCategory;

    @TableField("GroupNo")
    private Integer groupNo;

    @TableField("ClinicUnit")
    private String clinicUnit;

    @TableField("ClinicQty")
    private BigDecimal clinicQty;

    @TableField("BasicUnit")
    private String basicUnit;

    @TableField("Dosage")
    private String dosage;

    @TableField("DosageUnit")
    private String dosageUnit;

    @TableField("DrugGauge")
    private String drugGauge;

    @TableField("CheckCode")
    private String checkCode;

    @TableField("ExecuteDept")
    private Integer executeDept;

    @TableField("Times")
    private Integer times;

    @TableField("Price")
    private BigDecimal price;

    @TableField("TotalAmount")
    private BigDecimal totalAmount;

    @TableField("DeptId")
    private Integer deptId;

    @TableField("DoctorId")
    private Integer doctorId;

    @TableField("DataFrom")
    private String dataFrom;

    @TableField("RecipeNum")
    private Integer recipeNum;

    @TableField("FeeType")
    private Integer feeType;

    @TableField("IsDrug")
    private Integer isDrug;

    @TableField("ExpensePrice")
    private BigDecimal expensePrice;

    @TableField("NonExpensePrice")
    private BigDecimal nonExpensePrice;

    @TableField("Usage")
    private String usage;

    @TableField("zhkfrq")
    private Date zhkfrq;

    @TableField("hospitalCode")
    private String hospitalCode;
    /**
     * 0-未缴费，1-已收费 ,2-已退费
     */
    @TableField("isCharge")
    private Integer isCharge;

    @TableField("ItemCategoryName")
    private String itemCategoryName;

    @TableField("regno")
    private Long regno;

    @TableField("hospno")
    private String hospno;
    /**
     * 退费申请状态 0-正常 ；1-已申请退费
     */
    @TableField("sqzt")
    private Integer sqzt;

    @TableField("tfrid")
    private Integer tfrid;

    @TableField("tftime")
    private Date tftime;

    public ThirdAddAccount(ChargeItemView item) {
        this.itemID = item.getItemCode();
        this.itemName = item.getItemName();
        this.itemCategory = item.getItemCategory();
        this.groupNo = 0;
        this.clinicUnit = item.getClinicUnit();
        this.clinicQty = BigDecimal.valueOf(item.getClinicQty());
        this.basicUnit = item.getWardUnit();
        this.dosage = "";
        this.dosageUnit = "";
        this.drugGauge = item.getDrugGuage();
        this.checkCode = item.getCheckCode();
        this.times = 1;
        this.price = item.getExpensePrice().add(item.getNonExpensePrice()).multiply(
            BigDecimal.valueOf(item.getClinicQty()));
        this.feeType = 0;
        this.isDrug = 0;
        this.expensePrice = item.getExpensePrice();
        this.nonExpensePrice = item.getNonExpensePrice();
        this.usage = "0";
        this.hospitalCode = String.valueOf(item.getHospitalId());
        this.itemCategoryName = "其他";

    }
}
