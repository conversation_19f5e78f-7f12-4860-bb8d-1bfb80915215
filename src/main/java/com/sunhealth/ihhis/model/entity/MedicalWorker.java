package com.sunhealth.ihhis.model.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import org.apache.commons.lang3.StringUtils;

import java.io.Serializable;
import java.util.Date;

@Data
@TableName("System_Tb_Worker")
public class MedicalWorker implements Serializable {

    private static final long serialVersionUID = 1L;

    // 职工ID
    @TableId("WorkerId")
    private Integer workerId;

    // 职工工号
    @TableField("WorkerNo")
    private String workerNo;

    // 职工姓名
    @TableField("Name")
    private String name;

    // 输入码1
    @TableField("InputCode1")
    private String inputCode1;

    // 姓名拼写
    @TableField("InputCode2")
    private String inputCode2;

    // 生日
    @TableField("BirthDay")
    private Date birthDay;

    // 性别
    @TableField("Sex")
    private Integer sex;

    // 身份证号
    @TableField("IdentityCard")
    private String identityCard;

    // 员工类型
    @TableField("StaffType")
    private Integer staffType;

    // 办公电话
    @TableField("OfficePhone")
    private String officePhone;

    // 上海医保编码
    @TableField("FamilyPhone")
    private String familyPhone;

    // 科室ID
    @TableField("DeptId")
    private Integer deptId;

    // 职称
    @TableField("Title")
    private Integer title;

    // 职务
    @TableField("Position")
    private Integer position;

    // 地址
    @TableField("Address")
    private String address;

    // 手机号码
    @TableField("Telephone")
    private String telephone;

    // 公司电话
    @TableField("CompanyTel")
    private String companyTel;

    // 备注
    @TableField("Remark")
    private String remark;

    // 所属院区
    @TableField("HospitalId")
    private Integer hospitalId;

    // 状态
    @TableField("Status")
    private Integer status;

    // 创建者
    @TableField("CreatorId")
    private Integer creatorId;

    // 创建时间
    @TableField("CreateDate")
    private Date createDate;

    // 修改人
    @TableField("UpdaterId")
    private Integer updaterId;

    // 修改时间
    @TableField("UpdateDate")
    private Date updateDate;

    // 国家医保编码
    @TableField("NationCode")
    private String nationCode;

    public String getName() {
        return StringUtils.isEmpty(name) ? name : name.trim();
    }
}
