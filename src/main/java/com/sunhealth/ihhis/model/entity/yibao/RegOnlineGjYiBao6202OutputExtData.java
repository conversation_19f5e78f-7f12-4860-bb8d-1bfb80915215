package com.sunhealth.ihhis.model.entity.yibao;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import lombok.Data;

@Data
@TableName("Reg_Online_GjYiBao_6202_Output_ExtData")
public class RegOnlineGjYiBao6202OutputExtData implements Serializable {

    @TableId("ID")
    private Long id; // 不能为NULL

    @TableField("PatName")
    private String patName; // 就诊人姓名

    @TableField("CardType")
    private String cardType; // 证件类型

    @TableField("CardNo")
    private String cardNo; // 证件号码

    @TableField("ChargeNo")
    private Long chargeNo; // 结算单号

    @TableField("RegNo")
    private Long regNo; // 门诊序号

    @TableField("Flag")
    private Integer flag; // 0 挂号 1 缴费

    @TableField("HospitalCode")
    private String hospitalCode; // 医院编码
    @TableField("acct_mulaid_pay")
    private BigDecimal acct_mulaid_pay; //	个人账户共济支付
    @TableField("acct_pay")
    private BigDecimal acct_pay; //	个人账户支付
    @TableField("act_pay_dedc")
    private BigDecimal act_pay_dedc; //	实际支付自费
    @TableField("age")
    private Integer age; //	年龄
    @TableField("balc")
    private BigDecimal balc; //	余额
    @TableField("brdy")
    private String brdy; //	出生日期
    @TableField("certno")
    private String certno; //	证件号码
    @TableField("clr_optins")
    private String clr_optins; //	清算机构
    @TableField("clr_type")
    private String clr_type; //	清算类别
    @TableField("clr_way")
    private String clr_way; //	清算方式
    @TableField("cvlserv_flag")
    private String cvlserv_flag; //	公务员医疗服务标志
    @TableField("cvlserv_pay")
    private BigDecimal cvlserv_pay; //	公务员医疗服务支付
    @TableField("fulamt_ownpay_amt")
    private BigDecimal fulamt_ownpay_amt; //	全自费金额
    @TableField("fund_pay_sumamt")
    private BigDecimal fund_pay_sumamt; //	基金支付总额
    @TableField("gend")
    private String gend; //	性别
    @TableField("hifes_pay")
    private BigDecimal hifes_pay; //	公务员医疗补助支付
    @TableField("hifmi_pay")
    private BigDecimal hifmi_pay; //	公务员医疗补助支付
    @TableField("hifob_pay")
    private BigDecimal hifob_pay; //	公务员医疗补助支付
    @TableField("hifp_pay")
    private BigDecimal hifp_pay; //	公务员医疗补助支付
    @TableField("hosp_part_amt")
    private BigDecimal hosp_part_amt; //	医院部分支付
    @TableField("inscp_scp_amt")
    private BigDecimal inscp_scp_amt; //	符合政策范围金额
    @TableField("insutype")
    private String insutype; //	险种类型
    @TableField("maf_pay")
    private BigDecimal maf_pay; //	医疗救助支付
    @TableField("mdtrt_cert_type")
    private String mdtrt_cert_type; //	就诊凭证类型
    @TableField("mdtrt_id")
    private String mdtrt_id; //	就诊ID
    @TableField("med_type")
    private String med_type; //	医疗类别
    @TableField("medfee_sumamt")
    private BigDecimal medfee_sumamt; //	医疗费总额
    @TableField("medins_setl_id")
    private String medins_setl_id; //	医保结算ID
    @TableField("oth_pay")
    private BigDecimal oth_pay; //	其他支付
    @TableField("overlmt_selfpay")
    private BigDecimal overlmt_selfpay; //	超限价自付
    @TableField("pool_prop_selfpay")
    private BigDecimal pool_prop_selfpay; //	统筹自付
    @TableField("preselfpay_amt")
    private BigDecimal preselfpay_amt; //	先行自付金额
    @TableField("psn_cash_pay")
    private BigDecimal psn_cash_pay; //	个人现金支付
    @TableField("psn_cert_type")
    private String psn_cert_type; //	人员证件类型
    @TableField("psn_name")
    private String psn_name; //	人员姓名
    @TableField("psn_no")
    private String psn_no; //	人员编号
    @TableField("psn_part_amt")
    private BigDecimal psn_part_amt; //	个人部分支付
    @TableField("psn_type")
    private String psn_type; //	人员类别
    @TableField("setl_time")
    private String setl_time; //	结算时间
}

