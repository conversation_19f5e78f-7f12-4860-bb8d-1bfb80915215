package com.sunhealth.ihhis.model.entity.sequences;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

/**
 * 处方明细序号序列表
 */
@Data
@TableName("Tb_Sequences_RecipeDetail")
public class SequencesRecipeDetailNo {

    @TableId(type = IdType.AUTO)
    private Long id;
    @TableField("CurrentValue")
    private Integer currentValue;

    public SequencesRecipeDetailNo(Integer value) {
        this.currentValue = value;
    }
}
