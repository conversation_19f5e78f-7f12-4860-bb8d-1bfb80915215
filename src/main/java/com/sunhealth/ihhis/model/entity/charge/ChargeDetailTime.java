package com.sunhealth.ihhis.model.entity.charge;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

@Data
@TableName("Reg_Tb_ChargeDetail_Time")
public class ChargeDetailTime implements Serializable {
    private static final long serialVersionUID = 1L;

    @TableId("ChargeDetl")
    private Long chargeDetl; // 主键字段 ChargeDetl

    @TableField("ChargeNo")
    private Long chargeNo;

    @TableField("RecipeNum")
    private Integer recipeNum;

    @TableField("RecipeID")
    private Long recipeID;

    @TableField("RecipeDetlID")
    private Long recipeDetlID;

    @TableField("ItemID")
    private Integer itemID;

    @TableField("ItemName")
    private String itemName;

    @TableField("Quantiy")
    private BigDecimal quantity; // 不能为NULL

    @TableField("ItemCategory")
    private Integer itemCategory;

    @TableField("PackageNo")
    private Integer packageNo;

    @TableField("GroupNo")
    private Integer groupNo;

    @TableField("ClinicUnit")
    private String clinicUnit;

    @TableField("BasicUnit")
    private String basicUnit;

    @TableField("ClinicQty")
    private Integer clinicQty;

    @TableField("Usage")
    private String usage;

    @TableField("Dosage")
    private String dosage;

    @TableField("DosageUnit")
    private String dosageUnit;

    @TableField("DrugGauge")
    private String drugGauge;

    @TableField("CheckCode")
    private String checkCode;

    @TableField("ExecuteDept")
    private Integer executeDept;

    @TableField("Times")
    private Integer times;

    @TableField("Price")
    private BigDecimal price;

    @TableField("ExpensePrice")
    private BigDecimal expensePrice;

    @TableField("NonExpensePrice")
    private BigDecimal nonExpensePrice;

    @TableField("Status")
    private Integer status;

    @TableField("TotalAmount")
    private BigDecimal totalAmount;

    @TableField("DoctorId")
    private Integer doctorId;

    @TableField("DeptId")
    private Integer deptId;

    @TableField("DoctorLevel")
    private Integer doctorLevel;

    @TableField("FeeType")
    private Integer feeType;

    @TableField("IsDrug")
    private Integer isDrug;

    @TableField("SpecialFlag")
    private Integer specialFlag;

    @TableField("DataFrom")
    private Integer dataFrom;

    @TableField("FromFlag")
    private Integer fromFlag;

    @TableField("DiscountAmount")
    private BigDecimal discountAmount;

    @TableField("IsDelete")
    private Boolean isDelete = false; // 不能为NULL

    @TableField("CreatedBy")
    private Integer createdBy;

    @TableField("CreatedDate")
    private Date createdDate;

    @TableField("UpdateBy")
    private Integer updateBy;

    @TableField("UpdateDate")
    private Date updateDate;

    @TableField("InsuranceTradeAmount")
    private BigDecimal insuranceTradeAmount;

    @TableField("SelfAmount")
    private BigDecimal selfAmount;

    @TableField("ClassifyTotal")
    private BigDecimal classifyTotal;

    @TableField("InsuranceCashAmount")
    private BigDecimal insuranceCashAmount;

    @TableField("HospitalCode")
    private Integer hospitalCode;

    @TableField("CreditAmt")
    private BigDecimal creditAmt;

    @TableField("RealAmt")
    private BigDecimal realAmt;

    @TableField("OtherAmt")
    private BigDecimal otherAmt;

    @TableField("JzAmount")
    private BigDecimal jzAmount;

    @TableField("OldChargeDetl")
    private Long oldChargeDetl;

    @TableField("IsDebitPay")
    private Boolean isDebitPay;

    @TableField("DebitAmt")
    private BigDecimal debitAmt;
}

