package com.sunhealth.ihhis.model.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

@Data
@TableName(value = "System_Tb_Department")
public class Dept implements Serializable {

    private static final long serialVersionUID = 1L;

    // 部门ID
    @TableId("DeptId")
    private Integer deptId;

    // 部门编码
    @TableField("DeptCode")
    private String deptCode;

    // 部门名称
    @TableField("DeptName")
    private String deptName;

    // 父部门ID
    @TableField("ParentId")
    private Integer parentId;

    // 输入码1
    @TableField("InputCode1")
    private String inputCode1;

    // 输入码2
    @TableField("InputCode2")
    private String inputCode2;

    // 医保部门编码
    @TableField("InsureDeptCode")
    private Integer insureDeptCode;

    // 记录部门编码
    @TableField("RecordDeptCode")
    private Integer recordDeptCode;

    // 是否可就诊
    @TableField("IsVisit")
    private Integer isVisit;

    // 部门电话
    @TableField("DeptPhone")
    private String deptPhone;

    // 部门类别编码
    @TableField("DeptClassCode")
    private Integer deptClassCode;

    // 是否特殊
    @TableField("IsSpecial")
    private Integer isSpecial;

    // 是否虚拟
    @TableField("IsVirtual")
    private Integer isVirtual;

    // SID
    @TableField("SID")
    private Integer sid;

    // 状态
    @TableField("Status")
    private Integer status;

    // 备注
    @TableField("Remark")
    private String remark;

    // 医院ID
    @TableField("HospitalId")
    private Integer hospitalId;

    // 药品类型
    @TableField("DrugType")
    private Integer drugType;

    // 创建者用户ID
    @TableField("CreatorUserId")
    private Integer creatorUserId;

    // 创建时间
    @TableField("CreateOn")
    private Date createOn;

    // 更新者用户ID
    @TableField("UpdateUserId")
    private Integer updateUserId;

    // 更新时间
    @TableField("UpdateOn")
    private Date updateOn;

    // 语言
    @TableField("Lang")
    private Integer lang;

    // 指南信息
    @TableField("GuideInfo")
    private String guideInfo;

    @TableField("NationDeptCode")
    private String nationDeptCode;

    // 级别
    @TableField("Level")
    private Integer level;

}
