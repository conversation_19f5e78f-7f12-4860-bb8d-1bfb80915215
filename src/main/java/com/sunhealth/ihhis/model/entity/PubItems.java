package com.sunhealth.ihhis.model.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.math.BigDecimal;

@Data
@TableName("System_Tb_PubItems")
public class PubItems {

    @TableField("ItemCode")
    private String itemCode;

    @TableField("ItemName")
    private String itemName;

    @TableField("InputCode1")
    private String inputCode1;

    @TableField("RetailPrice")
    private BigDecimal retailPrice;

    @TableField("ItemCategory")
    private Integer itemCategory;

    @TableField("Stopped")
    private Integer stopped;

    @TableField("HospitalId")
    private Integer hospitalId;
}
