package com.sunhealth.ihhis.model.entity.register;

import java.io.Serializable;
import java.util.Date;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.util.UUID;
import lombok.Data;
import org.apache.ibatis.type.JdbcType;

@Data
@TableName("TB_Appointment_LockNum")
public class AppointmentLockNum implements Serializable {
    private static final long serialVersionUID = 1L;

    @TableId("Id")
    private String id;

    @TableField("SubjectID") // 科目id
    private Integer subjectID;

    @TableField("DutyDate") // 排班日期
    private Date dutyDate;

    @TableField("TimeSpanID") // 时段id
    private Integer timeSpanID;

    @TableField("SourceId") // 号源id
    private Integer sourceId;

    @TableField("SeqNum") // 不能为NULL
    private Integer seqNum;

    @TableField("CertificateNo") // 身份证号
    private String certificateNo;

    @TableField(value = "LockNumOrderID", jdbcType = JdbcType.OTHER) // 锁号id
    private String lockNumOrderID;

    @TableField("LockTime") // 锁号时间
    private Date lockTime;

    @TableField("NumId")
    private String numId;

    @TableField("HospitalCode") // 医院编号
    private String hospitalCode;

    @TableField("flag") // 标识
    private Integer flag;

    @TableField("VisitFlag") // 访问标识
    private Integer visitFlag;

    @TableField("SQH") // 申请号 不能为NULL
    private Integer sqh;

    @TableField("IsCZF") // 初复诊
    private Integer isCZF;

    @TableField("OldOrderNumber")
    private String oldOrderNumber;

    @TableField("CardNo") // 患者卡号
    private String cardNo;
}

