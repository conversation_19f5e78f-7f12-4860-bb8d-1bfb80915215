package com.sunhealth.ihhis.model.entity.yibao;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

@Data
@TableName("Reg_Online_SHYiBao_uploadFeeRecord")
public class RegOnlineSHYiBaoUploadFeeRecord implements Serializable {

    @TableId("ID")
    private Long id; // 不能为NULL


    /**
     * 卡号
     */
    @TableField("Cardid")
    private String cardid;

    /**
     * 姓名
     */
    @TableField("Personname")
    private String personname;

    /**
     * 特殊人员标识
     * 0：普通 1：离休 2：伤残 3：干部保健定点
     */
    @TableField("Personspectag")
    private String personspectag;

    /**
     * 帐户标志,见字典表
     */
    @TableField("Accountattr")
    private String accountattr;

    /**
     * 减免结算标志
     * 0：正常结算，1：医保减免结算，2：财政减免结算
     */
    @TableField("Jmjsbz")
    private String jmjsbz;

    /**
     * 交易费用总额
     * 数字格式 A
     */
    @TableField("Totalexpense")
    private BigDecimal totalexpense;

    /**
     * 自负段现金支付数
     * 数字格式 A
     */
    @TableField("Zfdxjzfs")
    private BigDecimal zfdxjzfs;
    /**
     * 统筹段帐户支付数
     * 数字格式 A
     */
    @TableField("Tcdzhzfs")
    private BigDecimal tcdzhzfs;
    /**
     * 统筹段现金支付数
     * 数字格式 A
     */
    @TableField("Tcdxjzfs")
    private BigDecimal tcdxjzfs;
    /**
     * 统筹支付数
     * 工伤病人返回工伤基金支付数
     * 数字格式 A
     */
    @TableField("Tczfs")
    private BigDecimal tczfs;
    /**
     * 医保结算范围费用总额
     * 工伤病人返回工伤结算范围费用总额
     * 数字格式 A
     */
    @TableField("Ybjsfwfyze")
    private BigDecimal ybjsfwfyze;
    /**
     * 非医保结算范围费用总额
     * 工伤病人返回非工伤结算范围费用总额
     * 数字格式 A
     */
    @TableField("Fybjsfwfyze")
    private BigDecimal fybjsfwfyze;
    /**
     * 计算申请序号
     */
    @TableField("Jssqxh")
    private String jssqxh;

    @TableField("PatName")
    private String patName; // 就诊人姓名

    @TableField("CardType")
    private String cardType; // 证件类型

    @TableField("CardNo")
    private String cardNo; // 证件号码

    @TableField("ChargeNo")
    private Long chargeNo; // 结算单号

    @TableField("RegNo")
    private Long regNo; // 门诊序号

    @TableField("Flag")
    private Integer flag; // 0 挂号 1 缴费

    @TableField("HospitalCode")
    private String hospitalCode; // 医院编码

    @TableField("OrderNo")
    private String orderNo; // 医保-支付订单号

    @TableField("CreatedDate")
    private Date createdDate; // 创建时间

    @TableField("UpdatedDate")
    private Date updatedDate; // 更新时间

    @TableField("Payway")
    private Integer payway; // 支付渠道 11-支付宝小程序 17-微信小程序 13-微信公众号

    @TableField("AuthCode")
    private String authCode; // 微信授权码

    @TableField("UserName")
    private String userName; // 用户姓名

    /**
     * 发卡地区规划
     */
    @TableField("CityId")
    private String cityId;

    @TableField("PayAuthNo")
    private String payAuthNo; // 医保线上核验payAuthNo, 当使用医保线上支付功能时，返回payAuthNo

    @TableField("Longitude")
    private String longitude; // 经度

    @TableField("Latitude")
    private String latitude; // 纬度

    @TableField("EcQrcode")
    private String ecQrcode; // 医保线上核验ecQrcode, 当使用医保线上支付功能时，返回ecQrcode

    @TableField("UserCardNo")
    private String userCardNo; // 医保卡号

    @TableField("EcToken")
    private String ecToken; // 医保线上ecToken

}

