package com.sunhealth.ihhis.model.entity.report;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.util.Date;
import lombok.Data;

/**
 * 检查报告详情
 */
@Data
@TableName("Report_Exam_Detail")
public class ReportExamDetail implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @TableId("Id")
    private Long id;

    /**
     * 报告编号
     */
    @TableField("ReportNo")
    private String reportNo;

    /**
     * 指标项目代码
     */
    @TableField("ItemCode")
    private String itemCode;

    /**
     * 指标项目名称
     */
    @TableField("ItemName")
    private String itemName;

    /**
     * 指标结果
     */
    @TableField("ItemResult")
    private String itemResult;

    /**
     * 接收时间
     */
    @TableField("ReceiveTime")
    private Long receiveTime;
}
