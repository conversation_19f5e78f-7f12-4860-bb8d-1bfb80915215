package com.sunhealth.ihhis.model.entity.sequences;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

@Data
@TableName("Reg_Sequences_HospNo")
public class SequencesHospNo {

    @TableId(type = IdType.AUTO)
    private Long id;

    private Integer value;

    // 0未使用 1已使用
    private Integer status;

    public SequencesHospNo(Integer value, Integer status) {
        this.value = value;
        this.status = status;
    }
}
