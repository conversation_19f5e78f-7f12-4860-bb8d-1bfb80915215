package com.sunhealth.ihhis.model.entity.inpatient;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.util.Date;

/**
 * 住院患者信息表
 *  部分字段-根据需要引入
 */
@Data
@TableName("IO_Tb_InPatient")
public class InPatient {

    // 住院流水号
    @TableId("RegNo")
    private Long regNo;

    // 住院号
    @TableField("HospNo")
    private String hospNo;

    // 患者id
    @TableField("PatId")
    private Integer patId;

    // 患者姓名
    @TableField("PatName")
    private String patName;

    // 卡号
    @TableField("CardNo")
    private String cardNo;

    // 状态 2：在区 3：待出院 4：中期离区 5：转区 9：出院 10：无效 11：待入区
    @TableField("Status")
    private Integer status;

    // 医院编码
    @TableField("HospitalId")
    private Integer hospitalId;

    // 证件号
    @TableField("CertificateNo")
    private String certificateNo;

    // 入院时间
    @TableField("InTime")
    private Date inTime;
}
