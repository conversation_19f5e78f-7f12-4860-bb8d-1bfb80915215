package com.sunhealth.ihhis.model.entity.dqyibao;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

@Data
@TableName("Reg_Insurance_Charge")
public class RegInsuranceCharge implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId(value = "SerialNo", type = IdType.NONE)
    private Long serialNo;

    @TableField("ChargeNo")
    private Long chargeNo;

    @TableField("RegistType")
    private Integer registType;

    @TableField("PatID")
    private Long patId;

    @TableField("NewPatID")
    private Long newPatId;

    @TableField("ChargeType")
    private Integer chargeType;

    @TableField("IsInsureCard")
    private Integer isInsureCard;

    @TableField("RegNo")
    private Long regNo;

    @TableField("PatName")
    private String patName;

    @TableField("CardNo")
    private String cardNo;

    @TableField("OutpatientNo")
    private String outpatientNo;

    @TableField("CardType")
    private String cardType;

    @TableField("CardData")
    private String cardData;

    @TableField("DeptID")
    private String deptId;

    @TableField("DeptNum")
    private Integer deptNum;

    @TableField("SpecialFlag")
    private Integer specialFlag;

    @TableField("AccountFlag")
    private String accountFlag;

    @TableField("PatType")
    private Integer patType;

    @TableField("CalculationSerial")
    private String calculationSerial;

    @TableField("TreatmentFee")
    private BigDecimal treatmentFee;

    @TableField("ConsultationFee")
    private BigDecimal consultationFee;

    @TableField("OperationFee")
    private BigDecimal operationFee;

    @TableField("ExaminationFee")
    private BigDecimal examinationFee;

    @TableField("LaboratoryFee")
    private BigDecimal laboratoryFee;

    @TableField("FilmFee")
    private BigDecimal filmFee;

    @TableField("PerspectiveFee")
    private BigDecimal perspectiveFee;

    @TableField("WesternMedFee")
    private BigDecimal westernMedFee;

    @TableField("ChinesePatMedFee")
    private BigDecimal chinesePatMedFee;

    @TableField("ChineseHerbMedFee")
    private BigDecimal chineseHerbMedFee;

    @TableField("OtherFee")
    private BigDecimal otherFee;

    @TableField("ClassifyTotal")
    private BigDecimal classifyTotal;

    @TableField("ClassifyTreatmentFee")
    private BigDecimal classifyTreatmentFee;

    @TableField("ClassifyConsultationFee")
    private BigDecimal classifyConsultationFee;

    @TableField("ClassifyOperationFee")
    private BigDecimal classifyOperationFee;

    @TableField("ClassifyExaminationFee")
    private BigDecimal classifyExaminationFee;

    @TableField("ClassifyLaboratoryFee")
    private BigDecimal classifyLaboratoryFee;

    @TableField("ClassifyFilmFee")
    private BigDecimal classifyFilmFee;

    @TableField("ClassifyPerspectiveFee")
    private BigDecimal classifyPerspectiveFee;

    @TableField("ClassifyWesternMedFee")
    private BigDecimal classifyWesternMedFee;

    @TableField("ClassifyChinesePatMedFee")
    private BigDecimal classifyChinesePatMedFee;

    @TableField("ClassifyChineseHerbMedFee")
    private BigDecimal classifyChineseHerbMedFee;

    @TableField("ClassifyOtherFee")
    private BigDecimal classifyOtherFee;

    @TableField("JyFlag")
    private Integer jyFlag;

    @TableField("WFlag")
    private Integer wflag;

    @TableField("RFlag")
    private Integer rflag;

    @TableField("CureCode")
    private String cureCode;

    @TableField("DiagCode")
    private String diagCode;

    @TableField("TradeSerialNo")
    private String tradeSerialNo;

    @TableField("CurrentAccountPay")
    private BigDecimal currentAccountPay;

    @TableField("LastAccountPay")
    private BigDecimal lastAccountPay;

    @TableField("SelfCashPay")
    private BigDecimal selfCashPay;

    @TableField("PubLastPay")
    private BigDecimal pubLastPay;

    @TableField("PubCashPay")
    private BigDecimal pubCashPay;

    @TableField("PubPay")
    private BigDecimal pubPay;

    @TableField("AppendCashPay")
    private BigDecimal appendCashPay;

    @TableField("AppendPay")
    private BigDecimal appendPay;

    @TableField("CurrentAccountMoney")
    private BigDecimal currentAccountMoney;

    @TableField("LastAccountMoney")
    private BigDecimal lastAccountMoney;

    @TableField("TotalAmount")
    private BigDecimal totalAmount;

    @TableField("TradeTotal")
    private BigDecimal tradeTotal;

    @TableField("InsuranceTotal")
    private BigDecimal insuranceTotal;

    @TableField("NonInsuranceTotal")
    private BigDecimal nonInsuranceTotal;

    @TableField("RecordNo")
    private String recordNo;

    @TableField("UploadTime")
    private Date uploadTime;

    @TableField("ReturnTime")
    private Date returnTime;

    @TableField("OpTime")
    private Date opTime;

    @TableField("OpCode")
    private Integer opCode;

    @TableField("ComputerNo")
    private String computerNo;

    @TableField("CheckFlag")
    private Integer checkFlag;

    @TableField("MsgValue")
    private String msgValue;

    @TableField("Days")
    private BigDecimal days;

    @TableField("HospitalFee")
    private BigDecimal hospitalFee;

    @TableField("NurseFee")
    private BigDecimal nurseFee;

    @TableField("BloodTransferFee")
    private BigDecimal bloodTransferFee;

    @TableField("OxygenTransferFee")
    private BigDecimal oxygenTransferFee;

    @TableField("ClassifyHospitalFee")
    private BigDecimal classifyHospitalFee;

    @TableField("ClassifyNurseFee")
    private BigDecimal classifyNurseFee;

    @TableField("ClassifyBloodTransferFee")
    private BigDecimal classifyBloodTransferFee;

    @TableField("ClassifyOxygenTransferFee")
    private BigDecimal classifyOxygenTransferFee;

    @TableField("IsDeleted")
    private Boolean isDeleted;

    @TableField("JzAmt")
    private BigDecimal jzAmt;

    @TableField("CashAmt")
    private BigDecimal cashAmt;

    @TableField("IndustrialInjury")
    private String industrialInjury;

    @TableField("IndustrialInjuryFundPay")
    private BigDecimal industrialInjuryFundPay;

    @TableField("CreatedDate")
    private Date createdDate;

    @TableField("Yllb")
    private String yllb;

    @TableField("SelfLastPay")
    private BigDecimal selfLastPay;

    @TableField("HospitalCode")
    private Integer hospitalCode;

    @TableField("AppendLastPay")
    private BigDecimal appendLastPay;

    @TableField("Jzdyh")
    private String jzdyh;

    @TableField("AcctMulaidPay")
    private BigDecimal acctMulaidPay;

    @TableField("SelfMulaidPay")
    private BigDecimal selfMulaidPay;

    @TableField("PubMulaidPay")
    private BigDecimal pubMulaidPay;

    @TableField("AppendMulaidPay")
    private BigDecimal appendMulaidPay;

    @TableField("InsuranceType")
    private Integer insuranceType;

    @TableField("MulaidPayTotal")
    private BigDecimal mulaidPayTotal;

    @TableField("QfdMulaidPay")
    private BigDecimal qfdMulaidPay;

    @TableField("GyMulaidPay")
    private BigDecimal gyMulaidPay;

}