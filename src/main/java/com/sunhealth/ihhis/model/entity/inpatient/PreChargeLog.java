package com.sunhealth.ihhis.model.entity.inpatient;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

/**
 * 住院预交金充值记录日志
 */
@Data
@TableName("IO_Tb_PatientPreChargeLog")
public class PreChargeLog {

    @TableId("PreChargeLogId")
    private Long preChargeLogId;

    @TableField("PreChargeId")
    private Long preChargeId;

    @TableField("RegNo")
    private Long regNo;

    @TableField("NewRegNo")
    private Long newRegNo;

    @TableField("HospNo")
    private String hospNo;

    @TableField("PatId")
    private Integer patId;

    @TableField("Amount")
    private BigDecimal amount;

    @TableField("PayTime")
    private Date payTime;

    @TableField("PayType")
    private Integer payType;

    @TableField("ChargeType")
    private Integer chargeType;

    @TableField("Source")
    private Integer source;

    @TableField("CreateUserId")
    private Integer createUserId;

    @TableField("CreateOn")
    private Date createOn;

    @TableField("VerifyUserId")
    private Integer verifyUserId;

    @TableField("VerifyOn")
    private Date verifyOn;

    @TableField("BillStatus")
    private Integer billStatus;

    @TableField("CheckStatus")
    private Boolean checkStatus;

    @TableField("PrintStatus")
    private Boolean printStatus;

    @TableField("IsDelete")
    private Boolean isDelete;

    @TableField("OpType")
    private String opType;

    @TableField("OpUserId")
    private Integer opUserId;

    @TableField("OpTime")
    private Date opTime;

    @TableField("HospitalId")
    private Integer hospitalId;

    @TableField("IsAccounts")
    private Integer isAccounts;

    @TableField("Origin")
    private Integer origin;

    @TableField("DataSources")
    private Integer dataSources;

    // HIS订单号
    @TableField("HospTradeNo")
    private String hospTradeNo;

    // 第三方平台流水号
    @TableField("PayTradeNo")
    private String payTradeNo;

//    private String recipeNo;
//    private String checkNo;
//    private Integer payWay;
//    private Long PairId;
//    private String OpenAct;
//    private String OpenBank;
//    private String OpenCorp;
//    private Integer PrIntegerUserId;
//    private Date PrIntegerOn;
//    private Long CheckId;
//    private Integer DeleteUserId;
//    private Date DeleteOn;
//    private String PayMethod;
//    private Long BillId;
//    private Integer DataSources;
//    private Integer CurrentNo;
//    private Integer PrIntegerNum;
//    private String GuaranteeName;
//    private String Tel;
//    private String remark;

    public PreChargeLog(PreCharge preCharge) {
        this.preChargeId = preCharge.getPreChargeId();
        this.regNo = preCharge.getRegNo();
        this.newRegNo = preCharge.getNewRegNo();
        this.hospNo = preCharge.getHospNo();
        this.hospTradeNo = preCharge.getHospTradeNo();
        this.payTradeNo = preCharge.getPayTradeNo();
        this.patId = preCharge.getPatId();
        this.amount = preCharge.getAmount();
        this.payTime = preCharge.getPayTime();
        this.payType = preCharge.getPayType();
        this.chargeType = preCharge.getChargeType();
        this.source = preCharge.getSource();
        this.createUserId = preCharge.getCreateUserId();
        this.createOn = preCharge.getCreateOn();
        this.verifyUserId = preCharge.getVerifyUserId();
        this.verifyOn = preCharge.getVerifyOn();
        this.billStatus = preCharge.getBillStatus();
        this.checkStatus = preCharge.getCheckStatus();
        this.printStatus = preCharge.getPrintStatus();
        this.isDelete = preCharge.getIsDelete();
        this.isAccounts = preCharge.getIsAccounts();
        this.hospitalId = preCharge.getHospitalId();
        this.origin = preCharge.getOrigin();
        this.dataSources = preCharge.getDataSources();
    }
}
