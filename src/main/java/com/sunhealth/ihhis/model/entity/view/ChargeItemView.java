package com.sunhealth.ihhis.model.entity.view;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import java.math.BigDecimal;
import java.util.Date;
import lombok.Data;


@TableName("System_Tb_ChargeItem")
@Data
public class ChargeItemView implements java.io.Serializable{

    private static final long serialVersionUID = 1L;

    @TableField("ItemCode")
    private Long itemCode;

    @TableField("NewItemCode")
    private Long newItemCode;

    @TableField("ItemName")
    private String itemName;

    @TableField("InputCode1")
    private String inputCode1;

    @TableField("InputCode2")
    private String inputCode2;

    @TableField("InputCode3")
    private String inputCode3;

    @TableField("ItemCategory")
    private Integer itemCategory;

    @TableField("ChildItemCategory")
    private Integer childItemCategory;

    @TableField("ExpensePrice")
    private BigDecimal expensePrice;

    @TableField("NonExpensePrice")
    private BigDecimal nonExpensePrice;

    @TableField("ClinicQty")
    private Integer clinicQty;

    @TableField("ClinicUnit")
    private String clinicUnit;

    @TableField("WardUnit")
    private String wardUnit;

    @TableField("Dosage")
    private Integer dosage;

    @TableField("DosageUnit")
    private String dosageUnit;

    @TableField("DrugGuage")
    private String drugGuage;

    @TableField("ExeDept")
    private Integer exeDept;

    @TableField("Stopped")
    private Integer stopped;

    @TableField("ABClass")
    private Integer abClass;

    @TableField("ProductPlace")
    private String productPlace;

    @TableField("TradePrice")
    private Integer tradePrice;

    @TableField("RetailPrice")
    private Integer retailPrice;

    @TableField("CheckCode")
    private String checkCode;

    @TableField("Creator")
    private Long creator;

    @TableField("CreateTime")
    private Date createTime;

    @TableField("Updater")
    private Long updater;

    @TableField("UpdateTime")
    private Date updateTime;

    @TableField("Stopper")
    private Long stopper;

    @TableField("StopTime")
    private Date stopTime;

    @TableField("HospitalId")
    private Integer hospitalId;

    @TableField("StandName")
    private String standName;

    @TableField("NationCode")
    private String nationCode;

    @TableField(exist = false)
    private BigDecimal regFee;
}
