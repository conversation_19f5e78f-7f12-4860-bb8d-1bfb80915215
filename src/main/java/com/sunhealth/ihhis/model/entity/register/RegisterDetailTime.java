package com.sunhealth.ihhis.model.entity.register;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import lombok.Data;
import org.springframework.beans.BeanUtils;

@Data
@TableName("Reg_Tb_RegisterDetail_Time")
public class RegisterDetailTime implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId("RegDtlNo")
    private Long regDtlNo; // 主键

    @TableField("RegNo")
    private Long regNo; // 不能为NULL

    @TableField("ReturnRegNo")
    private Long returnRegNo;

    @TableField("ReturnRegDtlNo")
    private Long returnRegDtlNo;

    @TableField("ItemID")
    private Integer itemID;

    @TableField("NewItemID")
    private Integer newItemID;

    @TableField("ItemName")
    private String itemName;

    @TableField("ItemCatetory")
    private Integer itemCatetory;

    @TableField("Price")
    private java.math.BigDecimal price;

    @TableField("ExpensePrice")
    private java.math.BigDecimal expensePrice;

    @TableField("NonExpensePrice")
    private java.math.BigDecimal nonExpensePrice;

    @TableField("TotalAmount")
    private java.math.BigDecimal totalAmount;

    @TableField("ExpenseAmout")
    private java.math.BigDecimal expenseAmout;

    @TableField("NonExpenseAmount")
    private java.math.BigDecimal nonExpenseAmount;

    @TableField("SelfCost")
    private java.math.BigDecimal selfCost;

    @TableField("OpCode")
    private Integer opCode;

    @TableField("OpTime")
    private java.util.Date opTime;

    @TableField("IsDelete")
    private Boolean isDelete; // 不能为NULL

    @TableField("[Order]")
    private Integer order;

    @TableField("IsUse")
    private Integer isUse;

    @TableField("CreatedBy")
    private Integer createdBy;

    @TableField("CreatedDate")
    private java.util.Date createdDate;

    @TableField("UpdateBy")
    private Integer updateBy;

    @TableField("UpdateDate")
    private java.util.Date updateDate;

    @TableField("HospitalCode")
    private Integer hospitalCode; // 不能为NULL

    @TableField("DiscountAmount")
    private java.math.BigDecimal discountAmount;

    @TableField("JzPay")
    private java.math.BigDecimal jzPay;

    @TableField("DoctorId")
    private Integer doctorId;

    @TableField("DoctorName")
    private String doctorName;

    @TableField("IsDebitPay")
    private Boolean isDebitPay;

    @TableField("DebitPay")
    private java.math.BigDecimal debitPay;

    @TableField("DebitStatus")
    private Integer debitStatus;

    @TableField("DebitCheckoutAmt")
    private java.math.BigDecimal debitCheckoutAmt;

    @TableField("DebitCheckoutDate")
    private java.util.Date debitCheckoutDate;

    @TableField("DebitCheckoutOpId")
    private Integer debitCheckoutOpId;

    public RegisterDetailTime() {}
    public RegisterDetailTime(PreRegisterDetail detail) {
        BeanUtils.copyProperties(detail, this);
    }
}
