package com.sunhealth.ihhis.model.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;

@Data
@TableName("MZYS_TB_MZYSZD")
public class Diagnose implements Serializable {

    /** 不能为NULL */
    @TableId("Id")
    private Integer id;

    /** 挂号流水号 */
    @TableField("regno")
    private Integer regno;

    /** 就诊流水号 */
    @TableField("jzlsh")
    private Integer jzlsh;

    /** 诊断类型 */
    @TableField("zdlx")
    private Integer zdlx;

    /** 诊断编码 */
    @TableField("zdbm")
    private String zdbm;

    /** 诊断名称 */
    @TableField("zdmc")
    private String zdmc;

    /** 诊断状态 */
    @TableField("zdzt")
    private Integer zdzt;

    /** 诊断标记 */
    @TableField("zdbj")
    private Short zdbj;

    /** 复核编码 */
    @TableField("fhbz")
    private Integer fhbz;

    /** 复核医生 */
    @TableField("fhys")
    private Integer fhys;

    /** 序号 */
    @TableField("xh")
    private Integer xh;

    /** 创建人 */
    @TableField("cjr")
    private Integer cjr;

    /** 创建时间 */
    @TableField("cjrq")
    private LocalDateTime cjrq;

    /** 修改人 */
    @TableField("xgr")
    private Integer xgr;

    /** 修改时间 */
    @TableField("xgrq")
    private LocalDateTime xgrq;

    /** 医院编码 */
    @TableField("yybm")
    private Integer yybm;

    /** 上报标记 */
    @TableField("sbbj")
    private Integer sbbj;

    /** 导入标记 */
    @TableField("drbj")
    private Integer drbj;

    @TableField("Prefix")
    private String prefix;

    @TableField("Suffix")
    private String suffix;

    @TableField("Property")
    private Integer property;

    @TableField("SyndromeCode")
    private String syndromeCode;

    @TableField("SyndromeName")
    private String syndromeName;
}

