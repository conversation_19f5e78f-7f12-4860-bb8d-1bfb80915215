package com.sunhealth.ihhis.model.entity.sequences;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

@Data
@TableName("IO_PreLog_MaxId")
public class PreLogMaxId {

    @TableId(type = IdType.AUTO)
    private Long id;

    private Integer value;

    public PreLogMaxId(Integer value) {
        this.value = value;
    }
}
