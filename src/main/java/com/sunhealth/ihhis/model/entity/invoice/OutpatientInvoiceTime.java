package com.sunhealth.ihhis.model.entity.invoice;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

@Data
@TableName("Reg_Tb_OutpatientInvoice_Time")
public class OutpatientInvoiceTime implements Serializable {
    private static final long serialVersionUID = 1L;

    @TableId("InvoiceID")
    private Long invoiceID; // 主键字段，不能为 NULL

    @TableField("ReturnInvoiceID")
    private Long returnInvoiceID;

    @TableField("RegNo")
    private Long regNo;

    @TableField("CardType")
    private String cardType;

    @TableField("CardNo")
    private String cardNo;

    @TableField("OutpatientNo")
    private String outpatientNo;

    @TableField("InvoiceNo")
    private Long invoiceNo;

    @TableField("PatID")
    private Long patID; // 不能为 NULL

    @TableField("NewPatID")
    private Long newPatID; // 不能为 NULL

    @TableField("PatName")
    private String patName;

    @TableField("ChargeType")
    private Integer chargeType;

    @TableField("InvoicePrefix")
    private String invoicePrefix;

    @TableField("InvoiceLabel")
    private String invoiceLabel;

    @TableField("InvoiceInfo")
    private String invoiceInfo;

    @TableField("OpCode")
    private Integer opCode;

    @TableField("OpTime")
    private Date opTime;

    @TableField("IsPrint")
    private Integer isPrint;

    @TableField("Printer")
    private Integer printer;

    @TableField("PrintTime")
    private Date printTime;

    /**
     * 总金额
     */
    @TableField("TotalAmount")
    private BigDecimal totalAmount;

    @TableField("CapitalAmount")
    private String capitalAmount;

    /**
     * 自费金额
     */
    @TableField("OwnFee")
    private BigDecimal ownFee;

    /**
     * 医保结算范围总额
     */
    @TableField("InsuranceTotal")
    private BigDecimal insuranceTotal;

    /**
     * 医保现金支付
     */
    @TableField("InsuranceCashTotal")
    private BigDecimal insuranceCashTotal;

    /**
     * 交易费用总额
     */
    @TableField("CostTotal")
    private BigDecimal costTotal;

    /**
     * 分类支付
     */
    @TableField("FLPay")
    private BigDecimal flPay;

    /**
     * 统筹支付
     */
    @TableField("PubPay")
    private BigDecimal pubPay;

    /**
     * 附加支付
     */
    @TableField("AppendPay")
    private BigDecimal appendPay;

    /**
     * 账户支付
     */
    @TableField("CurrAccountPay")
    private BigDecimal currAccountPay;

    /**
     * 历年账户支付
     */
    @TableField("LastAccountPay")
    private BigDecimal lastAccountPay;

    /**
     * 当年账户余额
     */
    @TableField("CurrAccountBalance")
    private BigDecimal currAccountBalance;

    /**
     * 历年账户余额
     */
    @TableField("LastAccountBalance")
    private BigDecimal lastAccountBalance;

    @TableField("HisCardFee")
    private BigDecimal hisCardFee;

    /**
     * 应付金额
     */
    @TableField("PayFee")
    private BigDecimal payFee;

    @TableField("CashFee")
    private BigDecimal cashFee;

    /**
     * 预收
     */
    @TableField("ReceiveAmount")
    private BigDecimal receiveAmount;

    @TableField("ChangeAmount")
    private BigDecimal changeAmount;

    @TableField("ErrorCents")
    private BigDecimal errorCents;

    @TableField("Status")
    private Integer status;

    @TableField("UseFlag")
    private Integer useFlag;

    @TableField("BussEvent")
    private Integer bussEvent;

    @TableField("BillNo")
    private Integer billNo;

    @TableField("Flag")
    private Integer flag;

    /**
     * 医保交易流水号
     */
    @TableField("TradeSerialNo")
    private String tradeSerialNo;

    @TableField("PrepaidRemind")
    private BigDecimal prepaidRemind;

    @TableField("PrepaidPay")
    private BigDecimal prepaidPay;

    @TableField("PrepaidId")
    private Integer prepaidId;

    @TableField("DataFrom")
    private Integer dataFrom;

    @TableField("FromFlag")
    private Integer fromFlag;

    @TableField("Rate")
    private BigDecimal rate;

    @TableField("DiscountAmount")
    private BigDecimal discountAmount;

    @TableField("ChargeNo")
    private Long chargeNo;

    @TableField("OriginalInvoiceId")
    private Long originalInvoiceId;

    @TableField("ComputerNo")
    private String computerNo;

    @TableField("TerminalType")
    private Integer terminalType;

    @TableField("PayOrderNo")
    private String payOrderNo;

    @TableField("ReturnOrderNo")
    private String returnOrderNo;

    @TableField("DispensingWindow")
    private String dispensingWindow;

    @TableField("IsDelete")
    private Boolean isDelete = false; // 不能为 NULL，默认值为 false

    @TableField("CreatedBy")
    private Integer createdBy;

    @TableField("CreatedDate")
    private Date createdDate;

    @TableField("UpdateBy")
    private Integer updateBy;

    @TableField("UpdateDate")
    private Date updateDate;

    @TableField("ReturnOpCode")
    private Integer returnOpCode;

    @TableField("ReturnOpTime")
    private Date returnOpTime;

    @TableField("DrugDept")
    private Integer drugDept;

    @TableField("HospitalCode")
    private Integer hospitalCode; // 不能为 NULL

    @TableField("SerialNo")
    private Long serialNo;

    @TableField("MzNo")
    private Long mzNo;

    @TableField("DeptKind")
    private Integer deptKind;

    /**
     * 记账金额
     */
    @TableField("JzPay")
    private BigDecimal jzPay;

    @TableField("RecipeDoctor")
    private Integer recipeDoctor;

    @TableField("RecipeDept")
    private Integer recipeDept;

    @TableField("AccountFlag")
    private String accountFlag;

    @TableField("DrugInfo")
    private String drugInfo;

    @TableField("PayType")
    private Integer payType;

    @TableField("PrintCount")
    private Integer printCount;

    @TableField("RemarkAI")
    private String remarkAI;

    @TableField("PrepaidCardPay")
    private BigDecimal prepaidCardPay;

    @TableField("DiffAmount")
    private BigDecimal diffAmount;

    @TableField("IsDebitPay")
    private Boolean isDebitPay;

    @TableField("DebitAmt")
    private BigDecimal debitAmt;

    @TableField("DebitStatus")
    private Integer debitStatus;

    @TableField("DebitCheckoutAmt")
    private BigDecimal debitCheckoutAmt;

    @TableField("DebitCheckoutDate")
    private String debitCheckoutDate;

    @TableField("DebitCheckoutOpId")
    private Integer debitCheckoutOpId;

    @TableField("DebitCheckoutInvoiceID")
    private Long debitCheckoutInvoiceID;
}

