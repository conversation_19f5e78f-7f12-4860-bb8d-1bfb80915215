package com.sunhealth.ihhis.model.entity.sequences;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

/**
 * 急诊护理加收ID
 */
@Data
@TableName("Reg_Sequences_ThirdAddAcount")
public class SequencesThirdAddAccountId {

    @TableId(type = IdType.AUTO)
    private Long id;

    private Integer value;

    public SequencesThirdAddAccountId(Integer value) {
        this.value = value;
    }
}
