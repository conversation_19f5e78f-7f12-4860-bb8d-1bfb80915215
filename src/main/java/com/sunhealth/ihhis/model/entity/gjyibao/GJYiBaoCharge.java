package com.sunhealth.ihhis.model.entity.gjyibao;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

@Data
@TableName("Reg_GjYiBao_Charge")
public class GJYiBaoCharge implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId("ChargeNo")
    private Long chargeNo;  // 收费流水号

    @TableField("HospitalCode")
    private Integer hospitalCode;  // 医院拜纳姆 不能为NULL

    @TableField("RegNo")
    private Long regNo;  // 挂号流水号 不能为NULL

    @TableField("MdtrtId")
    private String mdtrtId;  // 就诊ID

    @TableField("PsnNo")
    private String psnNo;  // 人员编号

    @TableField("PsnName")
    private String psnName;  // 姓名

    @TableField("PsnCertType")
    private String psnCertType;  // 证件类型

    @TableField("CertNo")
    private String certNo;  // 证件号

    @TableField("Gend")
    private String gend;  // 性别

    @TableField("Naty")
    private String naty;  // 民族

    @TableField("Brdy")
    private String brdy;  // 生日

    @TableField("Age")
    private BigDecimal age;  // 年龄

    @TableField("Insutype")
    private String insutype;  // 险种类型

    @TableField("PsnType")
    private String psnType;  // 人员类别

    @TableField("CvlservFlag")
    private String cvlservFlag;  // 公务员标识

    @TableField("SetlTime")
    private String setlTime;  // 结算时间

    @TableField("MdtrtCertType")
    private String mdtrtCertType;  // 就诊凭证类型

    @TableField("MedType")
    private String medType;  // 医疗类别

    @TableField("MedfeeSumamt")
    private BigDecimal medfeeSumamt;  // 医疗费用总额

    @TableField("FulamtOwnpayAmt")
    private BigDecimal fulamtOwnpayAmt;  // 全自费金额

    @TableField("OverlmtSelfpay")
    private BigDecimal overlmtSelfpay;  // 超限价自费费用

    @TableField("PreselfpayAmt")
    private BigDecimal preselfpayAmt;  // 先行自负金额

    @TableField("InscpScpAmt")
    private BigDecimal inscpScpAmt;  // 符合政策范围金额

    @TableField("ActPayDedc")
    private BigDecimal actPayDedc;  // 实际支付起付线

    @TableField("HifpPay")
    private BigDecimal hifpPay;  // 基本医疗报写统筹基金支出

    @TableField("PoolPropSelfpay")
    private BigDecimal poolPropSelfpay;  // 基本医疗保险统筹基金

    @TableField("CvlservPay")
    private BigDecimal cvlservPay;  // 公务员医疗补助资金支出

    @TableField("HifesPay")
    private BigDecimal hifesPay;  // 企业补充医疗保险基金支出

    @TableField("HifmiPay")
    private BigDecimal hifmiPay;  // 居民大病保险资金支出

    @TableField("HifobPay")
    private BigDecimal hifobPay;  // 职工大额医疗费用补助基金支出

    @TableField("MafPay")
    private BigDecimal mafPay;  // 医疗救助基金支出

    @TableField("OthPay")
    private BigDecimal othPay;  // 其他支出

    @TableField("FundPaySumamt")
    private BigDecimal fundPaySumamt;  // 基金支付总额

    @TableField("PsnPartAmt")
    private BigDecimal psnPartAmt;  // 个人担负总额

    @TableField("AcctPay")
    private BigDecimal acctPay;  // 个人账户支出

    @TableField("PsnCashPay")
    private BigDecimal psnCashPay;  // 个人现金支出

    @TableField("HospPartAmt")
    private BigDecimal hospPartAmt;  // 医院担负金额

    @TableField("Balc")
    private BigDecimal balc;  // 余额

    @TableField("AcctMulaidPay")
    private BigDecimal acctMulaidPay;  // 个人账户共济支付金额

    @TableField("MedinsSetlId")
    private String medinsSetlId;  // 医疗机构结算ID

    @TableField("ClrOptins")
    private String clrOptins;  // 清算经办机构

    @TableField("ClrWay")
    private String clrWay;  // 清算方式

    @TableField("ClrType")
    private String clrType;  // 清算类别

    @TableField("expContent")
    private String expContent;  // 补充字段

    @TableField("HifdmPay")
    private BigDecimal hifdmPay;  // 伤残人员医疗保险基金支付

    @TableField("SetlId")
    private String setlId;  // 结算ID

    @TableField("CreateTime")
    private Date createTime;  // 创建实际

    @TableField("UpdateTime")
    private Date updateTime;  // 修改实际

    @TableField("Status")
    private Integer status;  // 状态

    @TableField("MsgID")
    private String msgID;  // 国家医保收费记录表

    @TableField("insuplc_admdvs")
    private String insuplcAdmdvs;  // 参保地
}

