package com.sunhealth.ihhis.model.entity.gjyibao;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.util.Date;

@Data
@TableName("Reg_GjYiBao_PatientAccount")
public class GjYiBaoPatientAccount {

    @TableId("PatId")
    private Integer patId;

    @TableField("CardNo")
    private String cardNo;

    @TableField("PsnNo")
    private String psnNo;

    @TableField("PsnCertType")
    private String psnCertType;

    @TableField("Certno")
    private String certno;

    @TableField("PsnName")
    private String psnName;

    @TableField("Gend")
    private String gend;

    @TableField("Naty")
    private String naty;

    @TableField("Brdy")
    private String brdy;

    @TableField("Age")
    private String age;

    @TableField("Balc")
    private String balc;

    @TableField("Insutype")
    private String insutype;

    @TableField("PsnType")
    private String psnType;

    @TableField("PsnInsuStas")
    private String psnInsuStas;

    @TableField("PsnInsuDate")
    private Date psnInsuDate;

    @TableField("PausInsuDate")
    private Date pausInsuDate;

    @TableField("CvlservFlag")
    private String cvlservFlag;

    @TableField("InsuplcAdmdvs")
    private String insuplcAdmdvs;

    @TableField("EmpName")
    private String empName;

    @TableField("PsnIdetType")
    private String psnIdetType;

    @TableField("PsnTypeLv")
    private String psnTypeLv;

    @TableField("BegnTime")
    private String begnTime;

    @TableField("EndTime")
    private String endTime;

    @TableField("HospitalCode")
    private Integer hospitalCode;

    @TableField("AccountFlag")
    private String accountFlag;

    @TableField("CreateDate")
    private Date createDate;
}
