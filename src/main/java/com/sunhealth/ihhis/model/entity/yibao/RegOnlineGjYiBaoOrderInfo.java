package com.sunhealth.ihhis.model.entity.yibao;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

@Data
@TableName("Reg_Online_GjYiBao_OrderInfo")
public class RegOnlineGjYiBaoOrderInfo implements Serializable {

    @TableId(value = "ID", type = IdType.ASSIGN_ID)
    private Long id; // 不能为NULL

    @TableField("PatName")
    private String patName; // 就诊人姓名

    @TableField("CardType")
    private String cardType; // 证件类型

    @TableField("CardNo")
    private String cardNo; // 证件号码

    @TableField("ChargeNo")
    private Long chargeNo; // 结算单号

    @TableField("RegNo")
    private Long regNo; // 门诊序号

    @TableField("Flag")
    private Integer flag; // 0 挂号 1 缴费

    @TableField("HospitalCode")
    private String hospitalCode; // 医院编码

    @TableField("OrdStas")
    private String ordStas; // 医保-订单状态

    @TableField("PayOrdId")
    private String payOrdId; // 医保-支付订单号

    @TableField("CallType")
    private String callType; // 医保-回调类型

    @TableField("MedOrgOrd")
    private String medOrgOrd; // 医保-医院订单

    @TableField("TraceTime")
    private String traceTime; // 医保-交易时间

    @TableField("OrgCode")
    private String orgCode; // 医保-两定机构编码

    @TableField("OrgName")
    private String orgName; // 医保-两定机构名称

    @TableField("SetlType")
    private String setlType; // 医保-结算类型：ALL:医保自费全部，CASH:只结现金 HI:只结医保

    @TableField("FeeSumamt")
    private BigDecimal feeSumamt; // 医保-费用总额

    @TableField("OwnPayAmt")
    private BigDecimal ownPayAmt; // 医保-现金支付

    @TableField("PsnAcctPay")
    private BigDecimal psnAcctPay; // 医保-个人账户支出

    @TableField("FundPay")
    private BigDecimal fundPay; // 医保-医保基金支付

    @TableField("RevsToken")
    private String revsToken; // 医保-用于院内结算失败对医保的冲正授权

    @TableField("ExtData")
    private String extData; // 医保-扩展数据

    @TableField("Deposit")
    private BigDecimal deposit; // 医保-住院押金

    @TableField("HiChrgTime")
    private String hiChrgTime; // 医保-收费时间

    @TableField("HiDocSn")
    private String hiDocSn; // 医保-交易流水号

    @TableField("HiRgstSn")
    private String hiRgstSn; // 医保-挂号流水号

    @TableField("EcCode")
    private String ecCode; // 医保-电子凭证码值

    @TableField("CreatedDate")
    private Date createdDate; // 创建时间

    @TableField("insuplc_admdvs")
    private String insuplcAdmdvs; // 医保-参保地医保区划
    @TableField("setl_id")
    private String setlId;
    @TableField("psn_no")
    private String psnNo;
    @TableField("mdtrt_id")
    private String mdtrtId;
}

