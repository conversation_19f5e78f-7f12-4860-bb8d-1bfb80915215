package com.sunhealth.ihhis.model.entity.view;

import java.io.Serializable;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableField;
import lombok.Data;

@Data
@TableName("Reg_Tv_RegisterList")
public class RegisterListView implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId("RegNo")
    private Long regNo;
    /**
     * 退号记录的regno
     */
    @TableField("ReturnRegNo")
    private Long returnRegNo;

    @TableField("PatID")
    private Long patID; // 不能为NULL

    @TableField("NewPatID")
    private Long newPatID;

    @TableField("CardNo")
    private String cardNo;

    @TableField("OutPatientNo")
    private String outPatientNo;

    @TableField("HospNo")
    private String hospNo;

    @TableField("PatName")
    private String patName;

    @TableField("CourseID")
    private Integer courseID;

    @TableField("CourseName")
    private String courseName; // 不能为NULL

    @TableField("DeptID")
    private Integer deptID;

    @TableField("DoctorID")
    private Integer doctorID;

    @TableField("DoctorLevel")
    private Integer doctorLevel;
    /**
     * 收费类型，10自费，30医保，45大病
     */
    @TableField("ChargeType")
    private Integer chargeType;
    /**
     * 结算方式， 0 线下，1线上
     */
    @TableField("BlanceWay")
    private Integer blanceWay;
    /**
     * 挂号类型，0门诊，1急诊
     */
    @TableField("RegistType")
    private Integer registType = 0;
    /**
     * 预约途径，取值来自于预约数据，字典表TB_Dic_SourceType
     */
    @TableField("AppointmentWay")
    private Integer appointmentWay;
    /**
     *  预约号源SQH，表TB_Appointment字段SQH（预约数据唯一值）
     */
    @TableField("AppointmentNo")
    private String appointmentNo;
    /**
     * 挂号序号
     */
    @TableField("RegistOrder")
    private Integer registOrder;
    /**
     * 挂号方式，0缴费，1不缴费（诊间直接生成挂号数据只是为了收费）
     */
    @TableField("RegistMode")
    private Integer registMode;

    @TableField("VisitTime")
    private String visitTime;
    /**
     * 状态 0有效，80退费，120红冲
     */
    @TableField("Status")
    private Integer status; // 不能为NULL

    @TableField("RegistTime")
    private java.util.Date registTime;
    /**
     * 机器编码，本机IP设置里机器编号
     */
    @TableField("ComputerNo")
    private String computerNo;

    @TableField("OpCode")
    private Integer opCode;

    @TableField("CreateTime")
    private java.util.Date createTime;

    @TableField("CureCode")
    private String cureCode;
    /**
     *  初复诊标识 2表示复诊
     */
    @TableField("VisitFlag")
    private Integer visitFlag;

    @TableField("IsDelete")
    private Boolean isDelete; // 不能为NULL

    @TableField("CreatedBy")
    private Integer createdBy; // 不能为NULL

    @TableField("CreatedDate")
    private java.util.Date createdDate; // 不能为NULL

    @TableField("UpdateDate")
    private java.util.Date updateDate; // 不能为NULL

    @TableField("updateby")
    private Integer updateby; // 不能为NULL
    /**
     * 转院标记 0未转院，1已转院
     */
    @TableField("ReferralFlag")
    private Integer referralFlag;

    @TableField("DeptKind")
    private Short deptKind;
    /**
     * 接诊标记 0未接诊，1已接诊
     */
    @TableField("FzFlag")
    private Integer fzFlag;

    @TableField("HospitalCode")
    private Integer hospitalCode; // 不能为NULL

    @TableField("UnitNo")
    private String unitNo;
    /**
     * 医保结算类型0，1自费或者五期医保，2国家医保
     */
    @TableField("JsFlag")
    private Integer jsFlag;
   
    /**
     * 排班预约类型，字典表select * from TB_Dic_HisDictionaryExt where DictionaryTypeID=113
     */
    @TableField("ReservationType")
    private String reservationType;

    @TableField("AppointmentFlag")
    private String appointmentFlag;

    @TableField("IsGreenChannel")
    private Boolean isGreenChannel;

    @TableField("IsDebitPay")
    private Boolean isDebitPay;
}
