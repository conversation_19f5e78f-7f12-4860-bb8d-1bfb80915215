package com.sunhealth.ihhis.model.entity.sequences;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.util.Date;

@Data
@TableName("Tbt_Recipe_YBJS_PatSfzToKH")
public class TbtRecipeYBJSPatSfzToKH {

    @TableField("PatSfz")
    private String patSfz;

    @TableField("KH")
    private String kh;

    @TableField("Xzqh")
    private String xzqh;

    @TableField("WriteTime")
    private Date writeTime;
}
