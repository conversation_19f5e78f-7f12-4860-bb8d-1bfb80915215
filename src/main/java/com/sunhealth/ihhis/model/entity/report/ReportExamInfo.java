package com.sunhealth.ihhis.model.entity.report;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.util.Date;
import lombok.Data;

/**
 * 检查报告
 */
@Data
@TableName("Report_Exam_Info")
public class ReportExamInfo implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    @TableId("Id")
    private Long id;

    /**
     * 患者ID
     */
    @TableField("PatientId")
    private String patientId;

    /**
     * 患者类型：01 门诊 02 住院 03 急诊 04 体检
     */
    @TableField("PatientType")
    private String patientType;

    /**
     * 就诊流水号：门诊挂号流水号、住院流水号
     */
    @TableField("VisitNo")
    private String visitNo;

    /**
     * 患者姓名
     */
    @TableField("PatName")
    private String patName;

    /**
     * 住院号(门诊号)
     */
    @TableField("HospNo")
    private String hospNo;

    /**
     * 卡号
     */
    @TableField("CardNo")
    private String cardNo;

    /**
     * 床号
     */
    @TableField("BedNo")
    private String bedNo;

    /**
     * 医院名称
     */
    @TableField("HospitalName")
    private String hospitalName;

    /**
     * 院区ID：医院ID
     */
    @TableField("HospitalId")
    private Integer hospitalId;

    /**
     * 报告编号
     */
    @TableField("ReportNo")
    private String reportNo;

    /**
     * 报告类型(CT、放射、内镜等)
     */
    @TableField("ReportType")
    private String reportType;

    /**
     * 报告类型名称
     */
    @TableField("ReportTypeName")
    private String reportTypeName;

    /**
     * 申请单Id
     */
    @TableField("ApplyId")
    private String applyId;

    /**
     * 申请科室编码：申请科室
     */
    @TableField("ApplyDept")
    private String applyDept;

    /**
     * 申请科室名称
     */
    @TableField("ApplyDeptName")
    private String applyDeptName;

    /**
     * 申请病区编码：申请病区
     */
    @TableField("ApplyWard")
    private String applyWard;

    /**
     * 申请病区名称
     */
    @TableField("ApplyWardName")
    private String applyWardName;

    /**
     * 申请医生编码：申请医生ID
     */
    @TableField("ApplyDoctor")
    private String applyDoctor;

    /**
     * 申请医生名称
     */
    @TableField("ApplyDoctorName")
    private String applyDoctorName;

    /**
     * 申请时间
     */
    @TableField("ApplyTime")
    private Date applyTime;

    /**
     * 检查部位
     */
    @TableField("ExamSite")
    private String examSite;

    /**
     * 检查方式
     */
    @TableField("ExamMethod")
    private String examMethod;

    /**
     * 影像号
     */
    @TableField("RaditeNo")
    private String raditeNo;

    /**
     * 检查科室编码：检查科室
     */
    @TableField("ExamDept")
    private String examDept;

    /**
     * 检查科室名称
     */
    @TableField("ExamDeptName")
    private String examDeptName;

    /**
     * 检查时间
     */
    @TableField("ExamTime")
    private Date examTime;

    /**
     * 检查医生编码：检查医生ID
     */
    @TableField("ExamDoctor")
    private String examDoctor;

    /**
     * 检查医生名称
     */
    @TableField("ExamDoctorName")
    private String examDoctorName;

    /**
     * 报告时间
     */
    @TableField("ReportTime")
    private Date reportTime;

    /**
     * 报告医生
     */
    @TableField("ReportDoctor")
    private String reportDoctor;

    /**
     * 报告医生名称
     */
    @TableField("ReportDoctorName")
    private String reportDoctorName;

    /**
     * 报告审核时间
     */
    @TableField("AuditorTime")
    private Date auditorTime;

    /**
     * 报告审核医生
     */
    @TableField("AuditorDoctor")
    private String auditorDoctor;

    /**
     * 报告审核医生名称
     */
    @TableField("AuditorDoctorName")
    private String auditorDoctorName;

    /**
     * 报告Url地址
     */
    @TableField("ReportUrl")
    private String reportUrl;

    /**
     * 检查意见（提示）
     */
    @TableField("ExamVerdict")
    private String examVerdict;

    /**
     * 检查诊断
     */
    @TableField("ExamDiag")
    private String examDiag;

    /**
     * 检查所见
     */
    @TableField("ExamDescript")
    private String examDescript;

    /**
     * 危机值标志
     */
    @TableField("CriticalFlag")
    private Integer criticalFlag;

    /**
     * 危机值内容
     */
    @TableField("CriricalContent")
    private String criricalContent;

    /**
     * 报告状态(0 作废 1审核 )
     */
    @TableField("ReportStatus")
    private Integer reportStatus;

    /**
     * 接收时间戳
     */
    @TableField("ReceiveTime")
    private Long receiveTime;

    /**
     * 报告检查项目
     */
    @TableField(exist = false)
    private String itemName;

    @TableField(exist = false)
    private String sex;
    @TableField(exist = false)
    private String age;

}
