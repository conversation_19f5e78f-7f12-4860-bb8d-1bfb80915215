package com.sunhealth.ihhis.model.entity.patient;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

@Data
@TableName("Reg_Tb_PatientList")
public class RTPatientList implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId("PatID")
    private Long patId;

    @TableField("NewPatId")
    private Long newPatId;

    @TableField("PatName")
    private String patName;

    @TableField("HospNo")
    private String hospNo;

    @TableField("FirstName")
    private String firstName;

    @TableField("LastName")
    private String lastName;

    @TableField("ShowName")
    private String showName;

    @TableField("PatNameSpell")
    private String patNameSpell;

    @TableField("PatNameCode")
    private String patNameCode;

    @TableField("CertificateType")
    private String certificateType;

    @TableField("CertificateNo")
    private String certificateNo;

    @TableField("Birthday")
    private Date birthday;

    @TableField("Sex")
    private Integer sex;

    @TableField("OpCode")
    private Integer opCode;

    @TableField("CreateTime")
    private Date createTime;

    @TableField("IsDelete")
    private Boolean isDelete;

    @TableField("[Order]")
    private Integer order;

    @TableField("IsUse")
    private Integer isUse;

    @TableField("CreatedBy")
    private Integer createdBy;

    @TableField("CreatedDate")
    private Date createdDate;

    @TableField("UpdateBy")
    private Integer updateBy;

    @TableField("UpdateDate")
    private Date updateDate;

    @TableField("HospitalCode")
    private Integer hospitalCode;

    @TableField("RegionCode")
    private String regionCode;

    @TableField("IsUnknownInfo")
    private Boolean isUnknownInfo;
    @TableField(exist = false)
    private String sexStr;
    @TableField(exist = false)
    private int age;
}