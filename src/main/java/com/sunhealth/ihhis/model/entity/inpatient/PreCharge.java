package com.sunhealth.ihhis.model.entity.inpatient;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 住院预交金充值记录
 */
@Data
@TableName("IO_Tb_PatientPreCharge")
public class PreCharge implements Serializable {

    @TableId("PreChargeId")
    private Long preChargeId;

    @TableField("RegNo")
    private Long regNo;

    @TableField("NewRegNo")
    private Long newRegNo;

    @TableField("HospNo")
    private String hospNo;

    @TableField("PatId")
    private Integer patId;

    @TableField("Amount")
    private BigDecimal amount;

    @TableField("PayTime")
    private Date payTime;

    @TableField("PayType")
    private Integer payType;

    @TableField("ChargeType")
    private Integer chargeType;

    @TableField("Source")
    private Integer source;

    @TableField("CreateUserId")
    private Integer createUserId;

    @TableField("CreateOn")
    private Date createOn;

    @TableField("VerifyUserId")
    private Integer verifyUserId;

    @TableField("VerifyOn")
    private Date verifyOn;

    @TableField("BillStatus")
    private Integer billStatus;

    @TableField("CheckStatus")
    private Boolean checkStatus;

    @TableField("PrintStatus")
    private Boolean printStatus;

    @TableField("IsDelete")
    private Boolean isDelete;

    @TableField("HospitalId")
    private Integer hospitalId;

    @TableField("IsAccounts")
    private Integer isAccounts;

    /**
     * Origin 费用来源
     * 1-窗口
     * 2-便民（微信公众号）
     * 3-线上
     * 4-微信小程序
     * 5-支付宝小程序
     */
    @TableField("Origin")
    private Integer origin;

    @TableField("DataSources")
    private Integer dataSources;

    @TableField("HospTradeNo")
    private String hospTradeNo;

    @TableField("PayTradeNo")
    private String payTradeNo;

//    @TableField("RecipeNo")
//    private String recipeNo;

//    private String remark;
//    private String checkNo;
//    private Integer payWay;
//    private Long PairId;
//    private String OpenAct;
//    private String OpenBank;
//    private String OpenCorp;
//    private Integer PrIntegerUserId;
//    private Date PrIntegerOn;
//    private Long CheckId;
//    private Integer DeleteUserId;
//    private Date DeleteOn;
//    private String PayMethod;
//    private Long BillId;
//    private Integer DataSources;
//    private Integer CurrentNo;
//    private Integer PrIntegerNum;
//    private String GuaranteeName;
//    private String Tel;
}
