package com.sunhealth.ihhis.model.entity.patient;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.util.Date;

/**
 * 就诊人详情
 */
@Data
@TableName("Reg_Tb_PatientDetl")
public class RTPatientDetl {

    @TableId("PatId")
    private Long patId;

    /**
     * 医院编码
     */
    @TableField("HospitalCode")
    private Integer hospitalCode;

    /**
     * 地址
     */
    @TableField("Address")
    private String address;

    /**
     * 婚姻
     */
    @TableField("Marriage")
    private Integer marriage;

    /**
     * 民族
     */
    @TableField("Nation")
    private Integer nation;

    /**
     * 职业
     */
    @TableField("Occupation")
    private Integer occupation;

    /**
     * 国家
     */
    @TableField("Nationality")
    private Integer nationality;

    /**
     * 手机
     */
    @TableField("PatPhone")
    private String patPhone;

    /**
     * 备注
     */
    @TableField("Remark")
    private String remark;

    /**
     * 联系人名称
     */
    @TableField("ContactsName")
    private String contactsName;

    /**
     * 联系人电话
     */
    @TableField("ContactsRelationShipTel")
    private String contactsRelationShipTel;

    /**
     * 联系人关系
     */
    @TableField("ContactsRelationShip")
    private String contactsRelationShip;

    /**
     * 现住地，现住地址，详至门牌号
     */
    @TableField("LiveAddr")
    private String liveAddr;


    /**
     * 单位名称
     */
    @TableField("CompanyName")
    private String companyName;

    /**
     * 单位编码
     */
    @TableField("CompanyCode")
    private String companyCode;


    @TableField("CreateTime")
    private Date createTime;

    /**
     * 是否删除
     */
    @TableField("IsDelete")
    private Boolean isDelete;

    /**
     * 是否使用
     */
    @TableField("IsUse")
    private Integer isUse;

    @TableField("OpCode")
    private Integer opCode;

    /**
     * 创建人
     */
    @TableField("CreatedBy")
    private Integer createdBy;

    /**
     * 创建时间
     */
    @TableField("CreatedDate")
    private Date createdDate;

    /**
     * 修改人
     */
    @TableField("UpdateBy")
    private Integer updateBy;

    /**
     * 修改时间
     */
    @TableField("UpdateDate")
    private Date updateDate;



//    @TableField("NewPatID")
//    private Long newPatID;
//    @TableField("RecordNo")
//    private String recordNo;
//    @TableField("DiagnoseCode")
//    private String diagnoseCode;
//    /**
//     * 初复诊
//     */
//    @TableField("VisitFlag")
//    private Integer visitFlag;
//    /**
//     * 保健情况
//     */
//    @TableField("HealthCardNo")
//    private String healthCardNo;
//    /**
//     * 文化程度
//     */
//    @TableField("Education")
//    private Integer education;
//    @TableField("Religion")
//    private Integer religion;
//    @TableField("AccountFlag")
//    private String accountFlag;
//    /**
//     * 凭证编号
//     */
//    @TableField("EvidenceNo")
//    private String evidenceNo;
//    /**
//     * 工伤认定号
//     */
//    @TableField("IndustrialInjury")
//    private String industrialInjury;
//    @TableField("Member")
//    private String member;
//    @TableField("Source")
//    private Integer source;
//    /**
//     * 记账单位
//     */
//    @TableField("AccountUnit")
//    private Integer accountUnit;
//    /**
//     * 记账单位编码
//     */
//    @TableField("AccountUnitCode")
//    private String accountUnitCode;
//    /**
//     * 记账单位类型
//     */
//    @TableField("AccountUnitType")
//    private Integer accountUnitType;
//    /**
//     * 个人性质
//     */
//    @TableField("PersonalProperty")
//    private Integer personalProperty;
//    /**
//     * 保健情况
//     */
//    @TableField("HealthCare")
//    private Integer healthCare;
//    /**
//     * 职退情况
//     */
//    @TableField("RetirementSituation")
//    private Integer retirementSituation;
//    /**
//     * 患者属性
//     */
//    @TableField("Attributes")
//    private Integer attributes;
//    /**
//     * 患者类型
//     */
//    @TableField("PatType")
//    private Integer patType;
//    /**
//     * 单位类型
//     */
//    @TableField("CompanyType")
//    private Integer companyType;
//    /**
//     * 单位电话
//     */
//    @TableField("CompanyPhone")
//    private String companyPhone;
//    /**
//     * 单位联系人
//     */
//    @TableField("CompanyContacts")
//    private String companyContacts;
//    /**
//     * 单位联系人电话
//     */
//    @TableField("CompanyContactsTel")
//    private String companyContactsTel;
//    /**
//     * 单位联系人电话
//     */
//    @TableField("CompanyContactsPhone")
//    private String companyContactsPhone;
//    /**
//     * 单位地址
//     */
//    @TableField("CompanyAddress")
//    private String companyAddress;
//    @TableField("ContactsRelationShipPhone")
//    private String contactsRelationShipPhone;
//    /**
//     * 联系人邮编
//     */
//    @TableField("ContactsRelationZip")
//    private String contactsRelationZip;
//    /**
//     * 联系人地址
//     */
//    @TableField("ContactsRelationShipAddress")
//    private String contactsRelationShipAddress;
//    @TableField("OpCode")
//    private Integer opCode;
//    /**
//     * 创建时间
//     */
//    @TableField("CreateTime")
//    private Date createTime;
//    /**
//     * 是否删除
//     */
//    @TableField("IsDelete")
//    private Boolean isDelete;
//    @TableField("[Order]")
//    private Integer order;
//    /**
//     * 是否使用
//     */
//    @TableField("IsUse")
//    private Integer isUse;
//    /**
//     * 创建人
//     */
//    @TableField("CreatedBy")
//    private Integer createdBy;
//    /**
//     * 创建时间
//     */
//    @TableField("CreatedDate")
//    private Date createdDate;
//    /**
//     * 修改人
//     */
//    @TableField("UpdateBy")
//    private Integer updateBy;
//    /**
//     * 修改时间
//     */
//    @TableField("UpdateDate")
//    private Date updateDate;
//    /**
//     * 拓展信息
//     */
//    @TableField("ExtendInfo")
//    private String extendInfo;
//    @TableField("SpecialFlag")
//    private String specialFlag;
//    @TableField("SeriousDiseaseCode")
//    private String seriousDiseaseCode;
//    /**
//     * 病员性质
//     */
//    @TableField("Patflag")
//    private Integer patflag;
//    /**
//     * 出生地
//     */
//    @TableField("Birthplace")
//    private String birthplace;
//    @TableField("FallRisk")
//    private String fallRisk;
//    /**
//     * 既往住院情况
//     */
//    @TableField("AdmissionTimes")
//    private Integer admissionTimes;
//    /**
//     * 是否已进行抗精神病药物治疗
//     */
//    @TableField("IsAntiPsycTreat")
//    private Integer isAntiPsycTreat;
//    /**
//     * 单位邮编
//     */
//    @TableField("PatZip")
//    private String patZip;
//    /**
//     * 所属地区
//     */
//    @TableField("RegionType")
//    private Integer regionType;
//    /**
//     * 区域名称
//     */
//    @TableField("RegionCode")
//    private String regionCode;
//    /**
//     * 籍贯
//     */
//    @TableField("NativePlace")
//    private Integer nativePlace;
//    /**
//     * 家庭电话
//     */
//    @TableField("FamilyPhone")
//    private String familyPhone;
//    /**
//     * 家庭邮编
//     */
//    @TableField("FamilyZip")
//    private String familyZip;
//    /**
//     * 户别
//     */
//    @TableField("RegType")
//    private Integer regType;
//    /**
//     * 户籍地，省
//     */
//    @TableField("RegProv")
//    private String regProv;
//    /**
//     * 户籍地，市
//     */
//    @TableField("RegCity")
//    private String regCity;
//    /**
//     * 户籍地，县
//     */
//    @TableField("RegCounty")
//    private String regCounty;
//    /**
//     * 户籍地，乡镇
//     */
//    @TableField("RegTown")
//    private String regTown;
//    /**
//     * 户籍地，村
//     */
//    @TableField("RegVillage")
//    private String regVillage;
//    /**
//     * 户籍地址，详至门牌号
//     */
//    @TableField("RegAddr")
//    private String regAddr;
//    /**
//     * 现住地，省
//     */
//    @TableField("LiveProv")
//    private String liveProv;
//    /**
//     * 现住地，市
//     */
//    @TableField("LiveCity")
//    private String liveCity;
//    /**
//     * 现住地，县
//     */
//    @TableField("LiveCounty")
//    private String liveCounty;
//    /**
//     * 现住地，乡镇
//     */
//    @TableField("LiveTown")
//    private String liveTown;
//    /**
//     * 现住地，村
//     */
//    @TableField("LiveVillage")
//    private String liveVillage;
//    /**
//     * 公司所在地，省
//     */
//    @TableField("CompanyAddressProv")
//    private String companyAddressProv;
//    /**
//     * 公司所在地，市
//     */
//    @TableField("CompanyAddressCity")
//    private String companyAddressCity;
//    /**
//     * 公司所在地，县
//     */
//    @TableField("CompanyAddressCounty")
//    private String companyAddressCounty;
//    @TableField("ContactsRelationShipAddressProv")
//    private String contactsRelationShipAddressProv;
//    @TableField("ContactsRelationShipAddressCity")
//    private String contactsRelationShipAddressCity;
//    @TableField("ContactsRelationShipAddressCounty")
//    private String contactsRelationShipAddressCounty;
//    @TableField("PatDiagType")
//    private String patDiagType;
}
