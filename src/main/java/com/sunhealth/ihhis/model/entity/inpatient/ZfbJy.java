package com.sunhealth.ihhis.model.entity.inpatient;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

/**
 * 支付宝（或者微信）交易记录表
 */
@Data
@TableName("IO_Tb_ZfbJy")
public class ZfbJy {

    // 预交金流水号
    @TableId("PreChargeId")
    private Long preChargeId;

    // 住院流水号
    @TableField("RegNo")
    private Long regNo;

    // 患者姓名
    @TableField("PatName")
    private String patName;

    // 金额
    @TableField("RealAmount")
    private BigDecimal realAmount;

    @TableField("HospTradeNo")
    private String hospTradeNo;

    // 交易流水号
    @TableField("ZfbTradeNo")
    private String zfbTradeNo;

    // 交易时间
    @TableField("JyTime")
    private Date jyTime;

    // 住院号
    @TableField("HisCardNo")
    private String hisCardNo;

    // 卡号
    @TableField("CardNo")
    private String cardNo;

    // 状态
    @TableField("status")
    private Integer status;

    // 支付类型
    @TableField("PayType")
    private String payType;

    // 数据类型
    @TableField("dataType")
    private Integer dataType;

    @TableField("CreateUserId")
    private Integer createUserId;

    @TableField("CreateOn")
    private Date createOn;

    @TableField("UpdateUserId")
    private Integer updateUserId;

    @TableField("UpdateOn")
    private Date updateOn;

    @TableField("HospitalId")
    private Integer hospitalId;
}
