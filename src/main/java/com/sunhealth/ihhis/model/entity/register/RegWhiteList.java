package com.sunhealth.ihhis.model.entity.register;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import lombok.Data;

@Data
@TableName("Tbt_WhiteList")
public class RegWhiteList implements Serializable {
    private static final long serialVersionUID = 1L;

    @TableId(value = "Id", type = IdType.AUTO)
    private Integer Id;

    @TableField("PatName")
    private String patName;

    @TableField("CertificateType")
    private Integer certificateType;

    @TableField("CertificateNo")
    private String certificateNo;

    @TableField("PatID")
    private String patId;

    @TableField("PatTag")
    private String patTag;

    @TableField("CreateTime")
    private Date createTime;

    @TableField("Creater")
    private String creater;

    @TableField("IsUse")
    private boolean isUse;

    @TableField("HospNo")
    private String hospNo;

    @TableField("Reason")
    private String reason;

    @TableField("ExpiringDate")
    private Date expiringDate;

    @TableField("UpdateBy")
    private String updateBy;

    @TableField("updateDate")
    private Date updateDate;
}


