package com.sunhealth.ihhis;

import io.swagger.v3.oas.annotations.OpenAPIDefinition;
import io.swagger.v3.oas.annotations.info.Info;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.scheduling.annotation.EnableAsync;
import org.springframework.scheduling.annotation.EnableScheduling;

import java.nio.charset.StandardCharsets;
import java.util.TimeZone;

@SpringBootApplication
@EnableScheduling
@EnableAsync
@OpenAPIDefinition(info = @Info(title = "互联网医院his接口应用 API", version = "1.0", description = "互联网医院his接口应用API 文档"))

public class IhHisApplication {

    public static void main(String[] args) {
        TimeZone.setDefault(TimeZone.getTimeZone("GMT+8"));
        System.setProperty("file.encoding", StandardCharsets.UTF_8.name());
        SpringApplication.run(IhHisApplication.class, args);
    }

}
