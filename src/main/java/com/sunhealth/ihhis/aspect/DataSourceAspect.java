package com.sunhealth.ihhis.aspect;


import com.sunhealth.ihhis.config.holder.DataSourceContextHolder;
import com.sunhealth.ihhis.enums.DBTypeEnum;
import lombok.extern.slf4j.Slf4j;
import org.aspectj.lang.annotation.After;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Before;
import org.aspectj.lang.annotation.Pointcut;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Component;

/**
 * aop的实现的数据源切换
 * * aop切点，实现mapper类找寻，找到所属大本营以后，如db1Aspect(),则会调用<br> * db1()前面之前的操作，进行数据源的切换。
 */
@Component
@Order(value = -100)
@Slf4j
@Aspect
public class DataSourceAspect {
    @Pointcut("execution(* com.sunhealth.ihhis.dao.his..*.*(..))")
    private void hisAspect() {
    }

    @Pointcut("execution(* com.sunhealth.ihhis.dao.lis..*.*(..))")
    private void lisAspect() {
    }

    @Before("hisAspect()")
    public void db1() {
//        log.info("切换到his 数据源...");
        DataSourceContextHolder.setDbType(DBTypeEnum.his);
    }

    @Before("lisAspect()")
    public void db2() {
        log.info("切换到lis 数据源...");
        DataSourceContextHolder.setDbType(DBTypeEnum.lis);
    }
    @After("lisAspect()")
    public void cleardb2() {
        log.info("清除lis 数据源...");
        DataSourceContextHolder.clearDbType();
    }
}
