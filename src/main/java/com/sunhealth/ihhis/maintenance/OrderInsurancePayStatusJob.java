package com.sunhealth.ihhis.maintenance;

import com.sunhealth.ihhis.config.HisHospitalProperties;
import com.sunhealth.ihhis.dao.his.PreChargeMapper;
import com.sunhealth.ihhis.dao.his.PreRegisterListMapper;
import com.sunhealth.ihhis.service.ChargeService;
import com.sunhealth.ihhis.service.RegisterService;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import java.util.Date;
import java.util.List;

@Component
@Slf4j
@AllArgsConstructor
public class OrderInsurancePayStatusJob {

    private final PreRegisterListMapper preRegisterListMapper;
    private final PreChargeMapper preChargeMapper;
    private final RegisterService registerService;
    private final ChargeService chargeService;
    private final HisHospitalProperties hisHospitalProperties;
    @Scheduled(cron = "0 * * * * ?")
    public void checkRegisterInsurancePayStatus() {
        log.info("检查医保挂号支付状态定时任务开始...");
//        Date time = new Date(System.currentTimeMillis() - 30_0000_000_000L);
        Date time = new Date(System.currentTimeMillis() - 1200_000);

        List<Long> regNos = preRegisterListMapper.selectUnPaidYiBaoRegNo(hisHospitalProperties.getCode(), time);
        regNos.forEach(regNo -> {
            log.info("开始检查挂号医保支付状态: regNo = {}", regNo);
            try {
                registerService.queryYiBaoPayResult(hisHospitalProperties.getCode(), regNo, true);
            } catch (Exception e) {
                log.info(e.getMessage(), e);
            }
        });
    }

    @Scheduled(cron = "0 * * * * ?")
    public void checkChargeInsurancePayStatus() {
        log.info("检查医保门诊缴费支付状态定时任务开始...");
//        Date time = new Date(System.currentTimeMillis() - 30_0000_000_000L);
        Date time = new Date(System.currentTimeMillis() - 1200_000);

        List<Long> chargeNos = preChargeMapper.selectUnPaidYiBaoChargeNo(hisHospitalProperties.getCode(), time);
        chargeNos.forEach(chargeNo -> {
            log.info("开始检查缴费医保支付状态: chargeNo = {}", chargeNo);
            try {
                chargeService.queryYiBaoPayResult(hisHospitalProperties.getCode(), chargeNo, true);
            } catch (Exception e) {
                log.info(e.getMessage(), e);
            }
        });
    }

}
