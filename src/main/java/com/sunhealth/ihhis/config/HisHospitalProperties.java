package com.sunhealth.ihhis.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

@Component
@ConfigurationProperties(prefix = "hishospital")
@Data
public class HisHospitalProperties {
    private String code;

    /**
     * 医院名称,定点医药机构名称,必须要完全正确
     */
    private String hospitalName;
    /**
     * 作为普通号使用的医生id
     */
    private String normalDoctorId;

    private String hisApiBaseUrl;

    /**
     * quartz持久化文件存储路径，默认为项目根目录，jar运行时和jar在同一路径
     */
    private String quartzSchedulerFilePath;

    private Boolean freeReg;
    private int freeRegAge;
    private int opCode;
    /**
     * 操作员名字
     */
    private String opName;
    /**
     * 交通费项目代码
     */
    private String trafficItemCode;
    
    /**
     * 卫健委相关his请求url
     */
    private String wjwHisApiUrl;
    /**
     * 心电图系统 查询报告url
     */
    private String xdtReportUrl;

    /**
     * lis系统 查询报告url
     */
    private String lisReportUrl;

    /**
     * lis系统 查询报告详情url
     */
    private String lisReportResultUrl;
}
