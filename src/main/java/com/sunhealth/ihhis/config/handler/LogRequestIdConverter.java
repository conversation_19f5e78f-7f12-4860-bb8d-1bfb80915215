package com.sunhealth.ihhis.config.handler;

import ch.qos.logback.classic.pattern.MessageConverter;
import ch.qos.logback.classic.spi.ILoggingEvent;

public class LogRequestIdConverter extends MessageConverter {

    public final static String REQUEST_ID = "reqId";

    @Override
    public String convert(ILoggingEvent event) {
        // 线程中如果赋值，会导致不知道什么时候该清理，因此线程中执行时，不赋值
        String requestId = event.getMDCPropertyMap().get(LogRequestIdConverter.REQUEST_ID);
        if (requestId == null) {
            requestId = "";
//            requestId = UUID.randomUUID().toString().replaceAll("-", "");
//            MDC.put(LogRequestIdConverter.REQUEST_ID, requestId);
        }
        return REQUEST_ID + "=" + requestId;
    }

}
