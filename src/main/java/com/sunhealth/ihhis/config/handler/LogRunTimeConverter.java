package com.sunhealth.ihhis.config.handler;

import ch.qos.logback.classic.pattern.MessageConverter;
import ch.qos.logback.classic.spi.ILoggingEvent;

public class LogRunTimeConverter extends MessageConverter {

    public final static String RUN_TIME = "runTime";

    @Override
    public String convert(ILoggingEvent event) {
        // 线程中如果赋值，会导致不知道什么时候该清理，因此线程中执行时，不赋值
        try {
            String rs = event.getMDCPropertyMap().get(LogRunTimeConverter.RUN_TIME);
            if (rs == null) {
//                MDC.put(LogRunTimeConverter.RUN_TIME, System.currentTimeMillis() + "");
                return RUN_TIME + "=0";
            } else {
                return RUN_TIME + "=" + (System.currentTimeMillis() - Long.parseLong(rs));
            }
        } catch (Throwable e) {
//            MDC.put(LogRunTimeConverter.RUN_TIME, System.currentTimeMillis() + "");
            return RUN_TIME + "=0";
        }
    }

}
