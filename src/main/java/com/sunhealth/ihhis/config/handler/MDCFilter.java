package com.sunhealth.ihhis.config.handler;

import org.slf4j.MDC;
import org.springframework.stereotype.Component;

import javax.servlet.*;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.Random;

@Component
public class MDCFilter implements Filter {

    private final static Random rand = new Random();

    @Override
    public void init(FilterConfig filterConfig) throws ServletException {
    }

    @Override
    public void doFilter(ServletRequest request, ServletResponse response, FilterChain chain) throws IOException, ServletException {
        HttpServletRequest httpRequest = (HttpServletRequest) request;
        HttpServletResponse httpResponse = (HttpServletResponse) response;
        String requestId = httpRequest.getHeader(LogRequestIdConverter.REQUEST_ID);
        if (requestId == null) {
            requestId = String.valueOf(((System.currentTimeMillis() - 1489111610226L) << 15) + rand.nextInt(1024));
        }
        MDC.put(LogRequestIdConverter.REQUEST_ID, requestId);
        MDC.put(LogRunTimeConverter.RUN_TIME, System.currentTimeMillis() + "");
        httpResponse.setHeader(LogRequestIdConverter.REQUEST_ID, requestId);

        try {
            chain.doFilter(request, response);
        } finally {
            MDC.clear();
        }
    }

    @Override
    public void destroy() {
    }

}


