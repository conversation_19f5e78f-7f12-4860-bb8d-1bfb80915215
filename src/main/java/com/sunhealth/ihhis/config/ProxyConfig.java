package com.sunhealth.ihhis.config;

import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Configuration;
import org.springframework.util.AntPathMatcher;
import org.springframework.util.CollectionUtils;

import javax.annotation.PostConstruct;
import java.io.IOException;
import java.net.*;
import java.util.Collections;
import java.util.List;

@Configuration
@Slf4j
public class ProxyConfig {

    @Autowired
    private ApplicationProperties applicationProperties;

    @PostConstruct
    public void init() {
        AntPathMatcher antPathMatcher = new AntPathMatcher();
        ProxySelector defaultProxySelector = ProxySelector.getDefault();

        ProxySelector.setDefault(new ProxySelector() {

            @Override
            public List<Proxy> select(URI uri) {
                List<ApplicationProperties.IhProxy> proxies = applicationProperties.getProxy();
                String url = uri.getScheme() + "://" + uri.getHost();
                if (uri.getPort() > 0) {
                    url += (":" + uri.getPort());
                }
                url += "/";
                if (!proxies.isEmpty()) {
                    for (ApplicationProperties.IhProxy p : proxies) {
                        for (String pp : p.getProxyPath()) {
                            if (antPathMatcher.match(pp, url)) {
                                log.info("使用代理转发: " + p.getProtocol() + " - " + p.getHost() + " - " + p.getPort() + " - " + url);
                                Proxy proxy = new Proxy(Proxy.Type.HTTP, new InetSocketAddress(p.getHost(), p.getPort()));
                                return Collections.singletonList(proxy);
                            }
                        }
                    }
                }

                List<ApplicationProperties.IhProxy> defaultProxies = applicationProperties.getDefaultProxy();
                if (!defaultProxies.isEmpty()) {
                    for (ApplicationProperties.IhProxy p : defaultProxies) {
                        for (String pp : p.getProxyPath()) {
                            if (antPathMatcher.match(pp, url)) {
                                log.info("使用代理转发: " + p.getProtocol() + " - " + p.getHost() + " - " + p.getPort() + " - " + url);
                                Proxy proxy = new Proxy(Proxy.Type.HTTP, new InetSocketAddress(p.getHost(), p.getPort()));
                                return Collections.singletonList(proxy);
                            }
                        }
                    }
                }

                // 不使用代理，使用系统默认的代理
                return defaultProxySelector.select(uri);
            }

            @Override
            public void connectFailed(URI uri, SocketAddress sa, IOException ioe) {
                defaultProxySelector.connectFailed(uri, sa, ioe);
            }
        });

    }

    /**
     * 根据url获取是否需要使用代理
     * @param url
     * @return
     */
    public ApplicationProperties.IhProxy findProxyByUrl(String url) {
        URL uri;
        try {
            uri = new URL(url);
        } catch (MalformedURLException e) {
            log.error("url不合法: " + url);
            return null;
        }
        String protocolUrl = uri.getProtocol() + "://" + uri.getHost();
        if (uri.getPort() > 0) {
            protocolUrl += (":" + uri.getPort());
        }
        protocolUrl += "/";


        // getList
        AntPathMatcher antPathMatcher = new AntPathMatcher();
        List<ApplicationProperties.IhProxy> proxy = applicationProperties.getProxy();
        List<ApplicationProperties.IhProxy> defaultProxy = applicationProperties.getDefaultProxy();
        List<ApplicationProperties.IhProxy> allProxy = Lists.newArrayList();
        allProxy.addAll(proxy);
        allProxy.addAll(defaultProxy);

        // 先在proxy里面匹配, proxy里面匹配不到再到defaultProxy里面去匹配
        if (!CollectionUtils.isEmpty(allProxy)) {
            for (ApplicationProperties.IhProxy ihProxy : allProxy) {
                for (String pp : ihProxy.getProxyPath()) {
                    if (antPathMatcher.match(pp, protocolUrl)) {
                        return ihProxy;
                    }
                }
            }
        }
        log.error("根据url: {}没有找到对应的代理", url);
        return null;
    }
}
