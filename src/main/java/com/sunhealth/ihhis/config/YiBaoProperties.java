package com.sunhealth.ihhis.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.util.List;

@Component
@ConfigurationProperties(prefix = "yibao")
@Data
public class YiBaoProperties {
    private String baseUrl1;
    private String baseUrl2;
    /**
     * 国家医保医疗机构代码
     */
    private String orgCode;
    /**
     * 医保移动支付第三方渠道接入反馈单
     */
    private List<ChannelData> channelData;

    /**
     * 5期医保科室编号,解码时需要,那时候还没有挂号科室
     */
    private String termId;
    private String ip;
    /**
     * 地区医保医疗机构代码
     */
    private String dqOrgCode;

    /**
     * 发卡地行政区划代码
     * {@link com.sunhealth.ihhis.model.dq.sh.insurance.request.SHYBBasicRequest#getXzqhdm()}
     */
    private String xzqhdm;

    /**
     * 就医地医保区划
     * {@link com.sunhealth.ihhis.model.insurance.request.GJYBBasicRequest#getMdtrtareaAdmvs()}
     */
    private String mdtrtareaAdmvs;

    // 伤残病人普通挂号费全部可报,这个是单只挂号费金额
    private BigDecimal disabilityRegisterFee = new BigDecimal(6);

    /**
     * 地区,医保接口业务根据医院不同,调用不用方法
     * {@link com.sunhealth.ihhis.service.dqyb.DQYiBaoClient)
     * {@link com.sunhealth.ihhis.service.gjyb.GJYiBaoClient)
     */
    private String area = "huBei";

    /**
     * 上海调用国家医保需要使用apiKey
     */
    private String apiKey;

    /**
     * 国家医保普通号挂号医师编码
     */
    private String drNo;
    /**
     * 国家医保普通号挂号医师姓名
     */
    private String drName;
    /**
     * 国家医保普通号挂号诊断代码
     */
    private String diagCode = "Z71.900";
    /**
     * 国家医保普通号挂号诊断名称
     */
    private String diagName = "咨询";
    /**
     * 国家医保缴费医师编码
     */
    private String chargeDrNo;
    /**
     * 国家医保缴费医师姓名
     */
    private String chargeDrName;
    /**
     * 测试环境上海医师编码(生产环境不要配置)
     */
    private String cfysh;
    /**
     * 测试环境上海医师名称(生产环境不要配置)
     */
    private String cfysxm;

    // Getters and Setters
    @Data
    public static class ChannelData {
        private String channelCode;
        /**
         * 定点医药机构小程序/H5应用ID
         */
        private String appId;
        /**
         * 数字密钥
         */
        private String sm4Key;
        /**
         * 平台公钥
         */
        private String sm2Key;
        private String apiVersion;
        /**
         * 渠道私钥
         */
        private String appKey;
        /**
         * 渠道公钥
         */
        private String pubKey;
    }


}
