package com.sunhealth.ihhis.config;

import com.sunhealth.ihhis.service.CurrentHospital;
import javax.servlet.http.HttpServletRequest;

import com.sunhealth.ihhis.service.CurrentRequestId;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 */
@Configuration
@Slf4j
public class CurrentConfiguration {

    @Autowired
    private HttpServletRequest request;
//    private final UserCacheFindService userCacheFindService;
//
//    public CurrentConfiguration(UserCacheFindService userCacheFindService) {
//        this.userCacheFindService = userCacheFindService;
//    }
//
//    @Bean
//    public CurrentUser currentUser() {
//        return new CurrentUser() {
//            @Override
//            public Optional<User> get() {
//                return userCacheFindService.findOneByUsername(SecurityUtils.getCurrentUserLogin());
//            }
//        };
//    }

    @Bean
    public CurrentHospital currentHospital() {
        return new CurrentHospital() {
//            @Override
//            public Optional<Hospital> get() {
//                String hospitalCode = getHospitalCode();
//                if (StringUtils.isBlank(hospitalCode)) {
//                    return Optional.empty();
//                }
//                HospitalCache hospitalCache = AppContext.getInstance(HospitalCache.class);
//                return hospitalCache.getHospital(hospitalCode);
//            }

            @Override
            public String getHospitalCode() {
                if (request == null) {
                    return null;
                }
                String hospitalCode = request.getHeader("Region-Id");
                if (StringUtils.isBlank(hospitalCode)) {
                    log.info("请求{}，header没有携带医院Region-Id医院编码", request.getRequestURL());
                }
                return hospitalCode;
            }
//
//            @Override
//            public String getRequestItem() {
//                if (request == null) {
//                    return null;
//                }
//                return request.getHeader("ITEM");
//            }

        };
    }

    @Bean
    public CurrentRequestId currentRequestId() {
        return new CurrentRequestId() {
            @Override
            public String getRequestId() {
                if (request == null) {
                    return null;
                }
                String requestId = request.getHeader("Request-Id");
                if (StringUtils.isBlank(requestId)) {
                    log.info("请求{}，header没有携带Request-Id", request.getRequestURL());
                }
                return requestId;
            }

        };
    }
}
