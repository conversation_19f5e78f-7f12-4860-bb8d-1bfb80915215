package com.sunhealth.ihhis.config;

import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.module.SimpleModule;
import com.sunhealth.ihhis.common.DateStdDeserializer;
import com.sunhealth.ihhis.common.LongArrayToStringArraySerializer;
import com.sunhealth.ihhis.common.LongToStringSerializer;
import org.springframework.boot.autoconfigure.jackson.Jackson2ObjectMapperBuilderCustomizer;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import java.util.Date;

@Configuration
public class JacksonConfiguration {

    @Bean

    public Jackson2ObjectMapperBuilderCustomizer jackson2ObjectMapperBuilderCustomizer() {

        ObjectMapper objectMapper = new ObjectMapper();
        SimpleModule simpleModule = new SimpleModule();
        simpleModule.addSerializer(Long.class, LongToStringSerializer.instance);
        simpleModule.addSerializer(Long.TYPE, LongToStringSerializer.instance);
        simpleModule.addSerializer(long[].class, new LongArrayToStringArraySerializer());
        objectMapper.registerModule(simpleModule);

        return builder -> {
            builder.serializerByType(Long.TYPE, LongToStringSerializer.instance);
            builder.serializerByType(Long.class, LongToStringSerializer.instance);
            builder.serializerByType(long[].class, new LongArrayToStringArraySerializer());

            builder.deserializerByType(Date.class, new DateStdDeserializer());
            builder.featuresToEnable(DeserializationFeature.READ_UNKNOWN_ENUM_VALUES_AS_NULL);
            builder.featuresToEnable(DeserializationFeature.ACCEPT_EMPTY_STRING_AS_NULL_OBJECT);
        };

    }

}
