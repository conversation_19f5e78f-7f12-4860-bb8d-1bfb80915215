package com.sunhealth.ihhis.config;

import com.sunhealth.ihhis.utils.ProxyUtils;
import lombok.Getter;
import lombok.Setter;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

import java.util.ArrayList;
import java.util.List;

@Configuration
@ConfigurationProperties(prefix = "app")
@Getter
@Setter
public class ApplicationProperties {

    private final List<IhProxy> defaultProxy = new ArrayList<>();
    private final List<IhProxy> proxy = new ArrayList<>();

    public List<IhProxy> getDefaultProxy() {
        ProxyUtils.formatProxy("defaultProxy", defaultProxy);
        return defaultProxy;
    }

    public List<IhProxy> getProxy() {
        ProxyUtils.formatProxy("proxy", proxy);
        return proxy;
    }

    @Getter
    @Setter
    public static class IhProxy {
        private String protocol;
        private String host;
        private int port;
        private List<String> proxyPath = new ArrayList<>();
    }

}
