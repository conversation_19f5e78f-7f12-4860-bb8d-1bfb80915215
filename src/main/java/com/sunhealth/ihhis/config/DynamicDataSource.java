package com.sunhealth.ihhis.config;

import com.sunhealth.ihhis.config.holder.DataSourceContextHolder;
import lombok.extern.slf4j.Slf4j;
import org.springframework.jdbc.datasource.lookup.AbstractRoutingDataSource;

@Slf4j
public class DynamicDataSource extends AbstractRoutingDataSource {

    @Override
    protected Object determineCurrentLookupKey() {
        String dbType = DataSourceContextHolder.getDbType();
//        log.info("数据源为{}", dbType);
        return dbType;
    }
}
