package com.sunhealth.ihhis.error;

import com.google.common.base.Strings;
import com.sunhealth.ihhis.utils.UrlUtils;
import org.zalando.problem.Problem;
import org.zalando.problem.Status;
import org.zalando.problem.ThrowableProblem;

import java.net.URI;

/**
 */
public enum ErrorType {
    INTERNAL_SERVER_ERROR(Status.INTERNAL_SERVER_ERROR, "程序内部错误");

    private final Status status;
    private final String title;
    private final String detail;

    ErrorType(String title) {
        this(Status.BAD_REQUEST, title, title);
    }

    ErrorType(Status status, String title) {
        this(status, title, title);
    }

    ErrorType(Status status, String title, String detail) {
        this.status = status;
        this.title = title;
        this.detail = detail;
    }

    public URI getUri() {
        return uri(name().toLowerCase());
    }

    public String getCode() {
        return "error.drugstore." + name().toLowerCase();
    }

    public String getTitle() {
        return title;
    }

    public String getDetail() {
        return detail;
    }

    public Status getStatus() {
        return status;
    }

    public ThrowableProblem toProblem(String abbr, String message) {
        return toProblem(abbr, message, null);
    }

    public ThrowableProblem toProblem(String abbr, String message, String path) {
        return Problem.builder()
                .withType(getUri())
                .withStatus(status)
                .withTitle(Strings.isNullOrEmpty(abbr) ? title : abbr)
                .withDetail(Strings.isNullOrEmpty(message) ? detail : message)
                .with("path", path)
                .with("code", getCode())
                .with("errorCode", this.name())
                .build();
    }

    public ThrowableProblem toProblem(String title) {
        return toProblem(title, title);
    }

    public ThrowableProblem toProblem() {
        return toProblem(null, null);
    }

    public ThrowableProblem toProblemWithPath(String path) {
        return toProblem(null, null, path);
    }

    private static URI uri(String part) {
        return URI.create(UrlUtils.concatSegments(ErrorConstants.PROBLEM_BASE_URL, part));
    }

}
