package com.sunhealth.ihhis.nodered;

import com.fasterxml.jackson.core.type.TypeReference;
import com.google.common.collect.Maps;
import com.sunhealth.ihhis.config.NoderedProperties;
import com.sunhealth.ihhis.error.ErrorType;
import com.sunhealth.ihhis.model.dto.outpatientcharge.OrderRefundedReq;
import com.sunhealth.ihhis.model.dto.outpatientcharge.OrderRefundedRes;
import com.sunhealth.ihhis.model.dto.push.*;
import com.sunhealth.ihhis.nodered.data.NodeRedResponse;
import com.sunhealth.ihhis.utils.OkHttpUtils;
import com.sunhealth.ihhis.utils.StandardObjectMapper;
import com.sunhealth.ihhis.utils.TimeUtils;
import com.sunhealth.ihhis.utils.UrlUtils;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import okhttp3.Headers;
import okhttp3.Response;
import org.apache.commons.lang3.StringUtils;
import org.springframework.core.env.Environment;
import org.springframework.core.env.Profiles;
import org.springframework.stereotype.Component;
import org.zalando.problem.DefaultProblem;

import java.io.IOException;
import java.util.Date;
import java.util.Map;
import java.util.Objects;
import java.util.UUID;

@Slf4j
@Component
@AllArgsConstructor
public class NodeRedClient {

    private final NoderedProperties noderedProperties;
    private final Environment env;

    /**
     * 生成患者服务平台二维码
     * @param hospitalCode
     * @param qrCodeRequest
     * @return
     */
    public HisQRCodeResponse getQRCode(String hospitalCode, IHQRCodeRequest qrCodeRequest) {
        String path = "/B_QRCode";

        Map<String, String> headers = getHeaders(hospitalCode);
        return postForNodeRed(path, headers, qrCodeRequest, new TypeReference<HisQRCodeResponse>() {});
    }

    /**
     * 推送医疗信息消息
     */
    public MsgPushRes pushPatientMsg(Msg pushReq) {
        String path = "/B_PushPatientMsg";

        Map<String, String> headers = getHeaders(pushReq.getHospitalCode());

        try{
            postForNodeRed(path, headers, pushReq, null);
            return MsgPushRes.success();
        } catch (DefaultProblem problem) {
            return MsgPushRes.fail(problem.getDetail());
        }
    }

    public MsgPushRes pushPatientMsg(Msg pushReq, String url) {
        String path = "/B_PushPatientMsg";

        Map<String, String> headers = getHeaders(pushReq.getHospitalCode());

        try{
            postForNodeRed(UrlUtils.concatSegments(url, path), headers, pushReq, null);
            return MsgPushRes.success();
        } catch (DefaultProblem problem) {
            return MsgPushRes.fail(problem.getDetail());
        }
    }

    /**
     * 结算取消结果查询
     */
    public OrderRefundedRes orderRefunded(OrderRefundedReq req) {
        String path = "B_OrderRefunded";

        Map<String, String> headers = getHeaders(req.getHospitalCode());
        Map<String, String> body = Maps.newHashMap();
        body.put("settle_id", req.getSettle_id());

        OrderRefundedRes orderRefundedRes = postForNodeRed(path, headers, body, new TypeReference<OrderRefundedRes>() {
        });
        return orderRefundedRes;

    }

    private <T> T postForNodeRed(String path, Map<String, String> headers,
                                 Object body, TypeReference<T> returnTypeReference){
        String url;
        if (!StringUtils.startsWith(path, "http")) {
            url = UrlUtils.concatSegments(noderedProperties.getBaseUrl(), path);
        } else {
            url = path;
        }
        if (headers == null) {
            headers = Maps.newHashMap();
        }
        String bodyStr = null;
        if (body != null) {
            bodyStr = (body instanceof String) ? (String) body : StandardObjectMapper.stringify(body);
        }
        log.info("调用node-red 接口:url:{},headers:{},params:{},body:{}", url, headers, null, body);
        try (Response response = OkHttpUtils.post(url, Headers.of(headers), null, bodyStr)) {
            if (response.isSuccessful()) {
                if (response.body() == null) {
                    return null;
                }
                if (returnTypeReference == null) {
                    return null;
                }
                String responseString = OkHttpUtils.getResponseBody(response).orElse(null);
                log.info("调用node-red 接口 返回：{}",responseString);
                NodeRedResponse nodeRedResponse = StandardObjectMapper.readValue(responseString, new TypeReference<NodeRedResponse>() {});
                if (nodeRedResponse.isSuccess()) {
                    Object data = nodeRedResponse.getData();
                    if (Objects.isNull(data)) {
                        return null;
                    }
                    return StandardObjectMapper.getInstance().convertValue(data, returnTypeReference);
                } else {
                    log.info("NodeRed API调用成功-但返回了业务错误：[code]: " + nodeRedResponse.getCode() + " [msg]:" + nodeRedResponse.getMessage());
                    throw new IOException(nodeRedResponse.getMessage());
                }
            } else {
                log.info("NodeRed API调用失败：" + response.body().string());
                throw new Exception(response.body().string());
            }
        } catch (Exception e) {
            log.error("NodeRed API调用失败", e);
            throw ErrorType.INTERNAL_SERVER_ERROR.toProblem(e.getMessage());
        }
    }

    private Map<String, String> getHeaders(String hospcode) {
        Map<String, String> headers = Maps.newHashMap();
        // 使用uuid作为请求id
        headers.put("Request-Id", UUID.randomUUID().toString());
        // 医院编码
        headers.put("Region-Id", hospcode);
        // 请求时间戳 日期格式为北京时间：yyyyMMddHHmmss
        headers.put("Timestamp", TimeUtils.dateStringFormat(new Date(), "yyyyMMddHHmmss"));
        headers.put("apienv", getEnv());
        return headers;
    }

    private String getEnv() {
        if (env.acceptsProfiles(Profiles.of("prod"))) {
            return "prod";
        }
        if (env.acceptsProfiles(Profiles.of("uat"))) {
            return "uat";
        }
        return "dev";
    }
}
