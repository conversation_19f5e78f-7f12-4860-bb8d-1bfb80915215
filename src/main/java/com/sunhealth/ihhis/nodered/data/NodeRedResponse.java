package com.sunhealth.ihhis.nodered.data;

import lombok.Data;

@Data
public class NodeRedResponse implements java.io.Serializable {

    // 0	调用成功
    //4xx	客户端错误
    //5xx	服务器错误
    //100x	业务逻辑错误
    private String code;
    private String message;
    private Object data;

    public boolean isSuccess() {
        return "0".equals(code);
    }

    public NodeRedResponse() {
    }


    public NodeRedResponse(String code, String message) {
        this.code = code;
        this.message = message;
    }

    public NodeRedResponse(String code, String message, Object data) {
        this.code = code;
        this.message = message;
        this.data = data;
    }

}
