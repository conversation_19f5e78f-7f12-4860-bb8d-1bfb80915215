package com.sunhealth.ihhis.utils;

import com.sunhealth.ihhis.model.dq.sh.insurance.request.SH01Request;
import com.sunhealth.ihhis.model.dq.sh.insurance.request.SI11Request;
import com.sunhealth.ihhis.model.dq.sh.insurance.response.SH01Response;
import com.sunhealth.ihhis.model.dq.sh.insurance.response.SI11Response;
import com.sunhealth.ihhis.model.dq.sh.insurance.response.SJ11Response;
import com.sunhealth.ihhis.model.dq.sh.insurance.response.SN01Response;
import com.sunhealth.ihhis.model.entity.Dept;
import com.sunhealth.ihhis.model.entity.Diagnose;
import com.sunhealth.ihhis.model.entity.MedicalWorker;
import com.sunhealth.ihhis.model.entity.charge.PreChargeDetail;
import com.sunhealth.ihhis.model.entity.patient.RTPatientList;
import com.sunhealth.ihhis.model.entity.register.PreRegisterDetail;
import com.sunhealth.ihhis.model.entity.register.PreRegisterList;
import com.sunhealth.ihhis.model.insurance.response.Output1101;
import com.sunhealth.ihhis.model.insurance.response.Output2201;
import com.sunhealth.ihhis.model.insurance.response.Output6201;

import java.util.Date;
import java.util.List;

public class ThreadLocalUtils {

    /**
     * 预挂号数据
     */
    private static final ThreadLocal<PreRegisterList> PRE_REGISTER_LIST = new ThreadLocal<>();
    /**
     * 预挂号项目明细
     */
    private static final ThreadLocal<List<PreRegisterDetail>> PRE_REGISTER_DETAILS = new ThreadLocal<>();
    /**
     * 就诊人数据
     */
    private static final ThreadLocal<RTPatientList> PATIENT = new ThreadLocal<>();
    /**
     * 科室
     */
    private static final ThreadLocal<Dept> DEPT = new ThreadLocal<>();
    /**
     * 诊断明细
     */
    private static final ThreadLocal<List<Diagnose>> DIAGNOSES = new ThreadLocal<>();
    /**
     * 门诊预缴费项目明细
     */
    private static final ThreadLocal<List<PreChargeDetail>> PRE_CHARGE_DETAILS = new ThreadLocal<>();
    /**
     * 门诊单元号
     */
    private static final ThreadLocal<String> JZDYH = new ThreadLocal<>();
    /**
     * 险种Output1101.InsuInfo
     */
    private static final ThreadLocal<Output1101.InsuInfo> INSU_INFO = new ThreadLocal<>();

    private static final ThreadLocal<MedicalWorker> MEDICAL_WORKER = new ThreadLocal<>();
    private static final ThreadLocal<Date> NOW = new ThreadLocal<>();

    private static final ThreadLocal<SH01Request> SH01_REQUEST = new ThreadLocal<>();
    private static final ThreadLocal<SH01Response> SH01_RESPONSE = new ThreadLocal<>();
    private static final ThreadLocal<SJ11Response> SJ11_RESPONSE = new ThreadLocal<>();
    private static final ThreadLocal<SN01Response> SN01_RESPONSE = new ThreadLocal<>();
    private static final ThreadLocal<SI11Request> SI11_REQUEST = new ThreadLocal<>();
    private static final ThreadLocal<SI11Response> SI11_RESPONSE = new ThreadLocal<>();
    private static final ThreadLocal<Output1101> OUTPUT_1101 = new ThreadLocal<>();
    private static final ThreadLocal<Output2201.Data2201> OUTPUT_2201 = new ThreadLocal<>();
    private static final ThreadLocal<Output6201> OUTPUT_6201 = new ThreadLocal<>();

    public static void setPreRegisterList(PreRegisterList preRegisterList) {
        PRE_REGISTER_LIST.set(preRegisterList);
    }

    public static PreRegisterList getPreRegisterList() {
        return PRE_REGISTER_LIST.get();
    }

    public static void clearPreRegisterList() {
        PRE_REGISTER_LIST.remove();
    }

    public static void setPreRegisterDetail(List<PreRegisterDetail> PreRegisterDetail) {
        PRE_REGISTER_DETAILS.set(PreRegisterDetail);
    }

    public static List<PreRegisterDetail> getPreRegisterDetail() {
        return PRE_REGISTER_DETAILS.get();
    }

    public static void clearPreRegisterDetail() {
        PRE_REGISTER_DETAILS.remove();
    }

    public static void setPatient(RTPatientList patient) {
        PATIENT.set(patient);
    }

    public static RTPatientList getPatient() {
        return PATIENT.get();
    }

    public static void clearPatient() {
        PATIENT.remove();
    }

    public static void setDept(Dept dept) {
        DEPT.set(dept);
    }

    public static Dept getDept() {
        return DEPT.get();
    }

    public static void clearDept() {
        DEPT.remove();
    }

    public static void setSH01Request(SH01Request sh01Request) {
        SH01_REQUEST.set(sh01Request);
    }

    public static SH01Request getSH01Request() {
        return SH01_REQUEST.get();
    }

    public static void clearSH01Request() {
        SH01_REQUEST.remove();
    }

    public static void setSH01Response(SH01Response sh01Response) {
        SH01_RESPONSE.set(sh01Response);
    }

    public static SH01Response getSH01Response() {
        return SH01_RESPONSE.get();
    }

    public static void clearSH01Response() {
        SH01_RESPONSE.remove();
    }

    public static void setDiagnoses(List<Diagnose> diagnoses) {
        DIAGNOSES.set(diagnoses);
    }

    public static List<Diagnose> getDiagnoses() {
        return DIAGNOSES.get();
    }

    public static void clearDiagnoses() {
        DIAGNOSES.remove();
    }

    public static void setPreChargeDetails(List<PreChargeDetail> preChargeDetails) {
        PRE_CHARGE_DETAILS.set(preChargeDetails);
    }

    public static List<PreChargeDetail> getPreChargeDetails() {
        return PRE_CHARGE_DETAILS.get();
    }

    public static void clearPreChargeDetails() {
        PRE_CHARGE_DETAILS.remove();
    }

    public static void setSJ11Response(SJ11Response sj11Response) {
        SJ11_RESPONSE.set(sj11Response);
    }

    public static SJ11Response getSJ11Response() {
        return SJ11_RESPONSE.get();
    }

    public static void clearSJ11Response() {
        SJ11_RESPONSE.remove();
    }

    public static void setSN01Response(SN01Response sn01Response) {
        SN01_RESPONSE.set(sn01Response);
    }

    public static SN01Response getSN01Response() {
        return SN01_RESPONSE.get();
    }

    public static void clearSN01Response() {
        SN01_RESPONSE.remove();
    }

    public static void setSI11Request(SI11Request si11Request) {
        SI11_REQUEST.set(si11Request);
    }

    public static SI11Request getSI11Request() {
        return SI11_REQUEST.get();
    }

    public static void clearSI11Request() {
        SI11_REQUEST.remove();
    }

    public static void setSI11Response(SI11Response si11Response) {
        SI11_RESPONSE.set(si11Response);
    }

    public static SI11Response getSI11Response() {
        return SI11_RESPONSE.get();
    }

    public static void clearSI11Response() {
        SI11_RESPONSE.remove();
    }

    public static void setJZDYH(String jzdyh) {
        JZDYH.set(jzdyh);
    }

    public static String getJZDYH() {
        return JZDYH.get();
    }

    public static void clearJZDYH() {
        JZDYH.remove();
    }

    public static void setOutput1101(Output1101 output1101) {
        OUTPUT_1101.set(output1101);
    }

    public static Output1101 getOutput1101() {
        return OUTPUT_1101.get();
    }

    public static void clearOutput1101() {
        OUTPUT_1101.remove();
    }

    public static void setNow(Date date) {
        NOW.set(date);
    }

    public static Date getNow() {
        return NOW.get();
    }

    public static void clearNow() {
        NOW.remove();
    }

    public static void setOutput2201(Output2201.Data2201 output2201) {
        OUTPUT_2201.set(output2201);
    }

    public static Output2201.Data2201 getOutput2201() {
        return OUTPUT_2201.get();
    }

    public static void clearOutput2201() {
        OUTPUT_2201.remove();
    }

    public static void setMedicalWorker(MedicalWorker medicalWorker) {
        MEDICAL_WORKER.set(medicalWorker);
    }

    public static MedicalWorker getMedicalWorker() {
        return MEDICAL_WORKER.get();
    }

    public static void clearMedicalWorker() {
        MEDICAL_WORKER.remove();
    }

    public static void setOutput6201(Output6201 output6201) {
        OUTPUT_6201.set(output6201);
    }

    public static Output6201 getOutput6201() {
        return OUTPUT_6201.get();
    }

    public static void clearOutput6201() {
        OUTPUT_6201.remove();
    }

    public static void setInsuInfo(Output1101.InsuInfo insuInfo) {
        INSU_INFO.set(insuInfo);
    }

    public static Output1101.InsuInfo getInsuInfo() {
        return INSU_INFO.get();
    }

    public static void clearInsuInfo() {
        INSU_INFO.remove();
    }

}
