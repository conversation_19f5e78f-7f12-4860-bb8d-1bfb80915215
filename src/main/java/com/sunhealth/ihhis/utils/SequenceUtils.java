package com.sunhealth.ihhis.utils;

import java.util.Date;
import java.util.HashMap;
import java.util.Map;

/**
 * 只能在单实例中使用
 */
public class SequenceUtils {

    private static final Map<Long, Long> sequenceMap = new HashMap<>();
    private final static long DEFAULT_MAX_VALUE = 9999;

    /**
     * 获取序号，最大值 9999， 超过9999时回到1
     * @return
     */
    public static synchronized long getSequence() {
        long sequence = sequenceMap.containsKey(9999L) ? sequenceMap.get(9999L) : 0;
        if (sequence >= DEFAULT_MAX_VALUE) {
            sequence = 0;
        }
        sequence++;
        sequenceMap.put(9999L, sequence);
        return sequence;
    }

    /**
     * 获取序号, 超过最大值时回到1
     * @param maxValue 最大值
     * @return
     */
    public static synchronized long getSequence(long maxValue) {
        long sequence = sequenceMap.containsKey(maxValue) ? sequenceMap.get(maxValue) : 0;
        if (sequence >= maxValue) {
            sequence = 0;
        }
        sequence++;
        sequenceMap.put(maxValue, sequence);
        return sequence;
    }

}

