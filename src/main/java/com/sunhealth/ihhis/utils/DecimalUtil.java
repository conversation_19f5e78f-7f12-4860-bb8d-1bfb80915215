package com.sunhealth.ihhis.utils;

import java.math.BigDecimal;
import java.util.Objects;

public class DecimalUtil {
    /**
     * decimal 转 string  单位分
     *  默认 "0"
     *  his 单位元 需转成分
     * @param decimal
     * @return
     */
    public static String defaultString(BigDecimal decimal) {
        if (Objects.isNull(decimal)) {
            return "0";
        }
        return decimal.multiply(BigDecimal.valueOf(100))
                .setScale(0, BigDecimal.ROUND_HALF_UP)
                .toString();
    }

    /**
     * decimal 转 string  单位元
     * @param decimal
     * @param scale
     * @return
     */
    public static String defaultString(BigDecimal decimal, int scale) {
        if (Objects.isNull(decimal)) {
            return "0";
        }
        return decimal.setScale(scale, BigDecimal.ROUND_HALF_UP)
                .toString();
    }

    /**
     * decimal取负值
     */
    public static BigDecimal negate(BigDecimal decimal) {
        if (decimal == null) {
            return null;
        }
        return decimal.negate();
    }

    public static BigDecimal isNullAsZero(BigDecimal number) {
        if (number == null) {
            number = new BigDecimal("0");
        }
        return number;
    }

}
