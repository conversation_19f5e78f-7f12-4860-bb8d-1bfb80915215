package com.sunhealth.ihhis.utils;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.sunhealth.ihhis.common.PageBean;

import java.util.List;
import java.util.function.Function;
import java.util.function.Supplier;
import java.util.stream.Collectors;

public class PageUtils {

    /**
     * 将PageInfo对象泛型中的Po对象转化为Vo对象
     *
     * @param pageInfoPo PageInfo<Po>对象</>
     * @param <P>        Po类型
     * @param <V>        Vo类型
     * @return
     */
    public static <P, V> Page<V> pageInfo2PageInfoVo(Page<P> pageInfoPo, Supplier<List<V>> supplier) {
        Page<V> page = Page.of(pageInfoPo.getCurrent(), pageInfoPo.getSize(), pageInfoPo.getTotal());
        page.setRecords(supplier.get());
        return page;
    }

    public static <P, V> PageBean<V> PageInfo2PageInfoVo(Page<P> pageInfoPo, Function<? super P, ? extends V> fun) {
        // 创建Page对象，实际上是一个ArrayList类型的集合
        Page<V> page = Page.of(pageInfoPo.getCurrent(), pageInfoPo.getSize(), pageInfoPo.getTotal());
        page.setRecords(pageInfoPo.getRecords().stream().map(fun).collect(Collectors.toList()));
        return new PageBean<>(page);
    }

}
