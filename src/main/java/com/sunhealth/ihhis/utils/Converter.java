package com.sunhealth.ihhis.utils;


import org.springframework.beans.BeanUtils;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.UUID;

/**
 * 队形转换
 * <AUTHOR>
public class Converter {
    public Converter() {
    }

    public static <T> T toObject(Object source, Class<T> clazz) {
        try {
            T target = clazz.newInstance();
            BeanUtils.copyProperties(source, target);
            return target;
        } catch (Exception var3) {
            return null;
        }
    }

    public static String toString(Object obj) {
        return obj == null ? null : String.valueOf(obj);
    }

    public static Integer toInt32(Object obj) {
        return obj == null ? new Integer("0") : Integer.valueOf(String.valueOf(obj));
    }

    public static Long toInt64(Object obj) {
        return obj == null ? new Long("0") : Long.valueOf(String.valueOf(obj));
    }

    public static UUID toUUID(Object obj) {
        return obj == null ? null : UUID.fromString(String.valueOf(obj));
    }

    /**
     * 元单位转成分单位
     * @param yuan 元
     * @return 分
     */
    public static int yuanToFen(BigDecimal yuan) {
        if (yuan == null) {
            return 0;
        }
        return yuan.multiply(new BigDecimal(100))
                .setScale(0, RoundingMode.HALF_UP).intValue();
    }

    /**
     * 去掉字符串两边的空格，如果入参是null，返回null
     * @param str
     * @return
     */
    public static String trim(String str) {
        return str == null ? null : str.trim();
    }
}
