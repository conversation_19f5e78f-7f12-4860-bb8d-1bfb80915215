package com.sunhealth.ihhis.utils;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.fasterxml.jackson.core.type.TypeReference;
import com.sunhealth.ihhis.model.insurance.response.Output6202;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

import java.util.Iterator;
import java.util.Map;

@Slf4j
public class GJYBSm4Utils {

    /**
     * 对msg进行加密
     *
     * @param msg
     * @param appId
     * @param version
     * @param sm4
     * @param appKey
     * @param pubKey
     * @return
     * @throws Exception
     */
    public static String jm(String msg, String appId, String version, String sm4, String appKey, String pubKey)
        throws Exception {
        JSONObject req = JSONObject.parseObject(msg);
        JSONObject jsonObject = new JSONObject();
        jsonObject.put("appId", appId);
        jsonObject.put("encType", "SM4");
        jsonObject.put("signType", "SM2");
        jsonObject.put("timestamp", System.currentTimeMillis());
        jsonObject.put("version", version);
        jsonObject.put("data", req);
        removeEmpty(jsonObject);
        log.info("原始请求data：{}", jsonObject.toJSONString());
        jsonObject.remove("data");
        JSONObject dataObj = JsonSortUtil.startSort(req);
        removeEmpty(dataObj);
        jsonObject.put("data", dataObj);
        String encryptDataStr = HseEncAndDecUtil.sm4Encrypt(appId, sm4, dataObj.toJSONString());
        // 签名
        String signStr = HseEncAndDecUtil.signature(jsonObject.toJSONString(), sm4, appKey);
        boolean a = HseEncAndDecUtil.verify(dataObj.toJSONString(), jsonObject.toJSONString(), signStr, sm4, pubKey);
        jsonObject.put("signData", signStr);
        jsonObject.put("encData", encryptDataStr);
        jsonObject.remove("data");
        JSONObject jsonObjectSort = JsonSortUtil.startSort(jsonObject);
        return jsonObjectSort.toJSONString();
//        return encryptDataStr;
    }

    /**
     * 删除空值字段
     *
     * @param jsonObject
     */
    private static void removeEmpty(JSONObject jsonObject) {
        Iterator<Map.Entry<String, Object>> it = jsonObject.entrySet().iterator();
        while (it.hasNext()) {
            Map.Entry<String, Object> entry = it.next();
            Object value = entry.getValue();
            if (value instanceof JSONArray) {
                JSONArray jsonArray = (JSONArray) value;
                // 数组长度为0时将其处理,防止Gson转换异常
                if (jsonArray.isEmpty()) {
                    it.remove();
                } else {
                    for (Object o : jsonArray) {
                        JSONObject asJsonObject = (JSONObject) o;
                        removeEmpty(asJsonObject);
                    }
                }
            }
            if (value instanceof JSONObject) {
                JSONObject asJsonObject = (JSONObject) value;
                removeEmpty(asJsonObject);
            }
            if (value == null) {
                it.remove();
            }
            if (value instanceof String && StringUtils.isEmpty((String) value)) {
                it.remove();
            }
        }
    }

    public static void main(String[] args) throws Exception {
//        String datastr = "{\n"
//            + "  \"acctUsedFlag\":\"0\",\n"
//            + "  \"begntime\":\"2024-06-04 09:19:42\",\n"
//            + "  \"pubHospRfomFlag\":\"1\",\n"
//            + "  \"caty\":\"A02\",\n"
//            + "  \"atddrNo\":\"D420503002920\",\n"
//            + "  \"chrgBchno\":\"38709\",\n"
//            + "  \"deptCode\":\"1025\",\n"
//            + "  \"deptName\":\"普外科门诊\",\n"
//            + "  \"feedetailList\":[\n"
//            + "    {\n"
//            + "      \"feedetlSn\":\"23137\",\n"
//            + "      \"hospApprFlag\":\"0\",\n"
//            + "      \"detItemFeeSumamt\":1.500000,\n"
//            + "      \"chrgBchno\":\"38709\",\n"
//            + "      \"medListCodg\":\"001101000010000-110100001\",\n"
//            + "      \"cnt\":1,\n"
//            + "      \"pric\":1.500000,\n"
//            + "      \"bilgDrCodg\":\"D420503002920\",\n"
//            + "      \"bilgDrName\":\"陈点点\",\n"
//            + "      \"medinsListCodg\":\"1149698\"\n"
//            + "    },\n"
//            + "    {\n"
//            + "      \"feedetlSn\":\"23139\",\n"
//            + "      \"hospApprFlag\":\"0\",\n"
//            + "      \"detItemFeeSumamt\":4.000000,\n"
//            + "      \"chrgBchno\":\"38709\",\n"
//            + "      \"medListCodg\":\"001102000010000-110200001\",\n"
//            + "      \"cnt\":1,\n"
//            + "      \"pric\":4.000000,\n"
//            + "      \"bilgDrCodg\":\"D420503002920\",\n"
//            + "      \"bilgDrName\":\"陈点点\",\n"
//            + "      \"medinsListCodg\":\"1149699\"\n"
//            + "    }],\n"
//            + "  \"feeType\":\"01\",\n"
//            + "  \"idNo\":\"420322198512196617\",\n"
//            + "  \"idType\":\"01\",\n"
//            + "  \"iptOtpNo\":\"38709\",\n"
//            + "  \"mdtrtCertType\":\"02\",\n"
//            + "  \"medfeeSumamt\":5.5,\n"
//            + "  \"medOrgOrd\":\"38709\",\n"
//            + "  \"medType\":\"12\",\n"
//            + "  \"orgCodg\":\"H42050300004\",\n"
//            + "  \"payAuthNo\":\"AUTH420100202406040919360072858\",\n"
//            + "  \"uldLatlnt\":\"111.353350,30.643478\",\n"
//            + "  \"userName\":\"周学东\"\n"
//            + "}\n";
//        String jm = GJYBSm4Utils.jm(datastr, "1HTT1FU1G01E3F60C80A0000E8E03F2F", "2.0.1",
//                                    "1HTT1FU1L01F3F60C80A0000FC7E1C12",
//                                    "HjQ5aHOeFQrQg2wDM2bCmWD53Bs2ulsQLtpR0LInNBA=",
//                                    "BKuVqDnAE/dbffgC6ndGTuXlqhjf/Pw0r9/9813+uhmW+u00JZOb6aR7vv3xyO4r8rBHFNMdr1stfghQ6g8P0cQ=");

        String encData = "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";
        String dStr = HseEncAndDecUtil.sm4Decrypt("1HTT1FU1G01E3F60C80A0000E8E03F2F",
                                                  "1HTT1FU1L01F3F60C80A0000FC7E1C12",
                                                  encData);
        System.out.println("返回解析：" + dStr);
        Output6202 output6202 = StandardObjectMapper.getInstance().readValue(dStr, new TypeReference<Output6202>() {
        });
        log.info(StandardObjectMapper.stringify(output6202.getExtData()));
        System.out.println("返回解析：" + output6202);
//        Output6201 output6201 = StandardObjectMapper.getInstance().readValue(dStr, new TypeReference<Output6201>() {
//        });
//        Input6202 input6202 = new Input6202();
//        input6202.setPayAuthNo("AUTH420100202406040919360072858");
//        input6202.setChrgBchno("38709");
//        input6202.setPayOrdId(output6201.getPayOrdId());
//        input6202.setPayToken(output6201.getPayToken());
//        input6202.setOrgCodg("H42050300004");
//        input6202.setOrgBizSer(System.currentTimeMillis() + "");
//        String data6202 = StandardObjectMapper.stringify(input6202);
//        log.info("6202请求入参：{}" + data6202);
//        String jm1 = GJYBSm4Utils.jm(data6202, "1HTT1FU1G01E3F60C80A0000E8E03F2F", "2.0.1",
//                                    "1HTT1FU1L01F3F60C80A0000FC7E1C12",
//                                    "HjQ5aHOeFQrQg2wDM2bCmWD53Bs2ulsQLtpR0LInNBA=",
//                                    "BKuVqDnAE/dbffgC6ndGTuXlqhjf/Pw0r9/9813+uhmW"
//                                        + "+u00JZOb6aR7vv3xyO4r8rBHFNMdr1stfghQ6g8P0cQ=");
    }
}
