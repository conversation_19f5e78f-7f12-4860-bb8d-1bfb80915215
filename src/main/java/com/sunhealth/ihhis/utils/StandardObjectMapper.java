package com.sunhealth.ihhis.utils;

import com.fasterxml.jackson.core.JsonFactory;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.core.JsonParser.Feature;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.MapperFeature;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.SerializationFeature;
import com.fasterxml.jackson.datatype.guava.GuavaModule;
import io.dropwizard.jackson.Jackson;
import java.io.IOException;

public class StandardObjectMapper {
    public StandardObjectMapper() {
    }

    private static ObjectMapper newObjectMapper() {
        ObjectMapper mapper = Jackson.newObjectMapper();
        mapper.disable(SerializationFeature.FAIL_ON_EMPTY_BEANS);
        mapper.disable(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES);
        mapper.disable(new MapperFeature[]{MapperFeature.USE_GETTERS_AS_SETTERS});
        mapper.registerModule(new GuavaModule());
        JsonFactory jsonFactory = mapper.getFactory();
        jsonFactory.enable(Feature.ALLOW_COMMENTS);
        jsonFactory.enable(Feature.ALLOW_UNQUOTED_FIELD_NAMES);
        jsonFactory.enable(Feature.ALLOW_SINGLE_QUOTES);
        return mapper;
    }

    public static ObjectMapper getInstance() {
        return StandardObjectMapper.Holder.INSTANCE.objectMapper;
    }

    public static String stringify(Object data) {
        try {
            return getInstance().writeValueAsString(data);
        } catch (JsonProcessingException var2) {
            throw new RuntimeException(var2);
        }
    }

    public static <T> T readValue(String json, TypeReference<T> type) {
        try {
            return getInstance().readValue(json, type);
        } catch (IOException var3) {
            throw new RuntimeException(var3);
        }
    }

    private static enum Holder {
        INSTANCE;

        private final ObjectMapper objectMapper = StandardObjectMapper.newObjectMapper();

        private Holder() {
        }
    }
}
