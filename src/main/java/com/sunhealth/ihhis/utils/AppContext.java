package com.sunhealth.ihhis.utils;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeansException;
import org.springframework.context.ApplicationContext;
import org.springframework.context.ApplicationContextAware;
import org.springframework.core.env.Environment;
import org.springframework.core.env.Profiles;
import org.springframework.stereotype.Component;

import java.util.Objects;

@Component
public class AppContext implements ApplicationContextAware {
    private static final Logger logger = LoggerFactory.getLogger(AppContext.class);
    private static volatile ApplicationContext context;
    private static final Object $lock = new Object[0];

    public AppContext() {
    }

    public void setApplicationContext(ApplicationContext applicationContext) throws BeansException {
        logger.info("Setting up AppContext ...");
        if (context == null) {
            synchronized($lock) {
                if (context == null) {
                    context = applicationContext;
                }
            }
        }

    }

    public static ApplicationContext getContext() {
        return (ApplicationContext) Objects.requireNonNull(context, "ApplicationContext not initialized");
    }

    public static <T> T getInstance(Class<T> klass) {
        return context.getBean(klass);
    }

    public static <T> T getInstance(String name, Class<T> klass) {
        return context.getBean(name, klass);
    }

    public static boolean isDev() {
        return ((Environment)getInstance(Environment.class)).acceptsProfiles(Profiles.of(new String[]{"dev"}));
    }
}
