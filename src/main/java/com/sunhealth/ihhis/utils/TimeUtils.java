package com.sunhealth.ihhis.utils;

import com.google.common.collect.Lists;
import com.google.common.collect.Queues;
import lombok.Getter;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.time.DateUtils;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.time.*;
import java.time.format.DateTimeFormatter;
import java.time.temporal.ChronoUnit;
import java.time.temporal.TemporalAdjusters;
import java.util.*;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

public class TimeUtils {

    public enum Shift {
        UNKNOWN("未知", "-1"),
        MORNING("上午", "0"),
        AFTERNOON("下午", "1"),
        NIGHT("晚上", "2"),
        ALL_DAY("全天", "3"),
        DAYTIME("白天", "4"),
        LATE_NIGHT("后夜", "5"),
        MIDNIGHT("夜间", "6"),
        ;

        private final String name;

        private final String code;

        Shift(String name, String code) {
            this.name = name;
            this.code = code;
        }

        public String getName() {
            return name;
        }

        public String getCode() {
            return code;
        }

        public static Shift getByCode(String code) {
            for (Shift shift : Shift.values()) {
                if (shift.getCode().equals(code)) {
                    return shift;
                }
            }
            return UNKNOWN;
        }

    }

    /**
     * 连续数字的必须要放在最后，并且只能以多到少排序，不然转换会不正确
     */
    private static final String[] DATE_FORMATS = new String[] {
            "yyyy-MM-dd HH:mm:ss", "yyyy-MM-dd'T'HH:mm:ssX", "yyyy-MM-dd'T'HH:mm:ssXX", "yyyy-MM-dd'T'HH:mm:ssZZ",
            "yyyy-MM-dd HH:mm:ss.SSS", "yyyy-MM-dd'T'HH:mm:ss.SSSX", "yyyy-MM-dd'T'HH:mm:ss.SSSXX", "yyyy-MM-dd'T'HH:mm:ss.SSSZZ",
            "yyyy-MM-dd", "yyyy-MM-ddX", "yyyy-MM-ddXX", "yyyy-MM-ddZZ", "yyyyMMddX", "yyyyMMddXX", "yyyyMMddZZ",
            "yyyy/MM/dd HH:mm:ss", "yyyy/MM/dd'T'HH:mm:ssX", "yyyy/MM/dd'T'HH:mm:ssXX", "yyyy/MM/dd'T'HH:mm:ssZZ",
            "yyyy/MM/dd HH:mm:ss.SSS", "yyyy/MM/dd'T'HH:mm:ss.SSSX", "yyyy/MM/dd'T'HH:mm:ss.SSSXX", "yyyy/MM/dd'T'HH:mm:ss.SSSZZ",
            "yyyy/MM/dd", "yyyy/MM/ddX", "yyyy/MM/ddXX", "yyyy/MM/ddZZ", "yyyy-MM", "yyyy-MM-dd hh:mmaa",
            "yyyy-MM-dd hh:mm:ssaa", "MM dd yyyy hh:mmaa", "yyyyMMddHHmmss", "yyyyMMddHHmm", "yyyyMMdd", "yyyyMM", "yyyy"};

    private static final String TIME_STAMP_FORMAT = "^\\d+$";

    @Getter
    public enum TimeUnit {
        DAY(86400000),
        HOUR(3600000),
        MINUTE(60000),
        SECOND(1000);

        private final long millisecond;

        TimeUnit(long millisecond) {
            this.millisecond = millisecond;
        }

    }

    private TimeUtils() {
    }

    /**
     * 计算年龄 (年)
     * @param birthDay
     * @return
     */
    public static int age(Date birthDay) {
        LocalDateTime beforeLT = date2LocalDateTime(birthDay);
        LocalDateTime afterLT = getNowLocalDateTime();
        return age(beforeLT, afterLT);
    }

    /**
     * 时间戳转换成时间
     * @param time
     * @return
     */
    public static String timeToString(Long time) {
        DateTimeFormatter ftf = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
        return ftf
            .format(LocalDateTime.ofInstant(Instant.ofEpochMilli(time), ZoneId.systemDefault()));
    }

    /**
     * 获取当前字符串格式时间, yyyy-MM-dd HH:mm:ss
     * @return yyyy-MM-dd HH:mm:ss
     */
    public static String nowTimeLongString() {
        return TimeUtils.dateToString(new Date(), "yyyy-MM-dd HH:mm:ss");
    }

    /**
     * 日期转时间
     * @param date
     * @param pattern
     * @return
     */
    public static String dateToString(Date date, String pattern) {
        SimpleDateFormat format = new SimpleDateFormat(pattern);
        return format.format(date);
    }

    /**
     * 计算年龄 (年)
     * @param birthDay
     * @return
     */
    public static int age(LocalDateTime birthDay) {
        LocalDateTime afterLT = getNowLocalDateTime();
        return age(birthDay, afterLT);
    }

    /**
     * 计算年龄 (年)
     * @param beforeDate
     * @param afterDate
     * @return
     */
    public static int age(Date beforeDate, Date afterDate) {
        LocalDateTime beforeLT = date2LocalDateTime(beforeDate);
        LocalDateTime afterLT = date2LocalDateTime(afterDate);
        return age(beforeLT, afterLT);
    }

    /**
     * 计算年龄 (年)
     * @param beforeDate
     * @param afterDate
     * @return
     */
    public static int age(LocalDateTime beforeDate, LocalDateTime afterDate) {
        if (beforeDate.isAfter(afterDate)) {
            return age(afterDate, beforeDate);
        }

        int age = afterDate.getYear() - beforeDate.getYear() - 1;
        Queue<Integer> before = Queues.newLinkedBlockingQueue();
        before.add(beforeDate.getMonthValue());
        before.add(beforeDate.getDayOfMonth());
        before.add(beforeDate.getHour());
        before.add(beforeDate.getMinute());
        before.add(beforeDate.getSecond());

        Queue<Integer> after = Queues.newLinkedBlockingQueue();
        after.add(afterDate.getMonthValue());
        after.add(afterDate.getDayOfMonth());
        after.add(afterDate.getHour());
        after.add(afterDate.getMinute());
        after.add(afterDate.getSecond());

        age += one(before, after);
        return age;
    }

    /**
     * 计算时间差
     * @param beforeDate
     * @param afterDate
     * @param unit
     * @return
     */
    public static int intervalTime(Date beforeDate, Date afterDate, TimeUnit unit) {
        if (beforeDate.compareTo(afterDate) > 0) {
            return intervalTime(afterDate, beforeDate, unit);
        }

        return (int) ((afterDate.getTime() - beforeDate.getTime()) / unit.getMillisecond());
    }

    private static int one(Queue<Integer> before, Queue<Integer> after) {
        if (before.size() != after.size()) {
            return 0;
        }
        if (before.isEmpty()) {
            return 1;
        }
        int a = after.poll();
        int b = before.poll();
        if (a > b) {
            return 1;
        } else if (a == b) {
            return one(before, after);
        } else {
            return 0;
        }
    }

    public static LocalDateTime date2LocalDateTime(Date date) {
        return Instant.ofEpochMilli(date.getTime()).atZone(ZoneId.systemDefault())
            .toLocalDateTime();
    }

    public static LocalDate date2LocalDate(Date date) {
        return Instant.ofEpochMilli(date.getTime()).atZone(ZoneId.systemDefault())
            .toLocalDate();
    }

    public static Date localDateTime2Date(LocalDateTime localDateTime) {
        return Date.from(localDateTime.atZone(ZoneId.systemDefault()).toInstant());
    }

    public static LocalDateTime getNowLocalDateTime() {
        return LocalDateTime.now();
    }


    /**
     * 当前时间，增加几天
     * @return
     */
    public static LocalDateTime getAddDay(int day) {
        LocalDateTime startTime = LocalDateTime.now().plusDays(day);
        return startTime;

    }


    public static boolean timeIsIntersection(Date s1, Date e1, Date s2, Date e2) {
        return e1.getTime() > s2.getTime() && s1.getTime() < e2.getTime();
    }


    /**
     * 获取周几
     * @param date
     * @return
     */
    public static String getWeek(Date date) {
        String[] weekDays = {"周日", "周一", "周二", "周三", "周四", "周五", "周六"};
        Calendar cal = Calendar.getInstance();
        cal.setTime(date);
        int w = cal.get(Calendar.DAY_OF_WEEK) - 1;
        if (w < 0) {
            w = 0;
        }
        return weekDays[w];
    }


    // 获得某天最大时间 2020-02-19 xx to 2020-02-20 00:00:00
    public static Date getEndOfDay(Date date) {
        return DateUtils.addDays(getStartOfDay(date), 1);
    }

    // 获得某天最小时间 2020-02-17 00:00:00
    public static Date getStartOfDay(Date date) {
        return DateUtils.truncate(date, Calendar.DAY_OF_MONTH);
    }

    /**
     * 获取某天所在周的最大时间
     * @param date
     * @return
     */
    public static Date getEndOfWeek(Date date) {
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(date);
        int w = calendar.get(Calendar.DAY_OF_WEEK);
        if (w != 1) {
            calendar.setTime(new Date(date.getTime() + 604800000));
        }
        calendar.set(Calendar.DAY_OF_WEEK, 1);
        return getEndOfDay(calendar.getTime());
    }

    /**
     * 获取某天所在周的最小时间
     * @param date
     * @return
     */
    public static Date getStartOfWeek(Date date) {
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(date);
        int w = calendar.get(Calendar.DAY_OF_WEEK);
        if (w == 1) {
            calendar.setTime(new Date(date.getTime() - 604800000));
        }
        calendar.set(Calendar.DAY_OF_WEEK, 2);
        return getStartOfDay(calendar.getTime());
    }


    /**
     * 获取某天所在月的最大时间
     * @param date
     * @return
     */
    public static Date getEndOfMonth(Date date) {
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(date);
        calendar.set(Calendar.DAY_OF_MONTH, 1);
        calendar.set(Calendar.MONTH, calendar.get(Calendar.MONTH) + 1);
        return getStartOfDay(calendar.getTime());
    }

    /**
     * 获取某天所在月的最小时间
     * @param date
     * @return
     */
    public static Date getStartOfMonth(Date date) {
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(date);
        calendar.set(Calendar.DAY_OF_MONTH, 1);
        return getStartOfDay(calendar.getTime());
    }


    /**
     * 获取某天所在年的最大时间
     * @param date
     * @return
     */
    public static Date getEndOfYear(Date date) {
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(date);
        calendar.set(Calendar.DAY_OF_YEAR, 1);
        calendar.set(Calendar.YEAR, calendar.get(Calendar.YEAR) + 1);
        return getStartOfDay(calendar.getTime());
    }

    /**
     * 获取某天所在年的最小时间
     * @param date
     * @return
     */
    public static Date getStartOfYear(Date date) {
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(date);
        calendar.set(Calendar.DAY_OF_YEAR, 1);
        return getStartOfDay(calendar.getTime());
    }

    public static int dateDiff(LocalDateTime dt1, LocalDateTime dt2) {
        long t1 = dt1.toEpochSecond(ZoneOffset.ofHours(0));
        long day1 = t1 / (60 * 60 * 24);
        long t2 = dt2.toEpochSecond(ZoneOffset.ofHours(0));
        long day2 = t2 / (60 * 60 * 24);
        return (int) (day2 - day1);
    }

    /**
     * 获取当前时间所在一周/月/年/过去一年(截止到今天23：59：59)的时间范围内所有时间点
     * @param dateUnit week/month/year/oldYear
     * @return dateList
     */
    public static List<Date> getDateListByDateUnit(String dateUnit) {
        List<Date> param = Lists.newArrayList();
        LocalDateTime endOfDay = LocalDateTime.now().with(LocalTime.MAX);
        Date endTime = Date.from(endOfDay.atZone(ZoneId.systemDefault()).toInstant());
        Date startTime = getStartDateByDateUnit(dateUnit);
        switch (dateUnit) {
            case "week":
            case "month":
                while (startTime.getTime() < endTime.getTime()) {
                    param.add(startTime);
                    startTime = DateUtils.addDays(startTime, 1);
                }
                break;
            case "year":
            case "oldYear":
                while (startTime.getTime() < endTime.getTime()) {
                    param.add(startTime);
                    startTime = DateUtils.addMonths(startTime, 1);
                }
                break;
            default:
                throw new IllegalArgumentException("Unknown date");
        }
        return param;
    }

    /**
     * 获取当前时间所在一周/月/年/过去一年(截止到今天23：59：59)的时间范围内所有时间点
     * @param dateUnit week/month/year/oldYear
     * @return dateList
     */
    public static Date getStartDateByDateUnit(String dateUnit) {
        LocalDate date = LocalDate.now();

        Date startTime;
        switch (dateUnit) {
            case "week":
                LocalDateTime monday = LocalDateTime.of(date, LocalTime.MIN).with(DayOfWeek.MONDAY);
                startTime = Date.from(monday.atZone(ZoneId.systemDefault()).toInstant());
                break;
            case "month":
                startTime = DateUtils.truncate(new Date(), Calendar.MONTH);
                break;
            case "year":
                startTime = DateUtils.truncate(new Date(), Calendar.YEAR);
                break;
            case "oldYear":
                startTime = DateUtils.addMonths(DateUtils.truncate(new Date(), Calendar.MONTH), -12);
                break;
            case "tillNow":
                startTime = null;
                break;
            default:
                throw new IllegalArgumentException("Unknown date");
        }
        return startTime;
    }

    /**
     * 获取给定时间范围内的所有日期 （日/月）
     * @param startTime 时间1
     * @param endTime 时间2
     * @return 时间字符串集合
     */
    public static List<Date> getDateListCustom(Date startTime, Date endTime) {
        List<Date> param = Lists.newArrayList();
        if (DateUtils.addDays(startTime, 31).getTime() > endTime.getTime()) {
            while (startTime.getTime() < endTime.getTime()) {
                param.add(startTime);
                startTime = DateUtils.addDays(startTime, 1);
            }
        } else {
            Date truncate = DateUtils.truncate(startTime, Calendar.MONTH);
            while (truncate.getTime() < endTime.getTime()) {
                param.add(truncate);
                truncate = DateUtils.addMonths(truncate, 1);
            }
        }
        return param;
    }

    public static Date getTomorrowDate(LocalDate date) {
        ZonedDateTime zonedDateTime = date.atStartOfDay(ZoneId.systemDefault());
        Instant instant = zonedDateTime.toInstant();
        Date from = Date.from(instant);
        return DateUtils.addDays(from, 1);
    }

    public static Date getYesterdayDate(LocalDate date) {
        ZonedDateTime zonedDateTime = date.atStartOfDay(ZoneId.systemDefault());
        Instant instant = zonedDateTime.toInstant();
        Date from = Date.from(instant);
        return DateUtils.addDays(from, -1);
    }

    public static List<LocalDate> getDatesBetween(Date startDate, Date endDate) {
        LocalDate start = new java.sql.Date(startDate.getTime()).toLocalDate();
        LocalDate end = new java.sql.Date(endDate.getTime()).toLocalDate();
        long days = ChronoUnit.DAYS.between(start, end);
        List<LocalDate> dates = Lists.newArrayList();
        for (int i = 0; i <= days; i++) {
            dates.add(start.plusDays(i));
        }
        dates.remove(LocalDate.now());
        return dates;
    }

    public static Date getStartDate(Date date, String dateUnit) {
        if (date == null || StringUtils.isBlank(dateUnit)) {
            return date;
        }
        LocalDateTime dateTime = date2LocalDateTime(date);
        LocalDateTime startDate;

        switch (dateUnit) {
            case "day":
                startDate = dateTime.with(LocalTime.MIN);
                break;
            case "week":
                startDate = dateTime.with(TemporalAdjusters.previousOrSame(DayOfWeek.MONDAY)).with(LocalTime.MIN);
                break;
            case "month":
                startDate = dateTime.with(TemporalAdjusters.firstDayOfMonth()).with(LocalTime.MIN);
                break;
            default:
                throw new IllegalArgumentException("Unknown date unit");
        }
        return localDateTime2Date(startDate);
    }

    public static Date getEndDate(Date date, String dateUnit) {

        if (date == null || StringUtils.isBlank(dateUnit)) {
            return date;
        }
        LocalDateTime dateTime = date2LocalDateTime(date);
        LocalDateTime endDate;

        switch (dateUnit) {
            case "day":
                endDate = dateTime.with(LocalTime.MAX);
                break;
            case "week":
                endDate = dateTime.with(TemporalAdjusters.nextOrSame(DayOfWeek.SUNDAY))
                    .with(LocalTime.MAX);
                break;
            case "month":
                endDate = dateTime.with(TemporalAdjusters.lastDayOfMonth()).with(LocalTime.MAX);
                break;
            default:
                throw new IllegalArgumentException("Unknown date unit");
        }
        return localDateTime2Date(endDate);
    }

    public static Date getFirstDayOfLastYear() {
        Calendar calendar = Calendar.getInstance();
        calendar.add(Calendar.YEAR, -1);
        calendar.set(Calendar.MONTH, 0);
        calendar.set(Calendar.DAY_OF_MONTH, 1);
        return calendar.getTime();
    }

    public static Date getLastDayOfLastYear() {
        Calendar calendar = Calendar.getInstance();
        calendar.add(Calendar.YEAR, -1);
        calendar.set(Calendar.MONTH, 11);
        calendar.set(Calendar.DAY_OF_MONTH, 31);
        return calendar.getTime();
    }

    /**
     * 将字符串转换成日期
     * @param dateTimeStr 支持的格式: 1689159935091, "yyyy-MM-dd HH:mm:ss", "yyyy-MM-dd'T'HH:mm:ssX", "yyyy-MM-dd'T'HH:mm:ssXX", "yyyy-MM-dd'T'HH:mm:ssZZ",
     *         "yyyy-MM-dd HH:mm:ss.SSS", "yyyy-MM-dd'T'HH:mm:ss.SSSX", "yyyy-MM-dd'T'HH:mm:ss.SSSXX", "yyyy-MM-dd'T'HH:mm:ss.SSSZZ",
     *         "yyyy-MM-dd", "yyyy-MM-ddX", "yyyy-MM-ddXX", "yyyy-MM-ddZZ"，"yyyyMMddHHmmss", "yyyyMMddHHmm", yyyy-MM-dd hh:mm:ssaa
     * @return
     */
    public static Date convert(String dateTimeStr) {
        if (StringUtils.isBlank(dateTimeStr)) {
            return null;
        }
        dateTimeStr = dateTimeStr.trim();
        if (dateTimeStr.length() == 13 && dateTimeStr.matches(TIME_STAMP_FORMAT)) {
            try {
                return new Date(Long.parseLong(dateTimeStr));
            } catch (Exception e) {
                throw new RuntimeException(String.format("parser %s to Date fail", dateTimeStr));
            }
        }

        try {
            return DateUtils.parseDate(dateTimeStr, Locale.ENGLISH, DATE_FORMATS);
        } catch (ParseException e) {
            throw new RuntimeException(String.format("parser %s to Date fail", dateTimeStr));
        }
    }

    public static String localDateTime2String(LocalDateTime localDateTime, String pattern) {
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern(pattern);
        return localDateTime.format(formatter);
    }

    /**
     * 将字符串转换成日期
     * @param dateTimeStr
     * @param parsePatterns 提供转换的时间格式
     * @return
     * @throws ParseException
     */
    public static Date convert(String dateTimeStr, final String... parsePatterns) throws ParseException {
        for (String dateFormat : parsePatterns) {
            if ("yyyyMMdd".equals(dateFormat) && dateTimeStr.length() != 8) {
                continue;
            }
            if ("yyyy".equals(dateFormat) && dateTimeStr.length() != 4) {
                continue;
            }
            try {
                Date date;
//                if (dateTimeStr.length() == 12) {
//                    date = DateValidator.getInstance().validate(dateTimeStr, dateFormat);
//                    date = DateValidator.getInstance().validate(dateTimeStr, dateFormat);
//                } else {
                    date = DateUtils.parseDate(dateTimeStr, Locale.ENGLISH, dateFormat);
//                }
                if (date != null) {
                    return date;
                }
            } catch (Exception ignored) {
                // 这里不处理，失败进行下一个尝试
            }
        }
        throw new ParseException("Unable to parse the date: " + dateTimeStr, -1);
    }

    /**
     * 获取当前日期的yyyyMMdd的模式
     */
    public static String currentDateToString() {
        // 获取当前日期
        LocalDate currentDate = LocalDate.now();
        // 创建一个日期时间格式化器，指定要格式化的格式
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyyMMdd");
        // 使用格式化器将日期转换为字符串
        return currentDate.format(formatter);
    }


    /**
     * 解析字符串中的日期yyyyMMdd的模式
     * @param query
     * @return
     */
    public static String[] extractDatesToString(String query) {
        String[] dates = new String[2];
        Pattern pattern = Pattern.compile("\\d{4}-\\d{2}-\\d{2}");
        Matcher matcher = pattern.matcher(query);

        int dateIndex = 0;
        while (matcher.find() && dateIndex < 2) {
            SimpleDateFormat inputFormat = new SimpleDateFormat("yyyy-MM-dd");
            SimpleDateFormat outputFormat = new SimpleDateFormat("yyyyMMdd");
            try {
                Date date = inputFormat.parse(matcher.group());
                dates[dateIndex] = outputFormat.format(date);
                dateIndex++;
            } catch (ParseException e) {
                // 处理日期解析异常
                e.printStackTrace();
            }
        }
        return dates;
    }

    public static Date getDateBeforeHours(int hours) {
        // 获取当前时间
        Date currentTime = new Date();
        // 创建Calendar对象并设置为当前时间
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(currentTime);
        // 将时间向前
        calendar.add(Calendar.HOUR_OF_DAY, -1 * hours);
        // 获取推前两小时后的时间
        return calendar.getTime();
    }

    public static Date getDateAfterHours(Date start, int hours) {
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(start);
        // 将时间向前
        calendar.add(Calendar.HOUR_OF_DAY, hours);
        // 获取推前两小时后的时间
        return calendar.getTime();
    }

    public static Long dateDiff(Date dt1, Date dt2) {
        if (dt1 == null || dt2 == null) {
            return 0L;
        }
        long diffInSeconds = (dt1.getTime() - dt2.getTime()) / 1000;
        return Math.abs(diffInSeconds);
    }

    /**
     * 获取将yyyyMMddHHmm类型的两个时间段转换成HH:mm-HH:mm的格式
     * 现有两家医院,本钢传的是yyyyMMddHHmm 阜新传的是yyyyMMddHHmmss 需要兼容
     * @param beginTime
     * @param endTime
     * @return
     */
    public static String createVisitTimeSpan(String beginTime, String endTime) {
        try {
            SimpleDateFormat outputFormat = new SimpleDateFormat("HH:mm");
            return outputFormat.format(convert(beginTime)) + "-" + outputFormat.format(convert(endTime));
        } catch (Exception e) {
            e.printStackTrace();
            return null;
        }
    }

    /**
     * 获取筛选月份所在的最小时间和最大时间
     * @param monthString
     * @return
     */
    public static Date[] extractMonth(String monthString) {
        int year = Integer.parseInt(monthString.split("-")[0]);
        int month = Integer.parseInt(monthString.split("-")[1]);
        Calendar calendar = Calendar.getInstance();
        calendar.set(Calendar.YEAR, year);
        calendar.set(Calendar.MONTH, month - 1); // 月份从0开始，所以需要减1
        calendar.set(Calendar.DAY_OF_MONTH, 1); // 设置为该月第一天
        Date firstDayOfMonth = getStartOfMonth(calendar.getTime()); // 月的最小时间
        calendar.set(Calendar.DAY_OF_MONTH, calendar.getActualMaximum(Calendar.DAY_OF_MONTH));
        Date lastDayOfMonth = getEndOfMonth(calendar.getTime()); // 月的最大时间
        if (year == Calendar.getInstance().get(Calendar.YEAR) && month == Calendar.getInstance().get(Calendar.MONTH) + 1) {
            lastDayOfMonth = new Date();
        }
        return new Date[]{firstDayOfMonth, lastDayOfMonth};
    }

    /**
     * 获取筛选年份的最小时间和最大时间
     * @param year
     * @return
     */
    public static Date[] extractYear(int year) {
        Calendar calendar = Calendar.getInstance();
        calendar.set(Calendar.YEAR, year);
        calendar.set(Calendar.MONTH, Calendar.JANUARY); // 将月份设置为一月，即最小月
        calendar.set(Calendar.DAY_OF_MONTH, 1);
        Date firstMonth = getStartOfMonth(calendar.getTime());
        calendar.set(Calendar.MONTH, Calendar.DECEMBER); // 将月份设置为十二月，即最大月
        calendar.set(Calendar.DAY_OF_MONTH, calendar.getActualMaximum(Calendar.DAY_OF_MONTH));
        Date lastMonth = getEndOfMonth(calendar.getTime());
        if (year == Calendar.getInstance().get(Calendar.YEAR)) {
            // 如果年份与当前年份相同，使用当前时间作为最大月
            lastMonth = new Date();
        }
        return new Date[]{firstMonth, lastMonth};
    }

    public static Date getPreWeekToday() {
        // 获取当前日期
        Calendar calendar = Calendar.getInstance();
        Date today = calendar.getTime();

        // 获取上个月的今天
        calendar.add(Calendar.MONTH, -1);
        return calendar.getTime();
    }

    public static String getHisDateStr(Date date) {
        // 指定日期格式
        SimpleDateFormat dateFormat = new SimpleDateFormat("yyyyMMddHHmmss");

        // 将 Date 对象按照指定格式转换为字符串
        return dateFormat.format(date);

    }

    /**
     * 获取当前字符串格式时间, yyyyMMddHHmmss
     * @return yyyyMMddHHmmss
     */
    public static String nowTimeNumberString() {
        return TimeUtils.dateToString(new Date(), "yyyyMMddHHmmss");
    }

    /**
     * 拼接年月日 + 时分
     * @param date 2024-01-01 00:00:00 Date
     * @param time 08:00 String
     * @return
     */
    public static String joinDateAndTime(Date date, String time){
        SimpleDateFormat dateFormat = new SimpleDateFormat("yyyyMMdd");
        String formattedDate = dateFormat.format(date);
        // 将时间字符串解析为小时和分钟
        String[] timeParts = time.split(":");
        int hours = Integer.parseInt(timeParts[0].trim());
        int minutes = Integer.parseInt(timeParts[1].trim());
        // 设置时间
        date.setHours(hours);
        date.setMinutes(minutes);
        // 格式化时间
        SimpleDateFormat timeFormat = new SimpleDateFormat("HHmm");
        String formattedTime = timeFormat.format(date);
        // 将日期和时间拼接在一起
        return formattedDate + formattedTime;
    }

    /**
     * 0上午 07:00:00-12:00:00
     * 1下午 12:00:00-17:30:00
     * 2晚上 17:30:00-23:59:59
     * 3全天 00:00:00-23:59:59
     * 4白天 07:00:00-17:00:00
     * 5后夜 00:00:00-07:00:00
     * 6夜间 17:00:00-08:00:00
     *  根据时间范围获取对应编码
     */
    public static String getTimeType(String startTime, String endTime){
        LocalTime start = LocalTime.parse(startTime, DateTimeFormatter.ofPattern("HH:mm"));
        LocalTime end = LocalTime.parse(endTime, DateTimeFormatter.ofPattern("HH:mm"));
        LocalTime h0 = LocalTime.parse("00:00:00");
        LocalTime h7 = LocalTime.parse("07:00:00");
        LocalTime h8 = LocalTime.parse("08:00:00");
        LocalTime h12 = LocalTime.parse("12:00:00");
        LocalTime h1730 = LocalTime.parse("17:30:00");
        LocalTime hend = LocalTime.parse("23:59:59");
        if (start.compareTo(h7) >= 0 && end.compareTo(h12) <= 0) {
            return "0";
        }
        if (start.compareTo(h12) >= 0 && end.compareTo(h1730) <= 0) {
            return "1";
        }
        if (start.compareTo(h1730) >= 0 && end.compareTo(hend) <= 0) {
            return "2";
        }
        if (start.compareTo(h0) >= 0 && end.compareTo(h7) <= 0) {
            return "5";
        }
        if (start.compareTo(h7) >= 0 && end.compareTo(h1730) <= 0) {
            return "4";
        }
        if (start.compareTo(h1730) >= 0 && end.compareTo(h8) <= 0) {
            return "6";
        }
        return "3";
    }

    public static String dateStringFormat(Date date, String pattern){
        try {
            SimpleDateFormat format = new SimpleDateFormat(pattern);
            return format.format(date);
        } catch (Exception e) {
            return null;
        }
    }

}
