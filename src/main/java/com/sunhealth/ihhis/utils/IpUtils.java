package com.sunhealth.ihhis.utils;

import org.apache.logging.log4j.util.Strings;

import javax.servlet.http.HttpServletRequest;
import java.net.InetAddress;
import java.net.NetworkInterface;
import java.util.Enumeration;

/**
 */
public class IpUtils {

    public static long ipToLong(String ipAddress) {
        long result = 0;
        String[] ipAddressInArray = ipAddress.split("\\.");
        for (int i = 3; i >= 0; i--) {
            long ip = Long.parseLong(ipAddressInArray[3 - i]);
            // left shifting 24,16,8,0 and bitwise OR
            // 1. 192 << 24
            // 1. 168 << 16
            // 1. 1 << 8
            // 1. 2 << 0
            result |= ip << (i * 8);
        }
        return result;
    }

    public static String longToIp(long ip) {
        StringBuilder sb = new StringBuilder(15);
        for (int i = 0; i < 4; i++) {
            // 1. 2
            // 2. 1
            // 3. 168
            // 4. 192
            sb.insert(0, Long.toString(ip & 0xff));
            if (i < 3) {
                sb.insert(0, '.');
            }
            // 1. ***********
            // 2. 192.168.1
            // 3. 192.168
            // 4. 192
            ip = ip >> 8;
        }
        return sb.toString();
    }

    /**
     * 获取客户端ip
     * @param request
     * @return
     */
    public static String realClientIp(HttpServletRequest request) {
        String ips = request.getHeader("X-Forwarded-For");
        if (Strings.isEmpty(ips)) {
            return request.getRemoteAddr();
        } else {
            return ips.split(",")[0];
        }
    }

    /**
     * 获取本机ip地址
     * @return
     * @throws Exception
     */
    public static InetAddress getLocalHostLANAddress(String networkName) throws Exception {
        InetAddress candidateAddress = null;
        // 遍历所有的网络接口
        for (Enumeration<NetworkInterface> ifaces = NetworkInterface.getNetworkInterfaces(); ifaces.hasMoreElements(); ) {
            NetworkInterface iface = ifaces.nextElement();
            if (Strings.isNotEmpty(networkName) && !networkName.equals(iface.getName())) {
                continue;
            }
            // 在所有的接口下再遍历IP
            for (Enumeration<InetAddress> inetAddrs = iface.getInetAddresses(); inetAddrs.hasMoreElements(); ) {
                InetAddress inetAddr = inetAddrs.nextElement();
                if (!inetAddr.isLoopbackAddress()) {// 排除loopback类型地址
                    if (inetAddr.isSiteLocalAddress()) {
                        // 如果是site-local地址，就是它了
                        return inetAddr;
                    } else if (candidateAddress == null) {
                        // site-local类型的地址未被发现，先记录候选地址
                        candidateAddress = inetAddr;
                    }
                }
            }
        }
        if (candidateAddress != null) {
            return candidateAddress;
        }
        // 如果没有发现 non-loopback地址.只能用最次选的方案
        return InetAddress.getLocalHost();
    }

    /**
     * 获取本机mac地址
     * @param networkName
     * @return
     */
    public static String getMacAddress(String networkName) {
        try {
            InetAddress inetAddress = getLocalHostLANAddress(networkName);
            NetworkInterface networkInterface = NetworkInterface.getByInetAddress(inetAddress);
            byte[] mac = networkInterface.getHardwareAddress();
            StringBuilder macAddress = new StringBuilder();
            for (int i = 0; i < mac.length; i++) {
                macAddress.append(String.format("%02X%s", mac[i], (i < mac.length - 1) ? "-" : ""));
            }

            return macAddress.toString();
        } catch (Exception ignored) {
            return "";
        }
    }

    /**
     * 获取本机ip地址
     * @return
     * @throws Exception
     */
    public static String getIpAddress(String networkName) throws Exception {
        return getLocalHostLANAddress(networkName).getHostAddress();
    }

}
