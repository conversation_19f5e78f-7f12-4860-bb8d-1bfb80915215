package com.sunhealth.ihhis.utils;


// package com.sunhealth.ihhis.utils; // 您的包名

import org.w3c.dom.Document;
import org.xml.sax.InputSource;
import javax.xml.parsers.DocumentBuilder;
import javax.xml.parsers.DocumentBuilderFactory;
import java.io.StringReader;
import java.util.ArrayList;
import java.util.List;

public class MedicalRecordRenderer {

    /**
     * 解析病历XML并将其内容渲染为紧凑、左对齐的HTML。
     * 这个方法一步到位：接收完整的XML字符串，内部完成解析和渲染。
     *
     * @param xmlContent 包含XTextDocument的完整XML字符串
     * @return 格式化后的HTML内容；如果解析失败则返回错误提示的HTML。
     */
    public static String renderToHtml(String xmlContent) {
        // --- 步骤 1: 内部执行XML解析，提取BodyText (这部分代码不变) ---
        String bodyText;
        try {
            if (xmlContent == null || xmlContent.trim().isEmpty()) {
                return "<p style='color:red;'>输入内容为空。</p>";
            }

            DocumentBuilderFactory factory = DocumentBuilderFactory.newInstance();
            // 禁用外部实体，增加安全性
            factory.setFeature("http://apache.org/xml/features/disallow-doctype-decl", true);
            factory.setFeature("http://xml.org/sax/features/external-general-entities", false);
            factory.setFeature("http://xml.org/sax/features/external-parameter-entities", false);
            factory.setExpandEntityReferences(false);

            DocumentBuilder builder = factory.newDocumentBuilder();
            Document doc = builder.parse(new InputSource(new StringReader(xmlContent)));
            doc.getDocumentElement().normalize();

            org.w3c.dom.NodeList nodeList = doc.getElementsByTagName("BodyText");

            if (nodeList.getLength() > 0) {
                bodyText = nodeList.item(0).getTextContent();
            } else {
                return "<p style='color:red;'>错误：在XML中未找到 &lt;BodyText&gt; 节点。</p>";
            }
        } catch (Exception e) {
            e.printStackTrace(); // 在服务器端打印详细错误日志
            return "<p style='color:red;'>错误：XML解析失败 - " + e.getMessage() + "</p>";
        }

        // --- 步骤 2: 将提取出的纯文本渲染为HTML (主要修改CSS) ---
        if (bodyText == null || bodyText.trim().isEmpty()) {
            return "<p>病历内容为空。</p>";
        }

        StringBuilder html = new StringBuilder();

        // ========= CSS 修改区域开始 =========
        html.append("<style>")
            // 容器整体左对齐，移除任何可能导致缩进的padding
            .append(".record-container { font-family: 'PingFang SC', 'Microsoft YaHei', '宋体', sans-serif; font-size: 14px; line-height: 1.8; padding: 0; margin: 0; }")
            .append(".record-item { display: flex; margin-bottom: 8px; border-bottom: 1px dashed #e0e0e0; padding-bottom: 8px; align-items: flex-start; }")
            .append(".record-item:last-child { border-bottom: none; }")
            // 关键修改：将标签的文本对齐方式改为 'left'
            .append(".record-label { flex: 0 0 110px; font-weight: bold; color: #333; text-align: left; margin-left: 0px; }")
            .append(".record-content { flex: 1; color: #555; word-break: break-all; }")
            // 关键修改：为药品列表恢复项目符号和左侧内边距，实现缩进效果
            .append(".record-content ul { list-style-type: disc; padding-left: 1px; margin: 0; }")
            .append(".record-content ul li { margin-bottom: 5px; }")
            .append("</style>");
        // ========= CSS 修改区域结束 =========

        html.append("<div class='record-container'>");

        // Java逻辑部分不变
        String[] lines = bodyText.trim().split("\n");
        List<String> treatmentItems = new ArrayList<>();
        boolean inTreatmentSection = false;

        for (String line : lines) {
            line = line.trim();
            if (line.isEmpty()) continue;

            if (inTreatmentSection) {
                treatmentItems.add(line);
                continue;
            }

            if (line.contains("日期时间：")) { // 使用contains增加兼容性
                html.append("<div class='record-item'>")
                    .append("<span class='record-label'>基本信息:</span>")
                    .append("<span class='record-content'>").append(escapeHtml(line.replace("日期时间：", ""))).append("</span>")
                    .append("</div>");
            } else if (line.contains("代诊者关系：")) {
                html.append("<div class='record-item'>")
                    .append("<span class='record-label'>陪诊信息:</span>")
                    .append("<span class='record-content'>").append(escapeHtml(line.replace("代诊者关系：", ""))).append("</span>")
                    .append("</div>");
            } else if (line.contains("诊断：")) {
                html.append("<div class='record-item'>")
                    .append("<span class='record-label'>诊断意见:</span>")
                    .append("<span class='record-content'>").append(escapeHtml(line.replace("诊断：", ""))).append("</span>")
                    .append("</div>");
            } else if (line.contains("处理：")) {
                inTreatmentSection = true;
            } else {
                html.append("<div class='record-item'>")
                    .append("<span class='record-label'>主诉/现病史:</span>")
                    .append("<span class='record-content'>").append(escapeHtml(line)).append("</span>")
                    .append("</div>");
            }
        }

        if (inTreatmentSection) {
            html.append("<div class='record-item'>")
                .append("<span class='record-label'>处理意见:</span>")
                .append("<span class='record-content'>");
            if (!treatmentItems.isEmpty()) {
                html.append("<ul>");
                for (String item : treatmentItems) {
                    html.append("<li>").append(escapeHtml(item)).append("</li>");
                }
                html.append("</ul>");
            }
            html.append("</span></div>");
        }

        html.append("</div>");
        return html.toString();
    }

    private static String escapeHtml(String text) {
        if (text == null) return "";
        return text.replace("&", "&amp;")
            .replace("<", "&lt;")
            .replace(">", "&gt;")
            .replace("\"", "&quot;")
            .replace("'", "&#39;");
    }
}

