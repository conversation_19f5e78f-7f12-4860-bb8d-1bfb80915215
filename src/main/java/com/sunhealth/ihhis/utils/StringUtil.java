package com.sunhealth.ihhis.utils;

import org.apache.commons.lang3.StringUtils;
import org.springframework.web.util.HtmlUtils;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

/**
 */
public class StringUtil {

    public static String getDefault(Object v) {
        if (v == null) {
            return "";
        }
        return v.toString();
    }

    public static String joinWithDoubleSeparator(final Object[] array, final String separator) {
        if (array == null || array.length == 0) {
            return null;
        }
        String string = StringUtils.join(array, separator);
        if (StringUtils.isBlank(string)) {
            return string;
        } else {
            return separator + string + separator;
        }
    }

    public static String[] split(final String string, final String separator) {
        if (StringUtils.isEmpty(string)) {
            return new String[]{};
        }
        return Arrays.stream(string.split(separator))
            .filter(StringUtils::isNotEmpty).toArray(String[]::new);
    }

    /**
     * <pre>
     *     "1" -> true
     *     "true" -> true 不区分大小写
     *     others -> false
     * </pre>
     * @param string
     * @return
     */
    public static boolean stringToBoolean(String string) {
        return "1".equals(string) || "true".equalsIgnoreCase(string);
    }

    public static String excelStrToHtmlStr(String excelStr) {
        if (StringUtils.isEmpty(excelStr)) {
            return "<p></p>";
        }
        return "<p>" + StringUtils.join(HtmlUtils.htmlEscapeDecimal(
                                            excelStr.replaceAll("\r\n", "\n")).split("\n"),
                                        "</p><p>") + "</p>";
    }

    public static String replaceSqlSlash(String pattern) {
        return pattern.replaceAll("\\\\", "\\\\\\\\\\\\\\\\");
    }

    public static String formatHtml(String format, String... args) {
        return String.format(format, Arrays.stream(args).map(HtmlUtils::htmlEscapeDecimal).toArray());
    }

    /**
     * 解析一个字符串，获取所有连续的数字，用集合接收
     */
    public static List<String> parse(String str) {
        // 解析数字
        ArrayList<String> list = new ArrayList<>();
        int start = 0, end, len = str.length();
        for (int i = 0; i < len; i++) {
            char cTop = 0;
            char cRear = 0;
            char c = str.charAt(i);
            if (i > 0) {
                cTop = str.charAt(i - 1);
            }
            if (i < len - 1) {
                cRear = str.charAt(i + 1);
            }
            // 如果 c 是数字 且 它前一个数不是数字 或者 它是第 0 个字符时，获得 start
            if (isNumber(c) && (!isNumber(cTop) || i == 0)) {
                start = i;
            }
            // 如果 c 是数字 且 它后一个数不是数字 或者 它是最后一个字符时，获得 end
            if (isNumber(c) && (!isNumber(cRear) || i == len - 1)) {
                end = i + 1;
                list.add(str.substring(start, end));
            }
        }
        return list;
    }

    /**
     * 判断是否是数字
     */
    public static boolean isNumber(char c) {
        // ascii 编码
        return c >= 48 && c <= 57;
    }

}
