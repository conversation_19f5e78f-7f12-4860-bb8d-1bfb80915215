package com.sunhealth.ihhis.utils;

import com.sunhealth.ihhis.config.ApplicationProperties;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.util.CollectionUtils;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.concurrent.locks.Lock;
import java.util.concurrent.locks.ReentrantLock;
import java.util.stream.Collectors;

@Slf4j
public class ProxyUtils {

    private static final Lock lock = new ReentrantLock();
    private static final Map<String, String> proxyChangeCache = new HashMap<>();

    public static void formatProxy(String tag, List<ApplicationProperties.IhProxy> proxy) {
        if (CollectionUtils.isEmpty(proxy)) {
            return;
        }
        String hash = proxy.toString();
        if (Objects.equals(proxyChangeCache.get(tag), hash)) {
            return;
        }
        lock.lock();
        if (Objects.equals(proxyChangeCache.get(tag), hash)) {
            return;
        }
        try {
            proxy.forEach(u -> {
                if (StringUtils.isNotBlank(u.getProtocol())) {
                    u.setProtocol(u.getProtocol().split(":")[0] + "://");
                }
                if (u.getPort() == 0) {
                    if ("http://".equals(u.getProtocol())) {
                        u.setPort(80);
                    } else if ("https://".equals(u.getHost())) {
                        u.setPort(443);
                    }
                }
                List<String> paths = u.getProxyPath().stream().map(p -> {
                    if (StringUtils.isBlank(p)) {
                        return p;
                    }
                    if (p.contains("://")) {
                        String[] ps = p.split("://");
                        if (ps.length > 1) {
                            p = u.getProtocol() + ps[1];
                        } else {
                            p = u.getProtocol();
                        }
                    } else {
                        p = u.getProtocol() + p;
                    }
                    if (!p.endsWith("/")) {
                        p = p + "/";
                    }
                    return p;
                }).collect(Collectors.toList());
                u.setProxyPath(paths);
                if (StringUtils.isNotBlank(u.getHost())) {
                    if (u.getHost().contains("://")) {
                        String[] ps = u.getHost().split("://");
                        if (ps.length > 1) {
                            u.setHost(ps[1]);
                        } else {
                            u.setHost("");
                        }
                    }
                    if (u.getHost().contains("/")) {
                        u.setHost(u.getHost().split("/")[0]);
                    }
                }
            });
            proxyChangeCache.put(tag, hash);
        } catch (Exception e) {
            log.error("解析代理配置失败", e);
        } finally {
            lock.unlock();
        }
    }

}
