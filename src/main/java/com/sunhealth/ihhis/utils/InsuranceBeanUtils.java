package com.sunhealth.ihhis.utils;

import com.sunhealth.ihhis.config.YiBaoProperties;
import com.sunhealth.ihhis.enums.BusinessType;
import com.sunhealth.ihhis.enums.PayChannelType;
import com.sunhealth.ihhis.model.dq.sh.insurance.response.SE01Response;
import com.sunhealth.ihhis.model.dq.sh.insurance.response.SM01Response;
import com.sunhealth.ihhis.model.entity.charge.PreCharge;
import com.sunhealth.ihhis.model.entity.dqyibao.RegInsurancePreCharge;
import com.sunhealth.ihhis.model.insurance.MedicalInsuranceParam;
import com.sunhealth.ihhis.service.dqyb.DQYiBaoClient;
import com.sunhealth.ihhis.service.dqyb.charge.ChargeAmt;
import com.sunhealth.ihhis.service.gjyb.GJYiBaoClient;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Slf4j
public class InsuranceBeanUtils {

    public static Map<String, Boolean> YB_MAP = new HashMap<>();

    /**
     * 5期接口, 如果不接入5期医保, 什么也不做, 直接返回
     * 读取医保账户
     */
    public static void readAccount(BusinessType businessType, MedicalInsuranceParam insuranceParam, PayChannelType type) {
        DQYiBaoClient dqYiBaoClient = InsuranceBeanUtils.getDQYiBaoClient();
        if (dqYiBaoClient == null) {
            return;
        }
        SE01Response se01Response = dqYiBaoClient.getDecodeQuery(businessType, insuranceParam, type);
        SM01Response sm01 = dqYiBaoClient.getReadAccountStr(insuranceParam);
        String accountAttr = sm01.getAccountattr();
        // 互助帮困、民政帮困、工伤、干保使用5期接口, 其中工伤业务不做
        // TODO: 新规定, 慢特病, 大病直接走国家医疗保险, 需要调用6期国家接口判断, 王伟是说需要用520x查询, 不知道1101是否可以
        String flag2 = accountAttr.substring(1, 2); // 第2位 干部保健对象
        String flag12 = accountAttr.substring(11, 12); // 第12位 A：民政医疗帮困, G：医疗互助帮困对象
        if ("1".equals(flag2) || "G".equals(flag12) || "A".equals(flag12)) {
            insuranceParam.setDqYiBao(true);
        }
        Boolean yb = YB_MAP.get(se01Response.getIdNo());
        if (yb != null) {
            insuranceParam.setDqYiBao(yb);
        }
    }

    public static GJYiBaoClient getGJYiBaoClient() {
        YiBaoProperties yiBaoProperties = AppContext.getInstance(YiBaoProperties.class);
        return AppContext.getInstance(yiBaoProperties.getArea() + "GJYiBaoClient", GJYiBaoClient.class);
    }

    public static DQYiBaoClient getDQYiBaoClient() {
        YiBaoProperties yiBaoProperties = AppContext.getInstance(YiBaoProperties.class);
        String beanName = yiBaoProperties.getArea() + "DQYiBaoClient";
        if (AppContext.getContext().containsBean(beanName)) {
            return AppContext.getInstance(beanName, DQYiBaoClient.class);
        } else {
            return null;
        }
    }

    /**
     * 证件类型his->医保
     */
    public static String certificateTypeMapping(String certificateType) {
        if (certificateType == null) {
            return "01";
        }
        switch (certificateType){
            case "1":
            case "2":
                return "01";
            case "3":
                return "08";
            case "4":
                return "02";
            case "6":
                return "04";
            case "7":
                return "06";
            case "5":
            default:
                return "99";
        }
    }

    /**
     * 证件类型his->医保
     */
    public static String certificateTypeMapping(String certificateType, String certificateNo) {
        /*
         * 排查 李佳元-421023198901253479 使用医保缴费失败，返回未查询到参保信息，
         * 银海方博文说，该患者有两条参保信息，其中一条停用，但是我们调用时他取错了
         * 原因: 我们6201的参数的身份类型是99，因为在PatientList中该患者的身份类型为空（看创建时间应该是从老系统倒过来的数据）
         */
        if (StringUtils.isEmpty(certificateType) && StringUtils.isNotEmpty(certificateNo)) {
            // 正则判断是不是身份证号
            String regex  = "^[1-9]\\d{5}(18|19|([23]\\d))\\d{2}((0[1-9])|(10|11|12))(([0-2][1-9])|10|20|30|31)\\d{3}[0-9Xx]$";
            if (certificateNo.matches(regex)){
                log.info("患者-{} 身份类型为空，使用正则判断是身份证号", certificateNo);
                return "01";
            }
            log.info("患者-{} 身份类型为空，使用正则判断不是身份证号", certificateNo);
            return "99";
        }
        switch (certificateType){
            case "1":
            case "2":
                return "01";
            case "3":
                return "08";
            case "4":
                return "02";
            case "6":
                return "04";
            case "7":
                return "06";
            case "5":
            default:
                return "99";
        }
    }

    /**
     * 上海医保的医保预算
     * @param accountAttr
     * @param list
     * @return
     */
    public static RegInsurancePreCharge shYiBaoPreCharge(String accountAttr, List<PreCharge> list) {
        ChargeAmt chargeAmt = null;
        String flag12 = accountAttr.substring(11, 12);
        if ("0".equals(flag12) || "1".equals(flag12) || "2".equals(flag12) || "G".equals(flag12)) {
            chargeAmt = AppContext.getInstance("shcbChargeAmt", ChargeAmt.class);
        } else if ("A".equals(flag12)) {
            chargeAmt = AppContext.getInstance("shmzbkChargeAmt", ChargeAmt.class);
        }
        if (chargeAmt == null) {
            log.error("不支持的医保类型 accountAttr: " + accountAttr);
            throw new RuntimeException("上海医保,微信小程序目前只支普通医保,社区帮困,民政帮困");
        }
        return chargeAmt.preCharge(list);
    }
}
