package com.sunhealth.ihhis.utils;

import com.sun.jna.*;
import lombok.extern.slf4j.Slf4j;


@Slf4j
public class Call {

    public Call() {
    }

    public static int StringToFixedBytes(String str, byte[] by) {
        byte[] byTemp = str.getBytes();
        int strLen = byTemp.length;
        int byLen = by.length;
        if (strLen > byLen) {
            return -1;
        } else {
            if (strLen == 0) {
                for (int i = 0; i < byLen; ++i) {
                    by[i] = 0;
                }
            }

            System.arraycopy(byTemp, 0, by, 0, strLen);
            return strLen;
        }
    }

    public static String BytesToString(byte[] by) {
        String str = new String(by);
        int pos = str.indexOf(0);
        if (pos != -1) {
            str = str.substring(0, pos);
        }

        return str;
    }

    public static String SendRcv(String sendMsg) {
        try {
            Pointer p = new Memory(65000);
            String flag = "12345678";
            return DllInterface.INSTANCE.SendRcv4(flag, sendMsg, p);
        } catch (Exception ex) {
            log.error(ex.getMessage(), ex);
            throw new RuntimeException(ex.getMessage());
        }
    }


    public static class AssignMethod extends Structure {
        public String title;
        public OnAssignSN fp;

        public AssignMethod() {
        }

        public static class ByReference extends AssignMethod implements Structure.ByReference {
            public ByReference() {
            }
        }
    }

    public static class Char32msg extends Structure {
        public byte[] msg = new byte[32];

        public Char32msg() {
            this.allocateMemory();
        }

        public String getMsg() {
            return Call.BytesToString(this.msg);
        }
    }

    public interface CustumPlus extends Callback {
        int callback(int var1, int var2);
    }

    public static class DispLastSN implements OnAssignSN {
        public DispLastSN() {
        }

        public final int callback(SerialNum.ByReference pSN, int nCount) {
            if (nCount <= 0) {
                return 0;
            } else {
                System.out.println("回调显示:");
                SerialNum.ByReference[] sns = (SerialNum.ByReference[]) pSN.toArray(nCount);

                for (int i = 0; i < nCount; ++i) {
                    System.out.println(sns[i].getMsg());
                }

                return nCount;
            }
        }
    }

    public interface DllInterface extends Library {
        DllInterface INSTANCE = (DllInterface) Native.loadLibrary("C:\\SendRcv4.dll", DllInterface.class);

        String SendRcv4(String startFlag, String sendMsg, Pointer receivedMsg);
//        void DispInfor();
//
//        void DispMessage(String var1);
//
//        int Plus(int var1, int var2);
//
//        void LoadData(IntByReference var1, FloatByReference var2);
//
//        void AssignSN(Call.SerialNum var1);
//
//        int RectangleArea(Call.Rect.ByValue var1);
//
//        void Standardrize(Call.Rect.ByReference var1);
//
//        void EnlargeRect(Call.Rect var1);
//
//        int MultiOperate(Call.CustumPlus var1, int var2, int var3);
//
//        int ArrayInput(Call.SerialNum[] var1, int var2);
//
//        Call.SerialNum ArrayOutput(IntByReference var1);
//
//        void FreeArray(Call.SerialNum[] var1);
//
//        void CharArrayInput(Call.Char32msg[] var1, int var2);
//
//        void GetStaff(Call.Staff.ByReference var1);
//
//        void FreeMemory(Pointer var1);
//
//        int AssignSNWithCallBack(Call.OnAssignSN var1);
//
//        int AppointAssignMethod(Call.AssignMethod var1);
    }

    public interface OnAssignSN extends Callback {
        int callback(SerialNum.ByReference var1, int var2);
    }

    public static class Rect extends Structure {
        public int nLong;
        public int nShort;

        public Rect() {
        }

        public static class ByReference extends Rect implements Structure.ByReference {
            public ByReference() {
            }
        }

        public static class ByValue extends Rect implements Structure.ByValue {
            public ByValue() {
            }
        }
    }

    public static class SerialNum extends Structure {
        public int id;
        public byte[] msg = new byte[32];

        public SerialNum() {
            this.allocateMemory();
        }

        public int setMSG(String str) {
            return Call.StringToFixedBytes(str, this.msg);
        }

        public String getMsg() {
            return Call.BytesToString(this.msg);
        }

        public static class ByReference extends SerialNum implements Structure.ByReference {
            public ByReference() {
            }
        }

        public static class ByValue extends SerialNum implements Structure.ByValue {
            public ByValue() {
            }
        }
    }

    public static class SimpleAdd implements CustumPlus {
        public SimpleAdd() {
        }

        public final int callback(int a, int b) {
            return a + b;
        }
    }

    public static class Staff extends Structure {
        public SerialNum.ByReference pSN;
        public int nCount = 0;

        public Staff() {
        }

        public SerialNum.ByReference[] toArray() {
            return (SerialNum.ByReference[]) this.pSN.toArray(this.nCount);
        }

        public static class ByReference extends Staff implements Structure.ByReference {
            public ByReference() {
            }
        }
    }
}
