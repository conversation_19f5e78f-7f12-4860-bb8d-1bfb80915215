package com.sunhealth.ihhis.utils;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import org.apache.commons.lang3.StringUtils;

import java.util.*;


public class SignUtil {

    private final static List<String> IGNORE_SIGN = new ArrayList<>();

    static {
        IGNORE_SIGN.add("signData");
        IGNORE_SIGN.add("encData");
        IGNORE_SIGN.add("extra");
    }

    public static String getSignText(JSONObject jsonObject, String appSecret) {
        Map<String, String> signMap = new TreeMap<>();
        Set<Map.Entry<String, Object>> entrys = jsonObject.entrySet();

        for (Map.Entry<String, Object> entry : entrys) {
            if (!StringUtils.isEmpty(String.valueOf(entry.getValue())) && !IGNORE_SIGN.contains(entry.getKey())) {
                signMap.put(entry.getKey(), getValue(entry.getValue()));
            }
        }

        ArrayList<String> list = new ArrayList<>();

        for (Map.Entry<String, String> entry : signMap.entrySet()) {
            if (StringUtils.isNotEmpty(getObjString(entry.getValue()))) {
                list.add(entry.getKey() + "=" + entry.getValue() + "&");
            }
        }

        int size = list.size();
        String[] arrayToSort = list.toArray(new String[0]);
        Arrays.sort(arrayToSort, String.CASE_INSENSITIVE_ORDER);
        StringBuilder sb = new StringBuilder();

        for (int i = 0; i < size; ++i) {
            sb.append(arrayToSort[i]);
        }

        return sb.append("key=").append(appSecret).toString();
    }

    public static String getObjString(Object object) {
        return object == null ? "" : (String) object;
    }

    private static String getValue(Object value) {
        return value instanceof String ? getObjString(value) : treeJsonParam(value);
    }

    private static String treeJsonParam(Object value) {
        String jsonParam;
        if (value instanceof Map) {
            Map<String, Object> treeNestedMap = new TreeMap<>();
            Map<?, ?> nestedMap = (Map<?, ?>) value;

            for (Map.Entry<?, ?> entry : nestedMap.entrySet()) {
                treeNestedMap.put((String) entry.getKey(), entry.getValue());
            }

            jsonParam = JSONObject.toJSONString(treeParams(treeNestedMap));
        } else if (value instanceof ArrayList) {
            ArrayList<?> ar = (ArrayList<?>) value;
            jsonParam = JSONObject.toJSONString(treeList(ar));
        } else if (value instanceof JSONArray) {
            JSONArray jarr = (JSONArray) value;
            jsonParam = JSONObject.toJSONString(treeJsonArray(jarr));
        } else {
            jsonParam = value.toString();
        }

        return jsonParam;
    }

    private static Map<String, Object> treeParams(Map<String, Object> params) {
        if (params == null) {
            return new TreeMap<>();
        } else {
            Map<String, Object> treeParams = new TreeMap<>();

            for (Map.Entry<String, Object> entry : params.entrySet()) {
                String key = entry.getKey();
                Object value = entry.getValue();
                if (value instanceof Map) {
                    Map<String, Object> treeNestedMap = new TreeMap<>();
                    Map<?, ?> nestedMap = (Map<?, ?>) value;

                    for (Map.Entry<?, ?> item : nestedMap.entrySet()) {
                        treeNestedMap.put((String) item.getKey(), item.getValue());
                    }

                    treeParams.put(key, treeParams(treeNestedMap));
                } else if (value instanceof ArrayList) {
                    ArrayList<?> ar = (ArrayList<?>) value;
                    treeParams.put(key, treeList(ar));
                } else if (value instanceof JSONArray) {
                    JSONArray ar = (JSONArray) value;
                    treeParams.put(key, treeJsonArray(ar));
                } else if (!"".equals(value) && value != null) {
                    treeParams.put(key, value.toString());
                }
            }

            return treeParams;
        }
    }

    private static JSONArray treeList(ArrayList<?> list) {
        if (list != null && !list.isEmpty()) {
            JSONArray jsonArray = new JSONArray();
            int size = list.size();

            for (int i = 0; i < size; ++i) {
                jsonArray.add(i, list.get(i));
            }

            return treeJsonArray(jsonArray);
        } else {
            return null;
        }
    }

    private static JSONArray treeJsonArray(JSONArray jarr) {
        if (jarr != null && !jarr.isEmpty()) {
            JSONArray jsonArray = new JSONArray();
            int size = jarr.size();

            for (int i = 0; i < size; ++i) {
                Object value = jarr.get(i);
                if (!(value instanceof Map)) {
                    if (value instanceof ArrayList) {
                        ArrayList<?> ar = (ArrayList<?>) value;
                        jsonArray.add(i, treeList(ar));
                    } else if (value instanceof JSONArray) {
                        JSONArray ar = (JSONArray) value;
                        jsonArray.add(i, treeJsonArray(ar));
                    } else if (!"".equals(value)) {
                        jsonArray.add(i, value.toString());
                    }
                } else {
                    Map<String, Object> treeNestedMap = new TreeMap<>();
                    Map<?, ?> nestedMap = (Map<?, ?>) value;

                    for (Map.Entry<?, ?> entry : nestedMap.entrySet()) {
                        treeNestedMap.put((String) entry.getKey(), entry.getValue());
                    }

                    jsonArray.add(i, treeParams(treeNestedMap));
                }
            }

            return jsonArray;
        } else {
            return null;
        }
    }

}
