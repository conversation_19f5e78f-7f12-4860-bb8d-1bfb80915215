package com.sunhealth.ihhis.service;

import com.sunhealth.ihhis.model.dto.schedule.*;

import java.util.List;

public interface SchedulingService {

    /**
     * 查询指定排班的号序信息
     */
    List<SourceNumberRes> getCurrentDaySchedulingSourceNumber(SourceNumberReq req);

    /**
     * 查询时间区间内的有排班的科室信息
     */
    List<DeptListRes> getSchedulingDeptList(DeptListReq req);

    /**
     * 查询全院预约号源信息
     */
    List<SourceDetailsRes> getSourceDetails(SourceDetailsReq req);

    /**
     * 查询预约科室号源信息
     */
    List<DeptSourceDetailsRes> getSchedulingDeptSourceDetails(DeptSourceDetailsReq req);

    /**
     * 查询预约医生号源信息
     */
    List<DoctorSourceDetailsRes> getSchedulingDoctorSourceDetails(DoctorSourceDetailsReq req);

    /**
     * 4.2.4.	查询全院当天号源信息（含科室号源和医生号源）
     * @param req
     * @return
     */
    List<CurrentDayAppointmentRes> getGetCurrentDayAppointmentList(SourceDetailsReq req);

    /**
     * 查询全院当天号源信息（含科室号源和医生号源）
     * @param req
     * @return
     */
    List<CurrentDoctorSourceDetailsRes> getCurrentDayDoctorSourceDetail(CurrentDoctorSourceDetailsReq req);
}
