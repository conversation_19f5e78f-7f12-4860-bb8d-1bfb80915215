package com.sunhealth.ihhis.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.sunhealth.ihhis.model.dto.DictDataQueryReq;
import com.sunhealth.ihhis.model.dto.DoctorInfo;
import com.sunhealth.ihhis.model.entity.MedicalWorker;

public interface MedicalWorkerService extends IService<MedicalWorker> {
    Page<DoctorInfo> findAll(DictDataQueryReq req);

    MedicalWorker getById(Integer workerId);
}
