package com.sunhealth.ihhis.service.dqyb.charge;

import com.sunhealth.ihhis.dao.his.PreChargeAmtMapper;
import com.sunhealth.ihhis.dao.his.RegInsuranceChargeMapper;
import com.sunhealth.ihhis.dao.his.SystemTBItemCategoryMapper;
import com.sunhealth.ihhis.enums.FeeType;
import com.sunhealth.ihhis.model.entity.SystemTBItemCategory;
import com.sunhealth.ihhis.model.entity.charge.PreCharge;
import com.sunhealth.ihhis.model.entity.charge.PreChargeAmt;
import com.sunhealth.ihhis.model.entity.dqyibao.RegInsurancePreCharge;
import com.sunhealth.ihhis.utils.DecimalUtil;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * 区分甲乙类
 * 普通医保（12位为0，1，2）
 * 社区帮困（12位为G）
 * 大病
 */
@AllArgsConstructor
@Service("shcbChargeAmt")
public class SHCBChargeAmt implements ChargeAmt {

    private final RegInsuranceChargeMapper regInsuranceChargeMapper;
    private final PreChargeAmtMapper preChargeAmtMapper;
    private final SystemTBItemCategoryMapper systemTBItemCategoryMapper;

    @Override
    public RegInsurancePreCharge preCharge(List<PreCharge> list) {
        List<SystemTBItemCategory> itemCategories = systemTBItemCategoryMapper.selectList(null);

        BigDecimal otheramt = BigDecimal.ZERO;//分类支付
        BigDecimal zfamt = BigDecimal.ZERO;//自费
        BigDecimal realamt = BigDecimal.ZERO;//交易金额

        List<Long> selfPayByPayType = new ArrayList<>();//根据paytype判断为自费的项目id集合

        //计算分摊金额
        for (PreCharge preCharge : list) {
            preCharge.setCreditAmt(BigDecimal.ZERO);

            // 4928 医保适应症改造 判断明细表paytype （1：自费，2：医保）
            String paytype = regInsuranceChargeMapper.selectPayType(preCharge.getRecipeDetlID());
            if ("1".equals(paytype)) {
                selfPayByPayType.add(preCharge.getRecipeDetlID());
            }


            //如果是自费
            if (preCharge.getFeeType() == 9 || "1".equals(paytype)) {
                preCharge.setOtherAmt(preCharge.getTotalAmount().setScale(2, RoundingMode.HALF_UP));
                preCharge.setRealAmt(BigDecimal.ZERO);
                preCharge.setInsuranceTradeAmount(BigDecimal.ZERO);
                preCharge.setClassifyTotal(BigDecimal.ZERO);
                preCharge.setInsuranceCashAmount(BigDecimal.ZERO);
                preCharge.setSelfAmount(preCharge.getTotalAmount().setScale(2, RoundingMode.HALF_UP));
                preCharge.setJzAmount(BigDecimal.ZERO);
                zfamt = zfamt.add(preCharge.getSelfAmount());
                //专门存放自费的金额
            } else {
                //甲乙类
                preCharge.setOtherAmt(preCharge.getNonExpensePrice().multiply(preCharge.getQuantiy()).setScale(2, RoundingMode.HALF_UP));
                preCharge.setRealAmt(preCharge.getTotalAmount().setScale(2, RoundingMode.HALF_UP).subtract(preCharge.getOtherAmt()));
                preCharge.setInsuranceTradeAmount(preCharge.getRealAmt());
                preCharge.setClassifyTotal(preCharge.getOtherAmt());
                preCharge.setInsuranceCashAmount(BigDecimal.ZERO);
                preCharge.setSelfAmount(BigDecimal.ZERO);
                preCharge.setJzAmount(BigDecimal.ZERO);
                //专门存放分类支付金额
                otheramt = otheramt.add(preCharge.getOtherAmt());
                //专门存放交易金额
                realamt = realamt.add(preCharge.getRealAmt());
            }

            //甲类或者乙类（交易金额等于可保部分，分类支付等于不可报部分）
            PreChargeAmt preChargeAmt = new PreChargeAmt();
            preChargeAmt.setChargeNo(preCharge.getChargeNo());
            preChargeAmt.setRegNo(preCharge.getRegNo());
            preChargeAmt.setRecipeID(preCharge.getRecipeID());
            preChargeAmt.setRecipeDetlID(preCharge.getRecipeDetlID());
            preChargeAmt.setDiscountAmount(preCharge.getDiscountAmount());
            preChargeAmt.setSelfAmount(preCharge.getSelfAmount());
            preChargeAmt.setClassifyTotal(preCharge.getClassifyTotal());
            preChargeAmt.setInsuranceCashAmount(preCharge.getInsuranceCashAmount());
            preChargeAmt.setCreditAmt(preCharge.getCreditAmt());
            preChargeAmt.setRealAmt(preCharge.getRealAmt());
            preChargeAmt.setOtherAmt(preCharge.getOtherAmt());
            preChargeAmt.setInsuranceTradeAmount(preCharge.getInsuranceTradeAmount());
            preChargeAmt.setJzAmount(preCharge.getJzAmount());
            preChargeAmt.setFeeType(preCharge.getFeeType());

            int i = preChargeAmtMapper.countPreChargeAmt(preCharge.getChargeNo(), preCharge.getRecipeDetlID());
            if (i > 0) {
                preChargeAmtMapper.updateById(preChargeAmt);
            } else {
                preChargeAmtMapper.insert(preChargeAmt);
            }
            String ybMzCode = itemCategories.stream().filter(
                            u -> Objects.equals(u.getItemCode(), preCharge.getItemCategory())
                                    && Objects.equals(u.getHospitalCode().trim(), preCharge.getHospitalCode() + ""))
                    .map(SystemTBItemCategory::getYbMzCode).findFirst().orElse("");
            preCharge.setYbMzCode(ybMzCode.trim());
        }

        //非自费项目集合
        List<PreCharge> fzfList = list.stream().filter(p -> p.getFeeType() != FeeType.CLASS_OTHER.getValue()
                && !selfPayByPayType.contains(p.getRecipeDetlID())).collect(Collectors.toList());

        RegInsurancePreCharge insureCharge = new RegInsurancePreCharge();
        insureCharge.setExaminationFee(fzfList.stream().filter(p -> Objects.equals(p.getYbMzCode(), "04"))
                .map(p -> DecimalUtil.isNullAsZero(p.getTotalAmount()))
                .reduce(new BigDecimal(0), BigDecimal::add).setScale(2, RoundingMode.HALF_UP));
        insureCharge.setTreatmentFee(fzfList.stream().filter(p -> Objects.equals(p.getYbMzCode(), "02"))
                .map(p -> DecimalUtil.isNullAsZero(p.getTotalAmount()))
                .reduce(new BigDecimal(0), BigDecimal::add).setScale(2, RoundingMode.HALF_UP));
        insureCharge.setOperationFee(fzfList.stream().filter(p -> Objects.equals(p.getYbMzCode(), "03"))
                .map(p -> DecimalUtil.isNullAsZero(p.getTotalAmount()))
                .reduce(new BigDecimal(0), BigDecimal::add).setScale(2, RoundingMode.HALF_UP));
        insureCharge.setLaboratoryFee(fzfList.stream().filter(p -> Objects.equals(p.getYbMzCode(), "05"))
                .map(p -> DecimalUtil.isNullAsZero(p.getTotalAmount()))
                .reduce(new BigDecimal(0), BigDecimal::add).setScale(2, RoundingMode.HALF_UP));
        insureCharge.setFilmFee(fzfList.stream().filter(p -> Objects.equals(p.getYbMzCode(), "06"))
                .map(p -> DecimalUtil.isNullAsZero(p.getTotalAmount()))
                .reduce(new BigDecimal(0), BigDecimal::add).setScale(2, RoundingMode.HALF_UP));
        insureCharge.setPerspectiveFee(fzfList.stream().filter(p -> Objects.equals(p.getYbMzCode(), "07"))
                .map(p -> DecimalUtil.isNullAsZero(p.getTotalAmount()))
                .reduce(new BigDecimal(0), BigDecimal::add).setScale(2, RoundingMode.HALF_UP));
        insureCharge.setWesternMedFee(fzfList.stream().filter(p -> Objects.equals(p.getYbMzCode(), "08"))
                .map(p -> DecimalUtil.isNullAsZero(p.getTotalAmount()))
                .reduce(new BigDecimal(0), BigDecimal::add).setScale(2, RoundingMode.HALF_UP));
        insureCharge.setChineseHerbMedFee(fzfList.stream().filter(p -> Objects.equals(p.getYbMzCode(), "10"))
                .map(p -> DecimalUtil.isNullAsZero(p.getTotalAmount()))
                .reduce(new BigDecimal(0), BigDecimal::add).setScale(2, RoundingMode.HALF_UP));
        insureCharge.setChinesePatMedFee(fzfList.stream().filter(p -> Objects.equals(p.getYbMzCode(), "09"))
                .map(p -> DecimalUtil.isNullAsZero(p.getTotalAmount()))
                .reduce(new BigDecimal(0), BigDecimal::add).setScale(2, RoundingMode.HALF_UP));
        insureCharge.setOtherFee(fzfList.stream().filter(p -> Objects.equals(p.getYbMzCode(), "11"))
                .map(p -> DecimalUtil.isNullAsZero(p.getTotalAmount()))
                .reduce(new BigDecimal(0), BigDecimal::add).setScale(2, RoundingMode.HALF_UP));
        insureCharge.setTradeTotal(realamt);
        insureCharge.setInsuranceTotal(realamt.add(otheramt));
        insureCharge.setNonInsuranceTotal(zfamt);
        insureCharge.setClassifyTotal(otheramt);
        insureCharge.setClassifyTreatmentFee(fzfList.stream().filter(p -> Objects.equals(p.getYbMzCode(), "02"))
                .map(p -> DecimalUtil.isNullAsZero(p.getClassifyTotal()))
                .reduce(new BigDecimal(0), BigDecimal::add).setScale(2, RoundingMode.HALF_UP));
        insureCharge.setClassifyOperationFee(fzfList.stream().filter(p -> Objects.equals(p.getYbMzCode(), "03"))
                .map(p -> DecimalUtil.isNullAsZero(p.getClassifyTotal()))
                .reduce(new BigDecimal(0), BigDecimal::add).setScale(2, RoundingMode.HALF_UP));
        insureCharge.setClassifyExaminationFee(fzfList.stream().filter(p -> Objects.equals(p.getYbMzCode(), "04"))
                .map(p -> DecimalUtil.isNullAsZero(p.getClassifyTotal()))
                .reduce(new BigDecimal(0), BigDecimal::add).setScale(2, RoundingMode.HALF_UP));
        insureCharge.setClassifyLaboratoryFee(fzfList.stream().filter(p -> Objects.equals(p.getYbMzCode(), "05"))
                .map(p -> DecimalUtil.isNullAsZero(p.getClassifyTotal()))
                .reduce(new BigDecimal(0), BigDecimal::add).setScale(2, RoundingMode.HALF_UP));
        insureCharge.setClassifyFilmFee(fzfList.stream().filter(p -> Objects.equals(p.getYbMzCode(), "06"))
                .map(p -> DecimalUtil.isNullAsZero(p.getClassifyTotal()))
                .reduce(new BigDecimal(0), BigDecimal::add).setScale(2, RoundingMode.HALF_UP));
        insureCharge.setClassifyPerspectiveFee(fzfList.stream().filter(p -> Objects.equals(p.getYbMzCode(), "07"))
                .map(p -> DecimalUtil.isNullAsZero(p.getClassifyTotal()))
                .reduce(new BigDecimal(0), BigDecimal::add).setScale(2, RoundingMode.HALF_UP));
        insureCharge.setClassifyWesternMedFee(fzfList.stream().filter(p -> Objects.equals(p.getYbMzCode(), "08"))
                .map(p -> DecimalUtil.isNullAsZero(p.getClassifyTotal()))
                .reduce(new BigDecimal(0), BigDecimal::add).setScale(2, RoundingMode.HALF_UP));
        insureCharge.setClassifyChineseHerbMedFee(fzfList.stream().filter(p -> Objects.equals(p.getYbMzCode(), "10"))
                .map(p -> DecimalUtil.isNullAsZero(p.getClassifyTotal()))
                .reduce(new BigDecimal(0), BigDecimal::add).setScale(2, RoundingMode.HALF_UP));
        insureCharge.setClassifyChinesePatMedFee(fzfList.stream().filter(p -> Objects.equals(p.getYbMzCode(), "09"))
                .map(p -> DecimalUtil.isNullAsZero(p.getClassifyTotal()))
                .reduce(new BigDecimal(0), BigDecimal::add).setScale(2, RoundingMode.HALF_UP));
        insureCharge.setClassifyOtherFee(fzfList.stream().filter(p -> Objects.equals(p.getYbMzCode(), "11"))
                .map(p -> DecimalUtil.isNullAsZero(p.getClassifyTotal()))
                .reduce(new BigDecimal(0), BigDecimal::add).setScale(2, RoundingMode.HALF_UP));

        return insureCharge;
    }

}
