package com.sunhealth.ihhis.service.dqyb.charge;

import com.sunhealth.ihhis.dao.his.PreChargeAmtMapper;
import com.sunhealth.ihhis.dao.his.SystemTBItemCategoryMapper;
import com.sunhealth.ihhis.model.entity.SystemTBItemCategory;
import com.sunhealth.ihhis.model.entity.charge.PreCharge;
import com.sunhealth.ihhis.model.entity.charge.PreChargeAmt;
import com.sunhealth.ihhis.model.entity.dqyibao.RegInsurancePreCharge;
import com.sunhealth.ihhis.utils.DecimalUtil;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.List;
import java.util.Objects;

/**
 * 全部报销（包括自费）
 * 民政帮困 （12位 A）
 * 大病
 */
@AllArgsConstructor
@Service("shmzbkChargeAmt")
public class SHMZBKChargeAmt implements ChargeAmt {

    private final PreChargeAmtMapper preChargeAmtMapper;
    private final SystemTBItemCategoryMapper systemTBItemCategoryMapper;

    @Override
    public RegInsurancePreCharge preCharge(List<PreCharge> list) {
        List<SystemTBItemCategory> itemCategories = systemTBItemCategoryMapper.selectList(null);

        BigDecimal realamt = BigDecimal.ZERO;//交易金额

        //计算分摊金额
        for (PreCharge preCharge : list) {
            preCharge.setCreditAmt(BigDecimal.ZERO);
            preCharge.setOtherAmt(BigDecimal.ZERO);
            preCharge.setRealAmt(preCharge.getTotalAmount().setScale(2, RoundingMode.HALF_UP));
            realamt = realamt.add(preCharge.getRealAmt());
            preCharge.setInsuranceTradeAmount(preCharge.getTotalAmount().setScale(2, RoundingMode.HALF_UP));
            preCharge.setClassifyTotal(BigDecimal.ZERO);
            preCharge.setInsuranceCashAmount(BigDecimal.ZERO);
            preCharge.setSelfAmount(BigDecimal.ZERO);

            PreChargeAmt preChargeAmt = new PreChargeAmt();
            preChargeAmt.setChargeNo(preCharge.getChargeNo());
            preChargeAmt.setRegNo(preCharge.getRegNo());
            preChargeAmt.setRecipeID(preCharge.getRecipeID());
            preChargeAmt.setRecipeDetlID(preCharge.getRecipeDetlID());
            preChargeAmt.setDiscountAmount(preCharge.getDiscountAmount());
            preChargeAmt.setSelfAmount(preCharge.getSelfAmount());
            preChargeAmt.setClassifyTotal(preCharge.getClassifyTotal());
            preChargeAmt.setInsuranceCashAmount(preCharge.getInsuranceCashAmount());
            preChargeAmt.setCreditAmt(preCharge.getCreditAmt());
            preChargeAmt.setRealAmt(preCharge.getRealAmt());
            preChargeAmt.setOtherAmt(preCharge.getOtherAmt());
            preChargeAmt.setInsuranceTradeAmount(preCharge.getInsuranceTradeAmount());
            preChargeAmt.setJzAmount(preCharge.getJzAmount());
            preChargeAmt.setFeeType(preCharge.getFeeType());

            int i = preChargeAmtMapper.countPreChargeAmt(preCharge.getChargeNo(), preCharge.getRecipeDetlID());
            if (i > 0) {
                preChargeAmtMapper.updateById(preChargeAmt);
            } else {
                preChargeAmtMapper.insert(preChargeAmt);
            }
            String ybMzCode = itemCategories.stream().filter(
                            u -> Objects.equals(u.getItemCode(), preCharge.getItemCategory())
                                    && Objects.equals(u.getHospitalCode().trim(), preCharge.getHospitalCode() + ""))
                    .map(SystemTBItemCategory::getYbMzCode).findFirst().orElse("");
            preCharge.setYbMzCode(ybMzCode.trim());
        }

        RegInsurancePreCharge insureCharge = new RegInsurancePreCharge();
        insureCharge.setExaminationFee(list.stream().filter(p -> Objects.equals(p.getYbMzCode(), "04"))
                .map(p -> DecimalUtil.isNullAsZero(p.getTotalAmount()))
                .reduce(new BigDecimal(0), BigDecimal::add).setScale(2, RoundingMode.HALF_UP));
        insureCharge.setTreatmentFee(list.stream().filter(p -> Objects.equals(p.getYbMzCode(), "02"))
                .map(p -> DecimalUtil.isNullAsZero(p.getTotalAmount()))
                .reduce(new BigDecimal(0), BigDecimal::add).setScale(2, RoundingMode.HALF_UP));
        insureCharge.setOperationFee(list.stream().filter(p -> Objects.equals(p.getYbMzCode(), "03"))
                .map(p -> DecimalUtil.isNullAsZero(p.getTotalAmount()))
                .reduce(new BigDecimal(0), BigDecimal::add).setScale(2, RoundingMode.HALF_UP));
        insureCharge.setLaboratoryFee(list.stream().filter(p -> Objects.equals(p.getYbMzCode(), "05"))
                .map(p -> DecimalUtil.isNullAsZero(p.getTotalAmount()))
                .reduce(new BigDecimal(0), BigDecimal::add).setScale(2, RoundingMode.HALF_UP));
        insureCharge.setFilmFee(list.stream().filter(p -> Objects.equals(p.getYbMzCode(), "06"))
                .map(p -> DecimalUtil.isNullAsZero(p.getTotalAmount()))
                .reduce(new BigDecimal(0), BigDecimal::add).setScale(2, RoundingMode.HALF_UP));
        insureCharge.setPerspectiveFee(list.stream().filter(p -> Objects.equals(p.getYbMzCode(), "07"))
                .map(p -> DecimalUtil.isNullAsZero(p.getTotalAmount()))
                .reduce(new BigDecimal(0), BigDecimal::add).setScale(2, RoundingMode.HALF_UP));
        insureCharge.setWesternMedFee(list.stream().filter(p -> Objects.equals(p.getYbMzCode(), "08"))
                .map(p -> DecimalUtil.isNullAsZero(p.getTotalAmount()))
                .reduce(new BigDecimal(0), BigDecimal::add).setScale(2, RoundingMode.HALF_UP));
        insureCharge.setChineseHerbMedFee(list.stream().filter(p -> Objects.equals(p.getYbMzCode(), "10"))
                .map(p -> DecimalUtil.isNullAsZero(p.getTotalAmount()))
                .reduce(new BigDecimal(0), BigDecimal::add).setScale(2, RoundingMode.HALF_UP));
        insureCharge.setChinesePatMedFee(list.stream().filter(p -> Objects.equals(p.getYbMzCode(), "09"))
                .map(p -> DecimalUtil.isNullAsZero(p.getTotalAmount()))
                .reduce(new BigDecimal(0), BigDecimal::add).setScale(2, RoundingMode.HALF_UP));
        insureCharge.setOtherFee(list.stream().filter(p -> Objects.equals(p.getYbMzCode(), "11"))
                .map(p -> DecimalUtil.isNullAsZero(p.getTotalAmount()))
                .reduce(new BigDecimal(0), BigDecimal::add).setScale(2, RoundingMode.HALF_UP));
        insureCharge.setTradeTotal(realamt);
        insureCharge.setInsuranceTotal(realamt);
        insureCharge.setNonInsuranceTotal(BigDecimal.ZERO);
        insureCharge.setClassifyTotal(BigDecimal.ZERO);
        insureCharge.setClassifyTreatmentFee(BigDecimal.ZERO);
        insureCharge.setClassifyOperationFee(BigDecimal.ZERO);
        insureCharge.setClassifyExaminationFee(BigDecimal.ZERO);
        insureCharge.setClassifyLaboratoryFee(BigDecimal.ZERO);
        insureCharge.setClassifyFilmFee(BigDecimal.ZERO);
        insureCharge.setClassifyPerspectiveFee(BigDecimal.ZERO);
        insureCharge.setClassifyWesternMedFee(BigDecimal.ZERO);
        insureCharge.setClassifyChineseHerbMedFee(BigDecimal.ZERO);
        insureCharge.setClassifyChinesePatMedFee(BigDecimal.ZERO);
        insureCharge.setClassifyOtherFee(BigDecimal.ZERO);
        return insureCharge;
    }

}
