package com.sunhealth.ihhis.service.dqyb;

import com.fasterxml.jackson.core.type.TypeReference;
import com.google.common.collect.Lists;
import com.sunhealth.ihhis.cache.MemoryCache;
import com.sunhealth.ihhis.config.HisHospitalProperties;
import com.sunhealth.ihhis.config.YiBaoProperties;
import com.sunhealth.ihhis.dao.his.*;
import com.sunhealth.ihhis.enums.BusinessType;
import com.sunhealth.ihhis.enums.ItemCategoryEnum;
import com.sunhealth.ihhis.enums.PayChannelType;
import com.sunhealth.ihhis.model.dq.sh.insurance.EcTokenDTO;
import com.sunhealth.ihhis.model.dq.sh.insurance.request.*;
import com.sunhealth.ihhis.model.dq.sh.insurance.response.*;
import com.sunhealth.ihhis.model.entity.Dept;
import com.sunhealth.ihhis.model.entity.Diagnose;
import com.sunhealth.ihhis.model.entity.MedicalWorker;
import com.sunhealth.ihhis.model.entity.SystemTBItemCategory;
import com.sunhealth.ihhis.model.entity.charge.DrugTbDrugInfomation;
import com.sunhealth.ihhis.model.entity.charge.PreCharge;
import com.sunhealth.ihhis.model.entity.charge.PreChargeAmt;
import com.sunhealth.ihhis.model.entity.dqyibao.RegInsuranceRegister;
import com.sunhealth.ihhis.model.entity.patient.RTPatientList;
import com.sunhealth.ihhis.model.entity.register.PreRegisterDetail;
import com.sunhealth.ihhis.model.entity.register.PreRegisterList;
import com.sunhealth.ihhis.model.entity.view.ChargeItemView;
import com.sunhealth.ihhis.model.entity.view.RegisterListView;
import com.sunhealth.ihhis.model.insurance.MedicalInsuranceParam;
import com.sunhealth.ihhis.service.MedicalWorkerService;
import com.sunhealth.ihhis.utils.*;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * 目前,只有工伤, 干部保健对象, 民政医疗帮困, 医疗互助帮困对象走上海医保
 */
@Service("shangHaiDQYiBaoClient")
@Slf4j
@AllArgsConstructor
public class ShangHaiDQYiBaoClientImpl extends DQYiBaoClient {

    private final HisHospitalProperties hisHospitalProperties;
    private final YiBaoProperties yiBaoProperties;
    private final MemoryCache memoryCache;
    private final PreRegisterDetailMapper preRegisterDetailMapper;
    private final RegisterListViewMapper registerListViewMapper;
    private final RegInsuranceRegisterMapper regInsuranceRegisterMapper;
    private final MedicalWorkerService medicalWorkerService;
    private final ChargeItemViewMapper chargeItemViewMapper;
    private final SystemTBItemCategoryMapper systemTBItemCategoryMapper;
    private final DrugTbDrugInfomationMapper drugTbDrugInfomationMapper;
    private final PreChargeAmtMapper preChargeAmtMapper;

    @Override
    public SE01Response getDecodeQuery(BusinessType businessType, MedicalInsuranceParam insuranceParam, PayChannelType type) {
        try {
            String ecQrCode = insuranceParam.getEcQrcode();
            if (ecQrCode == null) {
                throw new RuntimeException("医保请求参数不能为空");
            }
            String payAuthNo = insuranceParam.getPayAuthNo();
            EcTokenDTO ectoken = memoryCache.getEctoken(ecQrCode);
            if (ectoken != null) {
                SE01Response se01Response = new SE01Response();
                se01Response.setEcToken(ectoken.getEcToken());
                se01Response.setIdNo(ectoken.getIdNo());
                se01Response.setIdType(ectoken.getIdType());
                se01Response.setUserName(ectoken.getUserName());
                insuranceParam.setEcToken(ectoken.getEcToken());
                insuranceParam.setInsuOrg(ectoken.getInsuOrg());
                return se01Response;
            }

            log.info("获取医保解码字符串开始");
            SE01Request se01 = new SE01Request();
            se01.setOrgId(yiBaoProperties.getDqOrgCode());
            se01.setEcQrCode(ecQrCode);
            String ecQrChannel;
            if (type == PayChannelType.WECHAT_MINI_PROGRAM) {
                ecQrChannel = "2";
            } else {
                throw new RuntimeException("不支持的平台类型, 目前只支持微信小程序");
            }
            String businessTypeStr;
            switch (businessType) {
                case REGISTER:
                    businessTypeStr = "01101";
                    break;
                case CHARGE:
                    businessTypeStr = "01301";
                    break;
                default:
                    throw new RuntimeException("不支持的医保业务类型");
            }
            se01.setBusinessType(businessTypeStr);
            se01.setEcQrChannel(ecQrChannel);
            se01.setTermId(yiBaoProperties.getTermId());
            String termIp = StringUtils.isEmpty(yiBaoProperties.getIp()) ? "127.0.0.1" : yiBaoProperties.getIp();
            se01.setTermIp(termIp);
            se01.setOperatorId(hisHospitalProperties.getOpCode() + "");
            se01.setOperatorName(hisHospitalProperties.getOpName());
//            se01.setOperatorId("9999");
//            se01.setOperatorName("9999");
            Dept dept = ThreadLocalUtils.getDept();
            se01.setOfficeId(dept.getDeptCode());
            se01.setOfficeName(dept.getDeptName());
//            se01.setOfficeId("15");
//            se01.setOfficeName("精神科");
            SHYBBasicRequest<SE01Request> request = new SHYBBasicRequest<>("SE01", se01, AppContext.getInstance(YiBaoProperties.class).getXzqhdm());
            SHYBBasicResponse<SE01Response> response = sendRcv(request, new TypeReference<SHYBBasicResponse<SE01Response>>() {});
            if (!Objects.equals("P001", response.getXxfhm())) {
                log.error("医保调用失败: " + response.getFhxx());
                throw new RuntimeException(response.getSourceString());
            }
            SE01Response se01Response = response.getXxnr();
            ectoken = new EcTokenDTO();
            ectoken.setMsgId(request.getMsgid());
            ectoken.setEcToken(se01Response.getEcToken());
            ectoken.setIdType(se01Response.getIdType());
            ectoken.setUserName(se01Response.getUserName());
            ectoken.setIdNo(se01Response.getIdNo());
            ectoken.setEcQrCode(ecQrCode);
            ectoken.setPayAuthNo(payAuthNo);
            ectoken.setInsuOrg(se01Response.getInsuOrg());
            memoryCache.putEctoken(ecQrCode, ectoken);
            insuranceParam.setEcToken(ectoken.getEcToken());
            insuranceParam.setInsuOrg(ectoken.getInsuOrg());
            return se01Response;
        } catch (Exception ex) {
            log.error("组织解码请求json出错", ex);
            throw new RuntimeException(ex);
        }
    }

    @Override
    public SM01Response getReadAccountStr(MedicalInsuranceParam insuranceParam) {
        SHYBBasicResponse<SM01Response> response = sendRcv(new SHYBBasicRequest<>("SM01",
                new SM01Request("3", insuranceParam.getEcToken()), insuranceParam.getInsuOrg()),
                new TypeReference<SHYBBasicResponse<SM01Response>>() {});
        if (!Objects.equals("P001", response.getXxfhm())) {
            log.error("医保调用失败: " + response.getFhxx());
            throw new RuntimeException(response.getSourceString());
        }
        SM01Response sm01 = response.getXxnr();
        insuranceParam.setAccountAttr(sm01.getAccountattr());
        return sm01;
    }

    @Override
    public SJ11Response postSJ11(MedicalInsuranceParam insuranceParam) {
        Dept dept = ThreadLocalUtils.getDept();
        SJ11Request sj11Request = new SJ11Request();
        sj11Request.setCardtype("3");
        sj11Request.setCarddata(insuranceParam.getEcToken());
        sj11Request.setDeptid(dept.getInsureDeptCode() + "");
        sj11Request.setDjtype("0");
        sj11Request.setDjno("");
        sj11Request.setStartdate(TimeUtils.dateToString(new Date(), "yyyyMMdd"));
        sj11Request.setEnddate("");
        sj11Request.setZdnos(new SJ11Request.Zdnos("Z71.900", "咨询"));
        SHYBBasicResponse<SJ11Response> sj11Response = sendRcv(new SHYBBasicRequest<>("SJ11", sj11Request, insuranceParam.getInsuOrg()),
                new TypeReference<SHYBBasicResponse<SJ11Response>>() {});
        ThreadLocalUtils.setSJ11Response(sj11Response.getXxnr());
        return sj11Response.getXxnr();
    }

    @Override
    public SH01Response postSH01(MedicalInsuranceParam insuranceParam) {
        postSJ11(insuranceParam);
        PreRegisterList preRegisterList = ThreadLocalUtils.getPreRegisterList();
        Dept dept = ThreadLocalUtils.getDept();
        SH01Request sh01Request = new SH01Request();
        sh01Request.setCardtype("3");
        sh01Request.setCarddata(insuranceParam.getEcToken());
        sh01Request.setDeptid(dept.getInsureDeptCode() + "");
        String zlxmdm = preRegisterDetailMapper.getZLXMDM(preRegisterList.getRegNo());
        sh01Request.setZlxmdm(zlxmdm);
        sh01Request.setPersonspectag(getSpecialFlag(insuranceParam.getAccountAttr()));
        // 普精卫目前线上没有急诊,这里只判断是大病还是门诊
        if (StringUtils.isBlank(preRegisterList.getCureCode())) {
            sh01Request.setYllb("S11");
//            sh01Request.setDbtype("");
        } else {
            sh01Request.setYllb("S13");
//            sh01Request.setDbtype(preRegisterList.getCureCode());
        }
        sh01Request.setDbtype(preRegisterList.getCureCode());
        sh01Request.setPersontype("0"); // 线上挂号目前没有工伤业务
        sh01Request.setGsrdh(""); // 线上挂号目前没有工伤业务
        /*
         * 伤残病人普通挂号费全部可报
         */
        List<PreRegisterDetail> regDetails = ThreadLocalUtils.getPreRegisterDetail();
        if ("2".equals(sh01Request.getPersonspectag())) {
            for (PreRegisterDetail regDetail : regDetails) {
                // 理论上要根据挂号级别来，主治或者普通挂号费可以报销, his数据目前没有办法判断挂号级别, 目前是根据挂号费
                if (yiBaoProperties.getDisabilityRegisterFee().equals(regDetail.getPrice())) {
                    regDetail.setExpensePrice(regDetail.getExpensePrice().add(regDetail.getNonExpensePrice()));
                    regDetail.setNonExpensePrice(new BigDecimal(0));
                    regDetail.setExpenseAmout(regDetail.getExpenseAmout().add(regDetail.getNonExpenseAmount()));
                    regDetail.setNonExpenseAmount(new BigDecimal(0));
                }
            }
        }

        /*
         * 医保结算范围费用总额=交易金额=诊疗费  非医保结算范围个人自费=挂号费
         */
        BigDecimal expenseAmount = regDetails.stream()
                .map(p -> DecimalUtil.isNullAsZero(p.getExpenseAmout()))
                .reduce(new BigDecimal(0), BigDecimal::add).setScale(2, RoundingMode.HALF_UP);
        BigDecimal nonExpenseAmount = regDetails.stream()
                .map(p -> DecimalUtil.isNullAsZero(p.getNonExpenseAmount()))
                .reduce(new BigDecimal(0), BigDecimal::add).setScale(2, RoundingMode.HALF_UP);
        BigDecimal regFee = regDetails.stream()
                .filter(p -> ItemCategoryEnum.RegistFee.getCode().equals(p.getItemCatetory()))
                .map(p -> DecimalUtil.isNullAsZero(p.getTotalAmount()))
                .reduce(new BigDecimal(0), BigDecimal::add).setScale(2, RoundingMode.HALF_UP);
        BigDecimal consultationFee = regDetails.stream()
                .filter(p -> ItemCategoryEnum.DisFee.getCode().equals(p.getItemCatetory()))
                .map(p -> DecimalUtil.isNullAsZero(p.getTotalAmount()))
                .reduce(new BigDecimal(0), BigDecimal::add).setScale(2, RoundingMode.HALF_UP);
        sh01Request.setTotalexpense(expenseAmount.toString());
        sh01Request.setYbjsfwfyze(sh01Request.getTotalexpense());
        sh01Request.setZhenlf(consultationFee.toString());
        sh01Request.setGhf(regFee.toString());
        sh01Request.setFybjsfwfyze(nonExpenseAmount.toString());
        // 这个金额计算感觉可能不对
//        sh01Request.setTotalexpense(consultationFee.toString());
//        sh01Request.setYbjsfwfyze(consultationFee.toString());
//        sh01Request.setZhenlf(consultationFee.toString());
//        sh01Request.setGhf(regFee.toString());
//        sh01Request.setFybjsfwfyze(regFee.toString());

        sh01Request.setJmbz("0");
        sh01Request.setXsywlx("1");
        SHYBBasicResponse<SH01Response> response = sendRcv(new SHYBBasicRequest<>("SH01", sh01Request, insuranceParam.getInsuOrg()),
                new TypeReference<SHYBBasicResponse<SH01Response>>() {});
        if (!Objects.equals("P001", response.getXxfhm())) {
            log.error("医保调用失败: " + response.getFhxx());
            throw new RuntimeException(response.getSourceString());
        }
        SH01Response sh01Response = response.getXxnr();
        ThreadLocalUtils.setSH01Request(sh01Request);
        ThreadLocalUtils.setSH01Response(sh01Response);
        return sh01Response;
    }

    @Override
    public SN01Response postSN01(String regNo, Long chargeNo, Long preChargeNo, MedicalInsuranceParam insuranceParam,
                                 List<PreCharge> preCharges) {
        RegInsuranceRegister regInsuranceRegister = regInsuranceRegisterMapper.getByRegNo(Long.parseLong(regNo));
        String jzdyh;
        if (regInsuranceRegister == null) {
            SJ11Response sj11Response = postSJ11(insuranceParam);
            jzdyh = sj11Response.getJzdyh();
        } else {
            jzdyh = regInsuranceRegister.getUnitNo();
        }
        ThreadLocalUtils.setJZDYH(jzdyh);
        SN01Request sn01Request = new SN01Request();
        sn01Request.setCardtype("3");
        sn01Request.setCarddata(insuranceParam.getEcToken());
        sn01Request.setJzdyh(jzdyh);
        sn01Request.setDjh(regNo);
        sn01Request.setMxzdh("");
        /* TODO:
         * 结算类型标志
         * 120：门诊结算
         * 220：急诊结算
         * 410：家床结算
         * 510：急观结算
         * 610：住院结算
         * 门诊大病前两位：32=大病结算
         * 门诊大病后一位：1=化疗；2=放疗；3=血透；4=腹透；6=肾移植抗排异；7=同位素治疗；8=介入治疗；9=中医药治疗；A=精神病；
         * Jslxbz 结算类型标志, 普通门诊和大病区分，字典 case  ChargeType when 45 then '32A' else '120' end  end jslxbz,
         */
        sn01Request.setJslxbz("120");
        BigDecimal totalAmountBigDecimal = new BigDecimal(0);
        List<SN01Request.SN01Item> mxxms = new ArrayList<>();

//        List<PreChargeDetail> details = ThreadLocalUtils.getPreChargeDetails();
        Dept dept = ThreadLocalUtils.getDept();
//        details.forEach(detail -> {
//            BigDecimal otherAmt = detail.getNonExpensePrice().multiply(detail.getQuantity());
//            BigDecimal totalAmount = detail.getTotalAmount();
//
//            PreChargeAmt preChargeAmt = new PreChargeAmt();
//            preChargeAmt.setChargeNo(chargeNo);
//            preChargeAmt.setRegNo(detail.getRegNo());
//            preChargeAmt.setRecipeID(preChargeNo);
//            preChargeAmt.setRecipeDetlID(detail.getRecipeDetlID());
//            preChargeAmt.setDiscountAmount(detail.getDiscountAmount());
//            preChargeAmt.setSelfAmount(totalAmount);
//            preChargeAmt.setClassifyTotal(otherAmt);
//            preChargeAmt.setInsuranceCashAmount(new BigDecimal(0));
//            preChargeAmt.setCreditAmt(new BigDecimal(0));
//            preChargeAmt.setRealAmt(totalAmount.subtract(otherAmt));
//            preChargeAmt.setOtherAmt(otherAmt);
//            preChargeAmt.setInsuranceTradeAmount(preChargeAmt.getRealAmt());
//            preChargeAmt.setJzAmount(new BigDecimal(0));
//            preChargeAmt.setFeeType(detail.getFeeType());
//
//            int i = preChargeAmtMapper.countPreChargeAmt(chargeNo, detail.getRecipeDetlID());
//            if (i > 0) {
//                preChargeAmtMapper.updateById(preChargeAmt);
//            } else {
//                preChargeAmtMapper.insert(preChargeAmt);
//            }
//        });
        for (int i = 0; i < preCharges.size(); i++) {
            PreCharge detail = preCharges.get(i);
            SN01Request.SN01Item item = new SN01Request.SN01Item();
            item.setXh((i + 1));
            item.setCfh(detail.getRecipeDetlID() + "");
            item.setDeptid(dept.getInsureDeptCode() + "");
            item.setKsmc(dept.getDeptName());
            MedicalWorker medicalWorker = medicalWorkerService.getById(detail.getDoctorId());
            if (StringUtils.isBlank(yiBaoProperties.getCfysh())) {
                item.setCfysh(medicalWorker.getFamilyPhone().trim()); // 不知道为什么用这个字段
            } else {
                item.setCfysh(yiBaoProperties.getCfysh());
            }

            if (StringUtils.isBlank(yiBaoProperties.getCfysxm())) {
                item.setCfysxm(medicalWorker.getName());
            } else {
                item.setCfysxm(yiBaoProperties.getCfysxm());
            }
            SystemTBItemCategory systemTBItemCategory = systemTBItemCategoryMapper.selectCategoryByIdAndHospital(detail.getItemCategory(),
                    hisHospitalProperties.getCode());
            item.setFylb(systemTBItemCategory.getYbMzCode().trim());
            ChargeItemView chargeItemView = chargeItemViewMapper.selectChargeItemByItemCode(detail.getItemID().toString());
            item.setMxxmbm(chargeItemView.getCheckCode());

            String mxxmmc = StringUtils.isBlank(chargeItemView.getStandName()) ? chargeItemView.getItemName() : chargeItemView.getStandName();
            String mxxmdw = chargeItemView.getClinicUnit();
            DrugTbDrugInfomation drug = drugTbDrugInfomationMapper.selectOneByDrugIdAndHospitalId(detail.getItemID(), hisHospitalProperties.getCode());
            if (drug != null) {
//                mxxmmc = drug.getSkuCode();
                // case b.HospitalId when 3 then  b.StoreNo else ApprovalNumber end  ,
//                mxxmdw = drug.getApprovalNumber();
                item.setYyclpp(StringUtils.trim(drug.getCommonName()));
                item.setMxxmgg(StringUtils.trim(drug.getRemark()));
            }
            item.setMxxmmc(mxxmmc);
            item.setMxxmdw(mxxmdw);
            item.setMxxmdj(detail.getPrice().setScale(3, RoundingMode.HALF_UP).toString());
            item.setMxxmsl(detail.getQuantiy().setScale(3, RoundingMode.HALF_UP).toString());
            item.setMxxmje(detail.getTotalAmount().setScale(3, RoundingMode.HALF_UP).toString());
            PreChargeAmt preChargeAmt = preChargeAmtMapper.selectOneByChargeNoAndRecipeDetlID(detail.getChargeNo(), detail.getRecipeDetlID());
            item.setMxxmjyfy(preChargeAmt.getInsuranceTradeAmount().setScale(3, RoundingMode.HALF_UP).toString());
            // Mxxmybjsfwfy和his中的存储过程不一样,经过反复测试和向his确认,用InsuranceTradeAmount+ClassifyTotal会导致发票无法开具
            item.setMxxmybjsfwfy(preChargeAmt.getInsuranceTradeAmount().setScale(3, RoundingMode.HALF_UP).toString());
//            item.setMxxmybjsfwfy(preChargeAmt.getInsuranceTradeAmount().add(preChargeAmt.getClassifyTotal()).setScale(3, RoundingMode.HALF_UP).toString());
            item.setZczh("");
            item.setMxxmsyrq(TimeUtils.dateStringFormat(detail.getRecipeOn() == null ? new Date() : detail.getRecipeOn(), "yyyyMMdd"));
            String bxbz = "0";
            if (preChargeAmt.getFeeType() == 9 && !Objects.equals(detail.getItemID(), 17604)) {
                bxbz = "1";
            }
            item.setBxbz(bxbz);
            item.setSftfbz("1");
            item.setJfbz("0");
            item.setSfxfmx("0");
            mxxms.add(item);
            totalAmountBigDecimal = totalAmountBigDecimal.add(detail.getTotalAmount());
        }
        sn01Request.setBcmxylfyze(totalAmountBigDecimal.toString());
        sn01Request.setMxxms(mxxms);
        SHYBBasicResponse<SN01Response> response = sendRcv(new SHYBBasicRequest<>("SN01", sn01Request, insuranceParam.getInsuOrg()),
                new TypeReference<SHYBBasicResponse<SN01Response>>() {});
        if (!Objects.equals("P001", response.getXxfhm())) {
            log.error("医保调用失败: " + response.getFhxx());
            throw new RuntimeException(response.getSourceString());
        }
        ThreadLocalUtils.setSN01Response(response.getXxnr());
        return response.getXxnr();
    }

    @Override
    public void postSE02Registration(String orderNo, MedicalInsuranceParam insuranceParam) {
        RTPatientList patientList = ThreadLocalUtils.getPatient();
        SH01Request sh01Request = ThreadLocalUtils.getSH01Request();
        SH01Response sh01Response = ThreadLocalUtils.getSH01Response();

        SE02Request se02Request = new SE02Request();
        /*
         * 订单号
         * 医院自行生成的唯一ID，生成规则：医疗机构代码（11 位）+yyyyMMddHHmmssSSS+6位随机数字
         */
        se02Request.setOrderNo(orderNo);
        se02Request.setEcToken(insuranceParam.getEcToken());
        se02Request.setPName(patientList.getPatName());
        se02Request.setPIdNo(patientList.getCertificateNo());

        SH02Request sh02Request = new SH02Request();
        sh02Request.setCardtype(sh01Request.getCardtype());
        sh02Request.setCarddata(sh01Request.getCarddata());
        sh02Request.setDeptid(sh01Request.getDeptid());
        sh02Request.setZlxmdm(sh01Request.getZlxmdm());
        sh02Request.setPersonspectag(sh01Request.getPersonspectag());
        sh02Request.setYllb(sh01Request.getYllb());
        sh02Request.setDbtype(sh01Request.getDbtype());
        sh02Request.setPersontype(sh01Request.getPersontype());
        sh02Request.setGsrdh(sh01Request.getGsrdh());
        sh02Request.setJssqxh(sh01Response.getJssqxh());
        sh02Request.setTotalexpense(sh01Request.getTotalexpense());
        sh02Request.setYbjsfwfyze(sh01Request.getYbjsfwfyze());
        sh02Request.setZhenlf(sh01Request.getZhenlf());
        sh02Request.setGhf(sh01Request.getGhf());
        sh02Request.setFybjsfwfyze(sh01Request.getFybjsfwfyze());
        sh02Request.setJmbz(sh01Request.getJmbz());
        sh02Request.setXsywlx(sh01Request.getXsywlx());
        se02Request.setPayRequest(sh02Request);
        SHYBBasicResponse<Void> response = sendRcv(new SHYBBasicRequest<>("SE02", se02Request, insuranceParam.getInsuOrg()),
                new TypeReference<SHYBBasicResponse<Void>>() {});
        if (!Objects.equals("P001", response.getXxfhm())) {
            log.error("医保调用失败: " + response.getFhxx());
            throw new RuntimeException(response.getSourceString());
        }
    }

    @Override
    public void postSE02Charge(String orderNo, MedicalInsuranceParam insuranceParam) {
        RTPatientList patientList = ThreadLocalUtils.getPatient();
        SI11Request si11Request = ThreadLocalUtils.getSI11Request();
        SI11Response si11Response = ThreadLocalUtils.getSI11Response();

        SE02Request se02Request = new SE02Request();
        /*
         * 订单号
         * 医院自行生成的唯一ID，生成规则：医疗机构代码（11 位）+yyyyMMddHHmmssSSS+6位随机数字
         */
        se02Request.setOrderNo(orderNo);
        se02Request.setEcToken(insuranceParam.getEcToken());
        se02Request.setPName(patientList.getPatName());
        se02Request.setPIdNo(patientList.getCertificateNo());

        SI12Request si12Request = new SI12Request();
        si12Request.setCardtype(si11Request.getCardtype());
        si12Request.setCarddata(si11Request.getCarddata());
        si12Request.setDeptid(si11Request.getDeptid()); // his代码中是写死的
        si12Request.setPersonspectag(si11Request.getPersonspectag());
        si12Request.setYllb(si11Request.getYllb());
        si12Request.setPersontype(si11Request.getPersontype());
        si12Request.setGsrdh(si11Request.getGsrdh());
        si12Request.setDbtype(si11Request.getDbtype());
        si12Request.setJsksrq(si11Request.getJsksrq());
        si12Request.setJsjsrq(si11Request.getJsjsrq());
        si12Request.setJzcs(si11Request.getJzcs());
        si12Request.setJzdyh(si11Request.getJzdyh());
        si12Request.setXsywlx(si11Request.getXsywlx());
        si12Request.setJssqxh(si11Response.getJssqxh());
        si12Request.setZdnos(si11Request.getZdnos().stream().map(u -> {
            SI12Request.Disease disease = new SI12Request.Disease();
            disease.setZdmc(u.getZdmc());
            disease.setZdno(u.getZdno());
            return disease;
        }).collect(Collectors.toList()));
        si12Request.setMxzdhs(si11Request.getMxzdhs().stream().map(u -> {
            SI12Request.Detail detail = new SI12Request.Detail();
            detail.setMxzdh(u.getMxzdh());
            detail.setTotalexpense(u.getTotalexpense());
            detail.setYbjsfwfyze(u.getYbjsfwfyze());
            detail.setFybjsfwfyze(u.getFybjsfwfyze());
            return detail;
        }).collect(Collectors.toList()));
        se02Request.setPayRequest(si12Request);
        SHYBBasicResponse<Void> response = sendRcv(new SHYBBasicRequest<>("SE02", se02Request, insuranceParam.getInsuOrg()),
                new TypeReference<SHYBBasicResponse<Void>>() {});
        if (!Objects.equals("P001", response.getXxfhm())) {
            log.error("医保调用失败: " + response.getFhxx());
            throw new RuntimeException(response.getSourceString());
        }
    }


    @Override
    public SE04Response postSE04(String ecToken, String insuOrg) {
        SE04Request se04Request = new SE04Request();
        se04Request.setEcToken(ecToken);
        SHYBBasicResponse<SE04Response> response = sendRcv(new SHYBBasicRequest<>("SE04", se04Request, insuOrg),
                new TypeReference<SHYBBasicResponse<SE04Response>>() {});
        if (!Objects.equals("P001", response.getXxfhm())) {
            log.error("医保调用失败: " + response.getFhxx());
            return null;
        }
        return response.getXxnr();
    }

    @Override
    public SHYBBasicResponse<SK01Response> postSK01(SK01Request sk01Request, MedicalInsuranceParam insuranceParam) {
        SHYBBasicResponse<SK01Response> response = sendRcv(new SHYBBasicRequest<>("SK01", sk01Request, insuranceParam.getInsuOrg()),
                new TypeReference<SHYBBasicResponse<SK01Response>>() {});
        return response;
    }

    @Override
    public SI11Response postSI11(String regNo, MedicalInsuranceParam insuranceParam) {
        Dept dept = ThreadLocalUtils.getDept();
        List<Diagnose> diagnoses = ThreadLocalUtils.getDiagnoses();
        SI11Request si11Request = new SI11Request();
        si11Request.setCardtype("3");
        si11Request.setCarddata(insuranceParam.getEcToken());
        si11Request.setDeptid(dept.getInsureDeptCode() + "");
        si11Request.setPersonspectag(getSpecialFlag(insuranceParam.getAccountAttr()));
        RegisterListView registerList = registerListViewMapper.selectByRegNo(Long.parseLong(regNo));
        // 普精卫目前线上没有急诊,这里只判断是大病还是门诊
        if (StringUtils.isEmpty(registerList.getCureCode())) {
            si11Request.setYllb("S11");
        } else {
            si11Request.setYllb("S13");
        }
        si11Request.setDbtype(registerList.getCureCode());
        si11Request.setPersontype("0"); // 线上挂号目前没有工伤业务
        si11Request.setGsrdh(""); // 线上挂号目前没有工伤业务
        si11Request.setJsjsrq("");
        si11Request.setJsksrq("");
        si11Request.setJzdyh(ThreadLocalUtils.getJZDYH());
        si11Request.setXsywlx("1");
        si11Request.setZdnos(diagnoses.stream().map(u -> {
            SI11Request.Disease disease = new SI11Request.Disease();
            disease.setZdno(StringUtils.trim(u.getZdbm()));
            disease.setZdmc(StringUtils.trim(u.getZdmc()));
            return disease;
        }).collect(Collectors.toList()));
        SN01Response sn01Response = ThreadLocalUtils.getSN01Response();
        List<SN01Response.SN01Item> mxxms = sn01Response.getMxxms();
        SI11Request.Detail detail = new SI11Request.Detail();
        detail.setMxzdh(sn01Response.getMxzdh());
        detail.setTotalexpense(mxxms.stream().map(SN01Response.SN01Item::getMxxmjyfy)
                .reduce(BigDecimal.ZERO, BigDecimal::add));
        detail.setYbjsfwfyze(mxxms.stream().map(SN01Response.SN01Item::getMxxmybjsfwfy)
                .reduce(BigDecimal.ZERO, BigDecimal::add));
        detail.setFybjsfwfyze(mxxms.stream().map(SN01Response.SN01Item::getMxxmje)
                .reduce(BigDecimal.ZERO, BigDecimal::add).subtract(detail.getYbjsfwfyze()));
        si11Request.setMxzdhs(Lists.newArrayList(detail));
        ThreadLocalUtils.setSI11Request(si11Request);
        SHYBBasicResponse<SI11Response> response = sendRcv(new SHYBBasicRequest<>("SI11", si11Request, insuranceParam.getInsuOrg()),
                new TypeReference<SHYBBasicResponse<SI11Response>>() {});
        if (!Objects.equals("P001", response.getXxfhm())) {
            log.error("医保调用失败: " + response.getFhxx());
            throw new RuntimeException(response.getSourceString());
        }
        ThreadLocalUtils.setSI11Response(response.getXxnr());
        return response.getXxnr();
    }

    /**
     * 根据账户获取特殊人员标识
     *
     * @param accountFlag
     * @return
     */
    private String getSpecialFlag(String accountFlag) {
        // 目前,只有工伤, 干部保健对象, 民政医疗帮困, 医疗互助帮困对象走上海医保
        // 慢特病, 大病变更为走国家医保
        if (accountFlag!=null && accountFlag.trim().length() == 16) {
            String bjqk = accountFlag.substring(1, 2);
            String tsdy = accountFlag.substring(11, 12);
            //特殊用户0 是特殊  1是正常
            if ("1".equals(bjqk) || "G".equals(tsdy) || "A".equals(tsdy)) {
                return "0";
            }
            return "0";
        }
        return "1";
    }

    /**
     * 调用5期医保接口
     * @param request
     * @return
     */
    public <T, E> SHYBBasicResponse<T> sendRcv(SHYBBasicRequest<E> request, TypeReference<SHYBBasicResponse<T>> returnTypeReference) {
        String json = StandardObjectMapper.stringify(request);
        log.info(request.getXxlxm() + "接口入参: " + json);
        String result = Call.SendRcv(json);
        log.info(request.getXxlxm() + "接口出参: " + result);
        SHYBBasicResponse<T> response = StandardObjectMapper.readValue(result, returnTypeReference);
        response.setSourceString(result);
        return response;
    }

}
