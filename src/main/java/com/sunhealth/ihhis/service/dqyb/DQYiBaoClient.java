package com.sunhealth.ihhis.service.dqyb;

import com.sunhealth.ihhis.enums.BusinessType;
import com.sunhealth.ihhis.enums.PayChannelType;
import com.sunhealth.ihhis.model.dq.sh.insurance.request.SK01Request;
import com.sunhealth.ihhis.model.dq.sh.insurance.response.*;
import com.sunhealth.ihhis.model.entity.charge.PreCharge;
import com.sunhealth.ihhis.model.insurance.MedicalInsuranceParam;

import java.util.List;

/**
 * 封装okhttpClient与国家医保的api交互
 */
public abstract class DQYiBaoClient {

    /**
     * 解码
     * @param insuranceParam
     * @return
     */
    public abstract SE01Response getDecodeQuery(BusinessType businessType, MedicalInsuranceParam insuranceParam, PayChannelType type);

    /**
     * 组织读账户请求
     * @param insuranceParam
     * @return
     */
    public abstract SM01Response getReadAccountStr(MedicalInsuranceParam insuranceParam);

    /**
     * sj11接口
     * @param insuranceParam
     * @return
     */
    public abstract SJ11Response postSJ11(MedicalInsuranceParam insuranceParam);

    /**
     * sh01接口
     * @param insuranceParam
     * @return
     */
    public abstract SH01Response postSH01(MedicalInsuranceParam insuranceParam);

    /**
     * sn01接口
     * @param regNo
     * @param insuranceParam
     * @return
     */
    public abstract SN01Response postSN01(String regNo, Long chargeNo, Long preChargeNo, MedicalInsuranceParam insuranceParam,
                                          List<PreCharge> preCharges);

    /**
     * se02接口, 挂号
     * @param orderNo 5期医保订单号
     * @param insuranceParam
     */
    public abstract void postSE02Registration(String orderNo, MedicalInsuranceParam insuranceParam);

    /**
     * se02接口, 门诊缴费
     * @param orderNo
     * @param insuranceParam
     */
    public abstract void postSE02Charge(String orderNo, MedicalInsuranceParam insuranceParam);

    /**
     * se04接口
     * @param ecToken
     * @return
     */
    public abstract SE04Response postSE04(String ecToken, String insuOrg);

    /**
     * sk01接口
     * @param sk01Request
     * @return
     */
    public abstract SHYBBasicResponse<SK01Response> postSK01(SK01Request sk01Request, MedicalInsuranceParam insuranceParam);

    /**
     * si11接口
     * @param regNo
     * @param insuranceParam
     * @return
     */
    public abstract SI11Response postSI11(String regNo, MedicalInsuranceParam insuranceParam);
}