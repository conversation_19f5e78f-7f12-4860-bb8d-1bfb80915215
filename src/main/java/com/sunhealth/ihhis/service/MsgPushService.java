package com.sunhealth.ihhis.service;

import com.sunhealth.ihhis.common.JsonResponse;
import com.sunhealth.ihhis.model.dto.push.HisQRCodeRequest;
import com.sunhealth.ihhis.model.dto.push.HisQRCodeResponse;
import com.sunhealth.ihhis.model.dto.push.MsgPushReq;
import com.sunhealth.ihhis.model.dto.push.MsgPushRes;

public interface MsgPushService {

    /**
     * 推送4010医保挂号结算成功的消息
     * @param hospitalCode
     * @param regNo
     * @param payOrderId
     * @param traceTime 交易时间
     */
    void pushRegister4010Succeeded(String hospitalCode, String regNo, String payOrderId, String billNo, String traceTime);

    /**
     * 推送4010医保门诊缴费结算成功的消息
     * @param hospitalCode
     * @param regNo
     * @param chargeNo
     * @param payOrderId
     * @param traceTime
     */
    void pushCharge4010Succeeded(String hospitalCode, String regNo, String chargeNo, String payOrderId, String billNo, String traceTime);

    /**
     * 推送医疗信息消息
     */
    MsgPushRes pushPatientMsg(MsgPushReq req, String hospitalCode);

    /**
     * 生成患者服务平台页面二维码
     * @param qrCodeRequest
     * @return
     */
    JsonResponse<HisQRCodeResponse> bQRCode(HisQRCodeRequest qrCodeRequest);
}
