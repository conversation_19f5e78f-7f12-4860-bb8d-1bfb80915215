package com.sunhealth.ihhis.service;

import com.sunhealth.ihhis.model.dto.outpatientcharge.OutpatientRecipeInfo;
import com.sunhealth.ihhis.model.dto.outpatientcharge.OutpatientRecipeReq;
import com.sunhealth.ihhis.model.dto.recipe.OnlineRecipeDeleteRequest;
import com.sunhealth.ihhis.model.dto.recipe.OnlineRecipeDeleteResponse;
import com.sunhealth.ihhis.model.dto.recipe.OnlineRecipeRequest;
import com.sunhealth.ihhis.model.dto.recipe.OnlineRecipeResponse;

import java.util.List;

public interface RecipeService {

    List<OutpatientRecipeInfo> getOutpatientRecipes(OutpatientRecipeReq req);

    OnlineRecipeResponse saveRecipeOnline(OnlineRecipeRequest req);

    OnlineRecipeDeleteResponse deleteRecipeOnline(OnlineRecipeDeleteRequest req);
}
