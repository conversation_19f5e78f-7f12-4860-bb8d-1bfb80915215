package com.sunhealth.ihhis.service;

import com.fasterxml.jackson.core.type.TypeReference;
import com.google.common.collect.Maps;
import com.sunhealth.ihhis.error.ErrorType;
import com.sunhealth.ihhis.model.dto.report.LisReport;
import com.sunhealth.ihhis.model.dto.report.LisReportResult;
import com.sunhealth.ihhis.model.dto.report.ReportListReq;
import com.sunhealth.ihhis.model.dto.report.ReportResultReq;
import com.sunhealth.ihhis.model.dto.report.RisReport;
import com.sunhealth.ihhis.model.dto.report.RisReportResult;
import com.sunhealth.ihhis.model.thirdapi.ThirdApiResponse;
import com.sunhealth.ihhis.utils.OkHttpUtils;
import com.sunhealth.ihhis.utils.StandardObjectMapper;
import java.io.IOException;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import okhttp3.Headers;
import okhttp3.Response;
import org.springframework.stereotype.Component;

@Slf4j
@Component
@AllArgsConstructor
public class ThirdApiClient {

    public List<RisReport> getXDTReports(String url, ReportListReq req) {
        return postRequest(url, null, null, req,
                           new TypeReference<ThirdApiResponse<List<RisReport>>>() {});
    }

    public List<RisReportResult> getXDTReportResult(String url, ReportResultReq req) {
        return postRequest(url, null, null, req,
                           new TypeReference<ThirdApiResponse<List<RisReportResult>>>(){});
    }

    public List<LisReport> getLisReports(String url, ReportListReq req) {
        return postRequest(url, null, null, req,
                           new TypeReference<ThirdApiResponse<List<LisReport>>>(){});
    }

    public List<LisReportResult> getLisReportResult(String url, ReportResultReq req) {
        return postRequest(url, null, null, req,
                           new TypeReference<ThirdApiResponse<List<LisReportResult>>>(){});
    }

    /**
     * 发送POST请求到第三方API
     *
     * @param url                 请求地址
     * @param headers             请求头
     * @param params              请求参数
     * @param body                请求体
     * @param returnTypeReference 返回类型
     * @param <T>                 返回类型
     * @return 返回响应数据或null
     */
    private <T> T postRequest(String url, Map<String, String> headers,
                                                    Map<String, String> params, Object body,
                                                    TypeReference<ThirdApiResponse<T>> returnTypeReference) {
        Map<String, String> requestHeaders = Optional.ofNullable(headers).orElseGet(Maps::newHashMap);
        String bodyStr = (body instanceof String) ? (String) body : StandardObjectMapper.stringify(body);

        log.info("发送请求到第三方接口，URL: {}, Headers: {}, Params: {}, Body: {}", url, requestHeaders, params, body);

        try (Response response = OkHttpUtils.post(url, Headers.of(requestHeaders), params, bodyStr)) {
            return handleResponse(response, returnTypeReference);
        } catch (IOException e) {
            log.error("请求失败 (URL: {}): {}", url, e.getMessage(), e);
            throw ErrorType.INTERNAL_SERVER_ERROR.toProblem("请求失败 (URL: " + url + "): " + e.getMessage());
        }
    }

    /**
     * 处理响应结果
     *
     * @param response            响应对象
     * @param returnTypeReference 返回类型引用
     * @param <T>                 返回类型
     * @return 返回解析后的响应数据
     * @throws IOException 处理错误时抛出
     */
    private <T> T handleResponse(Response response, TypeReference<ThirdApiResponse<T>> returnTypeReference) throws IOException {
        if (!response.isSuccessful()) {
            String errorResponse = OkHttpUtils.getResponseBody(response).orElse("无响应内容");
            log.info("第三方API请求失败，HTTP状态码: {}, URL: {}, 响应内容: {}", response.code(), response.request().url(),
                  errorResponse);
            throw ErrorType.INTERNAL_SERVER_ERROR.toProblem("API请求失败 (HTTP状态码: " + response.code() + "): " + errorResponse);
        }

        String responseString = OkHttpUtils.getResponseBody(response).orElse(null);
        log.info("第三方API响应内容：{}", responseString);

        if (returnTypeReference == null || responseString == null) {
            return null;
        }

        ThirdApiResponse<T> apiResponse = StandardObjectMapper.readValue(responseString, returnTypeReference);

        if (apiResponse.isSuccess()) {
            if (apiResponse.getData() != null) {
                return apiResponse.getData().getContent();
            } else {
                return null;
            }
        } else {
            log.info("API调用成功但业务返回错误，错误代码: {}，错误信息: {}", apiResponse.getCode(), apiResponse.getMsg());
            return null;
        }
    }
}
