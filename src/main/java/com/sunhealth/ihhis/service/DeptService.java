package com.sunhealth.ihhis.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.sunhealth.ihhis.model.dto.DeptInfo;
import com.sunhealth.ihhis.model.dto.DictDataQueryReq;
import com.sunhealth.ihhis.model.entity.Dept;
import java.util.List;

public interface DeptService extends IService<Dept> {
    Page<DeptInfo> findAll(DictDataQueryReq dictDataQueryReq);
}
