package com.sunhealth.ihhis.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.sunhealth.ihhis.model.dto.patient.HisPatientInfoReq;
import com.sunhealth.ihhis.model.dto.patient.HisPatientInfoRes;
import com.sunhealth.ihhis.model.dto.patient.PatientInfoUpdateReq;
import com.sunhealth.ihhis.model.dto.patient.PatientInfoUpdateRes;
import com.sunhealth.ihhis.model.entity.patient.RTPatientList;

import java.util.List;

public interface PatientListService extends IService<RTPatientList> {

    /**
     * 这个是测试方法
     * @return
     */
    @Deprecated
    List<RTPatientList> selectAllDemo();

    /**
     * 这个是测试方法
     * @return
     */
    @Deprecated
    Page<RTPatientList> selectPageDemo();

    /**
     * 获取就诊人详情
     * @param request
     * @return
     */
    List<HisPatientInfoRes> getPatientInfo(HisPatientInfoReq request);

    /**
     * 更新患者基本信息
     * @param request
     * @return
     */
    PatientInfoUpdateRes updatePatientInfo(PatientInfoUpdateReq request);

    /**
     * 根据身份证查询患者信息
     * @param hospitalCode
     * @param certificateNo
     * @return
     */
    RTPatientList getPatientListByCertificateNo(String hospitalCode, String certificateNo);
}
