package com.sunhealth.ihhis.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.google.common.collect.Lists;
import com.sunhealth.ihhis.config.HisHospitalProperties;
import com.sunhealth.ihhis.dao.his.InPatientMapper;
import com.sunhealth.ihhis.dao.his.InPatientPreChargeLogMapper;
import com.sunhealth.ihhis.dao.his.InPatientPreChargeMapper;
import com.sunhealth.ihhis.dao.his.ZfbJyMapper;
import com.sunhealth.ihhis.model.dto.inpatient.*;
import com.sunhealth.ihhis.model.entity.inpatient.InPatient;
import com.sunhealth.ihhis.model.entity.inpatient.PreCharge;
import com.sunhealth.ihhis.model.entity.inpatient.PreChargeLog;
import com.sunhealth.ihhis.model.entity.inpatient.ZfbJy;
import com.sunhealth.ihhis.model.vm.PushInpatientGuanKongMessageParam;
import com.sunhealth.ihhis.service.InpatientService;
import com.sunhealth.ihhis.service.processer.PreLogMaxIdProcessor;
import com.sunhealth.ihhis.service.processer.PreMaxIdProcessor;
import com.sunhealth.ihhis.task.scheduler.InpatientFlushGuanKongScheduler;
import com.sunhealth.ihhis.utils.DecimalUtil;
import com.sunhealth.ihhis.utils.TimeUtils;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

@Slf4j
@Service
@AllArgsConstructor
public class InpatientServiceImpl implements InpatientService {

    private final PreMaxIdProcessor preMaxIdProcessor;
    private final PreLogMaxIdProcessor preLogMaxIdProcessor;

    private final InPatientMapper inpatientMapper;
    private final InPatientPreChargeMapper preChargeMapper;
    private final InPatientPreChargeLogMapper preChargeLogMapper;
    private final ZfbJyMapper zfbJyMapper;
    private final HisHospitalProperties hisHospitalProperties;

    @Override
    public List<InpatientRecordRes> getInpatientRecord(InpatientRecordReq req) {
        req.setBeginDate(TimeUtils.convert(req.getBegin_date()));
        req.setEndDate(TimeUtils.getEndOfDay(TimeUtils.convert(req.getEnd_date())));
        List<InpatientRecord> inpatientRecordList = inpatientMapper.listInpatientRecord(req);
        return inpatientRecordList.stream().map(InpatientRecordRes::new).collect(Collectors.toList());
    }

    @Override
    public List<AdvanceChargeDetailRes> getInpatientAdvanceChargeDetail(AdvanceChargeDetailReq req) {
        List<AdvanceChargeDetail> advanceChargeDetails = inpatientMapper.listAdvanceChargeDetail(req);
        return advanceChargeDetails.stream().map(AdvanceChargeDetailRes::new).collect(Collectors.toList());
    }

    @Override
    public HospCardPreChargeRes inpatientHospCardPreCharge(HospCardPreChargeReq req) {
        // 生成预交金流水号
        Long preMaxId = preMaxIdProcessor.execute();
        Date now = new Date();
        // 查询住院患者
        InPatient patient = patientByRegNo(req.getRegno(), req.getPatname(), req.getHospitalCode());
        // 预充值时初始化记录
        PreCharge preCharge = initPreCharge(preMaxId, now, patient);
        preChargeMapper.insert(preCharge);

        return HospCardPreChargeRes.success(preMaxId + "",preMaxId + "");
    }

    @Override
    @Transactional
    public HospCardChargeRes inpatientHospCardCharge(HospCardChargeReq req) {
        LambdaQueryWrapper<PreCharge> wrapper = Wrappers.lambdaQuery();
        wrapper.eq(PreCharge::getPreChargeId, req.getAdvance_charge_id())
                .eq(PreCharge::getHospitalId, req.getHospitalCode());
        PreCharge preCharge = preChargeMapper.selectList(wrapper).stream().findFirst().orElse(null);

        if (Objects.isNull(preCharge)) {
            log.info("预交金充值-根据PreCharge未找到对应的记录");
            return HospCardChargeRes.fail(req.getAdvance_charge_id(), req.getOut_trade_no());
        }
        // 金额分转换成元
        BigDecimal amount = new BigDecimal(req.getSelf_amount());
        amount = amount.divide(BigDecimal.valueOf(100));
        preCharge.setAmount(amount);
        // 支付方式
        preCharge.setPayType(getPreChargePayType(req.getPay_type()));
        // 来源
        preCharge.setOrigin(getOrigin(req.getPay_type()));
        preCharge.setPayTime(TimeUtils.convert(req.getPay_time()));
        preCharge.setPayTradeNo(req.getSerial_no());
        // 0-新增(待审核），1-有效
        preCharge.setBillStatus(1);
        preCharge.setVerifyUserId(-1);
        preCharge.setVerifyOn(new Date());

        preChargeMapper.updateById(preCharge);

        PreChargeLog preChargeLog = new PreChargeLog(preCharge);
        preChargeLog.setOpType("新增预交金");
        preChargeLog.setOpTime(new Date());
        preChargeLog.setOpUserId(-1);
        Long preLogId = preLogMaxIdProcessor.execute();
        preChargeLog.setPreChargeLogId(preLogId);
        preChargeLogMapper.insert(preChargeLog);

        // 查询住院患者
        InPatient patient = patientByRegNo(req.getRegno(), req.getPatname(), req.getHospitalCode());
        // 交易记录
        ZfbJy zfbJy = initZfbJy(req, patient.getRegNo(), preCharge.getHospNo(), patient.getCardNo(), preCharge.getHospTradeNo());
        zfbJyMapper.insert(zfbJy);

        // 刷新住院患者预交金管控状态
        InpatientFlushGuanKongScheduler.delayTask(new PushInpatientGuanKongMessageParam(Lists.newArrayList(patient.getRegNo()),
                                                                                        Long.valueOf(patient.getHospitalId()),
                                                                                        (long) hisHospitalProperties.getOpCode()));

        String totalAmount = getAdvanceAccountAmount(patient.getRegNo(), req.getHospitalCode());
        return HospCardChargeRes.success(req.getAdvance_charge_id(), totalAmount, req.getOut_trade_no());
    }

    /**
     * 根据住院号查询患者信息（在院状态）
     * 状态
     * 0.无效
     * 1.待入区
     * 2：在区
     * 3：待出院
     * 4：中期离区
     * 5：转区
     * 9：出院
     * 1.2、3、4、5 为在院状态
     */
    private InPatient patientByRegNo(String regNo, String patName, String hospitalCode) {
        List<Integer> inHospStatus = Arrays.asList(1, 2, 3, 4, 5);
        LambdaQueryWrapper<InPatient> wrapper = Wrappers.lambdaQuery();
        wrapper.eq(InPatient::getHospNo, regNo)
                .eq(InPatient::getPatName, patName)
                .in(InPatient::getStatus, inHospStatus)
                .eq(InPatient::getHospitalId, hospitalCode)
                .orderByDesc(InPatient::getInTime);
        return inpatientMapper.selectList(wrapper)
                .stream().findFirst().orElse(null);
    }

    private PreCharge initPreCharge(Long preChargeId, Date time, InPatient patient) {
        PreCharge preCharge = new PreCharge();
        preCharge.setPreChargeId(preChargeId);
        preCharge.setRegNo(patient.getRegNo());
        preCharge.setNewRegNo(patient.getRegNo());
        preCharge.setHospNo(patient.getHospNo());
        preCharge.setPatId(patient.getPatId());
        // 生成HIS订单号
        String hospTradeNo = generateOutTradeNo(preChargeId, time);
        preCharge.setHospTradeNo(hospTradeNo);
        preCharge.setCreateUserId(hisHospitalProperties.getOpCode());
        preCharge.setCreateOn(time);
        // 0新增（待审核） 1有效
        preCharge.setBillStatus(0);
        // 支付类型 8-移动支付
        preCharge.setPayType(8);
        preCharge.setCheckStatus(false);
        preCharge.setPrintStatus(false);
        preCharge.setIsDelete(false);
        preCharge.setIsAccounts(0);
        /*
         * Origin 费用来源
         * 1-窗口
         * 2-便民（微信公众号）
         * 3-线上
         * 4-微信小程序
         * 5-支付宝小程序
         */
        preCharge.setOrigin(3);
        // 预交金用途 2代表预交金
        preCharge.setChargeType(2);
        // 费用组成 2代表个人
        preCharge.setSource(2);
        // 数据来源
        preCharge.setDataSources(1);
        preCharge.setHospitalId(patient.getHospitalId());
        return preCharge;
    }

    private ZfbJy initZfbJy(HospCardChargeReq req, Long regNo, String hospNo, String cardNo, String hospTradeNo) {
        ZfbJy zfbJy = new ZfbJy();
        zfbJy.setPreChargeId(Long.valueOf(req.getAdvance_charge_id()));
        zfbJy.setRegNo(regNo);
        zfbJy.setPatName(req.getPatname());
        // 金额分转换成元
        BigDecimal amount = new BigDecimal(req.getSelf_amount());
        zfbJy.setRealAmount(amount.divide(BigDecimal.valueOf(100)));
        zfbJy.setHospTradeNo(hospTradeNo);
        zfbJy.setZfbTradeNo(req.getTrade_no());
        zfbJy.setJyTime(new Date());
        zfbJy.setHisCardNo(hospNo);
        zfbJy.setCardNo(cardNo);
        zfbJy.setStatus(0);
        zfbJy.setPayType(getJyPayType(req.getPay_type()) + "");
        zfbJy.setDataType(1);
        zfbJy.setCreateUserId(hisHospitalProperties.getOpCode());
        zfbJy.setCreateOn(new Date());
        zfbJy.setUpdateUserId(hisHospitalProperties.getOpCode());
        zfbJy.setUpdateOn(new Date());
        zfbJy.setHospitalId(Integer.valueOf(req.getHospitalCode()));
        return zfbJy;
    }

    private String getAdvanceAccountAmount(Long regNo, String hospitalCode) {
        LambdaQueryWrapper<PreCharge> wrapper = Wrappers.lambdaQuery();
        wrapper.eq(PreCharge::getRegNo, regNo)
                .eq(PreCharge::getHospitalId, hospitalCode)
                .eq(PreCharge::getChargeType, 2)
                .eq(PreCharge::getBillStatus, 1)
                .eq(PreCharge::getIsDelete, false);
        List<PreCharge> preCharges = preChargeMapper.selectList(wrapper);
        BigDecimal amount = preCharges.stream().map(PreCharge::getAmount)
                .filter(Objects::nonNull)
                .reduce(BigDecimal.ZERO, BigDecimal::add);
        return DecimalUtil.defaultString(amount);
    }

    /**
     * His订单号生成规则
     *  eg: ******************************
     *  前4位供应商,（这里指互联网医院）
     *  第五位院区 F-分院、Z-总院
     *  第六位业务 A-挂号、B-收费、C-自助机（上海银行）
     *  14位时间
     *  1位分隔符A
     *  9位业务流水号
     */
    private String generateOutTradeNo(Long preChargeId, Date date) {
        // 订单号规则IH指互联网医院
        String supplier = "IH00";
        String time = TimeUtils.dateStringFormat(date, "yyyyMMddHHmmss");
        return String.format("%sZB%sA%s", supplier, time, preChargeId);
    }

    /**
     * 支付方式ih->his
     * ih: 1-微信小程序 2-支付宝 3-微信公众号
     * his: 11-支付宝小程序 17-微信小程序 13微信公众号
     */
    private Integer getJyPayType(String payType) {
        if ("1".equals(payType)) {
            return 17;
        }
        if ("2".equals(payType)) {
            return 11;
        }
        if ("3".equals(payType)) {
            return 13;
        }
        // 8-移动支付
        return 8;
    }

    private Integer getOrigin(String payType) {
        if ("1".equals(payType)) {
            // 微信小程序
            return 4;
        }
        if ("2".equals(payType)) {
            // 支付宝小程序
            return 5;
        }
        if ("3".equals(payType)) {
            // 微信公众号
            return 2;
        }
        // 3-线上
        return 3;
    }

    private Integer getPreChargePayType(String payType) {
        if ("1".equals(payType) || "3".equals(payType)) {
            // 微信
            return 6;
        }
        if ("2".equals(payType)) {
            // 支付宝
            return 4;
        }
        // 8-移动支付
        return 8;
    }

}
