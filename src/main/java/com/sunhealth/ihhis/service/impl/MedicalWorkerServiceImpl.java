package com.sunhealth.ihhis.service.impl;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.sunhealth.ihhis.dao.his.MedicalWorkerMapper;
import com.sunhealth.ihhis.model.dto.DictDataQueryReq;
import com.sunhealth.ihhis.model.dto.DoctorInfo;
import com.sunhealth.ihhis.model.entity.MedicalWorker;
import com.sunhealth.ihhis.service.MedicalWorkerService;
import org.springframework.stereotype.Service;

@Service
public class MedicalWorkerServiceImpl extends ServiceImpl<MedicalWorkerMapper, MedicalWorker> implements
    MedicalWorkerService {

    @Override
    public Page<DoctorInfo> findAll(DictDataQueryReq req) {
        Page<DoctorInfo> page = new Page<>(req.getPage(), req.getSize());
        return baseMapper.selectPageDoctor(page, 0, Integer.valueOf(req.getHospitalCode()), req.getDept_id(), req.getChannel_type());
    }

    @Override
    public MedicalWorker getById(Integer workerId) {
        if (workerId != null) {
            return baseMapper.selectById(workerId);
        }
        return null;
    }
}
