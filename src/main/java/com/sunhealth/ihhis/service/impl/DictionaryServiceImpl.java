package com.sunhealth.ihhis.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.sunhealth.ihhis.dao.his.DictionaryMapper;
import com.sunhealth.ihhis.model.entity.TBDicHisDictionary;
import com.sunhealth.ihhis.service.DictionaryService;
import org.springframework.stereotype.Service;

@Service
public class DictionaryServiceImpl extends ServiceImpl<DictionaryMapper, TBDicHisDictionary> implements DictionaryService {

    @Override
    public TBDicHisDictionary getDictByName(Integer typeId, Integer hospitalId, String name) {
        LambdaQueryWrapper<TBDicHisDictionary> wrapper = Wrappers.lambdaQuery();
        wrapper.eq(TBDicHisDictionary::getDictionaryTypeID, typeId)
                .eq(TBDicHisDictionary::getHospitalId, hospitalId)
                .eq(TBDicHisDictionary::getIsUse, 1)
                .eq(TBDicHisDictionary::getHisDictionaryName, name);
        return baseMapper.selectList(wrapper).stream().findFirst().orElse(null);
    }

    @Override
    public TBDicHisDictionary getDictByCode(Integer typeId, Integer hospitalId, Integer code) {
        LambdaQueryWrapper<TBDicHisDictionary> wrapper = Wrappers.lambdaQuery();
        wrapper.eq(TBDicHisDictionary::getDictionaryTypeID, typeId)
                .eq(TBDicHisDictionary::getHospitalId, hospitalId)
                .eq(TBDicHisDictionary::getIsUse, 1)
                .eq(TBDicHisDictionary::getHisDictionaryCode, code);
        return baseMapper.selectList(wrapper).stream().findFirst().orElse(null);
    }
}
