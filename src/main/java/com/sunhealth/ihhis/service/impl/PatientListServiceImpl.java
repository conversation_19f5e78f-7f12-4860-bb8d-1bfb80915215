package com.sunhealth.ihhis.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.OrderItem;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.sunhealth.ihhis.common.Constants;
import com.sunhealth.ihhis.dao.his.PatientDetlMapper;
import com.sunhealth.ihhis.dao.his.PatientListMapper;
import com.sunhealth.ihhis.enums.Gender;
import com.sunhealth.ihhis.enums.PatientCardTypeEnum;
import com.sunhealth.ihhis.model.dto.patient.*;
import com.sunhealth.ihhis.model.entity.SystemTBChargeType;
import com.sunhealth.ihhis.model.entity.TBDicHisDictionary;
import com.sunhealth.ihhis.model.entity.patient.RTPatientCard;
import com.sunhealth.ihhis.model.entity.patient.RTPatientDetl;
import com.sunhealth.ihhis.model.entity.patient.RTPatientList;
import com.sunhealth.ihhis.service.ChargeTypeService;
import com.sunhealth.ihhis.service.DictionaryService;
import com.sunhealth.ihhis.service.PatientCardService;
import com.sunhealth.ihhis.service.PatientListService;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Comparator;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

@Slf4j
@Service
@AllArgsConstructor
public class PatientListServiceImpl extends ServiceImpl<PatientListMapper, RTPatientList> implements PatientListService {

    private final ChargeTypeService chargeTypeService;

    private final DictionaryService dictionaryService;

    private final PatientCardService patientCardService;

    private final PatientListMapper patientListMapper;

    private final PatientDetlMapper patientDetlMapper;

    @Override
    public List<RTPatientList> selectAllDemo() {
        LambdaQueryWrapper<RTPatientList> wrapper = Wrappers.lambdaQuery();
        wrapper.orderByAsc(RTPatientList::getPatId);
        wrapper.last("OFFSET 0 ROWS FETCH NEXT 10 ROWS ONLY");
        return baseMapper.selectList(wrapper);
    }

    @Override
    public Page<RTPatientList> selectPageDemo() {
//        LambdaQueryWrapper<RTPatientList> wrapper = Wrappers.lambdaQuery();
//        wrapper.orderByAsc(RTPatientList::getPatId);
        Page<RTPatientList> page = new Page<>(2, 10);
        page.addOrder(OrderItem.asc("PatID"));
        return baseMapper.selectPage(page, null);
    }

    @Override
    public List<HisPatientInfoRes> getPatientInfo(HisPatientInfoReq request) {
        List<HisPatientInfo> patientInfoList = baseMapper.getPatientInfo(request);
        if (patientInfoList == null || patientInfoList.isEmpty()) {
            return new ArrayList<>();
        }
        // 医保卡和自费卡，分别找一张patId最大的，医保卡和自费卡的patId不需要相同
        List<HisPatientInfo> list = new ArrayList<>();
        // 医保
        patientInfoList.stream().filter(u -> !Objects.equals(u.getCardType(), "0"))
                .max(Comparator.comparing(HisPatientInfo::getPatid)).ifPresent(list::add);

        /*
         * 2024-09-24
         * 田金格反馈我们新增的医保卡的卡号与自费卡号一致
         * 经过飞哥、大佬确认目前的挂号、缴费业务不是根据卡类型来判断走哪种逻辑，同时就算是医保业务也与自费卡绑定
         * 所以注释掉下面根据医保信息创建医保卡的逻辑
         */
//        if (list.isEmpty()) {
//            // 没有医保卡，根据医保信息生成医保卡
//            patientCardService.createMedicareCard(request.getCertificate_no(), request.getHospitalCode());
//            // 重新查询
//            patientInfoList = baseMapper.getPatientInfo(request);
//            patientInfoList.stream().filter(u -> !Objects.equals(u.getCardType(), "0"))
//                    .max(Comparator.comparing(HisPatientInfo::getPatid)).ifPresent(list::add);
//        }
        // 自费
        patientInfoList.stream().filter(u -> Objects.equals(u.getCardType(), "0"))
                .max(Comparator.comparing(HisPatientInfo::getPatid)).ifPresent(list::add);

        List<HisPatientInfoRes> patientInfoResList = list.stream()
                .map(patientInfo -> {
                    HisPatientInfoRes patientInfoRes = new HisPatientInfoRes();
                    setBasicInfo(patientInfoRes, patientInfo);
                    setBirthInfo(patientInfoRes, patientInfo);
                    setNationInfo(patientInfoRes, request, patientInfo);
                    setOccupationInfo(patientInfoRes, request, patientInfo);
                    setChargeTypeInfo(patientInfoRes, request, patientInfo);
                    return patientInfoRes;
                })
                .collect(Collectors.toList());
        return patientInfoResList;
    }

    private void setBasicInfo(HisPatientInfoRes patientInfoRes, HisPatientInfo patientInfo) {
        if (patientInfo != null) {
            patientInfoRes.setPatid(String.valueOf(patientInfo.getPatid()));
            patientInfoRes.setPatname(patientInfo.getPatname());
            patientInfoRes.setHiscardno(patientInfo.getHisCardNo());
            patientInfoRes.setCardno(patientInfo.getCardNo());
            patientInfoRes.setCardtype(patientInfo.getCardType());
            patientInfoRes.setCertificate_no(patientInfo.getCertificateNo());
            patientInfoRes.setCertificate_type(certificateTypeMapping(patientInfo.getCertificateType(), patientInfo.getCertificateNo()));
            if (patientInfo.getSex() != null) {
                patientInfoRes.setSex(Gender.lookupByValue(patientInfo.getSex()).getKey());
            } else {
                patientInfoRes.setSex(Gender.UNKNOWN.getKey());
            }
            patientInfoRes.setMarriage(Objects.toString(patientInfo.getMarriage(), ""));
            patientInfoRes.setAddress(patientInfo.getAddress());
            patientInfoRes.setTelephone(patientInfo.getTelephone());
            //        patientInfoRes.setAccount_balance(); TODO 如何查？xml？
            //        patientInfoRes.setVirtual_card();
        }
    }

    private void setBirthInfo(HisPatientInfoRes patientInfoRes, HisPatientInfo patientInfo) {
        if (patientInfo != null && patientInfo.getBirthday() != null) {
            SimpleDateFormat format = new SimpleDateFormat("yyyyMMdd");
            patientInfoRes.setBirth(format.format(patientInfo.getBirthday()));
        }
    }

    private void setNationInfo(HisPatientInfoRes patientInfoRes, HisPatientInfoReq request, HisPatientInfo patientInfo) {
        if (patientInfo != null && patientInfo.getNation() != null) {
            Integer nationCode = patientInfo.getNation();
            TBDicHisDictionary nation = dictionaryService.getDictByCode(Constants.DICT_TYPE_NATION, request.getHospitalCode(), nationCode);
            patientInfoRes.setNation(nation == null ? "" : nation.getHisDictionaryName());
        }
    }

    private void setOccupationInfo(HisPatientInfoRes patientInfoRes, HisPatientInfoReq request, HisPatientInfo patientInfo) {
        if (patientInfo != null && patientInfo.getOccupation() != null) {
            Integer occupationCode = patientInfo.getOccupation();
            TBDicHisDictionary occupation = dictionaryService.getDictByCode(Constants.DICT_TYPE_OCCUPATION, request.getHospitalCode(), occupationCode);
            patientInfoRes.setCareer(occupation == null ? "" : occupation.getHisDictionaryName());
        }
    }

    private void setChargeTypeInfo(HisPatientInfoRes patientInfoRes, HisPatientInfoReq request, HisPatientInfo patientInfo) {
        if (patientInfo != null && patientInfo.getChargeType() != null) {
            Integer chargeTypeCode = patientInfo.getChargeType();
            SystemTBChargeType chargeType = chargeTypeService.getByCode(request.getHospitalCode(), chargeTypeCode);
            patientInfoRes.setChargetype_code(String.valueOf(chargeTypeCode));
            patientInfoRes.setChargetype_name(chargeType == null ? "" : chargeType.getChargeTypeName());
        }
    }

    @Override
    @Transactional
    public PatientInfoUpdateRes updatePatientInfo(PatientInfoUpdateReq request) {

        RTPatientList patientList = getPatientListByPatIdAndPatName(request.getPatid(), request.getPatname());
        RTPatientDetl patientDetl = getPatientDetlByPatIdAndPatName(request.getPatid());
        if (patientList == null || patientDetl == null) {
            return new PatientInfoUpdateRes(false, request.getRequestId(), "");
        }

        updatePatientCard(request);
        updatePatientList(request, patientList);
        updatePatientDetl(request, patientDetl);

        return new PatientInfoUpdateRes(true, request.getRequestId(), "");
    }

    private RTPatientList getPatientListByPatIdAndPatName(String patId, String patName) {
        LambdaQueryWrapper<RTPatientList> wrapper = Wrappers.lambdaQuery();
        wrapper.eq(RTPatientList::getPatId, patId).eq(RTPatientList::getPatName, patName);
        return baseMapper.selectList(wrapper).stream().findFirst().orElse(null);
    }

    private RTPatientDetl getPatientDetlByPatIdAndPatName(String patId) {
        LambdaQueryWrapper<RTPatientDetl> wrapper = Wrappers.lambdaQuery();
        wrapper.eq(RTPatientDetl::getPatId, patId);
        return patientDetlMapper.selectList(wrapper).stream().findFirst().orElse(null);
    }

    private void updatePatientCard(PatientInfoUpdateReq request) {
        // TODO 首诊患者如何确定?
        if (StringUtils.isNotEmpty(request.getCardno())) {
            RTPatientCard patientCard = patientCardService.getByPatId(request.getPatid());
            // 卡号类型为自费卡，且是首诊患者才能更新
            if (PatientCardTypeEnum.SELF_PAY.getValue().equals(patientCard.getCardType()) && StringUtils.isEmpty(patientCard.getCardNo())) {
                patientCard.setCardNo(request.getCardno());
                patientCardService.updateById(patientCard);
            }
        }
    }

    private void updatePatientList(PatientInfoUpdateReq request, RTPatientList patientList) {
        if (StringUtils.isNotEmpty(request.getCertificate_no()) && StringUtils.isEmpty(patientList.getCertificateNo())){
            patientList.setCertificateNo(request.getCertificate_no());
        }
        if (StringUtils.isEmpty(request.getCertificate_type()) && StringUtils.isEmpty(patientList.getCertificateType())) {
            patientList.setCertificateType("01");
        } else if (StringUtils.isNotEmpty(request.getCertificate_type())) {
            patientList.setCertificateType(request.getCertificate_type());
        }
        LambdaUpdateWrapper<RTPatientList> wrapper = Wrappers.lambdaUpdate();
        wrapper.eq(RTPatientList::getPatId, patientList.getPatId());
        patientListMapper.update(patientList,wrapper);
    }

    private void updatePatientDetl(PatientInfoUpdateReq request, RTPatientDetl patientDetl) {
        if (StringUtils.isNotEmpty(request.getAddress())) {
            patientDetl.setAddress(request.getAddress());
        }
        if (StringUtils.isNotEmpty(request.getTelephone())) {
            patientDetl.setPatPhone(request.getTelephone());
        }
        if (StringUtils.isNotEmpty(request.getMemo())) {
            patientDetl.setRemark(request.getMemo());
        }
        if (StringUtils.isNotEmpty(request.getContacts_name())) {
            patientDetl.setContactsName(request.getContacts_name());
        }
        if (StringUtils.isNotEmpty(request.getContacts_telephone())) {
            patientDetl.setContactsRelationShipTel(request.getContacts_telephone());
        }
        if (StringUtils.isNotEmpty(request.getContacts_relationship())) {
            patientDetl.setContactsRelationShip(request.getContacts_relationship());
        }
        if (!Objects.isNull(request.getNation())) {
            patientDetl.setNation(request.getNation());
        }
        if (!Objects.isNull(request.getCareer())) {
            patientDetl.setOccupation(request.getCareer());
        }
        if (!Objects.isNull(request.getNationality())) {
            patientDetl.setNationality(request.getNationality());
        }
        if (StringUtils.isNotEmpty(request.getWork_unit())) {
            patientDetl.setCompanyName(request.getWork_unit());
        }
        if (StringUtils.isNotEmpty(request.getResidence_county_address())) {
            patientDetl.setLiveAddr(request.getResidence_county_address());
        }
        LambdaUpdateWrapper<RTPatientDetl> wrapper = Wrappers.lambdaUpdate();
        wrapper.eq(RTPatientDetl::getPatId, patientDetl.getPatId());
        patientDetlMapper.update(patientDetl, wrapper);
    }

    @Override
    public RTPatientList getPatientListByCertificateNo(String hospitalCode, String certificateNo) {
        LambdaQueryWrapper<RTPatientList> wrapper = Wrappers.lambdaQuery();
        wrapper.eq(RTPatientList::getCertificateNo, certificateNo);
        wrapper.eq(RTPatientList::getHospitalCode, hospitalCode);
        return baseMapper.selectOne(wrapper);
    }

    /**
     * 证件类型his->ih
     * select * from TB_Dic_HisDictionary where DictionaryTypeID=27
     */
    private String certificateTypeMapping(String certificateType, String certificateNo) {
        if (StringUtils.isEmpty(certificateType) && StringUtils.isNotEmpty(certificateNo)) {
            // 正则判断是不是身份证号
            String regex  = "^[1-9]\\d{5}(18|19|([23]\\d))\\d{2}((0[1-9])|(10|11|12))(([0-2][1-9])|10|20|30|31)\\d{3}[0-9Xx]$";
            if (certificateNo.matches(regex)){
                log.info("患者-{} 身份类型为空，使用正则判断是身份证号", certificateNo);
                return "01";
            }
            log.info("患者-{} 身份类型为空，使用正则判断不是身份证号", certificateNo);
            return "99";
        }
        switch (certificateType){
            case "1":
                return "01";
            case "2":
                return "02";
            case "3":
                return "03";
            case "4":
                return "04";
            case "5":
                return "05";
            case "6":
                return "06";
            case "7":
                return "07";
            case "9":
                return "20"; // 外国人永久居留证
            default:
                return "99";
        }
    }

}
