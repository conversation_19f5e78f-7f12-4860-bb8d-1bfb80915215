package com.sunhealth.ihhis.service.impl;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.sunhealth.ihhis.dao.his.HospitalMapper;
import com.sunhealth.ihhis.dao.his.OutpatientInvoiceMapper;
import com.sunhealth.ihhis.model.dto.electronicInvoice.ElectronicInvoice;
import com.sunhealth.ihhis.model.dto.electronicInvoice.ElectronicInvoiceFileReq;
import com.sunhealth.ihhis.model.dto.electronicInvoice.ElectronicInvoiceFile;
import com.sunhealth.ihhis.model.dto.electronicInvoice.ElectronicInvoiceReq;
import com.sunhealth.ihhis.service.ElectronicInvoiceService;
import com.sunhealth.ihhis.utils.TimeUtils;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR> jzs
 * @Date : 2024-07-23
 */
@Slf4j
@Service
@AllArgsConstructor
public class ElectronicInvoiceServiceImpl implements ElectronicInvoiceService {

    private final OutpatientInvoiceMapper outpatientInvoiceMapper;
    private final HospitalMapper hospitalMapper;

    @Override
    public ElectronicInvoiceFile getElectronicInvoiceFile(ElectronicInvoiceFileReq req) {
        ElectronicInvoiceFile electronicInvoiceFile = outpatientInvoiceMapper.getElectronicInvoiceFile(req);
        electronicInvoiceFile.setElec_invoice_show_type("1");
        if (StringUtils.isNotBlank(electronicInvoiceFile.getInvoicing_date())) {
            String invoicingDate = TimeUtils.dateStringFormat(TimeUtils
                    .convert(electronicInvoiceFile.getInvoicing_date().substring(0, 14)),"YYYYMMddHHmmss");
            electronicInvoiceFile.setInvoicing_date(invoicingDate);
        }
        return electronicInvoiceFile;
    }

    @Override
    public Page<ElectronicInvoice> getElectronicInvoiceList(ElectronicInvoiceReq req) {
        Page<ElectronicInvoice> page = new Page<>(req.getPage(), req.getSize());
        return outpatientInvoiceMapper.getElectronicInvoiceList(page, req);
    }

}
