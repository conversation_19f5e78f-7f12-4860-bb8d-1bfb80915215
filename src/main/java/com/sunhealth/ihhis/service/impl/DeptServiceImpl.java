package com.sunhealth.ihhis.service.impl;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.sunhealth.ihhis.dao.his.DeptMapper;
import com.sunhealth.ihhis.model.dto.DeptInfo;
import com.sunhealth.ihhis.model.dto.DictDataQueryReq;
import com.sunhealth.ihhis.model.entity.Dept;
import com.sunhealth.ihhis.service.DeptService;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

@Service
public class DeptServiceImpl extends ServiceImpl<DeptMapper, Dept> implements DeptService {

    @Override
    public Page<DeptInfo> findAll(DictDataQueryReq req) {
        String channelType = req.getChannel_type();
//        LambdaQueryWrapper<Dept> wrapper = Wrappers.lambdaQuery();
//        if ("1".equals(channelType)) {
//            wrapper.eq(Dept::getIsVisit, channelType);
//        }
//        wrapper.eq(Dept::getStatus, "1");
//        wrapper.eq(Dept::getHospitalId, req.getHospitalCode());
        Page<DeptInfo> page = new Page<>(req.getPage(), req.getSize());
        if (StringUtils.isBlank(req.getChannel_type()) || "-1".equals(req.getChannel_type())) {
            req.setChannel_type("0");
        }
        return baseMapper.selectPageDept(page, 1, Integer.valueOf(req.getHospitalCode()), channelType);
    }
}
