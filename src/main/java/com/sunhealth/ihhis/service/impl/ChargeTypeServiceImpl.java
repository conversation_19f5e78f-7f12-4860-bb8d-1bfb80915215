package com.sunhealth.ihhis.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.sunhealth.ihhis.dao.his.ChargeTypeMapper;
import com.sunhealth.ihhis.model.entity.SystemTBChargeType;
import com.sunhealth.ihhis.service.ChargeTypeService;
import org.springframework.stereotype.Service;

@Service
public class ChargeTypeServiceImpl extends ServiceImpl<ChargeTypeMapper, SystemTBChargeType> implements ChargeTypeService {

    @Override
    public SystemTBChargeType getByCode(Integer hospitalCode, Integer code) {
        LambdaQueryWrapper<SystemTBChargeType> wrapper = Wrappers.lambdaQuery();
        wrapper.eq(SystemTBChargeType::getHospitalCode, hospitalCode)
                .eq(SystemTBChargeType::getChargeTypeCode, code);
        return baseMapper.selectList(wrapper).stream().findFirst().orElse(null);
    }
}
