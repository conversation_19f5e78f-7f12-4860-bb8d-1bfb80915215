package com.sunhealth.ihhis.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.sunhealth.ihhis.annotations.HisLog;
import com.sunhealth.ihhis.common.HttpContextUtil;
import com.sunhealth.ihhis.common.PageBean;
import com.sunhealth.ihhis.dao.his.OperationLogMapper;
import com.sunhealth.ihhis.model.entity.OperationLog;
import com.sunhealth.ihhis.model.vm.SelectOperationLogParam;
import com.sunhealth.ihhis.service.LogService;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.reflect.MethodSignature;
import org.springframework.stereotype.Service;

import javax.servlet.http.HttpServletRequest;
import java.lang.reflect.Method;
import java.util.Date;

@Service
public class LogServiceImpl extends ServiceImpl<OperationLogMapper, OperationLog> implements LogService {

    @Override
    public void saveLog(ProceedingJoinPoint point, Boolean result) {
        OperationLog operationLog = new OperationLog();
        MethodSignature signature = (MethodSignature) point.getSignature();
        Method method = signature.getMethod();

        HisLog logAnnotation = method.getAnnotation(HisLog.class);
        if (logAnnotation != null) {
            // 注解上的描述
            operationLog.setOperationName(logAnnotation.value());
        }
        HttpServletRequest request = HttpContextUtil.getHttpServletRequest();
        operationLog.setOperationDate(new Date());
        operationLog.setResult(result);
        baseMapper.insert(operationLog);
    }

    @Override
    public PageBean<OperationLog> getLog(SelectOperationLogParam param) {
        Page<OperationLog> page = new Page<>(param.getPage(), param.getSize());
        Page<OperationLog> operationLogs = baseMapper.selectPage(page, new LambdaQueryWrapper<>());
        PageBean<OperationLog> pageBean = new PageBean<>(operationLogs);
        return pageBean;
    }

}
