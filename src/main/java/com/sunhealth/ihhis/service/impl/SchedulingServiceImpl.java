package com.sunhealth.ihhis.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.sunhealth.ihhis.cache.MemoryCache;
import com.sunhealth.ihhis.config.HisHospitalProperties;
import com.sunhealth.ihhis.dao.his.DeptMapper;
import com.sunhealth.ihhis.dao.his.SchedulingMapper;
import com.sunhealth.ihhis.model.dto.schedule.*;
import com.sunhealth.ihhis.model.entity.Dept;
import com.sunhealth.ihhis.service.SchedulingService;
import com.sunhealth.ihhis.utils.TimeUtils;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.*;
import java.util.stream.Collectors;

@Service
@AllArgsConstructor
@Slf4j
public class SchedulingServiceImpl implements SchedulingService {

    private final SchedulingMapper schedulingMapper;
    private final DeptMapper deptMapper;
    private final HisHospitalProperties hisHospitalProperties;
    private final MemoryCache memoryCache;

    @Override
    public List<SourceNumberRes> getCurrentDaySchedulingSourceNumber(SourceNumberReq req) {
        List<SourceNumber> sourceNumbers = schedulingMapper.getSchedulingSourceNumber(req);
        if (CollectionUtils.isEmpty(sourceNumbers)) {
            return Collections.emptyList();
        }
//        Integer timeSpanId = sourceNumbers.stream().findFirst().map(SourceNumber::getTimespanId).orElse(null);
        List<Integer> timeSpanId = sourceNumbers.stream().map(SourceNumber::getTimespanId).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(timeSpanId)) {
            return Collections.emptyList();
        }
        req.setSource_number(null);
        List<SourceNumber> sourceNumberList = schedulingMapper.listSchedulingSourceNumber(req, timeSpanId);
        // 上一行查询的全部是可用号源
//        sourceNumberList.forEach(u -> u.setStatus(0));
        return sourceNumberList.stream().map(SourceNumberRes::new).collect(Collectors.toList());
    }

    @Override
    public List<DeptListRes> getSchedulingDeptList(DeptListReq req) {
        req.setBeginDate(TimeUtils.convert(req.getBegin_date()));
        req.setEndDate(TimeUtils.convert(req.getEnd_date()));
        List<DeptList> deptListList = schedulingMapper.listSchedulingDept(req);

        Map<Integer, Dept> deptMap = getDeptMap(req.getHospitalCode());

        Map<Integer, List<DeptList>> deptListMap = deptListList
                .stream()
                .collect(Collectors.groupingBy(DeptList::getDeptId));
        return deptListMap.values().stream().map(deptLists -> {
            DeptList firstItem = deptLists.get(0);
            Set<String> set = deptLists.stream()
                    .map(deptList -> matchType(deptList.getSchedulingType()))
                    .filter(StringUtils::isNotEmpty)
                    .collect(Collectors.toSet());
            String typeStr = String.join(",", set);
            return convertDeptListRes(firstItem, typeStr, deptMap);
        }).collect(Collectors.toList());
    }

    private synchronized Map<Integer, Dept> getDeptMap(String hospitalCode) {
        Map<Integer, Dept> result = null;
        try {
            result = memoryCache.getHospitalDepts(hospitalCode);
        } catch (Exception e) {
            log.error("从内存中读取医院科室失败", e);
        }
        if (result == null) {
            LambdaQueryWrapper<Dept> wrapper = Wrappers.lambdaQuery();
            wrapper.eq(Dept::getStatus, "1")
                    .eq(Dept::getDeptClassCode, "1")
                    .eq(Dept::getHospitalId, hospitalCode);
            result = deptMapper.selectList(wrapper).stream()
                    .collect(Collectors.toMap(Dept::getDeptId, dept -> dept));
            memoryCache.putHospitalDepts(hospitalCode, result);
        }
        return result;
    }

    private DeptListRes convertDeptListRes(DeptList deptList, String typeStr, Map<Integer, Dept> deptMap) {
        DeptListRes deptListRes = new DeptListRes();
        deptListRes.setDept_id(deptList.getDeptId() + "");
        deptListRes.setDept_name(deptList.getDeptName());

        Dept dept = new Dept();
        dept.setDeptId(deptList.getDeptId());
        dept.setDeptName(deptList.getDeptName());
        dept.setParentId(deptList.getParentId());
        dept.setLevel(deptList.getDeptLevel());

        Dept firstDept = findFirstDept(deptMap, dept);
        deptListRes.setFirst_dept_id(firstDept.getDeptId() + "");
        deptListRes.setFirst_dept_name(firstDept.getDeptName());
        Dept secondDept = findSecondDept(deptMap, dept);
        deptListRes.setSecond_dept_id(secondDept.getDeptId() + "");
        deptListRes.setSecond_dept_name(secondDept.getDeptName());

        deptListRes.setInputcode1(deptList.getInputCode1());
        deptListRes.setInputcode2(deptList.getInputCode2());
        deptListRes.setScheduling_type(typeStr);
        return deptListRes;
    }

    /**
     * 查询一级科室
     */
    private Dept findFirstDept(Map<Integer, Dept> deptMap, Dept dept) {
        if (dept.getLevel() == 1) {
            return dept;
        }
        Dept parentDept = deptMap.get(dept.getParentId());
        if (Objects.isNull(parentDept)) {
            return dept;
        }
        return findFirstDept(deptMap, parentDept);
    }

    /**
     * 查询二级科室
     */
    private Dept findSecondDept(Map<Integer, Dept> deptMap, Dept dept) {
        if (dept.getLevel() == 1 || dept.getLevel() == 2) {
            return dept;
        }
        Dept parentDept = deptMap.get(dept.getParentId());
        if (Objects.isNull(parentDept)) {
            return dept;
        }
        return findSecondDept(deptMap, parentDept);
    }

    @Override
    public List<SourceDetailsRes> getSourceDetails(SourceDetailsReq req) {

        Map<Integer, Dept> deptMap = getDeptMap(req.getHospitalCode());

        List<SourceDetails> sourceDetailsList = schedulingMapper.listSourceDetails(req);

        Dept dept = new Dept();
        String normalDoctorId = hisHospitalProperties.getNormalDoctorId();

        return sourceDetailsList.stream().map(item -> {
            SourceDetailsRes sourceDetailsRes = new SourceDetailsRes(item);
            //一二级科室
            dept.setDeptId(item.getDeptId());
            dept.setDeptName(item.getDeptName());
            dept.setParentId(item.getParentDeptId());
            dept.setLevel(item.getDeptLevel());
            Dept firstDept = findFirstDept(deptMap, dept);
            sourceDetailsRes.setFirst_dept_id(firstDept.getDeptId() + "");
            sourceDetailsRes.setFirst_dept_name(firstDept.getDeptName());
            // 适应本钢，二级部门暂取一级部门
//            Dept secondDept = findSecondDept(deptMap, dept);
            sourceDetailsRes.setSecond_dept_id(firstDept.getDeptId() + "");
            sourceDetailsRes.setSecond_dept_name(firstDept.getDeptName());
            String type = matchType(item.getSchedulingType());
            sourceDetailsRes.setScheduling_type(type);
            // 普通医生不显示姓名,且sourceType为1(1:科室号源，2:医生号源）
            // his那边生产环境普通号用医生id208排
            if ("0".equals(type) && (StringUtils.isEmpty(sourceDetailsRes.getDoctor_id())
                    || Objects.equals(normalDoctorId, sourceDetailsRes.getDoctor_id()))) {
                sourceDetailsRes.setSource_type("1");
                sourceDetailsRes.setDoctor_id(null);
                sourceDetailsRes.setDoctor_name(null);
            }
            return sourceDetailsRes;
        }).collect(Collectors.toList());
    }

    @Override
    public List<DeptSourceDetailsRes> getSchedulingDeptSourceDetails(DeptSourceDetailsReq req) {
        req.setBeginDate(TimeUtils.convert(req.getBegin_date()));
        req.setEndDate(TimeUtils.convert(req.getEnd_date()));

        List<DeptSourceDetails> deptSourceDetailsList = schedulingMapper.listDeptSourceDetails(req);

        List<SourceNumber> deptSourceNumberList = schedulingMapper.listSourceNumberByDept(req);

        Map<String, List<SourceNumber>> deptSourceNumberMap = deptSourceNumberList.stream()
                .collect(Collectors.groupingBy(sn -> sn.getSchedulingId() + "&" + sn.getTimespanId()));
        String normalDoctorId = hisHospitalProperties.getNormalDoctorId();

        return deptSourceDetailsList.stream()
                .filter(s -> {
                    String type = matchType(s.getSchedulingType());
                    return "0".equals(type) && (StringUtils.isEmpty(s.getDoctorId())
                            || Objects.equals(normalDoctorId, s.getDoctorId()));
                }) // 只查普通号
                .map(item -> {
                    DeptSourceDetailsRes deptSourceDetailsRes = new DeptSourceDetailsRes(item);
                    String key = item.getSchedulingId() + "&" + item.getTimespanId();
                    if (!deptSourceNumberMap.containsKey(key)) {
                        return deptSourceDetailsRes;
                    }
                    List<SourceNumber> sourceNumbers = deptSourceNumberMap.get(key);
                    List<SourceNumberInfos> numberInfos = sourceNumbers.stream()
                            .map(n -> new SourceNumberInfos(n.getSeqNum(), n.getStatus().toString()))
                            .collect(Collectors.toList());
                    deptSourceDetailsRes.setSource_number_infos(numberInfos);
                    return deptSourceDetailsRes;
                }).collect(Collectors.toList());
    }

    @Override
    public List<DoctorSourceDetailsRes> getSchedulingDoctorSourceDetails(DoctorSourceDetailsReq req) {
        req.setBeginDate(TimeUtils.convert(req.getBegin_date()));
        req.setEndDate(TimeUtils.convert(req.getEnd_date()));

        List<DoctorSourceDetails> doctorSourceDetailsList = schedulingMapper.listDoctorSourceDetails(req);

        List<SourceNumber> doctorSourceNumberList = schedulingMapper.listSourceNumberByDoct(req);

        Map<String, List<SourceNumber>> deptSourceNumberMap = doctorSourceNumberList.stream()
                .collect(Collectors.groupingBy(sn -> sn.getSchedulingId() + "&" + sn.getTimespanId()));

        return doctorSourceDetailsList.stream()
                .map(item -> {
                    DoctorSourceDetailsRes doctorSourceDetailsRes = new DoctorSourceDetailsRes(item);
                    String type = matchType(item.getSchedulingType());
                    doctorSourceDetailsRes.setScheduling_type(type);

                    String key = item.getSchedulingId() + "&" + item.getTimespanId();
                    if (!deptSourceNumberMap.containsKey(key)) {
                        return doctorSourceDetailsRes;
                    }
                    List<SourceNumber> sourceNumbers = deptSourceNumberMap.get(key);
                    List<SourceNumberInfos> numberInfos = sourceNumbers.stream()
                            .map(n -> new SourceNumberInfos(n.getSeqNum(), n.getStatus().toString()))
                            .collect(Collectors.toList());
                    doctorSourceDetailsRes.setSource_number_infos(numberInfos);
                    return doctorSourceDetailsRes;
                }).collect(Collectors.toList());
    }


    /**
     * 出诊类型匹配
     * @param typeStr
     * @return
     */
    private String matchType(String typeStr) {
        if (StringUtils.isEmpty(typeStr)) {
            return "";
        }
        // 现场环境特殊的普通出诊类型
        List<String> normal = Arrays.asList("免费号", "简易门诊", "急诊");
        if (normal.contains(typeStr) || typeStr.contains("普通")) {
            return "0";
        }
        if (typeStr.contains("主任")) {
            return "1";
        }
       return "";
    }

    @Override
    public List<CurrentDayAppointmentRes> getGetCurrentDayAppointmentList(SourceDetailsReq req) {
        List<CurrentDayAppointment> sourceDetailsList = schedulingMapper.currentDayAppointment(req);
        String normalDoctorId = hisHospitalProperties.getNormalDoctorId();
        return sourceDetailsList.stream()
                .filter(item -> item.getSourceQty() != null)
                .map(item -> {
                    CurrentDayAppointmentRes sourceRes = new CurrentDayAppointmentRes(item);
                    String type = matchType(item.getSchedulingType());
                    sourceRes.setScheduling_type(type);
                    // 普通医生不显示姓名,且sourceType为1(1:科室号源，2:医生号源）
                    // his那边生产环境普通号用医生id208排
                    if ("0".equals(type) && (StringUtils.isEmpty(sourceRes.getDoctor_id())
                            || Objects.equals(normalDoctorId, sourceRes.getDoctor_id()))) {
                        sourceRes.setSource_type("1");
                        sourceRes.setDoctor_id(null);
                        sourceRes.setDoctor_name(null);
                    }
                    return sourceRes;
                }).collect(Collectors.toList());
    }

    @Override
    public List<CurrentDoctorSourceDetailsRes> getCurrentDayDoctorSourceDetail(CurrentDoctorSourceDetailsReq req) {
        List<CurrentDoctorSourceDetails> sourceDetailsList = schedulingMapper.getCurrentDayDoctorSourceDetail(req);
        return sourceDetailsList.stream().map(item -> {
            CurrentDoctorSourceDetailsRes sourceDetailsRes = new CurrentDoctorSourceDetailsRes(item);
            String type = matchType(item.getSchedulingType());
            sourceDetailsRes.setScheduling_type(type);
            return sourceDetailsRes;
        }).collect(Collectors.toList());
    }

}
