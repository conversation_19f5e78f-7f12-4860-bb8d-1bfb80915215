package com.sunhealth.ihhis.service.impl;

import com.sunhealth.ihhis.model.dto.outpatientcharge.ConfirmChargeReq;
import com.sunhealth.ihhis.model.dto.outpatientcharge.ConfirmChargeResult;
import com.sunhealth.ihhis.model.dto.outpatientcharge.CreateHuLiDaoJiaChargeReq;
import com.sunhealth.ihhis.model.dto.outpatientcharge.CreateHuLiDaoJiaChargeResult;
import com.sunhealth.ihhis.model.dto.outpatientcharge.InsertThirdAddAccountReq;
import com.sunhealth.ihhis.model.dto.outpatientcharge.InsertThirdAddAccountResult;
import com.sunhealth.ihhis.model.dto.outpatientcharge.PreChargeResult;
import com.sunhealth.ihhis.model.dto.register.FreeRegisterReq;
import com.sunhealth.ihhis.model.dto.register.FreeRegisterResult;
import com.sunhealth.ihhis.model.entity.charge.ThirdAddAccount;
import com.sunhealth.ihhis.service.ChargeService;
import com.sunhealth.ihhis.service.HuLiDaoJiaBusinessService;
import com.sunhealth.ihhis.service.RegisterService;
import com.sunhealth.ihhis.service.ThirdAddAccountService;
import java.util.List;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

@Service
@Slf4j
@AllArgsConstructor
public class HuliDaoJiaBusinessServiceImpl implements HuLiDaoJiaBusinessService {
    private final RegisterService registerService;
    private final ThirdAddAccountService thirdAddAccountService;
    private final ChargeService chargeService;
    @Override
    @Transactional
    public CreateHuLiDaoJiaChargeResult applyElectronicInvoice(CreateHuLiDaoJiaChargeReq req) {
        // 免费挂号
        FreeRegisterReq freeRegisterReq = new FreeRegisterReq(req);
        FreeRegisterResult freeRegisterResult = registerService.freeRegister(freeRegisterReq);

        // 插入待缴费项目
        InsertThirdAddAccountReq thirdReq = new InsertThirdAddAccountReq();
        thirdReq.setItems(req.getItem_infos());
        thirdReq.setRegister(freeRegisterResult.getRegister());
        thirdReq.setTrafficAmount(req.getTraffic_amount());
        InsertThirdAddAccountResult thirdAddAccountResult = thirdAddAccountService.createThirdAddAccount(thirdReq);
        List<ThirdAddAccount> items = thirdAddAccountResult.getItems();
        // 预算
        PreChargeResult preChargeResult = chargeService.preChargeForHuLi(items, freeRegisterResult.getRegister());
        if (!preChargeResult.getShould_pay_amount().equals(req.getItem_amount() + "")) {
            log.error("预算金额与收费项目金额不一致，预算金额：{} 收费项目金额：{}", preChargeResult.getShould_pay_amount(), req.getItem_amount());
            throw new RuntimeException("预算价金额与实际支付金额不一致，预算金额：" + preChargeResult.getShould_pay_amount() + "，收费项目金额：" + req.getItem_amount());
        }
        //结算
        ConfirmChargeReq confirmChargeReq = new ConfirmChargeReq();
        confirmChargeReq.setPatid(req.getPatid());
        confirmChargeReq.setRegno(preChargeResult.getRegno());
        confirmChargeReq.setSettle_id(preChargeResult.getSettle_id());
        confirmChargeReq.setSerial_no(req.getSerial_no());
        confirmChargeReq.setTrade_no(req.getTrade_no());
        confirmChargeReq.setTotal_amount(req.getItem_amount() + "");
        confirmChargeReq.setShould_pay_amount(req.getItem_amount() + "");
        confirmChargeReq.setPay_type(req.getPay_type());
        confirmChargeReq.setPay_time(req.getPay_time());
        confirmChargeReq.setRecipe_no_list(String.valueOf(thirdAddAccountResult.getRecipeNo()));
        confirmChargeReq.setHospitalCode(req.getHospitalCode());
        ConfirmChargeResult confirmChargeResult = chargeService.confirmChargeForHuLiDaoJia(confirmChargeReq);

        CreateHuLiDaoJiaChargeResult result = new CreateHuLiDaoJiaChargeResult();
        result.setSettle_id(preChargeResult.getSettle_id());
        return result;
    }
}
