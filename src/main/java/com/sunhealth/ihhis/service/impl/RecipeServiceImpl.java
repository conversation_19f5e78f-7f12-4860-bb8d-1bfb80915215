package com.sunhealth.ihhis.service.impl;

import com.sunhealth.ihhis.cache.MemoryCache;
import com.sunhealth.ihhis.dao.his.RecipeMapper;
import com.sunhealth.ihhis.model.dto.outpatientcharge.OutpatientRecipeInfo;
import com.sunhealth.ihhis.model.dto.outpatientcharge.OutpatientRecipeReq;
import com.sunhealth.ihhis.model.dto.recipe.*;
import com.sunhealth.ihhis.service.*;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

@Service
@AllArgsConstructor
@Slf4j
public class RecipeServiceImpl implements RecipeService {

    private final RecipeMapper recipeMapper;

    private final MemoryCache memoryCache;

    @Override
    public List<OutpatientRecipeInfo> getOutpatientRecipes(OutpatientRecipeReq req) {
        List<OutpatientRecipeInfo> recipeInfos = recipeMapper.selectOutpatientRecipeList(req);
        recipeInfos.forEach(recipeInfo -> {
            if (StringUtils.isNotBlank(recipeInfo.getMain_diagnosis())) {
                String regex = "\"diagnosisName\"\\s*:\\s*\"([^\"]*)\"";
                Pattern pattern = Pattern.compile(regex);
                Matcher matcher = pattern.matcher(recipeInfo.getMain_diagnosis());
                if (matcher.find()) {
                    // 提取第一个捕获组中的内容，即 diagnosisName 的值
                    String diagnosisName = matcher.group(1);
                    recipeInfo.setMain_diagnosis(diagnosisName);
                }
            }
            recipeInfo.setItem_infos(recipeMapper.selectRecipeItemList1(recipeInfo.getRecipe_no()));
        });
        return recipeInfos;
    }

    @Override
    public OnlineRecipeResponse saveRecipeOnline(OnlineRecipeRequest req) {

        // 构造假的his处方单号
        String hisRecipeNo = "mock" +  System.currentTimeMillis();

        OnlineRecipeResponse response = new OnlineRecipeResponse();
        response.setSuccess(true);
        response.setHj_recipe_no(hisRecipeNo);
        response.setRegno(req.getRegno());

        // 在线处方保存先不实现，这里只是将处方金额汇总放进缓存中
        List<RecipeItem> itemInfos = req.getItem_infos();
        int totalAmount = itemInfos.stream()
                .filter(item -> item.getAmount() != null && !item.getAmount().isEmpty())
                .mapToInt(item -> Integer.parseInt(item.getAmount()))
                .sum();
        memoryCache.putOnlineRecipeAmount(hisRecipeNo, totalAmount);
        log.info("在线处方虚拟保存，处方号：{}，总金额：{}", hisRecipeNo, totalAmount);
        return response;
    }

    @Override
    public OnlineRecipeDeleteResponse deleteRecipeOnline(OnlineRecipeDeleteRequest req) {
        // 在线处方撤销暂不实现，直接返回成功
        OnlineRecipeDeleteResponse response = new OnlineRecipeDeleteResponse();
        response.setSuccess(true);
        return response;
    }
}
