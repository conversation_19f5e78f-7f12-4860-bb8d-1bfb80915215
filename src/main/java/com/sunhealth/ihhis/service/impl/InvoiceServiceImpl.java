package com.sunhealth.ihhis.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.sunhealth.ihhis.config.HisHospitalProperties;
import com.sunhealth.ihhis.dao.his.*;
import com.sunhealth.ihhis.model.dq.sh.insurance.response.SH02Response;
import com.sunhealth.ihhis.model.dq.sh.insurance.response.SI12Response;
import com.sunhealth.ihhis.model.dto.outpatientcharge.ConfirmChargeReq;
import com.sunhealth.ihhis.model.dto.register.ConfirmRegistReq;
import com.sunhealth.ihhis.model.entity.TBDicHisDictionary;
import com.sunhealth.ihhis.model.entity.charge.ChargeDetailTime;
import com.sunhealth.ihhis.model.entity.charge.ChargeListTime;
import com.sunhealth.ihhis.model.entity.invoice.*;
import com.sunhealth.ihhis.model.entity.patient.RTPatientCard;
import com.sunhealth.ihhis.model.entity.patient.RTPatientList;
import com.sunhealth.ihhis.model.entity.register.RegisterDetailTime;
import com.sunhealth.ihhis.model.entity.register.RegisterListTime;
import com.sunhealth.ihhis.model.insurance.response.Output6301;
import com.sunhealth.ihhis.model.insurance.response.Output6301.ExtData.Setlinfo;
import com.sunhealth.ihhis.model.vm.PushInvoiceMessageParam;
import com.sunhealth.ihhis.service.InvoiceService;
import com.sunhealth.ihhis.service.processer.RegInvoiceIDProcessor;
import com.sunhealth.ihhis.service.processer.RegSerialNoProcessor;
import com.sunhealth.ihhis.task.scheduler.InvoiceScheduler;
import com.sunhealth.ihhis.utils.MoneyToChineseUtil;
import com.sunhealth.ihhis.utils.SequenceUtils;
import com.sunhealth.ihhis.utils.TimeUtils;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

@Service
@AllArgsConstructor
@Slf4j
public class InvoiceServiceImpl implements InvoiceService {

    private final OutpatientInvoiceTimeMapper outpatientInvoiceTimeMapper;
    private final OutpatientInvoiceMapper outpatientInvoiceMapper;
    private final OpInvoiceSupplyTimeMapper opInvoiceSupplyTimeMapper;
    private final OpInvoicePaywayTimeMapper opInvoicePaywayTimeMapper;
    private final OpInvoicePaywayMapper opInvoicePaywayMapper;
    private final InvoiceInfoMapper invoiceInfoMapper;
    private final PatientCardMapper patientCardMapper;
    private final RegSerialNoProcessor regSerialNoProcessor;
    private final RegInvoiceIDProcessor regInvoiceIDProcessor;
    private final DictionaryMapper dictionaryMapper;

    private final OutpatientInvoiceViewMapper outpatientInvoiceViewMapper;
    private final PatientListMapper patientListMapper;

    private final HisHospitalProperties hisHospitalProperties;

    @Override
    @Transactional
    public OutpatientInvoiceTime saveRegisterInvoiceInfo(ConfirmRegistReq req, RegisterListTime register,
                                        List<RegisterDetailTime> registerDetails) {
        // 生成SerialNo, InvoiceID
        Long serialNo = regSerialNoProcessor.execute();
        Long invoiceID = regInvoiceIDProcessor.execute();
        Date date = new Date();

        OutpatientInvoiceTime invoice = new OutpatientInvoiceTime();
        invoice.setInvoiceID(invoiceID);
        invoice.setInvoicePrefix("N1");
        // 生成invoiceInfo
        String invoiceNo = getInvoiceInfoWithoutPrefix();
        String invoiceInfo = invoice.getInvoicePrefix() + "-" + invoiceNo;
        log.info("患者{}挂号成功，生成发票号{}", register.getPatName(), invoiceInfo);
        invoice.setInvoiceInfo(invoiceInfo);
        invoice.setInvoiceNo(Long.valueOf(invoiceNo));
        invoice.setSerialNo(serialNo);
        invoice.setRegNo(register.getRegNo());
        // 查询卡信息
        // 卡表里：0 自费，1 医保
        Integer cardType = 0;
        // 医保后续处理医保相关字段值
        if ("0".equals(req.getSelf_flag())) { // 医保，查医保卡
            cardType = 1;
        } else { // 自费，查自费卡
            cardType = 0;
        }
        RTPatientCard card;
        card = patientCardMapper.getByCardNoAndPatIdAndCardType(
            register.getCardNo(), register.getPatID(), cardType);
        if (card == null) {
            card = patientCardMapper.getByCardNoAndPatId(register.getCardNo(),
                                                                    register.getPatID());
        }
        invoice.setCardType(card.getCardType().toString());
        invoice.setCardNo(card.getCardNo());
        invoice.setOutpatientNo(register.getOutPatientNo());
        invoice.setPatID(register.getPatID());
        if (register.getNewPatID() != null) {
            invoice.setNewPatID(register.getNewPatID());
        } else {
            invoice.setNewPatID(0L);
        }
        invoice.setPatName(register.getPatName());
        invoice.setChargeType(register.getChargeType());
        invoice.setInvoiceLabel("");
        invoice.setOpCode(hisHospitalProperties.getOpCode());
        invoice.setOpTime(date);
        // 计算总金额
        BigDecimal totalAmountBigDecimal = new BigDecimal(0);
        BigDecimal selfPayBigDecimal = new BigDecimal(0);
        for (RegisterDetailTime detail : registerDetails) {
            totalAmountBigDecimal = totalAmountBigDecimal.add(detail.getTotalAmount());
            selfPayBigDecimal = selfPayBigDecimal.add(detail.getSelfCost());
        }
        invoice.setTotalAmount(totalAmountBigDecimal);
        invoice.setCapitalAmount(MoneyToChineseUtil.convert(totalAmountBigDecimal.toString()));
        invoice.setInsuranceTotal(BigDecimal.valueOf(0));
        invoice.setCostTotal(BigDecimal.valueOf(0));
        invoice.setPubPay(BigDecimal.valueOf(0));
        invoice.setAppendPay(BigDecimal.valueOf(0));
        invoice.setCurrAccountPay(BigDecimal.valueOf(0));
        invoice.setLastAccountPay(BigDecimal.valueOf(0));
        invoice.setInsuranceCashTotal(BigDecimal.valueOf(0));
        invoice.setPayFee(selfPayBigDecimal);
        invoice.setCashFee(selfPayBigDecimal);
        invoice.setReceiveAmount(selfPayBigDecimal);
        invoice.setOwnFee(BigDecimal.ZERO);
        invoice.setChangeAmount(BigDecimal.valueOf(0));
        invoice.setErrorCents(BigDecimal.valueOf(0.00));
        invoice.setStatus(0); // 有效0，80退费，120红冲
        invoice.setFlag(1); // 1挂号，2收费
        invoice.setDiscountAmount(BigDecimal.valueOf(0.00));
        invoice.setIsDelete(false);
        invoice.setCreatedBy(hisHospitalProperties.getOpCode());
        invoice.setCreatedDate(date);
        invoice.setUpdateBy(hisHospitalProperties.getOpCode());
        invoice.setUpdateDate(date);
        invoice.setChargeNo(0L);
        invoice.setMzNo(register.getRegNo());
        invoice.setDeptKind(2);
        invoice.setHospitalCode(Integer.valueOf(hisHospitalProperties.getCode()));
        invoice.setJzPay(BigDecimal.valueOf(0.00));
        invoice.setIsPrint(0);
        invoice.setPrinter(hisHospitalProperties.getOpCode());
        invoice.setPrintTime(date);
        invoice.setComputerNo("999999");
        invoice.setFromFlag(333);
        invoice.setPrepaidCardPay(BigDecimal.valueOf(0));
        invoice.setDiffAmount(BigDecimal.valueOf(0.00));
        invoice.setUseFlag(1);
        invoice.setLastAccountBalance(BigDecimal.valueOf(0));
        invoice.setCurrAccountBalance(BigDecimal.valueOf(0));
        //@PayOrderNo ()
        //移动支付医院订单编号
        //@ReturnOrderNo ()
        //移动支付返回的支付流水号
        invoice.setPayOrderNo(req.getSerial_no());
        invoice.setReturnOrderNo(req.getTrade_no());
        // 医保支付保存医保金额
        if ("0".equals(req.getSelf_flag())) {
            if (req.getJsFlag() == 2) {
                // 国家医保
                // 如果是医保类型，那么req中必有output6301，直接使用，如果报错则证明流程有问题，到时排查
                Output6301 output6301 = req.getOutput6301();
                Setlinfo setlinfo = output6301.getExtData().getSetlinfo();
                invoice.setTotalAmount(setlinfo.getMedfeeSumamt());
                invoice.setOwnFee(setlinfo.getFulamtOwnpayAmt().add(setlinfo.getOverlmtSelfpay()));
                invoice.setFlPay(setlinfo.getPreselfpayAmt());
                invoice.setJzPay(BigDecimal.valueOf(0));
                invoice.setInsuranceCashTotal(BigDecimal.valueOf(0));
                invoice.setInsuranceTotal(setlinfo.getInscpScpAmt().add(setlinfo.getPreselfpayAmt()));
                invoice.setCostTotal(setlinfo.getInscpScpAmt());
                invoice.setAppendPay(BigDecimal.valueOf(0));
                invoice.setPubPay(setlinfo.getFundPaySumamt());
                invoice.setCurrAccountPay(setlinfo.getAcctPay());
                invoice.setLastAccountPay(BigDecimal.valueOf(0));
                invoice.setCurrAccountBalance(setlinfo.getBalc());
                invoice.setLastAccountBalance(BigDecimal.valueOf(0));
                invoice.setTradeSerialNo(setlinfo.getSetlId());
                invoice.setPayFee(setlinfo.getPsnCashPay());
                invoice.setReceiveAmount(invoice.getPayFee());
                invoice.setCashFee(invoice.getPayFee());
            } else {
                // 发票,上海医保
                // 参考.net项目, RegisterBLL.cs 方法public long SaveRegister(RegisterSaveDTO register, out string msg, out long _RegNo), 行 var outpatientInvoice = new Reg_Tb_OutpatientInvoice_Time
                invoice.setChargeType(86); // his调整, 上海医保发票chargeType使用86
                SH02Response sh02Response = req.getSe04Response().getSh02Response();
                invoice.setTotalAmount(sh02Response.getTotalexpense().add(sh02Response.getFybjsfwfyze()));
                invoice.setOwnFee(sh02Response.getFybjsfwfyze());
                invoice.setFlPay(BigDecimal.valueOf(0));
                invoice.setInsuranceCashTotal(sh02Response.getZfdxjzfs().add(sh02Response.getTcdxjzfs()).add(sh02Response.getFjdxjzfs()));
                invoice.setInsuranceTotal(sh02Response.getYbjsfwfyze()); // 医保结算范围总额
                invoice.setCostTotal(sh02Response.getTotalexpense()); // 交易费用总额
                invoice.setAppendPay(sh02Response.getFjzfs()); // 附加支付
                invoice.setPubPay(sh02Response.getTczfs()); // 统筹支付
                invoice.setCurrAccountPay(sh02Response.getCuraccountpay()); // 账户支付
                invoice.setLastAccountPay(sh02Response.getTcdzhzfs().add(sh02Response.getFjdzhzfs()).add(sh02Response.getZfdlnzhzfs())); // 历年账户支付
                invoice.setCurrAccountBalance(sh02Response.getCuraccountamt()); // 当年账户余额
                invoice.setLastAccountBalance(sh02Response.getHisaccountamt()); // 历年账户余额
                invoice.setTradeSerialNo(sh02Response.getLsh()); // 医保交易流水号
                invoice.setPayFee(invoice.getOwnFee().add(invoice.getInsuranceCashTotal())); // 应付金额
                invoice.setReceiveAmount(invoice.getPayFee()); // 预收
                invoice.setCashFee(invoice.getPayFee());
            }
        }
        outpatientInvoiceTimeMapper.insert(invoice);

        // 2. insert into Reg_Tb_OutpatientInvoiceSupply_Time;
        // @InvoiceID(62974)
        //@InvoicePrefix(ZZS)
        //@InvoiceNo(1658)
        //@OutpatientNo(**********)
        //@Flag(1)
        //@OpTime(2024/3/23 8:41:26)
        //@OpCode(hisHospitalProperties.getOpCode())
        //@IsDelete(False)
        //@CreatedBy(hisHospitalProperties.getOpCode())
        //@CreatedDate(2024/3/23 8:41:26)
        //@UpdateBy(hisHospitalProperties.getOpCode())
        //@UpdateDate(2024/3/23 8:41:26)
        //@HospitalCode(99)
        OpInvoiceSupplyTime supply = new OpInvoiceSupplyTime();
        supply.setInvoiceID(invoiceID);
        supply.setInvoicePrefix(invoice.getInvoicePrefix());
        supply.setInvoiceNo(invoice.getInvoiceNo());
        supply.setOutpatientNo(invoice.getOutpatientNo());
        supply.setFlag(1);
        supply.setOpTime(invoice.getOpTime());
        supply.setOpCode(hisHospitalProperties.getOpCode());
        supply.setIsDelete(false);
        supply.setCreatedBy(hisHospitalProperties.getOpCode());
        supply.setCreatedDate(invoice.getCreatedDate());
        supply.setUpdateBy(hisHospitalProperties.getOpCode());
        supply.setUpdateDate(invoice.getUpdateDate());
        supply.setHospitalCode(Integer.valueOf(hisHospitalProperties.getCode()));

        opInvoiceSupplyTimeMapper.insert(supply);

        // 3. insert into Reg_Tb_OutpatientInvoicePayway_Time;
        // @SerialNo(0)
        //@InvoiceID(62974)
        //@ChargeNo(0)
        //@Payway(0)
        //@PaywayName(现金)
        //@PayAmount(8)
        //@OpCode(hisHospitalProperties.getOpCode())
        //@OpTime(2024/3/23 8:41:26)
        //@Status(0)
        //@IsDelete(False)
        //@CreatedBy(hisHospitalProperties.getOpCode())
        //@CreatedDate(2024/3/23 8:41:26)
        //@UpdateBy(hisHospitalProperties.getOpCode())
        //@HospitalCode(99)
        //@UpdateDate(2024/3/23 8:41:26)
        OpInvoicePaywayTime payway = new OpInvoicePaywayTime();
//        payway.setSerialNo(invoice.getSerialNo());
        payway.setInvoiceID(invoiceID);
        // 挂号发票没有账单号
        payway.setChargeNo(0L);
        setPaywayInfo(req.getPay_type(), payway);
        payway.setPayAmount(invoice.getPayFee());
        payway.setOpCode(hisHospitalProperties.getOpCode());
        payway.setOpTime(date);
        payway.setStatus(0);
        payway.setIsDelete(false);
        payway.setCreatedBy(hisHospitalProperties.getOpCode());
        payway.setCreatedDate(date);
        payway.setUpdateBy(hisHospitalProperties.getOpCode());
        payway.setHospitalCode(Integer.valueOf(hisHospitalProperties.getCode()));
        payway.setUpdateDate(date);

        opInvoicePaywayTimeMapper.insert(payway);

        return invoice;
    }


    @Override
    @Transactional
    public OutpatientInvoiceTime saveChargeInvoiceInfo(ConfirmChargeReq req, ChargeListTime charge,
                                      List<ChargeDetailTime> chargeDetails) {
        // 生成SerialNo, InvoiceID
        Long serialNo = regSerialNoProcessor.execute();
        Long invoiceID = regInvoiceIDProcessor.execute();
        Date date = new Date();

        OutpatientInvoiceTime invoice = new OutpatientInvoiceTime();
        invoice.setInvoiceID(invoiceID);
        invoice.setInvoicePrefix("N1");
        // 生成invoiceInfo
        String invoiceNo = getInvoiceInfoWithoutPrefix();
        String invoiceInfo = invoice.getInvoicePrefix() + "-" + invoiceNo;
        log.info("患者{}缴费成功，生成发票号{}", charge.getCardNo(), invoiceInfo);
        invoice.setInvoiceInfo(invoiceInfo);
        invoice.setInvoiceNo(Long.valueOf(invoiceNo));
        invoice.setSerialNo(serialNo);
        invoice.setRegNo(charge.getRegNo());
        // 发票表直接取收费表中卡数据
        invoice.setCardType(charge.getCardType().toString());
        invoice.setCardNo(charge.getCardNo());
        invoice.setOutpatientNo(charge.getOutpatientNo());
        invoice.setPatID(charge.getPatID());
        if (charge.getNewPatID() != null) {
            invoice.setNewPatID(charge.getNewPatID());
        } else {
            invoice.setNewPatID(0L);
        }
        RTPatientList patientList = patientListMapper.selectByPatId(charge.getPatID() + "");
        if (patientList == null) {
            throw new RuntimeException("院内无此患者信息或患者信息已停用");
        }
        invoice.setPatName(patientList.getPatName());
        invoice.setInvoiceLabel("");
        invoice.setOpCode(hisHospitalProperties.getOpCode());
        invoice.setOpTime(date);
        // 计算总金额
        BigDecimal totalAmountBigDecimal = new BigDecimal(0);
        BigDecimal selfPayBigDecimal = new BigDecimal(0);
        for (ChargeDetailTime detail : chargeDetails) {
            totalAmountBigDecimal = totalAmountBigDecimal.add(detail.getTotalAmount());
            selfPayBigDecimal = selfPayBigDecimal.add(detail.getSelfAmount());
        }
        invoice.setTotalAmount(totalAmountBigDecimal);
        invoice.setCapitalAmount(MoneyToChineseUtil.convert(totalAmountBigDecimal.toString()));
        invoice.setInsuranceTotal(BigDecimal.valueOf(0));
        invoice.setCostTotal(BigDecimal.valueOf(0));
        invoice.setPubPay(BigDecimal.valueOf(0));
        invoice.setAppendPay(BigDecimal.valueOf(0));
        invoice.setCurrAccountPay(BigDecimal.valueOf(0));
        invoice.setLastAccountPay(BigDecimal.valueOf(0));
        invoice.setInsuranceCashTotal(BigDecimal.valueOf(0));
        invoice.setPayFee(selfPayBigDecimal);
        invoice.setCashFee(selfPayBigDecimal);
        invoice.setOwnFee(BigDecimal.ZERO);
        invoice.setReceiveAmount(selfPayBigDecimal);
        invoice.setChangeAmount(BigDecimal.valueOf(0));
        invoice.setErrorCents(BigDecimal.valueOf(0.00));
        invoice.setStatus(0); // 有效0，80退费，120红冲
        invoice.setFlag(2); // 1挂号，2收费
        invoice.setDiscountAmount(BigDecimal.valueOf(0.00));
        invoice.setIsDelete(false);
        invoice.setCreatedBy(hisHospitalProperties.getOpCode());
        invoice.setCreatedDate(date);
        invoice.setUpdateBy(hisHospitalProperties.getOpCode());
        invoice.setUpdateDate(date);
        invoice.setChargeNo(charge.getChargeNo());
        invoice.setChargeType(charge.getChargeType());
        invoice.setMzNo(charge.getChargeNo());
        invoice.setDeptKind(2);
        invoice.setHospitalCode(Integer.valueOf(hisHospitalProperties.getCode()));
        invoice.setJzPay(BigDecimal.valueOf(0.00));
        invoice.setIsPrint(0);
        invoice.setPrinter(hisHospitalProperties.getOpCode());
        invoice.setPrintTime(date);
        invoice.setComputerNo("999999");
        invoice.setFromFlag(333);
        invoice.setPrepaidCardPay(BigDecimal.valueOf(0));
        invoice.setDiffAmount(BigDecimal.valueOf(0.00));
        invoice.setUseFlag(1);
        invoice.setLastAccountBalance(BigDecimal.valueOf(0));
        invoice.setCurrAccountBalance(BigDecimal.valueOf(0));
        // 2025年02月26日 发票表中补充开方科室和医生
        invoice.setRecipeDept(chargeDetails.get(0).getDeptId());
        invoice.setRecipeDoctor(chargeDetails.get(0).getDoctorId());
        //@PayOrderNo ()
        //移动支付医院订单编号
        //@ReturnOrderNo ()
        //移动支付返回的支付流水号
        invoice.setPayOrderNo(req.getSerial_no());
        invoice.setReturnOrderNo(req.getTrade_no());

        invoice.setDataFrom(charge.getDataFrom());
        invoice.setTerminalType(3);
        // 医保支付保存医保金额
        if ("0".equals(req.getSelf_flag())) {
            if (req.getJsFlag() == 2) {
                // 国家医保
                // 如果是医保类型，那么req中必有output6301，直接使用，如果报错则证明流程有问题，到时排查
                Output6301 output6301 = req.getOutput6301();
                Setlinfo setlinfo = output6301.getExtData().getSetlinfo();

                invoice.setTotalAmount(setlinfo.getMedfeeSumamt());
                invoice.setOwnFee(setlinfo.getFulamtOwnpayAmt().add(setlinfo.getOverlmtSelfpay()));
                invoice.setFlPay(setlinfo.getPreselfpayAmt());
                invoice.setJzPay(BigDecimal.valueOf(0));
                invoice.setInsuranceCashTotal(BigDecimal.valueOf(0));
                invoice.setInsuranceTotal(setlinfo.getInscpScpAmt().add(setlinfo.getPreselfpayAmt()));
                invoice.setCostTotal(setlinfo.getInscpScpAmt());
                invoice.setAppendPay(BigDecimal.valueOf(0));
                invoice.setPubPay(setlinfo.getFundPaySumamt());
                invoice.setCurrAccountPay(setlinfo.getAcctPay());
                invoice.setLastAccountPay(BigDecimal.valueOf(0));
                invoice.setCurrAccountBalance(setlinfo.getBalc());
                invoice.setLastAccountBalance(BigDecimal.valueOf(0));
                invoice.setTradeSerialNo(setlinfo.getSetlId());
                invoice.setPayFee(setlinfo.getPsnCashPay());
                invoice.setReceiveAmount(invoice.getPayFee());
                invoice.setCashFee(invoice.getPayFee());
            } else {
                // 上海医保发票
                invoice.setChargeType(86); // his调整, 上海医保发票chargeType使用86
                SI12Response si12Response = req.getSe04Response().getSi12Response();
                invoice.setTotalAmount(si12Response.getYbjsfwfyze().add(si12Response.getFybjsfwfyze()));
                BigDecimal pubAccount = si12Response.getCuraccountpay().add(si12Response.getHisaccountpay());
                BigDecimal pub = si12Response.getTcdzhzfs().add(si12Response.getTczfs()).add(si12Response.getFjdzhzfs()).add(si12Response.getFjzfs());
                BigDecimal self = invoice.getTotalAmount().subtract(pub).subtract(pubAccount);
                invoice.setOwnFee(self);
                invoice.setFlPay(BigDecimal.valueOf(0));
                invoice.setInsuranceCashTotal(si12Response.getZfdxjzfs().add(si12Response.getTcdxjzfs()).add(si12Response.getFjdxjzfs()));
                invoice.setInsuranceTotal(si12Response.getYbjsfwfyze()); // 医保结算范围总额
                invoice.setCostTotal(si12Response.getTotalexpense()); // 交易费用总额
                invoice.setAppendPay(si12Response.getFjzfs()); // 附加支付
                invoice.setPubPay(si12Response.getTczfs()); // 统筹支付
                invoice.setCurrAccountPay(si12Response.getCuraccountpay()); // 账户支付
                invoice.setLastAccountPay(si12Response.getTcdzhzfs().add(si12Response.getFjdzhzfs()).add(si12Response.getZfdlnzhzfs())); // 历年账户支付
                invoice.setCurrAccountBalance(si12Response.getCuraccountamt()); // 当年账户余额
                invoice.setLastAccountBalance(si12Response.getHisaccountamt()); // 历年账户余额
                invoice.setTradeSerialNo(si12Response.getLsh()); // 医保交易流水号
                invoice.setPayFee(invoice.getOwnFee().add(invoice.getInsuranceCashTotal())); // 应付金额
                invoice.setReceiveAmount(invoice.getPayFee()); // 预收
                invoice.setCashFee(invoice.getPayFee());
            }
        }

        outpatientInvoiceTimeMapper.insert(invoice);

        // 2. insert into Reg_Tb_OutpatientInvoiceSupply_Time;
        // @InvoiceID(62974)
        //@InvoicePrefix(ZZS)
        //@InvoiceNo(1658)
        //@OutpatientNo(**********)
        //@Flag(1)
        //@OpTime(2024/3/23 8:41:26)
        //@OpCode(hisHospitalProperties.getOpCode())
        //@IsDelete(False)
        //@CreatedBy(hisHospitalProperties.getOpCode())
        //@CreatedDate(2024/3/23 8:41:26)
        //@UpdateBy(hisHospitalProperties.getOpCode())
        //@UpdateDate(2024/3/23 8:41:26)
        //@HospitalCode(99)
        OpInvoiceSupplyTime supply = new OpInvoiceSupplyTime();
        supply.setInvoiceID(invoiceID);
        supply.setInvoicePrefix(invoice.getInvoicePrefix());
        supply.setInvoiceNo(invoice.getInvoiceNo());
        supply.setOutpatientNo(invoice.getOutpatientNo());
        supply.setFlag(1);
        supply.setOpTime(invoice.getOpTime());
        supply.setOpCode(hisHospitalProperties.getOpCode());
        supply.setIsDelete(false);
        supply.setCreatedBy(hisHospitalProperties.getOpCode());
        supply.setCreatedDate(invoice.getCreatedDate());
        supply.setUpdateBy(hisHospitalProperties.getOpCode());
        supply.setUpdateDate(invoice.getUpdateDate());
        supply.setHospitalCode(Integer.valueOf(hisHospitalProperties.getCode()));

        opInvoiceSupplyTimeMapper.insert(supply);

        // 3. insert into Reg_Tb_OutpatientInvoicePayway_Time;
        // @SerialNo(0)
        //@InvoiceID(62974)
        //@ChargeNo(0)
        //@Payway(0)
        //@PaywayName(现金)
        //@PayAmount(8)
        //@OpCode(hisHospitalProperties.getOpCode())
        //@OpTime(2024/3/23 8:41:26)
        //@Status(0)
        //@IsDelete(False)
        //@CreatedBy(hisHospitalProperties.getOpCode())
        //@CreatedDate(2024/3/23 8:41:26)
        //@UpdateBy(hisHospitalProperties.getOpCode())
        //@HospitalCode(99)
        //@UpdateDate(2024/3/23 8:41:26)
        OpInvoicePaywayTime payway = new OpInvoicePaywayTime();
//        payway.setSerialNo(invoice.getSerialNo());
        payway.setInvoiceID(invoiceID);
        // 挂号发票没有账单号
        payway.setChargeNo(charge.getChargeNo());
        // pay_type 0 现金 1 微信 2 支付宝
        setPaywayInfo(req.getPay_type(), payway);
        payway.setPayAmount(invoice.getPayFee());
        payway.setOpCode(hisHospitalProperties.getOpCode());
        payway.setOpTime(date);
        payway.setStatus(0);
        payway.setIsDelete(false);
        payway.setCreatedBy(hisHospitalProperties.getOpCode());
        payway.setCreatedDate(date);
        payway.setUpdateBy(hisHospitalProperties.getOpCode());
        payway.setHospitalCode(Integer.valueOf(hisHospitalProperties.getCode()));
        payway.setUpdateDate(date);

        opInvoicePaywayTimeMapper.insert(payway);

        // 挂号结算5.发票号跳号
//        updateInvoiceCurrentNo(invoiceID, -1, hisHospitalProperties.getOpCode()L);

        return invoice;
    }

    @Override
    @Transactional
    public Long iteratorRegisterInvoiceInfo(Long sourceInvoiceId, Long returnRegNo) {
        // 生成SerialNo, InvoiceID
        Long invoiceID = regInvoiceIDProcessor.execute();
        Date date = new Date();
        String hospitalCode = null;
        String opCode = null;
        String invoiceInfo = null;
        OutpatientInvoice outpatientInvoice = outpatientInvoiceMapper.selectById(sourceInvoiceId);
        OutpatientInvoiceTime outpatientInvoiceTime = outpatientInvoiceTimeMapper.selectById(sourceInvoiceId);
        if (outpatientInvoice != null ){
            hospitalCode = outpatientInvoice.getHospitalCode() + "";
            opCode = outpatientInvoice.getOpCode() + "";
            invoiceInfo = outpatientInvoice.getInvoiceInfo();
            OutpatientInvoiceTime returnOutpatientInvoice = new OutpatientInvoiceTime();
            BeanUtils.copyProperties(outpatientInvoice, returnOutpatientInvoice);
            returnOutpatientInvoice.setRegNo(returnRegNo);
            returnOutpatientInvoice.setInvoiceID(invoiceID);
            returnOutpatientInvoice.setReturnInvoiceID(sourceInvoiceId);
            returnOutpatientInvoice.setOpTime(date);
            returnOutpatientInvoice.setCreatedBy(hisHospitalProperties.getOpCode());
            returnOutpatientInvoice.setCreatedDate(date);
            returnOutpatientInvoice.setPrintTime(date);
            returnOutpatientInvoice.setPrinter(hisHospitalProperties.getOpCode());
            returnOutpatientInvoice.setUpdateBy(hisHospitalProperties.getOpCode());
            returnOutpatientInvoice.setUpdateDate(date);
            returnOutpatientInvoice.setReturnOpCode(hisHospitalProperties.getOpCode());
            returnOutpatientInvoice.setReturnOpTime(date);
            returnOutpatientInvoice.setTotalAmount(outpatientInvoice.getTotalAmount().negate());
            returnOutpatientInvoice.setOwnFee(outpatientInvoice.getOwnFee().negate());
            returnOutpatientInvoice.setReceiveAmount(outpatientInvoice.getReceiveAmount().negate());
            returnOutpatientInvoice.setChangeAmount(outpatientInvoice.getChangeAmount().negate());
            returnOutpatientInvoice.setErrorCents(outpatientInvoice.getErrorCents().negate());
            returnOutpatientInvoice.setPayFee(outpatientInvoice.getPayFee().negate());
            returnOutpatientInvoice.setPubPay(outpatientInvoice.getPubPay().negate());
            returnOutpatientInvoice.setCurrAccountPay(outpatientInvoice.getCurrAccountPay().negate());
            returnOutpatientInvoice.setInsuranceTotal(outpatientInvoice.getInsuranceTotal().negate());
            returnOutpatientInvoice.setInsuranceCashTotal(outpatientInvoice.getInsuranceCashTotal().negate());
            returnOutpatientInvoice.setCashFee(outpatientInvoice.getCashFee().negate());
            returnOutpatientInvoice.setCostTotal(outpatientInvoice.getCostTotal().negate());
            returnOutpatientInvoice.setStatus(120);
            outpatientInvoiceTimeMapper.insert(returnOutpatientInvoice);

            outpatientInvoice.setStatus(80);
            outpatientInvoice.setReturnInvoiceID(invoiceID);
            outpatientInvoice.setUpdateDate(date);
            outpatientInvoice.setUpdateBy(hisHospitalProperties.getOpCode());
            outpatientInvoice.setReturnOpCode(hisHospitalProperties.getOpCode());
            outpatientInvoice.setReturnOpTime(date);
            outpatientInvoiceMapper.updateById(outpatientInvoice);
            // payway
            LambdaQueryWrapper<OpInvoicePayway> wrapper = new LambdaQueryWrapper<>();
            wrapper.eq(OpInvoicePayway::getInvoiceID, sourceInvoiceId);
            List<OpInvoicePayway> payways = opInvoicePaywayMapper.selectList(wrapper);
            for (OpInvoicePayway payway : payways) {
                OpInvoicePaywayTime returnPayway = new OpInvoicePaywayTime();
                BeanUtils.copyProperties(payway, returnPayway, "serialNo");
                returnPayway.setInvoiceID(invoiceID);
                returnPayway.setOpTime(date);
                returnPayway.setCreatedBy(hisHospitalProperties.getOpCode());
                returnPayway.setCreatedDate(date);
                returnPayway.setUpdateBy(hisHospitalProperties.getOpCode());
                returnPayway.setUpdateDate(date);
                returnPayway.setStatus(120);
                returnPayway.setPayAmount(payway.getPayAmount().negate());
                opInvoicePaywayTimeMapper.insert(returnPayway);

                payway.setStatus(80);
                payway.setUpdateBy(hisHospitalProperties.getOpCode());
                payway.setUpdateDate(date);
                opInvoicePaywayMapper.updateById(payway);
            }
        }

        if (outpatientInvoiceTime != null) {
            hospitalCode = outpatientInvoiceTime.getHospitalCode() + "";
            opCode = outpatientInvoiceTime.getOpCode() + "";
            invoiceInfo = outpatientInvoiceTime.getInvoiceInfo();
            OutpatientInvoiceTime returnOutpatientInvoiceTime = new OutpatientInvoiceTime();
            BeanUtils.copyProperties(outpatientInvoiceTime, returnOutpatientInvoiceTime);
            returnOutpatientInvoiceTime.setRegNo(returnRegNo);
            returnOutpatientInvoiceTime.setInvoiceID(invoiceID);
            returnOutpatientInvoiceTime.setReturnInvoiceID(sourceInvoiceId);
            returnOutpatientInvoiceTime.setOpTime(date);
            returnOutpatientInvoiceTime.setCreatedBy(hisHospitalProperties.getOpCode());
            returnOutpatientInvoiceTime.setCreatedDate(date);
            returnOutpatientInvoiceTime.setUpdateBy(hisHospitalProperties.getOpCode());
            returnOutpatientInvoiceTime.setUpdateDate(date);
            returnOutpatientInvoiceTime.setReturnOpCode(hisHospitalProperties.getOpCode());
            returnOutpatientInvoiceTime.setReturnOpTime(date);
            returnOutpatientInvoiceTime.setTotalAmount(outpatientInvoiceTime.getTotalAmount().negate());
            returnOutpatientInvoiceTime.setOwnFee(outpatientInvoiceTime.getOwnFee().negate());
            returnOutpatientInvoiceTime.setReceiveAmount(outpatientInvoiceTime.getReceiveAmount().negate());
            returnOutpatientInvoiceTime.setChangeAmount(outpatientInvoiceTime.getChangeAmount().negate());
            returnOutpatientInvoiceTime.setErrorCents(outpatientInvoiceTime.getErrorCents().negate());
            returnOutpatientInvoiceTime.setPayFee(outpatientInvoiceTime.getPayFee().negate());
            returnOutpatientInvoiceTime.setPubPay(outpatientInvoiceTime.getPubPay().negate());
            returnOutpatientInvoiceTime.setCurrAccountPay(outpatientInvoiceTime.getCurrAccountPay().negate());
            returnOutpatientInvoiceTime.setInsuranceTotal(outpatientInvoiceTime.getInsuranceTotal().negate());
            returnOutpatientInvoiceTime.setInsuranceCashTotal(outpatientInvoiceTime.getInsuranceCashTotal().negate());
            returnOutpatientInvoiceTime.setCashFee(outpatientInvoiceTime.getCashFee().negate());
            returnOutpatientInvoiceTime.setCostTotal(outpatientInvoiceTime.getCostTotal().negate());

            returnOutpatientInvoiceTime.setStatus(120);
            outpatientInvoiceTimeMapper.insert(returnOutpatientInvoiceTime);

            outpatientInvoiceTime.setStatus(80);
            outpatientInvoiceTime.setReturnInvoiceID(invoiceID);
            outpatientInvoiceTime.setUpdateDate(date);
            outpatientInvoiceTime.setUpdateBy(hisHospitalProperties.getOpCode());
            outpatientInvoiceTime.setReturnOpCode(hisHospitalProperties.getOpCode());
            outpatientInvoiceTime.setReturnOpTime(date);
            outpatientInvoiceTimeMapper.updateById(outpatientInvoiceTime);
            // payway
            LambdaQueryWrapper<OpInvoicePaywayTime> wrapper = new LambdaQueryWrapper<>();
            wrapper.eq(OpInvoicePaywayTime::getInvoiceID, sourceInvoiceId);
            List<OpInvoicePaywayTime> payways = opInvoicePaywayTimeMapper.selectList(wrapper);
            for (OpInvoicePaywayTime payway : payways) {
                OpInvoicePaywayTime returnPayway = new OpInvoicePaywayTime();
                BeanUtils.copyProperties(payway, returnPayway, "serialNo");
                returnPayway.setInvoiceID(invoiceID);
                returnPayway.setOpTime(date);
                returnPayway.setCreatedBy(hisHospitalProperties.getOpCode());
                returnPayway.setCreatedDate(date);
                returnPayway.setUpdateBy(hisHospitalProperties.getOpCode());
                returnPayway.setUpdateDate(date);
                returnPayway.setStatus(120);
                returnPayway.setPayAmount(payway.getPayAmount().negate());
                opInvoicePaywayTimeMapper.insert(returnPayway);

                payway.setStatus(80);
                payway.setUpdateBy(hisHospitalProperties.getOpCode());
                payway.setUpdateDate(date);
                opInvoicePaywayTimeMapper.updateById(payway);
            }


        }

        // 发生了红冲记录写入行为，需要调电子发票接口来撤销原电子发票
        // 2024年07月12日16:06:31  目前只会有挂号的红冲
        if (StringUtils.isNotBlank(hospitalCode)) {
            InvoiceScheduler.delayTask(new PushInvoiceMessageParam(sourceInvoiceId, 3, 2, hospitalCode, opCode, invoiceInfo));
        }

        return invoiceID;
    }

    private void setPaywayInfo(String payType, OpInvoicePaywayTime payway) {
        //0 现金 1 微信小程序 2支付宝 3 微信公众号
        String paywayName;
        // 2024年07月04日10:10:12 发票打印只识别微信 现金 支付宝
        switch (payType) {
            case "0":
                paywayName = "现金";
                break;
            case "2":
                paywayName = "支付宝小程序";
                break;
            case "3":
                paywayName = "微信公众号";
                break;
            case "1":
            default:
                paywayName = "微信小程序";
                break;
        }
        TBDicHisDictionary tbDicHisDictionary = dictionaryMapper.selectByDictionaryTypeIDAndHisDictionaryNameAndHospitalId(
                56, paywayName, hisHospitalProperties.getCode());
        payway.setPaywayName(tbDicHisDictionary.getHisDictionaryName());
        payway.setPayway(tbDicHisDictionary.getHisDictionaryCode());
    }

    /**
     * 获取正常门诊发票
     * @param regNo
     * @param patId
     * @param hospitalCode
     * @return
     */
    @Override
    public OutpatientInvoiceView getNormalOutpatientInvoice(String patId, String regNo, String hospitalCode,
                                                            Integer flag) {
        LambdaQueryWrapper<OutpatientInvoiceView> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(OutpatientInvoiceView::getStatus, 0)
            .eq(OutpatientInvoiceView::getRegNo, regNo)
            .eq(OutpatientInvoiceView::getPatID, patId)
            .eq(OutpatientInvoiceView::getHospitalCode, hospitalCode)
            .eq(OutpatientInvoiceView::getFlag, flag);
        return outpatientInvoiceViewMapper.selectOne(wrapper);
    }

    private String getInvoiceInfoWithoutPrefix() {
        Date date = new Date();
        String hmmss = TimeUtils.dateStringFormat(date, "MMddHHmmss");
        return hmmss + String.format("%04d", SequenceUtils.getSequence());
    }
}
