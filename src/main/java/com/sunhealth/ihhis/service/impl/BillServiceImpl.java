package com.sunhealth.ihhis.service.impl;

import com.sunhealth.ihhis.config.HisHospitalProperties;
import com.sunhealth.ihhis.dao.his.BillMapper;
import com.sunhealth.ihhis.model.dto.bill.BillOrder;
import com.sunhealth.ihhis.model.dto.bill.BillOrderDTO;
import com.sunhealth.ihhis.model.dto.bill.HisBillReq;
import com.sunhealth.ihhis.model.dto.bill.HisBillRes;
import com.sunhealth.ihhis.service.BillService;
import com.sunhealth.ihhis.utils.DecimalUtil;
import com.sunhealth.ihhis.utils.TimeUtils;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.*;

@Slf4j
@Service
@AllArgsConstructor
public class BillServiceImpl implements BillService {

    private final BillMapper billMapper;
    private final HisHospitalProperties hisHospitalProperties;

    @Override
    public HisBillRes getTradeHisBill(HisBillReq req) {
        Date beginDate = TimeUtils.convert(req.getBill_date());
        req.setBeginDate(beginDate);
        Date endDate = TimeUtils.getEndOfDay(beginDate);
        req.setEndDate(endDate);
        req.setOpCode(hisHospitalProperties.getOpCode());
        List<Integer> payTypes = payTypeList(req.getPay_type());

        List<BillOrder> billOrders = new ArrayList<>();
        // 门诊支付
        List<BillOrder> paymentOutPatientBill = billMapper.getPaymentOutPatientBill(req, payTypes);
        billOrders.addAll(paymentOutPatientBill);

        // 门诊退款
        List<BillOrder> refundOutPatientBill = billMapper.getRefundOutPatientBill(req, payTypes);
        billOrders.addAll(refundOutPatientBill);

        List<BillOrder> inPatientBill = billMapper.getInPatientBill(req, payTypes);
        billOrders.addAll(inPatientBill);

        return buildHisBillRes(billOrders);
    }

    /**
     * 支付方式ih->his
     * ih: 1-微信小程序 2-支付宝 3-微信公众号
     * his: 17-微信小程序 11-支付宝小程序 13微信公众号
     */
    private List<Integer> payTypeList(String payType) {
        if ("1".equals(payType)) {
            return Arrays.asList(12,17);
        }
        if ("2".equals(payType)) {
            return Arrays.asList(11);
        }
        if ("3".equals(payType)) {
            return Arrays.asList(13);
        }
        return Arrays.asList(11, 12,17, 13);
    }

    private HisBillRes buildHisBillRes(List<BillOrder> billOrders) {

        List<BillOrderDTO> billOrderDTOS = new ArrayList<>();

        HisBillRes hisBillRes = new HisBillRes();
        hisBillRes.setOrder_count(billOrders.size() + "");
        hisBillRes.setRecharge_voucher_refund_amount_count("0.00");
        hisBillRes.setHandling_fee_count("0.00");
        BigDecimal settlement = new BigDecimal("0.00");
        BigDecimal refundAmount = new BigDecimal("0.00");
        BigDecimal orderAmount = new BigDecimal("0.00");
        BigDecimal raAmount = new BigDecimal("0.00");

        for (BillOrder billOrder : billOrders) {
            if (Objects.nonNull(billOrder.getSettlement_order_amount())){
                settlement = settlement.add(billOrder.getSettlement_order_amount());
            }
            if (Objects.nonNull(billOrder.getRefund_amount())){
                refundAmount = refundAmount.add(billOrder.getRefund_amount());
            }
            if (Objects.nonNull(billOrder.getOrder_amount())){
                orderAmount = orderAmount.add(billOrder.getOrder_amount());
            }
            if (Objects.nonNull(billOrder.getRefund_application_amount())){
                raAmount = raAmount.add(billOrder.getRefund_application_amount());
            }
            BillOrderDTO billOrderDTO = new BillOrderDTO(billOrder);
            switch (billOrder.getPay_type()) {
                case "17":
                    billOrderDTO.setPay_type("1");
                    break;
                case "11":
                    billOrderDTO.setPay_type("2");
                    break;
                case "13":
                    billOrderDTO.setPay_type("3");
                    break;
            }
            billOrderDTOS.add(billOrderDTO);
        }
        hisBillRes.setSettlement_order_amount_count(DecimalUtil.defaultString(settlement, 2));
        hisBillRes.setRefund_amount_count(DecimalUtil.defaultString(refundAmount, 2));
        hisBillRes.setOrder_amount_count(DecimalUtil.defaultString(orderAmount, 2));
        hisBillRes.setRefund_application_amount_count(DecimalUtil.defaultString(raAmount, 2));
        hisBillRes.setOrders(billOrderDTOS);
        return hisBillRes;
    }
}
