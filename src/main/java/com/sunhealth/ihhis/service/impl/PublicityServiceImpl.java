package com.sunhealth.ihhis.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.sunhealth.ihhis.dao.his.PubItemsMapper;
import com.sunhealth.ihhis.model.dto.publicity.PricePublicityReq;
import com.sunhealth.ihhis.model.dto.publicity.PricePublicityRes;
import com.sunhealth.ihhis.model.entity.PubItems;
import com.sunhealth.ihhis.service.PublicityService;
import lombok.AllArgsConstructor;
import org.apache.commons.lang.StringUtils;
import org.springframework.stereotype.Service;

import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

@Service
@AllArgsConstructor
public class PublicityServiceImpl implements PublicityService {

    private final PubItemsMapper pubItemsMapper;

    @Override
    public List<PricePublicityRes> getPricePublicityList(PricePublicityReq request) {
        LambdaQueryWrapper<PubItems> wrapper = Wrappers.lambdaQuery();
        wrapper.eq(PubItems::getStopped, 0);
        wrapper.eq(PubItems::getHospitalId, request.getHospitalCode());
        if (StringUtils.isNotEmpty(request.getKeyword())) {
            wrapper.and(i -> i.like(PubItems::getItemName, request.getKeyword()).or().like(PubItems::getInputCode1, request.getKeyword()));
        }
        if (StringUtils.isNotEmpty(request.getPrice_publicity_type()) && "1".equals(request.getPrice_publicity_type())) {
            // 药品
            wrapper.in(PubItems::getItemCategory, Arrays.asList(12, 13, 14));
        }
        if (StringUtils.isNotEmpty(request.getPrice_publicity_type()) && "2".equals(request.getPrice_publicity_type())) {
            // 项目
            wrapper.notIn(PubItems::getItemCategory, Arrays.asList(12, 13, 14));
        }
        List<PubItems> pubItems = pubItemsMapper.selectList(wrapper);
        return pubItems.stream().map(PricePublicityRes::new).collect(Collectors.toList());
    }
}
