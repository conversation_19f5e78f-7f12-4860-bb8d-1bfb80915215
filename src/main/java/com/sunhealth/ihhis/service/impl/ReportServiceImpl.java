package com.sunhealth.ihhis.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.google.common.collect.Lists;
import com.sunhealth.ihhis.dao.his.*;
import com.sunhealth.ihhis.model.dto.patient.HisPatientSimpleData;
import com.sunhealth.ihhis.model.dto.report.*;
import com.sunhealth.ihhis.model.entity.patient.RTPatientList;
import com.sunhealth.ihhis.model.entity.report.*;
import com.sunhealth.ihhis.service.ReportService;
import com.sunhealth.ihhis.utils.TimeUtils;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.Optional;
import java.util.stream.Stream;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.time.DateUtils;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.List;
import java.util.stream.Collectors;

@Service
@AllArgsConstructor
@Slf4j
public class ReportServiceImpl implements ReportService {

    private final ReportExamInfoMapper reportExamInfoMapper;
    private final ReportExamDetailMapper reportExamDetailMapper;
    private final PatientListMapper patientListMapper;
    private final ReportTestInfoMapper reportTestInfoMapper;
    private final ReportTestDetailMapper reportTestDetailMapper;

    // --- 新增部分：定义时间分割点 ---
    /**
     * 新老数据分割日期。此日期之前的数据（不包含当天）被认为是老数据。
     */
    private static final String SPLIT_DATE_STRING = "2025-05-05";
    private static final Date SPLIT_DATE;

    static {
        try {
            // 初始化静态的 Date 对象，避免每次调用都解析
            SPLIT_DATE = new SimpleDateFormat("yyyy-MM-dd").parse(SPLIT_DATE_STRING);
        } catch (ParseException e) {
            log.error("关键配置[新老数据分割日期]格式错误，应用无法正常工作!", e);
            // 在实际应用中，这种关键配置错误应该导致应用启动失败
            throw new RuntimeException("Invalid SPLIT_DATE_STRING format: " + SPLIT_DATE_STRING, e);
        }
    }




    @Override
    public List<RisReport> getRisReportsByPatId(ReportListReq req) {
        // 1. 预处理：处理用户来源和通过证件号查询PatId的逻辑（保持不变）
        preprocessRequest(req);
        if (StringUtils.isBlank(req.getPatid())) {
            return Lists.newArrayList();
        }

        // 2. 解析前端传入的日期
        Date reqStartDate = TimeUtils.convert(req.getBegin_date());
        Date reqEndDate = DateUtils.addDays(TimeUtils.convert(req.getEnd_date()), 1); // 结束日期+1，用于<比较

        List<RisReport> newReports = new ArrayList<>();
        List<RisReport> oldReports = new ArrayList<>();

        // 3. 根据时间范围决定查询策略
        // 3.1 检查是否需要查询老数据 (请求的开始时间在分割点之前)
        if (reqStartDate.before(SPLIT_DATE)) {
            // 老数据的查询范围：从请求开始时间到[请求结束时间]和[分割点]中的较小者
            Date oldQueryEndDate = reqEndDate.before(SPLIT_DATE) ? reqEndDate : SPLIT_DATE;

            // --- 开始填充旧报告查询逻辑 ---
            log.info("查询老 RIS 数据, patId: {}, 时间范围: {} -> {}", req.getPatid(), reqStartDate, oldQueryEndDate);
            Integer comeFlag = null;
            if ("门诊".equals(req.getUser_source())) {
                comeFlag = 0;
            } else if ("住院".equals(req.getUser_source())) {
                comeFlag = 1;
            }
            List<ReportExamInfo> oldReportInfos = reportExamInfoMapper.selectOldRisReports(
                req.getPatid(), comeFlag, reqStartDate, oldQueryEndDate, null); // 列表查询reportNo为null
            oldReports = oldReportInfos.stream().map(RisReport::new).collect(Collectors.toList());
            // --- 填充结束 ---

        }

        // 3.2 检查是否需要查询新数据 (请求的结束时间在分割点之后)
        if (!reqEndDate.before(SPLIT_DATE)) {
            // 新数据的查询范围：从[请求开始时间]和[分割点]中的较大者到请求结束时间
            Date newQueryStartDate = reqStartDate.after(SPLIT_DATE) ? reqStartDate : SPLIT_DATE;

            ReportListReq newReq = copyRequestWithNewDates(req, newQueryStartDate, reqEndDate);

            log.info("查询新 RIS 数据, patId: {}, 时间范围: {} -> {}", newReq.getPatid(), newReq.getBeginTime(), newReq.getEndTime());
            List<ReportExamInfo> reportExamInfos = reportExamInfoMapper.list(newReq);
            newReports = reportExamInfos.stream().map(RisReport::new).collect(Collectors.toList());
        }

        // 4. 合并并返回结果
        return Stream.concat(oldReports.stream(), newReports.stream()).collect(Collectors.toList());
    }


    // ... getRisReportResult 方法保持不变 ...
    @Override
    public List<RisReportResult> getRisReportResult(ReportResultReq req) {
        // --- 重构此方法以支持新旧数据 ---
        String reportNo = req.getReport_no();
        List<RisReportResult> risReportResults = Lists.newArrayList();

        // 1. 优先查询新库
        LambdaQueryWrapper<ReportExamInfo> wrapper = Wrappers.lambdaQuery();
        wrapper.eq(ReportExamInfo::getReportNo, reportNo);
        Optional<ReportExamInfo> onlyOne = reportExamInfoMapper.selectList(wrapper).stream().findFirst();

        if (onlyOne.isPresent()) {
            // 是新报告，执行原逻辑
            log.info("查询到新库RIS报告详情, reportNo: {}", reportNo);
            ReportExamInfo info = onlyOne.get();
            LambdaQueryWrapper<ReportExamDetail> detailWrapper = Wrappers.lambdaQuery();
            detailWrapper.eq(ReportExamDetail::getReportNo, info.getReportNo());
            List<ReportExamDetail> reportExamDetails = reportExamDetailMapper.selectList(detailWrapper);

            HisPatientSimpleData patient = patientListMapper.getPatientSimpleDataByExamReportNo(info.getReportNo());
            risReportResults = reportExamDetails.stream().map(u -> {
                RisReportResult risReportResult = new RisReportResult(info, u);
                if (patient != null) {
                    risReportResult.setPatname(patient.getName());
                    risReportResult.setAge(patient.getAge());
                    risReportResult.setSex("1".equals(patient.getSex()) ? "男" : "女");
                }
                return risReportResult;
            }).collect(Collectors.toList());

        } else {
            // 新库中未找到，查询旧库
            log.info("新库未找到，尝试查询旧库RIS报告详情, reportNo (RisId): {}", reportNo);
            // a. 查询旧报告主信息 (复用列表查询方法，通过 reportNo 筛选)
            List<ReportExamInfo> oldInfos = reportExamInfoMapper.selectOldRisReports(null, null, null, null, reportNo);
            if (!CollectionUtils.isEmpty(oldInfos)) {
                ReportExamInfo oldInfo = oldInfos.get(0);
                // b. 查询旧报告详情 (模拟的详情)
                List<ReportExamDetail> oldDetails = reportExamInfoMapper.selectOldRisReportDetails(reportNo);

                // c. 组装结果
                risReportResults = oldDetails.stream().map(detail -> {
                    RisReportResult result = new RisReportResult(oldInfo, detail);
                    // 从主信息中填充患者信息
                    result.setPatname(oldInfo.getPatName());
                    // 旧数据中没有性别和年龄，需要额外处理，或留空
                     result.setAge(oldInfo.getAge());
                     result.setSex(oldInfo.getSex());
                    return result;
                }).collect(Collectors.toList());
            }
        }
        return risReportResults;
    }



    @Override
    public List<LisReport> getLisReportsByPatId(ReportListReq req) {
        // 1. 预处理：与 getRisReportsByPatId 逻辑相同
        preprocessRequest(req);
        if (StringUtils.isBlank(req.getPatid())) {
            return Lists.newArrayList();
        }

        // 2. 解析前端传入的日期
        Date reqStartDate = TimeUtils.convert(req.getBegin_date());
        Date reqEndDate = DateUtils.addDays(TimeUtils.convert(req.getEnd_date()), 1);

        List<LisReport> newReports = new ArrayList<>();
        List<LisReport> oldReports = new ArrayList<>();

        // 3. 根据时间范围决定查询策略
        // 3.1 检查是否需要查询老数据 (请求的开始时间在分割点之前)
        if (reqStartDate.before(SPLIT_DATE)) {
            Date oldQueryEndDate = reqEndDate.before(SPLIT_DATE) ? reqEndDate : SPLIT_DATE;

            log.info("查询老 LIS 数据, patId: {}, 时间范围: {} -> {}", req.getPatid(), reqStartDate, oldQueryEndDate);
            RTPatientList patient = patientListMapper.selectByPatId(req.getPatid());
            String sex = patient.getSex() == 1 ? "男":"女";
            List<ReportTestInfo> reportTestInfos = reportTestInfoMapper.selectOldLisReports(patient.getPatName(), sex, null,reqStartDate,
                                                                  oldQueryEndDate, null , null, req.getUser_source());
            oldReports = reportTestInfos.stream().map(LisReport::new).collect(Collectors.toList());
        }

        // 3.2 检查是否需要查询新数据 (请求的结束时间在分割点之后)
        if (!reqEndDate.before(SPLIT_DATE)) {
            Date newQueryStartDate = reqStartDate.after(SPLIT_DATE) ? reqStartDate : SPLIT_DATE;

            ReportListReq newReq = copyRequestWithNewDates(req, newQueryStartDate, reqEndDate);

            log.info("查询新 LIS 数据, patId: {}, 时间范围: {} -> {}", newReq.getPatid(), newReq.getBeginTime(), newReq.getEndTime());
            List<ReportTestInfo> reportTestInfos = reportTestInfoMapper.selectNewList(newReq);
            newReports = reportTestInfos.stream().map(LisReport::new).collect(Collectors.toList());
        }

        // 4. 合并并返回结果
        return Stream.concat(oldReports.stream(), newReports.stream()).collect(Collectors.toList());
    }


    @Override
    public List<LisReportResult> getLisReportResult(ReportResultReq req) {
        String reportNo = req.getReport_no();
        List<LisReportResult> lisReportResultList = Lists.newArrayList();
        List<ReportTestInfo> infos = reportTestInfoMapper.selectByReportNo(reportNo);
        if (!CollectionUtils.isEmpty(infos)) {
            HisPatientSimpleData patient = patientListMapper.getPatientSimpleDataByTestReportNo(reportNo);
            // 根据报告信息查到报告详情
            List<ReportTestDetail> reportTestDetails = reportTestDetailMapper.selectByReportNo(reportNo);
            lisReportResultList = reportTestDetails.stream().map(u -> {
                LisReportResult lisReportResult = new LisReportResult(infos.get(0), u);
                lisReportResult.setPatname(patient.getName());
                lisReportResult.setAge(patient.getAge());
                lisReportResult.setSex("1".equals(patient.getSex()) ? "男" : "女");
                return lisReportResult;
            }).collect(Collectors.toList());
        } else {
            // 旧检验报告是 202502084700064 这种格式 转换成 Date类型报告日期 2025-02-08 00:00:00.000 和 报告编号 4700064
            // 2. 拆分字符串
            String dateStr = reportNo.substring(0, 8);
            String reportNumber = reportNo.substring(8);
            Date reportDate = TimeUtils.convert(dateStr);
            ReportTestInfo newInfo = reportTestInfoMapper.selectOldLisReports(null, null, null, null, null,
                                                                                   reportNumber,
                                                                           reportDate, null).get(0);
            List<ReportTestDetail> reportTestDetails = reportTestInfoMapper.selectOldLisReportResults(reportNumber,
                                                                                                    reportDate);
            lisReportResultList = reportTestDetails.stream().map(u -> {
                LisReportResult lisReportResult = new LisReportResult(newInfo, u);
                lisReportResult.setPatname(newInfo.getPatName());
                lisReportResult.setAge(newInfo.getAge());
                lisReportResult.setSex(newInfo.getSex());
                return lisReportResult;
            }).collect(Collectors.toList());
        }
        return lisReportResultList;
    }


    // --- 新增的辅助方法 ---

    /**
     * 预处理请求，包括转换用户来源和通过证件号查找patId
     * @param req 请求对象
     */
    private void preprocessRequest(ReportListReq req) {
        if ("1".equals(req.getUser_source())) {
            req.setUser_source("门诊");
        } else if ("2".equals(req.getUser_source())) {
            req.setUser_source("住院");
        } else {
            // 可以设置一个默认值或保持不变
            req.setUser_source(null);
        }

        if (StringUtils.isBlank(req.getPatid()) && StringUtils.isNotBlank(req.getCertificate_no())) {
            LambdaQueryWrapper<RTPatientList> patientWrapper = Wrappers.lambdaQuery();
            patientWrapper.eq(RTPatientList::getCertificateNo, req.getCertificate_no());
            patientWrapper.eq(RTPatientList::getCertificateType, "1");
            List<RTPatientList> patients = patientListMapper.selectList(patientWrapper);
            if (!CollectionUtils.isEmpty(patients)) {
                // 优先取 isUse=1 的记录，如果没有，则取第一条
                String patId = patients.stream()
                    .filter(p -> p.getIsUse() == 1)
                    .map(p -> p.getPatId().toString())
                    .findFirst()
                    .orElse(patients.get(0).getPatId().toString());
                req.setPatid(patId);
            }
        }
    }

    /**
     * 复制请求对象并设置新的时间范围，避免修改原始请求对象
     * @param original 原始请求
     * @param beginTime 新的开始时间
     * @param endTime 新的结束时间
     * @return 带有新时间范围的复制对象
     */
    private ReportListReq copyRequestWithNewDates(ReportListReq original, Date beginTime, Date endTime) {
        ReportListReq copy = new ReportListReq();
        copy.setPatid(original.getPatid());
        copy.setUser_source(original.getUser_source());
        copy.setPatname(original.getPatname());
        copy.setHospitalCode(original.getHospitalCode());
        copy.setCertificate_no(original.getCertificate_no());
        // 设置新的时间
        copy.setBeginTime(beginTime);
        copy.setEndTime(endTime);
        return copy;
    }
}
