package com.sunhealth.ihhis.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.sunhealth.ihhis.config.HisHospitalProperties;
import com.sunhealth.ihhis.config.YiBaoProperties;
import com.sunhealth.ihhis.dao.his.*;
import com.sunhealth.ihhis.model.dto.register.CancelAppointmentReq;
import com.sunhealth.ihhis.model.dto.register.LockNumberResult;
import com.sunhealth.ihhis.model.dto.register.SaveAppointmentReq;
import com.sunhealth.ihhis.model.dto.register.SaveAppointmentResult;
import com.sunhealth.ihhis.model.dto.schedule.ScheduleDetail;
import com.sunhealth.ihhis.model.dto.schedule.SourceNumber;
import com.sunhealth.ihhis.model.dto.schedule.SourceNumberReq;
import com.sunhealth.ihhis.model.entity.MedicalWorker;
import com.sunhealth.ihhis.model.entity.patient.RTPatientDetl;
import com.sunhealth.ihhis.model.entity.patient.RTPatientList;
import com.sunhealth.ihhis.model.entity.register.Appointment;
import com.sunhealth.ihhis.model.entity.register.AppointmentLockNum;
import com.sunhealth.ihhis.service.*;
import com.sunhealth.ihhis.service.processer.MzNoProcessor;
import com.sunhealth.ihhis.service.processer.PatIDProcessor;
import com.sunhealth.ihhis.service.processer.RegDtlNoProcessor;
import com.sunhealth.ihhis.utils.DecimalUtil;
import com.sunhealth.ihhis.utils.TimeUtils;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

@Service
@Slf4j
@AllArgsConstructor
public class AppointmentServiceImpl implements AppointmentService {
    private final MsgPushService msgPushService;
    private final YiBaoProperties yiBaoProperties;
    private final PreRegisterListMapper preRegisterListMapper;
    private final PreRegisterDetailMapper preRegisterDetailMapper;
    private final PatientListMapper patientListMapper;
    private final SchedulingMapper schedulingMapper;
    private final ChargeItemViewMapper chargeItemViewMapper;
    private final MedicalWorkerService medicalWorkerService;
    private final RegisterListTimeMapper registerListTimeMapper;
    private final RegisterListMapper registerListMapper;
    private final RegisterDetailTimeMapper registerDetailTimeMapper;
    private final RegisterDetailMapper registerDetailMapper;
    private final RegisterListViewMapper registerListViewMapper;
    private final InvoiceService invoiceService;
    private final RecipeEInvoiceNoMapper recipeEInvoiceNoMapper;
    private final PatientDetlMapper patientDetlMapper;
    private final MzNoProcessor mzNoProcessor;
    private final RegDtlNoProcessor regDtlNoProcessor;
    private final PatientCardMapper patientCardMapper;
    private final PatIDProcessor patIDProcessor;
    private final AppointmentMapper appointmentMapper;
    private final AppointmentLockNumMapper appointmentLockNumMapper;
    private final DeptMapper deptMapper;
    private final PatientListService patientListService;
    private final RegOnlineGjYiBaoUploadFeeRecordMapper regOnlineGjYiBaoUploadFeeRecordMapper;
    private final RegOnlineSHYiBaoUploadFeeRecordMapper regOnlineSHYiBaoUploadFeeRecordMapper;
    private final RegOnlineGjYiBao6202OutputExtDataMapper regOnlineGjYiBao6202OutputExtDataMapper;
    private final RegOnlineGjYiBaoOrderInfoMapper regOnlineGjYiBaoOrderInfoMapper;
    private final HisHospitalProperties hisHospitalProperties;
    private final GJYiBaoRegisterMapper gjYiBaoRegisterMapper;
    private final GJYiBaoRefundRegisterMapper gjYiBaoRefundRegisterMapper;
    private final RegFeeConfigMapper regFeeConfigMapper;
    private final RegWhiteListMapper regWhiteListMapper;
    private final MedicalWorkerMapper medicalWorkerMapper;
    private final PatientInfoMapper patientInfoMapper;
    private final PatientRegisterMapper patientRegisterMapper;
    private final GJYiBaoPatientAccountMapper gjYiBaoPatientAccountMapper;
    private final RegInsuranceRegisterMapper regInsuranceRegisterMapper;


    @Transactional
    public SaveAppointmentResult appointment(SaveAppointmentReq req) {
        Integer status = 1;
        SourceNumberReq sourceNumberReq = new SourceNumberReq();
        sourceNumberReq.setHospitalCode(req.getHospitalCode());
        sourceNumberReq.setScheduling_id(req.getScheduling_id());
        sourceNumberReq.setSource_number(req.getSource_number());
        // 通过scheduling_id， source_number查询排班信息
        List<SourceNumber> sourceNumbers = schedulingMapper.getSchedulingSourceNumber(sourceNumberReq);
        SourceNumber sourceNumber = sourceNumbers.stream().filter(u -> u.getStatus() == 0).findFirst()
            .orElse(null);
        if (CollectionUtils.isEmpty(sourceNumbers) || sourceNumber == null) {
            log.error("预约登记：未查询到排班号源信息，schedulingId:{}，sourceNumber:{} status: 1", req.getScheduling_id(),
                      req.getSource_number());
            throw new RuntimeException("预约登记：未查询到排班号源信息");
        }
        ScheduleDetail scheduleDetail = schedulingMapper.selectScheduleDetail(req.getScheduling_id(),
                                                                              req.getHospitalCode());
        // 同一人同一天同一个科室只能预约一个号
        RTPatientList patientList = patientListMapper.selectByPatId(req.getPatid());
        if (patientList == null) {
            throw new RuntimeException("院内无此患者信息或患者信息已停用");
        }

        Date start = TimeUtils.getStartOfDay(sourceNumber.getDutyDate());
        Date end = TimeUtils.getEndOfDay(sourceNumber.getDutyDate());

        List<Appointment> todayAppointments = appointmentMapper.selectTodayAppointments(start, end,
                                                                                        scheduleDetail.getDeptID(),
                                                                                        scheduleDetail.getDoctorID(),
                                                                                        patientList.getCertificateNo());
        if (!CollectionUtils.isEmpty(todayAppointments)) {
            throw new RuntimeException("预约：当天已在该科室预约或挂号过，无法再次预约");
        }

        // 查询TB_APPOINTMENT表，如果存在则直接返回成功，消息提示不需要锁号，可直接挂号
        LambdaQueryWrapper<Appointment> wrapper = Wrappers.lambdaQuery();
        wrapper.eq(Appointment::getTimeSpanID, sourceNumber.getTimespanId());
        wrapper.eq(Appointment::getCheckDate, sourceNumber.getDutyDate());
        wrapper.eq(Appointment::getAppointmentSeqNum, sourceNumber.getSeqNum());
        wrapper.eq(Appointment::getSubjectID, sourceNumber.getSubjectID());
        wrapper.in(Appointment::getAppointmentStatus, 0,5);
        List<Appointment> appointments = appointmentMapper.selectList(wrapper);
        if (!CollectionUtils.isEmpty(appointments)) {
            throw new RuntimeException("预约：该号源已被预约，请重新选择号源预约");
        }
        sourceNumber.setStatus(status);
        schedulingMapper.updateSourceNumberStatus(sourceNumber);


        RTPatientDetl patientDetl = patientDetlMapper.selectById(req.getPatid());

        AppointmentLockNum appointmentLockNum = new AppointmentLockNum();

        // 保存预占号信息TB_APPOINTMENT_LOCKNUM
        appointmentLockNum.setSubjectID(sourceNumber.getSubjectID());
        appointmentLockNum.setDutyDate(sourceNumber.getDutyDate());
        appointmentLockNum.setTimeSpanID(sourceNumber.getTimespanId());
        appointmentLockNum.setSourceId(Integer.parseInt(req.getScheduling_id()));
        appointmentLockNum.setSeqNum(Integer.valueOf(sourceNumber.getSeqNum()));
        appointmentLockNum.setCertificateNo(patientList.getCertificateNo());
        appointmentLockNum.setLockTime(new Date());
        appointmentLockNum.setNumId(sourceNumber.getNumId());
        appointmentLockNum.setHospitalCode(req.getHospitalCode());
        appointmentLockNum.setFlag(0);
        appointmentLockNum.setVisitFlag(0);
        appointmentLockNum.setIsCZF(0);

        appointmentLockNumMapper.insertAppointmentLockNum(appointmentLockNum);
        appointmentLockNum = appointmentLockNumMapper.selectAppointmentLockNumBySqh(appointmentLockNum.getSqh());

        Date date = new Date();
        // 保存预约信息
        Appointment appointment = new Appointment();
        appointment.setPatName(patientList.getPatName());
        appointment.setCertificateNo(StringUtils.trim(patientList.getCertificateNo()));
        appointment.setTelephone(patientDetl.getPatPhone());
        appointment.setSubjectID(sourceNumber.getSubjectID());
        appointment.setVisitDate(TimeUtils.dateStringFormat(sourceNumber.getDutyDate(), "yyyy-MM-dd") + " " + sourceNumber.getStartTime() + "-" + sourceNumber.getEndTime());
        appointment.setAppointmentStatus(0);
        appointment.setCheckDate(sourceNumber.getDutyDate());
        appointment.setTimeSpanID(sourceNumber.getTimespanId());
        appointment.setSqh(appointmentLockNum.getSqh());
        appointment.setHospitalCode(hisHospitalProperties.getCode());
        appointment.setAppointmentSeqNum(Integer.valueOf(sourceNumber.getSeqNum()));
        appointment.setAppointmentNum(appointment.getAppointmentSeqNum());
        Integer visiteCount = registerListTimeMapper.checkVisitFlag(appointment.getCertificateNo(),
                                                          Integer.valueOf(appointment.getHospitalCode()));
        if (visiteCount > 0) {
            appointment.setAppointmentType(2);
            appointment.setCfz(Short.valueOf("2"));
        } else {
            appointment.setAppointmentType(1);
            appointment.setCfz(Short.valueOf("1"));
        }
        appointment.setAppointmentOrderID(appointmentLockNum.getLockNumOrderID());
        appointment.setDeptId(scheduleDetail.getDeptID());
        appointment.setDoctorId(scheduleDetail.getDoctorID());
        appointment.setZtStatus(0);
        appointment.setCreatedBy(hisHospitalProperties.getOpCode());
        appointment.setOperator(hisHospitalProperties.getOpCode());
        appointment.setCreatedDate(date);
        appointment.setUpdatedBy(hisHospitalProperties.getOpCode());
        appointment.setUpdatedDate(date);
        appointment.setIsDelete(0);
        appointment.setIsUse(1);
        appointment.setSourceID(appointmentLockNum.getSourceId());
        appointment.setBeginTime(sourceNumber.getStartTime());
        appointment.setEndTime(sourceNumber.getEndTime());
        appointment.setBmbh("xiaochengxu");
        appointment.setSex(patientList.getSex() + "");
        // 时段名称
        appointment.setTimsMs(sourceNumber.getName());
        appointment.setRemarkA1("GTYY");
        appointment.setCardNo(req.getMemo());
        appointment.setPatSfz(patientList.getCertificateNo());
        appointment.setMovePhone(patientDetl.getPatPhone());
        appointment.setPatNo(Math.toIntExact(patientList.getPatId()));

        appointmentMapper.insert(appointment);

        SaveAppointmentResult saveAppointmentResult = new SaveAppointmentResult();
        saveAppointmentResult.setSuccess("true");
        saveAppointmentResult.setMessage("预约登记成功");
        saveAppointmentResult.setAppointment_id(appointment.getAppointmentID() + "");
        saveAppointmentResult.setDept_id(appointment.getDeptId().toString());
        saveAppointmentResult.setDept_name(deptMapper.selectDeptName(saveAppointmentResult.getDept_id(), req.getHospitalCode()));
        saveAppointmentResult.setDoctor_id(appointment.getDoctorId().toString());
        MedicalWorker doctor = medicalWorkerMapper.selectByDoctorIdAndHospitalCode(
            saveAppointmentResult.getDoctor_id(),
            req.getHospitalCode());
        saveAppointmentResult.setDoctor_name(doctor.getName());
        saveAppointmentResult.setVisit_date(TimeUtils.getHisDateStr(sourceNumber.getDutyDate()));
        saveAppointmentResult.setAppoint_date(TimeUtils.getHisDateStr(new Date()));
        saveAppointmentResult.setSource_number(appointment.getSqh().toString());
        BigDecimal registrationFee = regFeeConfigMapper.selectRegistrationFeeByDoctorLevel(scheduleDetail.getDoctorLevel(),
                                                                                      req.getHospitalCode());
        BigDecimal treatmentFee = regFeeConfigMapper.selectTreatmentFeeByDoctorLevel(scheduleDetail.getDoctorLevel(),
                                                                                     req.getHospitalCode());
        if (registrationFee == null) {
            registrationFee = BigDecimal.ZERO;
        }
        if (treatmentFee == null) {
            treatmentFee = BigDecimal.ZERO;
        }
        saveAppointmentResult.setRegistration_fee(DecimalUtil.defaultString(registrationFee));
        saveAppointmentResult.setTreatment_fee(DecimalUtil.defaultString(treatmentFee));
        saveAppointmentResult.setShould_pay_amount(DecimalUtil.defaultString(registrationFee.add(treatmentFee)));
        saveAppointmentResult.setTotal_amount(saveAppointmentResult.getShould_pay_amount());
        saveAppointmentResult.setMemo("");
        return saveAppointmentResult;
    }

    @Transactional
    @Override
    public LockNumberResult returnAppointment(CancelAppointmentReq req) {
        LockNumberResult lockNumberResult = new LockNumberResult();

        Appointment appointment = appointmentMapper.selectById(req.getAppointment_id());
        if (appointment != null) {
            log.info("锁号解锁：已存在预约信息，更改预约状态为退号  lock_number_id(SQH):{}", appointment.getSqh());
            schedulingMapper.updateAppointmentStatus(appointment.getAppointmentID().toString(), 3);
        } else {
            lockNumberResult.setSuccess(false);
            lockNumberResult.setMessage("根据预约id" + req.getAppointment_id() + "未查询到预约信息");
            return lockNumberResult;
        }
        Integer sqh = appointment.getSqh();
        // 查询预占号信息
        AppointmentLockNum appointmentLockNum =
            appointmentLockNumMapper.selectAppointmentLockNumBySqh(sqh);
        if (appointmentLockNum == null) {
            log.error("未查询到预占号信息，lock_number_id:{}", sqh);
        } else {
            // 退号后将TB_Appointment_LockNum表中flag变成2
            appointmentLockNumMapper.updateFlagBySqh(2, sqh);
        }

        // 退号后将TB_Config_SubjectNumDetail的Status变成0
        SourceNumber sourceNumber = new SourceNumber();
        sourceNumber.setNumId(appointmentLockNum.getNumId());
        sourceNumber.setStatus(0);
        schedulingMapper.updateSourceNumberStatus(sourceNumber);

        lockNumberResult.setSuccess(true);
        lockNumberResult.setMessage("预约登记取消成功");
        return lockNumberResult;
    }
}
