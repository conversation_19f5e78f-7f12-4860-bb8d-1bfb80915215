package com.sunhealth.ihhis.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.fasterxml.jackson.core.type.TypeReference;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.sunhealth.ihhis.cache.MemoryCache;
import com.sunhealth.ihhis.config.HisHospitalProperties;
import com.sunhealth.ihhis.config.NoderedProperties;
import com.sunhealth.ihhis.config.YiBaoProperties;
import com.sunhealth.ihhis.dao.his.*;
import com.sunhealth.ihhis.enums.BusinessType;
import com.sunhealth.ihhis.enums.PayChannelType;
import com.sunhealth.ihhis.error.ErrorResponseException;
import com.sunhealth.ihhis.model.dq.sh.insurance.EcTokenDTO;
import com.sunhealth.ihhis.model.dq.sh.insurance.response.*;
import com.sunhealth.ihhis.model.dto.outpatientcharge.*;
import com.sunhealth.ihhis.model.dto.push.Msg;
import com.sunhealth.ihhis.model.dto.push.MsgPushReq;
import com.sunhealth.ihhis.model.dto.push.MsgPushRes;
import com.sunhealth.ihhis.model.dto.register.GetOutpatientPayResult;
import com.sunhealth.ihhis.model.entity.Dept;
import com.sunhealth.ihhis.model.entity.Diagnose;
import com.sunhealth.ihhis.model.entity.MedicalWorker;
import com.sunhealth.ihhis.model.entity.charge.*;
import com.sunhealth.ihhis.model.entity.dqyibao.RegInsuranceCharge;
import com.sunhealth.ihhis.model.entity.dqyibao.RegInsurancePreCharge;
import com.sunhealth.ihhis.model.entity.gjyibao.GJYiBaoCharge;
import com.sunhealth.ihhis.model.entity.gjyibao.GjYiBaoPatientAccount;
import com.sunhealth.ihhis.model.entity.invoice.OutpatientInvoiceTime;
import com.sunhealth.ihhis.model.entity.invoice.OutpatientInvoiceView;
import com.sunhealth.ihhis.model.entity.patient.RTPatientCard;
import com.sunhealth.ihhis.model.entity.patient.RTPatientList;
import com.sunhealth.ihhis.model.entity.register.RecipeEInvoiceNo;
import com.sunhealth.ihhis.model.entity.register.RegisterList;
import com.sunhealth.ihhis.model.entity.register.RegisterListTime;
import com.sunhealth.ihhis.model.entity.view.RegisterListView;
import com.sunhealth.ihhis.model.entity.yibao.RegOnlineGjYiBaoOrderInfo;
import com.sunhealth.ihhis.model.entity.yibao.RegOnlineGjYiBaoUploadFeeRecord;
import com.sunhealth.ihhis.model.entity.yibao.RegOnlineSHYiBaoUploadFeeRecord;
import com.sunhealth.ihhis.model.insurance.MedicalInsuranceParam;
import com.sunhealth.ihhis.model.insurance.request.CallBack6302;
import com.sunhealth.ihhis.model.insurance.request.Input6203;
import com.sunhealth.ihhis.model.insurance.request.Input6301;
import com.sunhealth.ihhis.model.insurance.response.Output6201;
import com.sunhealth.ihhis.model.insurance.response.Output6202;
import com.sunhealth.ihhis.model.insurance.response.Output6203;
import com.sunhealth.ihhis.model.insurance.response.Output6301;
import com.sunhealth.ihhis.model.insurance.response.Output6301.ExtData;
import com.sunhealth.ihhis.model.insurance.response.Output6301.ExtData.Setlinfo;
import com.sunhealth.ihhis.model.vm.PushInvoiceMessageParam;
import com.sunhealth.ihhis.model.vm.RecipeStatusMessageParam;
import com.sunhealth.ihhis.nodered.NodeRedClient;
import com.sunhealth.ihhis.service.*;
import com.sunhealth.ihhis.service.dqyb.DQYiBaoClient;
import com.sunhealth.ihhis.service.processer.ChargeDtlNoProcessor;
import com.sunhealth.ihhis.service.processer.MzNoProcessor;
import com.sunhealth.ihhis.service.processer.PreChargeNoProcessor;
import com.sunhealth.ihhis.service.processer.RegSerialNoProcessor;
import com.sunhealth.ihhis.task.scheduler.InvoiceScheduler;
import com.sunhealth.ihhis.task.scheduler.RecipeStatusScheduler;
import com.sunhealth.ihhis.utils.*;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.jetbrains.annotations.NotNull;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.UUID;
import java.util.stream.Collectors;

@Service
@AllArgsConstructor
@Slf4j
public class ChargeServiceImpl implements ChargeService {

    private final ChargeItemViewMapper chargeItemViewMapper;
    private final PreChargeDetailMapper preChargeDetailMapper;
    private final PreChargeNoProcessor preChargeNoProcessor;
    private final PatientListMapper patientListMapper;
    private final PreChargeMapper preChargeMapper;
    private final MzNoProcessor mzNoProcessor;
    private final PreChargeAmtMapper preChargeAmtMapper;
    private final ChargeListTimeMapper chargeListTimeMapper;
    private final ChargeDetailTimeMapper chargeDetailTimeMapper;
    private final ChargeDtlNoProcessor chargeDtlNoProcessor;
    private final InvoiceService invoiceService;
    private final RecipeEInvoiceNoMapper recipeEInvoiceNoMapper;
    private final RegOnlineGjYiBaoUploadFeeRecordMapper regOnlineGjYiBaoUploadFeeRecordMapper;
    private final RegOnlineSHYiBaoUploadFeeRecordMapper regOnlineSHYiBaoUploadFeeRecordMapper;
    private final PatientListService patientListService;
    private final YiBaoProperties yiBaoProperties;
    private final MsgPushService msgPushService;
    private final NodeRedClient nodeRedClient;
    private final DeptMapper deptMapper;
    private final DiagnoseMapper diagnoseMapper;
    private final RegSerialNoProcessor regSerialNoProcessor;
    private final HisHospitalProperties hisHospitalProperties;
    private final NoderedProperties noderedProperties;
    private final RegOnlineGjYiBaoOrderInfoMapper regOnlineGjYiBaoOrderInfoMapper;
    private final OutpatientInvoiceViewMapper outpatientInvoiceViewMapper;
    private final GJYiBaoChargeMapper gjYiBaoChargeMapper;
    private final GJYiBaoPatientAccountMapper gjYiBaoPatientAccountMapper;
    private final MemoryCache memoryCache;
    private final RegInsuranceChargeMapper regInsuranceChargeMapper;
    private final MedicalWorkerService medicalWorkerService;
    private final PatientCardMapper patientCardMapper;
    private final RegInsurancePreChargeMapper regInsurancePreChargeMapper;

    @Override
    public List<OutpatientCharge> getOutpatientChargeList(UnChargeReq req) {
        if ("-1".equals(req.getChannel_type())) req.setChannel_type(null);
        return chargeItemViewMapper.selectOutpatientChargeList(req);
    }

    @Override
    public List<OutpatientChargeRecipeInfo> getChargeRecipeList(ChargeDetailReq req) {
        List<OutpatientChargeRecipeInfo> recipeInfos = chargeItemViewMapper.selectChargeRecipeList(req);
        recipeInfos.forEach(recipeInfo -> {
            recipeInfo.setItem_infos(
                chargeItemViewMapper.selectRecipeItemList(recipeInfo.getRecipe_no(), req.getSettle_id()));
        });
        return recipeInfos;
    }

    @Override
    public OutpatientChargeDetail getOutpatientChargeDetail(ChargeDetailReq req) {
        OutpatientChargeDetail outpatientChargeDetail = new OutpatientChargeDetail();
        outpatientChargeDetail.setSettle_id(req.getSettle_id());

        List<OutpatientChargeRecipeInfo> recipeInfos = chargeItemViewMapper.selectChargeRecipeList(req);
        if (CollectionUtils.isEmpty(recipeInfos)) {
            return null;
        }
        recipeInfos.forEach(recipeInfo -> {
            recipeInfo.setItem_infos(
                chargeItemViewMapper.selectRecipeItemList(recipeInfo.getRecipe_no(), req.getSettle_id()));
        });
        outpatientChargeDetail.setRecipe_infos(recipeInfos);
        // 根据settle_id查询缴费信息
        List<OutpatientInvoiceView> outpatientInvoiceViews = outpatientInvoiceViewMapper.selectByChargeNo(
            req.getSettle_id(), req.getHospitalCode());
        if (!CollectionUtils.isEmpty(outpatientInvoiceViews)) {
            OutpatientInvoiceView outpatientInvoiceView = outpatientInvoiceViews.get(0);
            if (outpatientInvoiceView.getChargeType() == 30 || outpatientInvoiceView.getChargeType() == 86) {
                outpatientChargeDetail.setTotal_fee(outpatientInvoiceView.getTotalAmount().multiply(new BigDecimal(100)).intValue() + "");
                outpatientChargeDetail.setSelf_fee(outpatientInvoiceView.getPayFee().multiply(new BigDecimal(100)).intValue() + "");
                outpatientChargeDetail.setInsurance_fee(outpatientInvoiceView.getCurrAccountPay().add(outpatientInvoiceView.getPubPay()).multiply(new BigDecimal(100)).intValue() + "");
            } else {
                outpatientChargeDetail.setTotal_fee(outpatientInvoiceView.getTotalAmount().multiply(new BigDecimal(100)).intValue() + "");
            }

        }
        return outpatientChargeDetail;
    }

    @Override
    public List<OutpatientUnChargeRecipeInfo> getOutpatientUnChargeRecipe(UnChargeReq req) {
        req.setBeginDate(TimeUtils.convert(req.getBegin_date()));
        req.setEndDate(TimeUtils.getEndOfDay(TimeUtils.convert(req.getEnd_date())));
        List<OutpatientUnChargeRecipeInfo> recipeInfos = chargeItemViewMapper.selectUnChargeRecipeList(req);
        recipeInfos.forEach(recipeInfo -> {
            recipeInfo.setItem_infos(
                chargeItemViewMapper.selectUnChargeRecipeItemList(recipeInfo.getRecipe_no(), req.getHospitalCode()));
        });
        return recipeInfos;
    }
    @Override
    @Transactional
    public PreChargeResult preCharge(PreChargeReq req) {
        throwErrorForFreeCharge(req);
        if ("0".equals(req.getSelf_flag())) {// 医保结算
            return preChargeInsurance(req);
        } else {
            return preChargeSelf(req);
        }
    }

    /**
     * 门诊缴费自费预算
     * @param req
     * @return
     */
    @Transactional
    public PreChargeResult preChargeSelf(PreChargeReq req) {
        PreChargeResult result = new PreChargeResult();
        Date date = new Date();
        RTPatientList patient = patientListMapper.selectByPatId(req.getPatid());
        if (patient == null) {
            throw new RuntimeException("院内无此患者信息或患者信息已停用");
        }
        String[] split = req.getRecipe_no_list().split(",");

        // 处理在线处方预算的情况,在线处方只会有一个
        if (split.length != 0 && split[0].startsWith("mock")) {
            Integer amount = memoryCache.getOnlineRecipeAmount(split[0]);

            log.info("在线处方虚拟预算，处方号：{}，处方金额：{}", split[0], amount);

            result.setRegno(req.getRegno());
            result.setSettle_id("mock" + req.getRegno());
            result.setTotal_amount("" + amount);
            result.setSelf_amount("" + amount);
            result.setShould_pay_amount("" + amount);
            result.setDiscount_amount("0");
            return result;
        }

        Long proChargeNo = preChargeNoProcessor.execute();
        List<PreChargeDetail> preChargeDetailList = Lists.newArrayList();
        List<ItemInfo> itemInfoList = Lists.newArrayList();
        for (String recipeNo : split) {
            // 0.使用待缴费接口查出用户看到的需要缴纳的费用
            List<ItemInfo> itemInfos = chargeItemViewMapper.selectUnChargeRecipeItemList(recipeNo,
                                                                                         req.getHospitalCode());
            itemInfoList.addAll(itemInfos);
            // 1.根据处方号查询收费明细数据
            List<PreChargeDetail> preChargeDetails = preChargeDetailMapper.selectPreChargeDetailList(recipeNo,
                                                                                                     req.getHospitalCode());
            // 2.保存每条收费明细表
            for (PreChargeDetail detail : preChargeDetails) {
                if (detail.getScbj() == 1) {
                    throw new RuntimeException("处方已被删除或修改，请重新查询待缴费项目进行缴费");
                }
                detail.setPreChargeNo(proChargeNo);
                detail.setRegistType(detail.getRegistType());
                detail.setCardNo(req.getCard_no());
                detail.setPatId(patient.getPatId());
                detail.setNewPatId(patient.getNewPatId());
                detail.setRegNo(Long.parseLong(req.getRegno()));
                detail.setPackageNo(0);
                detail.setBasicUnit("");
                detail.setStatus(0);
                detail.setDataFrom(111);
                detail.setFromFlag(0);
                detail.setIsDelete(false);
                detail.setCreatedBy(hisHospitalProperties.getOpCode());
                detail.setCreatedDate(date);
                if(StringUtils.length(detail.getClinicUnit()) > 7) {
                    detail.setClinicUnit(StringUtils.substring(detail.getClinicUnit(), 0, 7));
                }
                if(StringUtils.length(detail.getDosageUnit()) > 3) {
                    detail.setDosageUnit(StringUtils.substring(detail.getDosageUnit(), 0, 3));
                }

                preChargeDetailMapper.insert(detail);
                preChargeDetailList.add(detail);
            }
        }

        // 3.检查发票数量是否满足结算  暂不实现 TODO 后续实现
        // exec [Usp_Charge_CheckCharge_V1] @hospital,@prechargeno,@invoid,@ip|Parameters:@hospital()99,@prechargeno()*********,@invoid()35,@ip()*********

        // 4.保存预收费表
        Long chargeNo = mzNoProcessor.execute();
        int chargeType = 10;
        preChargeMapper.insertPreCharge(proChargeNo, chargeType, chargeNo, new Date());

        // 6.返回预收费号
        preChargeDetailList.forEach(detail -> {
            BigDecimal totalAmount = detail.getTotalAmount();

            PreChargeAmt preChargeAmt = new PreChargeAmt();
            preChargeAmt.setChargeNo(chargeNo);
            preChargeAmt.setRegNo(detail.getRegNo());
            preChargeAmt.setRecipeID(proChargeNo);
            preChargeAmt.setRecipeDetlID(detail.getRecipeDetlID());
            preChargeAmt.setDiscountAmount(detail.getDiscountAmount());
            preChargeAmt.setInsuranceTradeAmount(new BigDecimal(0));
            preChargeAmt.setSelfAmount(totalAmount);
            // [ClassifyTotal], [InsuranceCashAmount], [CreditAmt]
            preChargeAmt.setClassifyTotal(new BigDecimal(0));
            preChargeAmt.setInsuranceCashAmount(new BigDecimal(0));
            preChargeAmt.setCreditAmt(new BigDecimal(0));
            // [RealAmt], [OtherAmt], [JzAmount], FeeType
            preChargeAmt.setRealAmt(totalAmount);
            preChargeAmt.setOtherAmt(new BigDecimal(0));
            preChargeAmt.setJzAmount(new BigDecimal(0));
            preChargeAmt.setFeeType(detail.getFeeType());

            int i = preChargeAmtMapper.countPreChargeAmt(chargeNo, detail.getRecipeDetlID());
            if (i > 0) {
                preChargeAmtMapper.updateById(preChargeAmt);
            } else {
                preChargeAmtMapper.insert(preChargeAmt);
            }
        });
        BigDecimal feeAmount = preChargeDetailList.stream().map(PreChargeDetail::getTotalAmount)
            .reduce(BigDecimal.ZERO, BigDecimal::add);
        int feeAmountResult = feeAmount.multiply(new BigDecimal(100)).intValue();
        int showResult = itemInfoList.stream().map(u -> new BigDecimal(u.getAmount())).reduce(BigDecimal.ZERO, BigDecimal::add)
            .intValue();
        log.info("缴费预算----预结算金额：{}，用户看到的金额：{}", feeAmountResult, showResult);
        result.setRegno(req.getRegno());
        result.setSettle_id(chargeNo.toString());
        result.setTotal_amount("" + feeAmountResult);
        result.setSelf_amount("" + feeAmountResult);
        result.setShould_pay_amount("" + feeAmountResult);
        result.setDiscount_amount("0");

        List<Long> idList = preChargeDetailList.stream().map(PreChargeDetail::getRecipeDetlID)
            .collect(Collectors.toList());
        if (!CollectionUtils.isEmpty(idList)) {
            chargeItemViewMapper.updateRecipeDetailStatus(idList, -1);
            // 2024年08月23日17:37:27 修改对应申请单的收费状态为1 已收费
            chargeItemViewMapper.updateApplyDetailStatus(idList, -1);
            chargeItemViewMapper.updateApplyDetailCostStatus(idList, -1);
            log.info("缴费预算----修改预算待缴费项目状态为正在收费 ids:{}", idList);
        }

        // 2024年09月25日21:59:29 定时更新-1的为0  相当于解锁
        RecipeStatusMessageParam recipeStatusMessageParam = new RecipeStatusMessageParam();
        recipeStatusMessageParam.setIds(idList);
        recipeStatusMessageParam.setSettleId(chargeNo.toString());
        RecipeStatusScheduler.delayTask(recipeStatusMessageParam);

        return result;
    }

    /**
     * 门诊缴费医保预算
     * @param req
     * @return
     */
    @Transactional
    public PreChargeResult preChargeInsurance(PreChargeReq req) {
        Long patId = Long.parseLong(req.getPatid());
        Long regNo = Long.parseLong(req.getRegno());
        PreChargeResult result = new PreChargeResult();
        Date date = new Date();
        ThreadLocalUtils.setNow(date);
        RTPatientList patient = patientListMapper.selectByPatId(req.getPatid());
        if (patient == null) {
            throw new RuntimeException("院内无此患者信息或患者信息已停用");
        }
        String[] split = req.getRecipe_no_list().split(",");
        Long proChargeNo = preChargeNoProcessor.execute();
        List<PreChargeDetail> preChargeDetailList = Lists.newArrayList();
        for (String recipeNo : split) {
            // 1.根据处方号查询收费明细数据
            List<PreChargeDetail> preChargeDetails = preChargeDetailMapper.selectPreChargeDetailList(recipeNo,
                                                                                                     req.getHospitalCode());
            if (CollectionUtils.isEmpty(preChargeDetails)) {
                throw new RuntimeException("根据处方单号" + recipeNo + "未查询到待收费明细");
            }
            // 2.保存每条收费明细表
            for (PreChargeDetail detail : preChargeDetails) {
                if (detail.getScbj() == 1) {
                    throw new RuntimeException("处方已被删除或修改，请重新查询待缴费项目进行缴费");
                }
                detail.setPreChargeNo(proChargeNo);
                detail.setRegistType(detail.getRegistType());
                detail.setCardNo(req.getCard_no());
                detail.setPatId(patient.getPatId());
                detail.setNewPatId(patient.getNewPatId());
                detail.setRegNo(regNo);
                detail.setPackageNo(0);
                detail.setBasicUnit("");
                detail.setStatus(0);
                detail.setDataFrom(111);
                detail.setFromFlag(0);
                detail.setIsDelete(false);
                detail.setCreatedBy(hisHospitalProperties.getOpCode());
                detail.setCreatedDate(date);
                if(StringUtils.length(detail.getClinicUnit()) > 7) {
                    detail.setClinicUnit(StringUtils.substring(detail.getClinicUnit(), 0, 7));
                }
                if(StringUtils.length(detail.getDosageUnit()) > 3) {
                    detail.setDosageUnit(StringUtils.substring(detail.getDosageUnit(), 0, 3));
                }
                preChargeDetailMapper.insert(detail);
                preChargeDetailList.add(detail);
            }
        }

        LambdaQueryWrapper<Dept> deptWrapper = Wrappers.lambdaQuery();
        deptWrapper.eq(Dept::getDeptCode, preChargeDetailList.get(0).getDeptId());
        Dept dept = deptMapper.selectOne(deptWrapper);
        ThreadLocalUtils.setDept(dept);
        ThreadLocalUtils.setPreChargeDetails(preChargeDetailList);
        // 3.检查发票数量是否满足结算  暂不实现 TODO 后续实现
        // exec [Usp_Charge_CheckCharge_V1] @hospital,@prechargeno,@invoid,@ip|Parameters:@hospital()99,@prechargeno()*********,@invoid()35,@ip()*********

        MedicalInsuranceParam insuranceParam = MedicalInsuranceParam.fromHisParam(req.getInsurance_param());
        InsuranceBeanUtils.readAccount(BusinessType.CHARGE, insuranceParam, PayChannelType.getByValue(req.getPay_type()));

        // 4.保存预收费表
        Long chargeNo = mzNoProcessor.execute();
        int chargeType = "0".equals(req.getSelf_flag()) ? (insuranceParam.isDqYiBao() ? 86 : 30) : 10;
        preChargeMapper.insertPreCharge(proChargeNo, chargeType, chargeNo, date);

        List<Diagnose> diagnoses = diagnoseMapper.selectByRegno(req.getRegno(), req.getHospitalCode());
        ThreadLocalUtils.setDiagnoses(diagnoses);
        ThreadLocalUtils.setPatient(patient);

        MedicalWorker medicalWorker = medicalWorkerService.getById(preChargeDetailList.get(0).getDoctorId());
        ThreadLocalUtils.setMedicalWorker(medicalWorker);

        if (insuranceParam.isDqYiBao()) {
            String accountAttr = insuranceParam.getAccountAttr();
//            String flag2 = accountAttr.substring(1, 2); // 第2位 干部保健对象
//            String flag12 = accountAttr.substring(11, 12); // 第12位 A：民政医疗帮困, G：医疗互助帮困对象
//            if ("1".equals(flag2) || "G".equals(flag12) || "A".equals(flag12)) {
//                throw new RuntimeException("小程序暂不支持,民政帮困,互助帮困,请去线下或自助机缴费");
//            }
            DQYiBaoClient dqYiBaoClient = InsuranceBeanUtils.getDQYiBaoClient();
            List<PreCharge> preCharges = preChargeMapper.selectPreChargeList(chargeNo + "", hisHospitalProperties.getCode());
            RegInsurancePreCharge insureCharge = InsuranceBeanUtils.shYiBaoPreCharge(accountAttr, preCharges);

            SN01Response sn01Response = dqYiBaoClient.postSN01(req.getRegno(), chargeNo, proChargeNo, insuranceParam, preCharges);
            SI11Response si11Response = dqYiBaoClient.postSI11(req.getRegno(), insuranceParam);

            insureCharge.setHospitalCode(Integer.parseInt(hisHospitalProperties.getCode()));
            insureCharge.setSerialNo(regSerialNoProcessor.executeInsurance());
            insureCharge.setChargeNo(chargeNo);
            RTPatientCard card = patientCardMapper.getByCardNoAndPatId(req.getCard_no(), patId);
            insureCharge.setCardData(card.getCardData());
            insureCharge.setCardNo(card.getCardNo());
            insureCharge.setCardType(card.getCardType() + "");
            insureCharge.setRegistType(preCharges.get(0).getRegistType());
            insureCharge.setPatId(patId);
            insureCharge.setNewPatId(preCharges.get(0).getNewPatId());
            insureCharge.setPatName(patient.getPatName());
            insureCharge.setIsInsureCard(1);
            insureCharge.setChargeType(chargeType);
            insureCharge.setRegNo(regNo);
            insureCharge.setOutpatientNo(card.getHisCardNo());
            insureCharge.setDeptId(dept.getInsureDeptCode() + "");
            insureCharge.setAccountFlag(accountAttr);
            insureCharge.setPatType(0);
            insureCharge.setSpecialFlag(Integer.parseInt(si11Response.getPersonspectag()));
            insureCharge.setUploadTime(date);
            insureCharge.setOpTime(date);
            insureCharge.setJyFlag(0);
            insureCharge.setWflag(0);
            insureCharge.setCreatedDate(date);
            insureCharge.setIsDeleted(false);
            regInsurancePreChargeMapper.insert(insureCharge);
            // 医院自行生成的唯一ID，生成规则：医疗机构代码（11 位）+yyyyMMddHHmmssSSS+6位随机数字
            String orderNo = yiBaoProperties.getDqOrgCode() + TimeUtils.dateStringFormat(new Date(), "yyyyMMddHHmmssSSS")
                    + StringUtils.right("00000" + SequenceUtils.getSequence(999999), 6);
            dqYiBaoClient.postSE02Charge(orderNo, insuranceParam);

            RegOnlineSHYiBaoUploadFeeRecord record = new RegOnlineSHYiBaoUploadFeeRecord();
            BeanUtils.copyProperties(sn01Response, record);
            BeanUtils.copyProperties(si11Response, record);

            record.setPatName(patient.getPatName());
            record.setChargeNo(chargeNo);
            record.setRegNo(Long.parseLong(req.getRegno()));
            record.setOrderNo(orderNo);
            record.setFlag(1);
            record.setHospitalCode(req.getHospitalCode());
            record.setCreatedDate(date);
            record.setUpdatedDate(date);
            EcTokenDTO ecToken = memoryCache.getEctoken(insuranceParam.getEcQrcode());
            record.setCardType(ecToken.getIdType());
            record.setCardNo(ecToken.getIdNo());
            // 保存线上医保入参
            record.setAuthCode(insuranceParam.getAuthCode());
            record.setUserName(insuranceParam.getUserName());
            record.setCityId(insuranceParam.getInsuOrg());
            record.setPayAuthNo(insuranceParam.getPayAuthNo());
            record.setLongitude(insuranceParam.getUserLongitudeLatitude().getLongitude());
            record.setLatitude(insuranceParam.getUserLongitudeLatitude().getLatitude());
            record.setEcQrcode(insuranceParam.getEcQrcode());
            record.setUserCardNo(insuranceParam.getUserCardNo());
            record.setPayway(PayChannelType.getByValue(req.getPay_type()).getByHisValue());
            record.setEcToken(insuranceParam.getEcToken());
            regOnlineSHYiBaoUploadFeeRecordMapper.insert(record);

            result.setRegno(req.getRegno());
            result.setSettle_id(chargeNo.toString());

            BigDecimal total = si11Response.getYbjsfwfyze().add(si11Response.getFybjsfwfyze());
//            BigDecimal self = si11Response.getZfdxjzfs().add(si11Response.getTcdxjzfs()).add(si11Response.getFjdxjzfs())
//                    .add(si11Response.getFybjsfwfyze());
            BigDecimal pubAccount = si11Response.getCuraccountpay().add(si11Response.getHisaccountpay());
            BigDecimal pub = si11Response.getTcdzhzfs().add(si11Response.getTczfs()).add(si11Response.getFjdzhzfs()).add(si11Response.getFjzfs());
            BigDecimal self = total.subtract(pub).subtract(pubAccount);

            result.setShould_pay_amount(self.multiply(new BigDecimal(100)).intValue() + "");
            result.setPub_account_pay(pubAccount.multiply(new BigDecimal(100)).intValue() + "");
            result.setPub_pay(pub.multiply(new BigDecimal(100)).intValue() + "");
            result.setTotal_amount(total.multiply(new BigDecimal(100)).intValue() + "");
            result.setSelf_amount(self.multiply(new BigDecimal(100)).intValue() + "");
            result.setDiscount_amount("0");
            // 医保特有字段返回
            result.setMedical_order_no(chargeNo + ""); // TODO: 不知道是不是用这个字段
            result.setBill_no(orderNo);
            result.setGmt_out_create(TimeUtils.getHisDateStr(date));
            return result;
        } else {
            // 5. 医保缴费，需要走医保流程
            // 5. 医保缴费，需要走医保流程
            InsuranceBeanUtils.getGJYiBaoClient().post1101(insuranceParam);
            InsuranceBeanUtils.getGJYiBaoClient().post2201A(insuranceParam, req.getRegno());

            // 透传请求给IH
            Output6201 output6201 = InsuranceBeanUtils.getGJYiBaoClient().post6201Charge(chargeNo, insuranceParam, PayChannelType.getByValue(req.getPay_type()),
                    req.getHospitalCode());

            Output6202 output6202 = InsuranceBeanUtils.getGJYiBaoClient().post6202Charge(chargeNo, insuranceParam,
                    PayChannelType.getByValue(req.getPay_type()), req.getHospitalCode());

            // self_pay先不算了
            // DONE 6202返回数据入库
            RegOnlineGjYiBaoUploadFeeRecord record = new RegOnlineGjYiBaoUploadFeeRecord();
            BeanUtils.copyProperties(output6201, record);
            BeanUtils.copyProperties(output6202, record);
            record.setPatName(patient.getPatName());
            record.setChargeNo(chargeNo);
            record.setRegNo(Long.parseLong(req.getRegno()));
            record.setFlag(1);
            record.setHospitalCode(req.getHospitalCode());
            record.setCreatedDate(date);
            record.setUpdatedDate(date);
            record.setCardType(InsuranceBeanUtils.certificateTypeMapping(patient.getCertificateType(), patient.getCertificateNo()));
            record.setCardNo(patient.getCertificateNo());
            // 保存线上医保入参
            record.setAuthCode(insuranceParam.getAuthCode());
            record.setUserName(insuranceParam.getUserName());
            record.setCityId(insuranceParam.getCityId());
            record.setPayAuthNo(insuranceParam.getPayAuthNo());
            record.setLongitude(insuranceParam.getUserLongitudeLatitude().getLongitude());
            record.setLatitude(insuranceParam.getUserLongitudeLatitude().getLatitude());
            record.setEcQrcode(insuranceParam.getEcQrcode());
            record.setUserCardNo(insuranceParam.getUserCardNo());
            record.setPayway(PayChannelType.getByValue(req.getPay_type()).getByHisValue());
            regOnlineGjYiBaoUploadFeeRecordMapper.insert(record);

            result.setRegno(req.getRegno());
            result.setSettle_id(chargeNo.toString());
            result.setShould_pay_amount(output6202.getOwnPayAmt().multiply(new BigDecimal(100)).toString());
            result.setSelf_amount(output6202.getOwnPayAmt().multiply(new BigDecimal(100)).toString());
            result.setPub_account_pay(output6202.getPsnAcctPay().multiply(new BigDecimal(100)).toString());
            result.setPub_pay(output6202.getFundPay().multiply(new BigDecimal(100)).toString());
            result.setTotal_amount(output6202.getFeeSumamt().multiply(new BigDecimal(100)).toString());
            result.setDiscount_amount("0");
            // 医保特有字段返回
            result.setMedical_order_no(chargeNo + "");
            result.setPay_order_id(output6201.getPayOrdId());
            result.setGmt_out_create(TimeUtils.getHisDateStr(date));

            return result;
        }
    }

    @Override
    @Transactional
    public ConfirmChargeResult confirmCharge(ConfirmChargeReq req, RegOnlineSHYiBaoUploadFeeRecord feeRecord) {
        ConfirmChargeResult result = new ConfirmChargeResult();
        // 处理在线处方结算的情况(虚假数据)
        if (req.getSettle_id() != null && req.getSettle_id().startsWith("mock")) {
            log.info("在线处方虚拟结算，settle_id:{}", req.getSettle_id());
            result.setSuccess("true");
            result.setMessage("收费结算成功");
            result.setSettle_id(req.getSettle_id());
            result.setCharge_time(TimeUtils.getHisDateStr(new Date()));
            result.setMemo("");
        }

        // 先查预收费记录
        List<PreCharge> preChargeList = preChargeMapper.selectPreChargeList(req.getSettle_id(), req.getHospitalCode());
        if (CollectionUtils.isEmpty(preChargeList)) {
//            result.setSuccess("false");
//            result.setMessage("未查询到预收费记录，settle_id无效");
//            return result;
            throw new ErrorResponseException("未查询到预收费记录，settle_id无效");
        }
        // 不允许重复结算
        ChargeListTime charge = chargeListTimeMapper.selectById(Long.valueOf(req.getSettle_id()));
        if (charge != null) {
//            result.setSuccess("false");
//            result.setMessage(req.getSettle_id() + "已结算，不能重复结算");
//            return result;
            throw new ErrorResponseException(req.getSettle_id() + "已结算，不能重复结算");
        }
        for (PreCharge preCharge : preChargeList) {
            int i = chargeItemViewMapper.countPaidItem(preCharge.getRecipeDetlID().toString(),
                                                       preCharge.getHospitalCode().toString());
            if (i > 0) {
//                log.info("处方明细{}已结算，不能重复结算", preCharge.getRecipeDetlID());
//                result.setSuccess("false");
//                result.setMessage(preCharge.getItemName() + "已结算，不能重复结算");
//                return result;
                throw new ErrorResponseException(preCharge.getItemName() + "已结算，不能重复结算");
            }
            int j = chargeItemViewMapper.countDeletedItem(preCharge.getRecipeDetlID().toString(),
                             preCharge.getHospitalCode().toString());
            if (j > 0) {
                throw new ErrorResponseException(preCharge.getItemName() + "相关处方已被删除，不能结算");
            }
        }

        PreCharge preList = preChargeList.get(0);
        //逻辑顺序：
        //ih 		------------> 		his
        //支付成功					1.保存收费信息（ChargeList+Chargedetail）
        //						            2.保存发票信息（OutpatientInvoice + OpInvoiceSupply + OpInvoicePayway） 发票号跳号 （Usp_Charge_UpdateInvoideNum）
        //						            3.保存医生出诊信息 （Tbt_Recipe_eInvoiceNo）
        //                                 4.修改处方明细表MZYS_TB_MZCFMX的zt字段为1 -1 准备收费 1 已收费 3 已退费
        // 1.保存收费信息 list
        Long chargeNo =  Long.valueOf(req.getSettle_id());
        String accountFlag = req.getHosp_account_flag();
        int recipeCount = Math.toIntExact(preChargeList.size());
        String computerNo = "999999";
        Integer opId = hisHospitalProperties.getOpCode();
        Date time = new Date();
        Short dataForm = 111;
        int jsFlag;
        int cardType;
        if (preChargeList.get(0).getChargeType() == 30 || preChargeList.get(0).getChargeType() == 86) {
            jsFlag = req.getJsFlag();
            cardType = 1;
        } else {
            jsFlag = 0;
            cardType = 0;
        }
        chargeListTimeMapper.insertChargeListTime(chargeNo, accountFlag, recipeCount, computerNo, opId, time,
                                                  dataForm, jsFlag, cardType);

        // 1.1 保存收费信息 detail
        for (PreCharge preCharge : preChargeList) {
            Long detailNo = chargeDtlNoProcessor.execute();
            chargeDetailTimeMapper.insertChargeDetailTime(detailNo, chargeNo, opId, time, preCharge.getRecipeDetlID());

        }
        // 2.保存发票信息 发票号跳号
        // 查出前两步保存的数据，保存发票信息
        ChargeListTime chargeListTime = chargeListTimeMapper.selectById(chargeNo);
        List<ChargeDetailTime> chargeDetailTimes = chargeDetailTimeMapper.selectList(Wrappers.<ChargeDetailTime>lambdaQuery().eq(ChargeDetailTime::getChargeNo, chargeNo));

        OutpatientInvoiceTime invoiceTime = invoiceService.saveChargeInvoiceInfo(req, chargeListTime, chargeDetailTimes);

        // 医保挂号保存国家医保数据
        if ("0".equals(req.getSelf_flag())) {
            if (req.getJsFlag() == 2) {
                GJYiBaoCharge gjYiBaoCharge = new GJYiBaoCharge();
                Output6301 output6301 = req.getOutput6301();
                ExtData extData = output6301.getExtData();
                Setlinfo setlinfo = extData.getSetlinfo();
                BeanUtils.copyProperties(setlinfo, gjYiBaoCharge);
                gjYiBaoCharge.setHospitalCode(chargeListTime.getHospitalCode());
                gjYiBaoCharge.setChargeNo(chargeNo);
                gjYiBaoCharge.setRegNo(chargeListTime.getRegNo());
                gjYiBaoCharge.setPsnCertType(setlinfo.getPsnCertType());
                gjYiBaoCharge.setCertNo(setlinfo.getCertno());
                gjYiBaoCharge.setAge(new BigDecimal(setlinfo.getAge()));
                Date date = new Date();
                gjYiBaoCharge.setCreateTime(date);
                gjYiBaoCharge.setUpdateTime(date);
                gjYiBaoCharge.setInsuplcAdmdvs(extData.getInsuplcAdmdvs());
                gjYiBaoCharge.setMsgID(setlinfo.getMedinsSetlId());
                gjYiBaoCharge.setStatus(1);
                gjYiBaoChargeMapper.insert(gjYiBaoCharge);

                // 添加或更新医保账户信息
                GjYiBaoPatientAccount ac = gjYiBaoPatientAccountMapper.selectOneByPsnNoAndPatIdAndCardNo(
                        setlinfo.getPsnNo(),
                        chargeListTime.getPatID().toString(),
                        chargeListTime.getCardNo());

                boolean acExit = true;
                if (ac == null) {
                    ac = new GjYiBaoPatientAccount();
                    acExit = false;
                }
                ac.setPsnNo(setlinfo.getPsnNo());
                ac.setPsnName(setlinfo.getPsnName());
                ac.setPatId(chargeListTime.getPatID().intValue());
                ac.setCardNo(chargeListTime.getCardNo());
                ac.setPsnCertType(setlinfo.getPsnCertType());
                ac.setCertno(setlinfo.getCertno());
                ac.setPsnName(setlinfo.getPsnName());
                ac.setGend(setlinfo.getGend());
//            ac.setNaty();
                ac.setBrdy(setlinfo.getBrdy());
                ac.setAge(setlinfo.getAge() + "");
                ac.setBalc(setlinfo.getBalc().toString());
                ac.setInsutype(setlinfo.getInsutype());
                ac.setPsnType(setlinfo.getPsnType());
//            ac.setPsnInsuStas();
                ac.setCvlservFlag(setlinfo.getCvlservFlag());
                ac.setInsuplcAdmdvs(extData.getInsuplcAdmdvs());
                ac.setHospitalCode(Integer.parseInt(req.getHospitalCode()));
                if (acExit) {
                    LambdaQueryWrapper<GjYiBaoPatientAccount> acWrapper = Wrappers.lambdaQuery();
                    acWrapper.eq(GjYiBaoPatientAccount::getPatId, ac.getPatId());
                    acWrapper.eq(GjYiBaoPatientAccount::getCardNo, ac.getCardNo());
                    acWrapper.eq(GjYiBaoPatientAccount::getPsnNo, ac.getPsnNo());
                    gjYiBaoPatientAccountMapper.update(ac, acWrapper);
                } else {
                    gjYiBaoPatientAccountMapper.insert(ac);
                }
            } else {
                RegInsurancePreCharge insurancePreCharge = regInsurancePreChargeMapper.selectByChargeNo(chargeNo);
                RegInsuranceCharge shYiBaoCharge = new RegInsuranceCharge();
                SE04Response se04Response = req.getSe04Response();
                SI12Response si12Response = se04Response.getSi12Response();
                BeanUtils.copyProperties(insurancePreCharge, shYiBaoCharge);
                BeanUtils.copyProperties(si12Response, shYiBaoCharge);
                shYiBaoCharge.setCardType(si12Response.getCardtype());
                Date date = new Date();
                shYiBaoCharge.setUploadTime(date);
                shYiBaoCharge.setIsDeleted(false);
                shYiBaoCharge.setOpCode(hisHospitalProperties.getOpCode());
                shYiBaoCharge.setOpTime(date);
                shYiBaoCharge.setPatId(chargeListTime.getPatID());
                shYiBaoCharge.setCardNo(chargeListTime.getCardNo());
                shYiBaoCharge.setRegistType(chargeListTime.getRegistType());
                shYiBaoCharge.setNewPatId(preList.getNewItemID());
                RTPatientList patient = patientListMapper.selectByPatId(chargeListTime.getPatID() + "");
                shYiBaoCharge.setPatName(patient.getPatName());
                shYiBaoCharge.setIsInsureCard(1);
                shYiBaoCharge.setPatType(0);
                if (StringUtils.isNotBlank(feeRecord.getPersonspectag())) {
                    shYiBaoCharge.setSpecialFlag(Integer.parseInt(feeRecord.getPersonspectag()));
                }
                shYiBaoCharge.setWflag(1);
                shYiBaoCharge.setTradeSerialNo(si12Response.getLsh());
                shYiBaoCharge.setJyFlag(2);
                shYiBaoCharge.setMsgValue("SI12");

                shYiBaoCharge.setCurrentAccountMoney(si12Response.getCuraccountamt());
                shYiBaoCharge.setLastAccountMoney(si12Response.getHisaccountamt());
                shYiBaoCharge.setCurrentAccountPay(si12Response.getCuraccountpay());
                shYiBaoCharge.setLastAccountPay(si12Response.getHisaccountpay());
                shYiBaoCharge.setTotalAmount(si12Response.getFybjsfwfyze().add(si12Response.getYbjsfwfyze()));
                shYiBaoCharge.setTradeTotal(si12Response.getTotalexpense());
                shYiBaoCharge.setInsuranceTotal(si12Response.getYbjsfwfyze());
                shYiBaoCharge.setNonInsuranceTotal(si12Response.getFybjsfwfyze());
                shYiBaoCharge.setPubCashPay(si12Response.getTcdxjzfs());
                shYiBaoCharge.setPubLastPay(si12Response.getTcdzhzfs());
                shYiBaoCharge.setPubPay(si12Response.getTczfs());
                shYiBaoCharge.setAppendCashPay(si12Response.getFjdxjzfs());
                shYiBaoCharge.setAppendLastPay(si12Response.getFjdzhzfs());
                shYiBaoCharge.setAppendPay(si12Response.getFjzfs());
                shYiBaoCharge.setSelfCashPay(si12Response.getZfdxjzfs());
                shYiBaoCharge.setSelfLastPay(si12Response.getZfdlnzhzfs());

                if (si12Response.getGjzhzfs() != null) {
                    shYiBaoCharge.setAcctMulaidPay(si12Response.getGjzhzfs());
                    shYiBaoCharge.setSelfMulaidPay(si12Response.getZfdgjzhzfs());
                    shYiBaoCharge.setPubMulaidPay(si12Response.getTcdgjzhzfs());
                    shYiBaoCharge.setAppendMulaidPay(si12Response.getFjdgjzhzfs());
                    shYiBaoCharge.setMulaidPayTotal(shYiBaoCharge.getAcctMulaidPay().add(shYiBaoCharge.getSelfMulaidPay())
                            .add(shYiBaoCharge.getPubMulaidPay()).add(shYiBaoCharge.getAppendMulaidPay()));
                    shYiBaoCharge.setQfdMulaidPay(BigDecimal.ZERO);
                    shYiBaoCharge.setGyMulaidPay(BigDecimal.ZERO);
                    shYiBaoCharge.setInsuranceType(1);
                }

                //历年账户支付有值，其他账户支付都为0，将自负段历年账户支付设定为历年账户支付
                if (shYiBaoCharge.getLastAccountPay().compareTo(BigDecimal.ZERO) > 0) {
                    if (shYiBaoCharge.getSelfLastPay().add(shYiBaoCharge.getPubLastPay()).add(shYiBaoCharge.getAppendLastPay())
                            .add(shYiBaoCharge.getMulaidPayTotal()).compareTo(BigDecimal.ZERO) == 0) {
                        shYiBaoCharge.setSelfLastPay(shYiBaoCharge.getLastAccountPay());
                    }
                }

                //医保返回的总现金数
                BigDecimal insuranceCashAmount = si12Response.getZfdlnzhzfs().add(si12Response.getTcdxjzfs())
                        .add(si12Response.getTcdzhzfs());

                //因为二次结算的上传现金要包含分类支付
                shYiBaoCharge.setCashAmt(insuranceCashAmount.compareTo(BigDecimal.ZERO) < 0 ? BigDecimal.ZERO
                        : insuranceCashAmount.add(shYiBaoCharge.getClassifyTotal()));
                shYiBaoCharge.setJzAmt(BigDecimal.ZERO);
                regInsuranceChargeMapper.insert(shYiBaoCharge);
            }
        }


        // 3.保存医生出诊信息
        RecipeEInvoiceNo recipeEInvoiceNo = new RecipeEInvoiceNo();
        recipeEInvoiceNo.setWorkerId(chargeDetailTimes.get(0).getDoctorId());
        recipeEInvoiceNo.setHospitalCode(chargeListTime.getHospitalCode());
        recipeEInvoiceNo.setInvoId(Math.toIntExact(invoiceTime.getInvoiceID()));
        recipeEInvoiceNo.setCurrentKey(0);
        recipeEInvoiceNo.setFpNum(1);
        recipeEInvoiceNo.setOpTime(time);
        recipeEInvoiceNoMapper.insert(recipeEInvoiceNo);

        // 4.修改处方明细表MZYS_TB_MZCFMX的zt字段为1 -1 准备收费 1 已收费 3 已退费
        List<Long> idList = chargeDetailTimes.stream().map(ChargeDetailTime::getRecipeDetlID)
            .collect(Collectors.toList());
        chargeItemViewMapper.updateRecipeDetailStatus(idList, 1);
        // 2024年08月23日17:37:27 修改对应申请单的收费状态为1 已收费
        chargeItemViewMapper.updateApplyDetailStatus(idList, 1);
        chargeItemViewMapper.updateApplyDetailCostStatus(idList, 1);
        chargeItemViewMapper.uspChargeSaveSendDrug(chargeListTime.getChargeNo(), hisHospitalProperties.getOpCode(), 0, "");
        String recipeDetlIDs = chargeDetailTimes.stream().map(u -> Long.toString(u.getRecipeDetlID())).distinct().collect(Collectors.joining(","));
        String recipeIDs = chargeDetailTimes.stream().map(u -> Long.toString(u.getRecipeID())).distinct().collect(Collectors.joining(","));
        chargeItemViewMapper.uspNewHisUpdateRecipe(recipeDetlIDs, recipeIDs, 1);
        result.setSuccess("true");
        result.setMessage("收费结算成功");
        result.setSettle_id(req.getSettle_id());
        result.setCharge_time(TimeUtils.getHisDateStr(time));
        result.setMemo("");

        // invoiceType 发票类型 1 收费 3 挂号
        // Flag=1  门诊挂号 ； Flag=2 红冲； Flag=3 门诊收费
        // 调用接口生成电子发票
        InvoiceScheduler.delayTask(new PushInvoiceMessageParam(invoiceTime.getInvoiceID(), 1, 3,
                chargeListTime.getHospitalCode() + "", chargeListTime.getOpCode() + "", invoiceTime.getInvoiceInfo()));
        return result;
    }
    @Override
    public ConfirmChargeResult confirmChargeForHuLiDaoJia(ConfirmChargeReq req) {
        ConfirmChargeResult result = new ConfirmChargeResult();
        // 先查预收费记录
        LambdaQueryWrapper<PreCharge> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(PreCharge::getChargeNo, req.getSettle_id());
        wrapper.eq(PreCharge::getHospitalCode, req.getHospitalCode());
        List<PreCharge> preChargeList = preChargeMapper.selectList(wrapper);
        if (CollectionUtils.isEmpty(preChargeList)) {
            throw new ErrorResponseException("未查询到预收费记录，settle_id无效");
        }
        // 不允许重复结算
        ChargeListTime charge = chargeListTimeMapper.selectById(Long.valueOf(req.getSettle_id()));
        if (charge != null) {
            throw new ErrorResponseException(req.getSettle_id() + "已结算，不能重复结算");
        }

        //逻辑顺序：
        //ih 		------------> 		his
        //支付成功					1.保存收费信息（ChargeList+Chargedetail）
        //						            2.保存发票信息（OutpatientInvoice + OpInvoiceSupply + OpInvoicePayway） 发票号跳号 （Usp_Charge_UpdateInvoideNum）
        //						            3.保存医生出诊信息 （Tbt_Recipe_eInvoiceNo）
        //                                 4.修改处方明细表MZYS_TB_MZCFMX的zt字段为1 -1 准备收费 1 已收费 3 已退费
        // 1.保存收费信息 list
        Long chargeNo =  Long.valueOf(req.getSettle_id());
        String accountFlag = req.getHosp_account_flag();
        int recipeCount = Math.toIntExact(preChargeList.size());
        String computerNo = "999999";
        Integer opId = hisHospitalProperties.getOpCode();
        Date time = new Date();
        Short dataForm = 111;
        Integer jsFlag;
        Integer cardType;
        if (preChargeList.get(0).getChargeType() == 30 || preChargeList.get(0).getChargeType() == 86) {
            jsFlag = 2;
            cardType = 1;
        } else {
            jsFlag = 0;
            cardType = 0;
        }
        chargeListTimeMapper.insertChargeListTime(chargeNo, accountFlag, recipeCount, computerNo, opId, time,
                                                  dataForm, jsFlag, cardType);

        // 1.1 保存收费信息 detail
        for (PreCharge preCharge : preChargeList) {
            Long detailNo = chargeDtlNoProcessor.execute();
            chargeDetailTimeMapper.insertChargeDetailTime(detailNo, chargeNo, opId, time, preCharge.getRecipeDetlID());

        }
        // 2.保存发票信息 发票号跳号
        // 查出前两步保存的数据，保存发票信息
        ChargeListTime chargeListTime = chargeListTimeMapper.selectById(chargeNo);
        List<ChargeDetailTime> chargeDetailTimes = chargeDetailTimeMapper.selectList(Wrappers.<ChargeDetailTime>lambdaQuery().eq(ChargeDetailTime::getChargeNo, chargeNo));

        OutpatientInvoiceTime invoiceTime = invoiceService.saveChargeInvoiceInfo(req, chargeListTime, chargeDetailTimes);

        result.setSuccess("true");
        result.setMessage("收费结算成功");
        result.setSettle_id(req.getSettle_id());
        result.setCharge_time(TimeUtils.getHisDateStr(time));
        result.setMemo("");

        // invoiceType 发票类型 1 收费 3 挂号
        // Flag=1  门诊挂号 ； Flag=2 红冲； Flag=3 门诊收费
        // 调用接口生成电子发票
        InvoiceScheduler.delayTask(new PushInvoiceMessageParam(invoiceTime.getInvoiceID(), 1, 3,
                                                               chargeListTime.getHospitalCode() + "",
                                                               chargeListTime.getOpCode() + "", invoiceTime.getInvoiceInfo()));
        return result;
    }

    @Override
    public ConfirmedRefundedRes confirmedRefunded(ConfirmedRefundedReq req) {
        // TODO 退款成功后的逻辑，门诊需要按照分类来处理
        return ConfirmedRefundedRes.success();
    }

    @Override
    public OrderRefundedRes orderRefunded(OrderRefundedReq req) {
        // 透传请求给IH
        return nodeRedClient.orderRefunded(req);
    }

    // TODO: 现在只做了查询医保缴费是否结算成功，非医保的如果有需求再做
//    public GetOutpatientPayResult queryGJYiBaoPayResult(String hospitalCode, long regNo) {
//        ChargeListTime rList = chargeListTimeMapper.selectChargeListByChargeNo(chargeNo).stream().findFirst().orElse(null);
//        if (rList != null) {
//            // 已挂号
//        } else {
//            // 需要判断是不是医保的，医保的，调用queryGJYiBaoPayResult非医保的返回未结算
//        }
//    }

    @Override
    @Transactional
    public GetOutpatientPayResult queryYiBaoPayResult(String hospitalCode, long chargeNo, boolean push) {
        LambdaQueryWrapper<PreCharge> wrapper = Wrappers.lambdaQuery();
        wrapper.eq(PreCharge::getHospitalCode, hospitalCode);
        wrapper.eq(PreCharge::getChargeNo, chargeNo);
        List<PreCharge> preCharges = preChargeMapper.selectList(wrapper);
        if (CollectionUtils.isEmpty(preCharges)) {
            throw new RuntimeException("未预算，chargeNo: " + chargeNo);
        }
        Long regNo = preCharges.get(0).getRegNo();
        RegOnlineGjYiBaoUploadFeeRecord feeRecord = regOnlineGjYiBaoUploadFeeRecordMapper.selectByChargeNo(chargeNo + "");
        RegOnlineSHYiBaoUploadFeeRecord shFeeRecord = null;
        Integer payway;
        if (feeRecord == null) {
            shFeeRecord = regOnlineSHYiBaoUploadFeeRecordMapper.selectByChargeNo(chargeNo + "");
            if (shFeeRecord == null) {
                throw new RuntimeException("这个不是线上挂号医保支付，chargeNo: " + chargeNo);
            } else {
                payway = shFeeRecord.getPayway();
            }
        } else {
            payway = feeRecord.getPayway();
        }

        RTPatientList patient = patientListService.getById(preCharges.get(0).getPatId());
        // 支付渠道 11-支付宝小程序 17-微信小程序 13-微信公众号
        PayChannelType payChannelType = null;
        switch (payway) {
            case 11:
                payChannelType = PayChannelType.ALIPAY_MINI_PROGRAM;
                break;
            case 17:
                payChannelType = PayChannelType.WECHAT_MINI_PROGRAM;
                break;
            case 13:
                payChannelType = PayChannelType.WECHAT_OFFICIAL_ACCOUNT;
                break;
            default:
                throw new RuntimeException("不支持的支付方式: chargeNo: " + chargeNo + ", payWay: " + payway);
        }
        if (feeRecord == null) {
            return handlerSHYiBao(hospitalCode, push, shFeeRecord, patient, payChannelType, regNo);
        } else {
            return handlerGJYiBao(hospitalCode, push, feeRecord, patient, payChannelType, regNo);
        }

    }

    /**
     * 门诊缴费,国家医保支付
     * @param hospitalCode
     * @param push
     * @param feeRecord
     * @param patient
     * @param payChannelType
     * @param regNo
     * @return
     */
    @NotNull
    private GetOutpatientPayResult handlerGJYiBao(String hospitalCode, boolean push, RegOnlineGjYiBaoUploadFeeRecord feeRecord,
                                                  RTPatientList patient, PayChannelType payChannelType, Long regNo) {
        long chargeNo = feeRecord.getChargeNo();
        Long patId = patient.getPatId();
        GetOutpatientPayResult payResult = new GetOutpatientPayResult();
        payResult.setTrade_type("0");
        payResult.setOut_trade_no(feeRecord.getChargeNo() + "");
        payResult.setPay_order_id(feeRecord.getPayOrdId());

        Input6301 input6301 = new Input6301();
        input6301.setIdNo(patient.getCertificateNo());
        input6301.setUserName(patient.getPatName());
        input6301.setIdType("01");
        input6301.setOrgCodg(yiBaoProperties.getOrgCode());
        input6301.setPayOrdId(feeRecord.getPayOrdId());
        input6301.setPayToken(feeRecord.getPayToken());
        // 现在没有锁，在发请求前和发请求后都判断一次
        ChargeListTime rList = chargeListTimeMapper.selectChargeListByChargeNo(chargeNo).stream().findFirst().orElse(null);
        if (rList != null) {
            // 已结算，不做后续处理
            payResult.setStatus("0");
            payResult.setCharge_time(TimeUtils.getHisDateStr(rList.getChargeTime()));
            return payResult;
        }

        Output6301 output6301 = InsuranceBeanUtils.getGJYiBaoClient().post6301(input6301, payChannelType, hospitalCode);
        log.info("查询医保支付结果: chargeNo: " + chargeNo + ", result: " + StandardObjectMapper.stringify(output6301));
        if (output6301 != null && ("4".equals(output6301.getOrdStas()) || "6".equals(output6301.getOrdStas()))) {
            log.info("医保已支付: chargeNo: " + chargeNo);

            // 现在没有锁，在发请求前和发请求后都判断一次
            rList = chargeListTimeMapper.selectChargeListByChargeNo(chargeNo).stream().findFirst().orElse(null);
            if (rList != null) {
                // 已挂号，不做后续处理
                payResult.setStatus("0");
                payResult.setCharge_time(TimeUtils.getHisDateStr(rList.getChargeTime()));
                return payResult;
            }
            saveRegOnlineGjYiBaoOrderInfo(patient, input6301, output6301, feeRecord);

            ConfirmChargeReq req = new ConfirmChargeReq();
            req.setSettle_id(chargeNo + "");
            req.setHospitalCode(hospitalCode);
            req.setHosp_account_flag("0");
            req.setSerial_no(output6301.getMedOrgOrd());
            req.setTrade_no(chargeNo + "");
            req.setPay_type(payChannelType.getValue());
            req.setRegno(regNo + "");
            req.setPatid(patId + "");
            req.setHospitalCode(hospitalCode);
            req.setTotal_amount(output6301.getFeeSumamt().multiply(new BigDecimal(100)).intValue() + "");
            req.setShould_pay_amount(output6301.getFeeSumamt().multiply(new BigDecimal(100)).intValue() + "");
            req.setPay_type(payChannelType.getValue());
            req.setSerial_no(output6301.getMedOrgOrd());
            req.setSelf_flag("0");
            req.setJsFlag(2);

            req.setOutput6301(output6301);
            ConfirmChargeResult chargeResult = confirmCharge(req, null);
            if (chargeResult != null && "true".equalsIgnoreCase(chargeResult.getSuccess())) {
                log.info("医保已支付，业务处理成功: chargeNo: " + chargeNo);
                payResult.setStatus("0");
                payResult.setCharge_time(TimeUtils.getHisDateStr(new Date()));
                if (push) {
                    msgPushService.pushCharge4010Succeeded(hospitalCode, regNo + "" , chargeNo + "",
                            feeRecord.getPayOrdId(), null, output6301.getTraceTime());
                }
            } else {
                log.error("医保结算成功业务处理失败: result: " + StandardObjectMapper.stringify(chargeResult));
                payResult.setStatus("2");
            }
        } else {
            log.info("医保未结算: chargeNo: " + chargeNo);
            payResult.setStatus("2");
        }

        return payResult;
    }

    /**
     * 门诊缴费,国家医保支付
     * @param hospitalCode
     * @param push
     * @param feeRecord
     * @param patient
     * @param payChannelType
     * @param regNo
     * @return
     */
    @NotNull
    private GetOutpatientPayResult handlerSHYiBao(String hospitalCode, boolean push, RegOnlineSHYiBaoUploadFeeRecord feeRecord,
                                                  RTPatientList patient, PayChannelType payChannelType, Long regNo) {
        long chargeNo = feeRecord.getChargeNo();
        Long patId = patient.getPatId();
        GetOutpatientPayResult payResult = new GetOutpatientPayResult();
        payResult.setTrade_type("0");
        payResult.setOut_trade_no(feeRecord.getChargeNo() + "");
        payResult.setBill_no(feeRecord.getOrderNo());

        // 现在没有锁，在发请求前和发请求后都判断一次
        ChargeListTime rList = chargeListTimeMapper.selectChargeListByChargeNo(chargeNo).stream().findFirst().orElse(null);
        if (rList != null) {
            // 已结算，不做后续处理
            payResult.setStatus("0");
            payResult.setCharge_time(TimeUtils.getHisDateStr(rList.getChargeTime()));
            return payResult;
        }

        SE04Response se04Response = InsuranceBeanUtils.getDQYiBaoClient().postSE04(feeRecord.getEcToken(), feeRecord.getCityId());
        if (se04Response != null) {
            log.info("医保已支付: chargeNo: " + chargeNo);
            // 现在没有锁，在发请求前和发请求后都判断一次
            rList = chargeListTimeMapper.selectChargeListByChargeNo(chargeNo).stream().findFirst().orElse(null);
            if (rList != null) {
                // 已挂号，不做后续处理
                payResult.setStatus("0");
                payResult.setCharge_time(TimeUtils.getHisDateStr(rList.getChargeTime()));
                return payResult;
            }

            ConfirmChargeReq req = new ConfirmChargeReq();
            req.setSettle_id(chargeNo + "");
            req.setHospitalCode(hospitalCode);
            req.setHosp_account_flag("0");
            req.setSerial_no(chargeNo + "");
            req.setTrade_no(chargeNo + "");
            req.setPay_type(payChannelType.getValue());
            req.setRegno(regNo + "");
            req.setPatid(patId + "");
            req.setHospitalCode(hospitalCode);

            SI12Response sh02Response = se04Response.getSi12Response();
            BigDecimal total = sh02Response.getYbjsfwfyze().add(sh02Response.getFybjsfwfyze());
            BigDecimal pubAccount = sh02Response.getCuraccountpay().add(sh02Response.getHisaccountpay());
            BigDecimal pub = sh02Response.getTcdzhzfs().add(sh02Response.getTczfs()).add(sh02Response.getFjdzhzfs()).add(sh02Response.getFjzfs());
            BigDecimal self = total.subtract(pub).subtract(pubAccount);
            req.setTotal_amount(total.multiply(new BigDecimal(100)).intValue() + "");
            req.setShould_pay_amount(self.multiply(new BigDecimal(100)).intValue() + "");
            req.setPay_type(payChannelType.getValue());
            req.setSelf_flag("0");
            req.setJsFlag(1);

            req.setSe04Response(se04Response);
            ConfirmChargeResult chargeResult = confirmCharge(req, feeRecord);
            if (chargeResult != null && "true".equalsIgnoreCase(chargeResult.getSuccess())) {
                log.info("医保已支付，业务处理成功: chargeNo: " + chargeNo);
                payResult.setStatus("0");
                payResult.setCharge_time(TimeUtils.getHisDateStr(new Date()));
                if (push) {
                    msgPushService.pushCharge4010Succeeded(hospitalCode, regNo + "" , chargeNo + "", "",
                            feeRecord.getOrderNo(), TimeUtils.getHisDateStr(TimeUtils.convert(se04Response.getGmt())));
                }
            } else {
                log.error("医保结算成功业务处理失败: result: " + StandardObjectMapper.stringify(chargeResult));
                payResult.setStatus("2");
            }
        } else {
            log.info("医保未结算: chargeNo: " + chargeNo);
            payResult.setStatus("2");
        }

        return payResult;
    }

    @Override
    @Transactional
    public void chargeCallBackPaySuccess(CallBack6302 callBack6302, String hospitalCode, RegOnlineGjYiBaoUploadFeeRecord feeRecord) {
        Long chargeNo = feeRecord.getChargeNo();
        // 现在没有锁，在发请求前和发请求后都判断一次
        ChargeListTime rList = chargeListTimeMapper.selectChargeListByChargeNo(chargeNo).stream().findFirst().orElse(null);
        if (rList != null) {
            // 已结算，不做后续处理
            return;
        }
        LambdaQueryWrapper<PreCharge> wrapper = Wrappers.lambdaQuery();
        wrapper.eq(PreCharge::getHospitalCode, hospitalCode);
        wrapper.eq(PreCharge::getChargeNo, chargeNo);
        List<PreCharge> preCharges = preChargeMapper.selectList(wrapper);
        if (CollectionUtils.isEmpty(preCharges)) {
            throw new RuntimeException("未预算，chargeNo: " + chargeNo);
        }
        Long regNo = preCharges.get(0).getRegNo();
        Long patId = preCharges.get(0).getPatId();
        RTPatientList patient = patientListService.getById(preCharges.get(0).getPatId());
        // 支付渠道 11-支付宝小程序 17-微信小程序 13-微信公众号
        PayChannelType payChannelType = null;
        switch (feeRecord.getPayway()) {
            case 11:
                payChannelType = PayChannelType.ALIPAY_MINI_PROGRAM;
                break;
            case 17:
                payChannelType = PayChannelType.WECHAT_MINI_PROGRAM;
                break;
            case 13:
                payChannelType = PayChannelType.WECHAT_OFFICIAL_ACCOUNT;
                break;
            default:
                throw new RuntimeException("不支持的支付方式: chargeNo: " + chargeNo + ", payWay: " + feeRecord.getPayway());
        }
        GetOutpatientPayResult payResult = new GetOutpatientPayResult();
        payResult.setTrade_type("0");
        payResult.setOut_trade_no(feeRecord.getChargeNo() + "");
        payResult.setPay_order_id(feeRecord.getPayOrdId());

        Input6301 input6301 = new Input6301();
        input6301.setIdNo(patient.getCertificateNo());
        input6301.setUserName(patient.getPatName());
        input6301.setIdType("01");
        input6301.setOrgCodg(yiBaoProperties.getOrgCode());
        input6301.setPayOrdId(feeRecord.getPayOrdId());
        input6301.setPayToken(feeRecord.getPayToken());

        Output6301 output6301 = new Output6301(callBack6302);
        output6301.setOrgCodg(yiBaoProperties.getOrgCode());

        saveRegOnlineGjYiBaoOrderInfo(patient, input6301, output6301, feeRecord);

        ConfirmChargeReq req = new ConfirmChargeReq();
        req.setSettle_id(chargeNo+ "");
        req.setHospitalCode(hospitalCode);
        req.setHosp_account_flag("0");
        req.setSerial_no(output6301.getMedOrgOrd());
        req.setTrade_no(chargeNo + "");
        req.setPay_type(payChannelType.getValue());
        req.setRegno(regNo + "");
        req.setPatid(patId + "");
        req.setHospitalCode(hospitalCode);
        req.setTotal_amount(output6301.getFeeSumamt().multiply(new BigDecimal(100)).intValue() + "");
        req.setShould_pay_amount(output6301.getFeeSumamt().multiply(new BigDecimal(100)).intValue() + "");
        req.setPay_type(payChannelType.getValue());
        req.setSerial_no(output6301.getMedOrgOrd());
        req.setSelf_flag("0");
        req.setJsFlag(2);

        req.setOutput6301(output6301);
        ConfirmChargeResult chargeResult = confirmCharge(req, null);
        if (chargeResult != null && "true".equalsIgnoreCase(chargeResult.getSuccess())) {
            log.info("医保已支付，业务处理成功: chargeNo: " + chargeNo);
            payResult.setStatus("0");
            payResult.setCharge_time(TimeUtils.getHisDateStr(new Date()));
            msgPushService.pushCharge4010Succeeded(hospitalCode, regNo + "" , chargeNo + "",
                    feeRecord.getPayOrdId(), "", output6301.getTraceTime());
        } else {
            log.error("医保结算成功业务处理失败: result: " + StandardObjectMapper.stringify(chargeResult));
            payResult.setStatus("2");
        }
    }

    @Override
    @Transactional
    public void chargeCallBackPaySuccess(CallBackSH03 callBackSH03, String hospitalCode, RegOnlineSHYiBaoUploadFeeRecord feeRecord) {
        Long chargeNo = feeRecord.getChargeNo();
        // 现在没有锁，在发请求前和发请求后都判断一次
        ChargeListTime rList = chargeListTimeMapper.selectChargeListByChargeNo(chargeNo).stream().findFirst().orElse(null);
        if (rList != null) {
            // 已结算，不做后续处理
            return;
        }
        LambdaQueryWrapper<PreCharge> wrapper = Wrappers.lambdaQuery();
        wrapper.eq(PreCharge::getHospitalCode, hospitalCode);
        wrapper.eq(PreCharge::getChargeNo, chargeNo);
        List<PreCharge> preCharges = preChargeMapper.selectList(wrapper);
        if (CollectionUtils.isEmpty(preCharges)) {
            throw new RuntimeException("未预算，chargeNo: " + chargeNo);
        }
        Long regNo = preCharges.get(0).getRegNo();
        Long patId = preCharges.get(0).getPatId();
        RTPatientList patient = patientListService.getById(preCharges.get(0).getPatId());
        // 支付渠道 11-支付宝小程序 17-微信小程序 13-微信公众号
        PayChannelType payChannelType = null;
        switch (feeRecord.getPayway()) {
            case 11:
                payChannelType = PayChannelType.ALIPAY_MINI_PROGRAM;
                break;
            case 17:
                payChannelType = PayChannelType.WECHAT_MINI_PROGRAM;
                break;
            case 13:
                payChannelType = PayChannelType.WECHAT_OFFICIAL_ACCOUNT;
                break;
            default:
                throw new RuntimeException("不支持的支付方式: chargeNo: " + chargeNo + ", payWay: " + feeRecord.getPayway());
        }
        GetOutpatientPayResult payResult = new GetOutpatientPayResult();
        payResult.setTrade_type("0");
        payResult.setOut_trade_no(feeRecord.getChargeNo() + "");
        payResult.setBill_no(feeRecord.getOrderNo());

        ConfirmChargeReq req = new ConfirmChargeReq();
        req.setSettle_id(chargeNo+ "");
        req.setHospitalCode(hospitalCode);
        req.setHosp_account_flag("0");
        req.setSerial_no(chargeNo + "");
        req.setTrade_no(chargeNo + "");
        req.setPay_type(payChannelType.getValue());
        req.setRegno(regNo + "");
        req.setPatid(patId + "");
        req.setHospitalCode(hospitalCode);
        SI12Response sh02Response = callBackSH03.getSi12Response();
        BigDecimal total = sh02Response.getYbjsfwfyze().add(sh02Response.getFybjsfwfyze());
        BigDecimal pubAccount = sh02Response.getCuraccountpay().add(sh02Response.getHisaccountpay());
        BigDecimal pub = sh02Response.getTcdzhzfs().add(sh02Response.getTczfs()).add(sh02Response.getFjdzhzfs()).add(sh02Response.getFjzfs());
        BigDecimal self = total.subtract(pub).subtract(pubAccount);
        req.setTotal_amount(total.multiply(new BigDecimal(100)).intValue() + "");
        req.setShould_pay_amount(self.multiply(new BigDecimal(100)).intValue() + "");
        req.setPay_type(payChannelType.getValue());
        req.setSelf_flag("0");
        req.setJsFlag(1);

        req.setSe04Response(callBackSH03);
        ConfirmChargeResult chargeResult = confirmCharge(req, null);
        if (chargeResult != null && "true".equalsIgnoreCase(chargeResult.getSuccess())) {
            log.info("医保已支付，业务处理成功: chargeNo: " + chargeNo);
            payResult.setStatus("0");
            payResult.setCharge_time(TimeUtils.getHisDateStr(new Date()));
            msgPushService.pushCharge4010Succeeded(hospitalCode, regNo + "" , chargeNo + "",
                    "", feeRecord.getOrderNo(), TimeUtils.getHisDateStr(TimeUtils.convert(callBackSH03.getGmt())));
        } else {
            log.error("医保结算成功业务处理失败: result: " + StandardObjectMapper.stringify(chargeResult));
            payResult.setStatus("2");
        }
    }

    @Override
    public PreChargeResult preChargeForHuLi(List<ThirdAddAccount> items, RegisterListTime register) {
        PreChargeResult result = new PreChargeResult();
        Long proChargeNo = preChargeNoProcessor.execute();
        Date date = new Date();
        // 1.根据处方号查询收费明细数据
        List<PreChargeDetail> preChargeDetails = items.stream().map(u -> {
            PreChargeDetail detail = new PreChargeDetail();
//                detail.setRecipeNum();
            detail.setRecipeID(u.getRecipeId());
            detail.setRecipeDetlID(u.getRecipeDetlID());
            detail.setItemID(Math.toIntExact(u.getItemID()));
//                detail.setNewItemID();
            detail.setQuantity(u.getQuantiy());
            detail.setItemCategory(u.getItemCategory());
            detail.setItemCategoryName(u.getItemCategoryName());
            detail.setGroupNo(u.getGroupNo());
            detail.setClinicUnit(u.getClinicUnit());
            detail.setBasicUnit(u.getBasicUnit());
            detail.setClinicQty(u.getClinicQty().intValue());
            detail.setDosage(u.getDosage());
            detail.setDosageUnit(u.getDosageUnit());
            detail.setDrugGauge(u.getDrugGauge());
            detail.setCheckCode(u.getCheckCode());
            detail.setExecuteDept(u.getExecuteDept());
            detail.setTimes(u.getTimes());
            detail.setPrice(u.getPrice());
            detail.setExpensePrice(u.getExpensePrice());
            detail.setNonExpensePrice(u.getNonExpensePrice());
            detail.setTotalAmount(u.getTotalAmount());
            detail.setDoctorId(u.getDoctorId());
            detail.setDeptId(u.getDeptId());
            detail.setFeeType(u.getFeeType());
            detail.setIsDrug(0);
            if (u.getItemCategory() == 12 || u.getItemCategory() == 13 || u.getItemCategory() == 14) {
                u.setIsDrug(1);
            }
            detail.setHospitalCode(Integer.valueOf(u.getHospitalCode()));
            detail.setRecipeOn(u.getZhkfrq());
//                detail.setCfzt(u.getSqzt());
            detail.setRegistType(register.getRegistType());
            detail.setPreChargeNo(proChargeNo);
            detail.setRegistType(detail.getRegistType());
            detail.setCardNo(register.getCardNo());
            detail.setPatId(register.getPatID());
            detail.setNewPatId(register.getNewPatID());
            detail.setRegNo(register.getRegNo());
            detail.setPackageNo(0);
            detail.setBasicUnit("");
            detail.setStatus(0);
            detail.setDataFrom(111);
            detail.setFromFlag(0);
            detail.setIsDelete(false);
            detail.setCreatedBy(hisHospitalProperties.getOpCode());
            detail.setCreatedDate(date);
            if(StringUtils.length(detail.getClinicUnit()) > 7) {
                detail.setClinicUnit(StringUtils.substring(detail.getClinicUnit(), 0, 7));
            }
            if(StringUtils.length(detail.getDosageUnit()) > 3) {
                detail.setDosageUnit(StringUtils.substring(detail.getDosageUnit(), 0, 3));
            }
            preChargeDetailMapper.insert(detail);
            return detail;
        }).collect(Collectors.toList());

        // 4.保存预收费表
        Long chargeNo = mzNoProcessor.execute();
        int chargeType = 10;
        preChargeMapper.insertPreCharge(proChargeNo, chargeType, chargeNo, new Date());
        // 6.返回预收费号
        preChargeDetails.forEach(detail -> {
            BigDecimal totalAmount = detail.getTotalAmount();

            PreChargeAmt preChargeAmt = new PreChargeAmt();
            preChargeAmt.setChargeNo(chargeNo);
            preChargeAmt.setRegNo(detail.getRegNo());
            preChargeAmt.setRecipeID(proChargeNo);
            preChargeAmt.setRecipeDetlID(detail.getRecipeDetlID());
            preChargeAmt.setDiscountAmount(detail.getDiscountAmount());
            preChargeAmt.setInsuranceTradeAmount(new BigDecimal(0));
            preChargeAmt.setSelfAmount(totalAmount);
            // [ClassifyTotal], [InsuranceCashAmount], [CreditAmt]
            preChargeAmt.setClassifyTotal(new BigDecimal(0));
            preChargeAmt.setInsuranceCashAmount(new BigDecimal(0));
            preChargeAmt.setCreditAmt(new BigDecimal(0));
            // [RealAmt], [OtherAmt], [JzAmount], FeeType
            preChargeAmt.setRealAmt(totalAmount);
            preChargeAmt.setOtherAmt(new BigDecimal(0));
            preChargeAmt.setJzAmount(new BigDecimal(0));
            preChargeAmt.setFeeType(detail.getFeeType());

            int i = preChargeAmtMapper.countPreChargeAmt(chargeNo, detail.getRecipeDetlID());
            if (i > 0) {
                preChargeAmtMapper.updateById(preChargeAmt);
            } else {
                preChargeAmtMapper.insert(preChargeAmt);
            }
        });
        BigDecimal feeAmount = preChargeDetails.stream().map(PreChargeDetail::getTotalAmount)
            .reduce(BigDecimal.ZERO, BigDecimal::add);
        int feeAmountResult = feeAmount.multiply(new BigDecimal(100)).intValue();

        log.info("护理上门缴费预算----预结算金额单位分：{}", feeAmountResult);
        result.setRegno(register.getRegNo().toString());
        result.setSettle_id(chargeNo.toString());
        result.setTotal_amount("" + feeAmountResult);
        result.setSelf_amount("" + feeAmountResult);
        result.setShould_pay_amount("" + feeAmountResult);
        result.setDiscount_amount("0");
        return result;
    }

    /**
     * 保存医保结算数据
     * @param patient
     * @param input6301
     * @param output6301
     * @param feeRecord
     */
    @Transactional(propagation = Propagation.REQUIRES_NEW)
    public void saveRegOnlineGjYiBaoOrderInfo(RTPatientList patient, Input6301 input6301, Output6301 output6301,
                                               RegOnlineGjYiBaoUploadFeeRecord feeRecord) {
        LambdaQueryWrapper<RegOnlineGjYiBaoOrderInfo> wrapper = Wrappers.lambdaQuery();
        wrapper.eq(RegOnlineGjYiBaoOrderInfo::getPayOrdId, output6301.getPayOrdId());
        RegOnlineGjYiBaoOrderInfo gjYiBaoOrderInfo = regOnlineGjYiBaoOrderInfoMapper.selectOne(wrapper);
        if (gjYiBaoOrderInfo != null) {
            return;
        }
        gjYiBaoOrderInfo = new RegOnlineGjYiBaoOrderInfo();
        gjYiBaoOrderInfo.setCreatedDate(new Date());
        gjYiBaoOrderInfo.setPatName(patient.getPatName());
        gjYiBaoOrderInfo.setCardType(input6301.getIdType());
        gjYiBaoOrderInfo.setCardNo(input6301.getIdNo());
        gjYiBaoOrderInfo.setChargeNo(feeRecord.getChargeNo());
        gjYiBaoOrderInfo.setRegNo(feeRecord.getRegNo());
        gjYiBaoOrderInfo.setFlag(feeRecord.getFlag());
        gjYiBaoOrderInfo.setHospitalCode(patient.getHospitalCode() + "");
        gjYiBaoOrderInfo.setOrdStas(output6301.getOrdStas());
        gjYiBaoOrderInfo.setPayOrdId(output6301.getPayOrdId());
        gjYiBaoOrderInfo.setCallType(output6301.getCallType());
        gjYiBaoOrderInfo.setMedOrgOrd(output6301.getMedOrgOrd());
        gjYiBaoOrderInfo.setTraceTime(output6301.getTraceTime());
        gjYiBaoOrderInfo.setOrgCode(output6301.getOrgCodg());
        gjYiBaoOrderInfo.setOrgName(output6301.getOrgName());
        gjYiBaoOrderInfo.setSetlType(output6301.getSetlType());
        gjYiBaoOrderInfo.setFeeSumamt(output6301.getFeeSumamt());
        gjYiBaoOrderInfo.setOwnPayAmt(output6301.getOwnPayAmt());
        gjYiBaoOrderInfo.setPsnAcctPay(output6301.getPsnAcctPay());
        gjYiBaoOrderInfo.setFundPay(output6301.getFundPay());
        gjYiBaoOrderInfo.setRevsToken(output6301.getRevsToken());
        gjYiBaoOrderInfo.setExtData(StandardObjectMapper.stringify(output6301.getExtData()));
        gjYiBaoOrderInfo.setDeposit(new BigDecimal(0));
        gjYiBaoOrderInfo.setHiChrgTime(output6301.getHiChrgTime());
        gjYiBaoOrderInfo.setHiDocSn(output6301.getHiDocSn());
        gjYiBaoOrderInfo.setHiRgstSn(output6301.getHiRgstSn());
        if (output6301.getExtData() != null) {
            gjYiBaoOrderInfo.setSetlId(output6301.getExtData().getSetlinfo().getSetlId());
            gjYiBaoOrderInfo.setMdtrtId(output6301.getExtData().getSetlinfo().getMdtrtId());
            gjYiBaoOrderInfo.setPsnNo(output6301.getExtData().getSetlinfo().getPsnNo());
            gjYiBaoOrderInfo.setInsuplcAdmdvs(output6301.getExtData().getInsuplcAdmdvs());

        }

        regOnlineGjYiBaoOrderInfoMapper.insert(gjYiBaoOrderInfo);
    }


    @Override
    public String returnOutPatientInsuranceFee(OutpatientChargeRefundedReq req) {
        // 查询上传明细
        RegOnlineGjYiBaoUploadFeeRecord uploadRecord = regOnlineGjYiBaoUploadFeeRecordMapper.selectByChargeNo(
            req.getSettle_id());
        ChargeListTime chargeListTime = chargeListTimeMapper.selectChargeListByChargeNo(
        Long.parseLong(req.getSettle_id())).stream().findFirst().orElse(null);
        if (chargeListTime == null) {
            return "未查询到收费记录，settle_id无效";
        }
        if (chargeListTime.getReturnChargeNo() == null) {
            Input6203 input6203 = new Input6203();
            input6203.setPayOrdId(uploadRecord.getPayOrdId());
            input6203.setAppRefdSn(req.getSettle_id());
            input6203.setAppRefdTime(TimeUtils.getHisDateStr(new Date()));
            input6203.setTotlRefdAmt(uploadRecord.getFeeSumamt());
            input6203.setPsnAcctRefdAmt(uploadRecord.getPsnAcctPay());
            input6203.setFundRefdAmt(uploadRecord.getFundPay());
            input6203.setCashRefdAmt(uploadRecord.getOwnPayAmt());
            // ALL:全部 CASH:只退现金 HI:只退医保
            input6203.setRefdType("HI");
            // 退号时医保需要重新授权传入
            MedicalInsuranceParam insuranceParam = req.getInsuranceParam();
            if (null != insuranceParam && StringUtils.isNotBlank(insuranceParam.getPayAuthNo())) {
                input6203.setPayAuthNo(insuranceParam.getPayAuthNo());
            } else {
                return "医保退费失败 没有医保参数无法退费";
            }

            Output6203 output6203 = InsuranceBeanUtils.getGJYiBaoClient().post6203(input6203, insuranceParam,
                                                           PayChannelType.getByHisValue(uploadRecord.getPayway()),
                                                           req.getHospitalCode());
            if (!"SUCC".equals(output6203.getRefStatus())) {
                return "医保退费失败 outData:" + StandardObjectMapper.stringify(output6203);
            }
            chargeListTime.setReturnChargeNo(1L);
            chargeListTimeMapper.updateById(chargeListTime);
        }
        
        // 医保退费成功后，发4003消息通知IH退自费部分
        MsgPushReq msgPushReq = new MsgPushReq();
        //     @ApiModelProperty("挂号流水号")
        //    private String regno;
        //
        //    @ApiModelProperty("就诊类别 1门诊 2住院")
        //    private String user_source;
        //
        //    @ApiModelProperty("消息类型")
        //    private String msg_type;
        //
        //    @ApiModelProperty("业务流水号")
        //    private String operation_sn;
        msgPushReq.setRegno(chargeListTime.getRegNo().toString());
        msgPushReq.setUser_source("1");
        msgPushReq.setMsg_type("4003");
        msgPushReq.setOperation_sn(req.getSettle_id());
        Map<String, Object> msgData = Maps.newHashMap();
        //         "settle_id": "39295",
        //        "type": "2",
        //        "total_refund_amount": "550",
        //        "self_refund_amount": "550",
        //        "insurance_refund_amount": "0",
        //        "refund_time": "20240611203516"
        msgData.put("settle_id", req.getSettle_id());
        msgData.put("type", "1");
        msgData.put("total_refund_amount", new BigDecimal(
            String.valueOf(uploadRecord.getFeeSumamt())).multiply(new BigDecimal(100)).toString());
        msgData.put("self_refund_amount", new BigDecimal(String.valueOf(uploadRecord.getOwnPayAmt())).multiply(new BigDecimal(100)).toString());
        msgData.put("insurance_refund_amount",
                    new BigDecimal(String.valueOf(uploadRecord.getFundPay())).multiply(new BigDecimal(100)).toString());
        msgData.put("refund_time", TimeUtils.getHisDateStr(new Date()));
        msgPushReq.setMsg_details(msgData);
        Msg msg = new Msg();
        msg.setRegno(msgPushReq.getRegno());
        msg.setOrgan_name("sxrh");
        msg.setMsg_type(msgPushReq.getMsg_type());
        if (!com.baomidou.mybatisplus.core.toolkit.StringUtils.isEmpty(msgPushReq.getOperation_sn())) {
            msg.setOperation_sn(msgPushReq.getOperation_sn());
        } else {
            msg.setOperation_sn(msg.getRegno());
        }
        msg.setTimestamp(TimeUtils.dateStringFormat(new Date(), "yyyyMMddHHmmss"));
        msg.setMsg_details(msgPushReq.getMsg_details());
        msg.setRequest_id(UUID.randomUUID().toString());
        msg.setHospitalCode(hisHospitalProperties.getCode());
        msg.setOrgan_code(hisHospitalProperties.getCode());
        msg.setPat_name("sxrh");
        msg.setPatid("sxrh");
        msg.setMsg_type("4003");
        msg.setUser_source("1");
        msg.setCertificate_no("411123199512160012");


        MsgPushRes msgPushRes = nodeRedClient.pushPatientMsg(msg, noderedProperties.getBaseUrl());
        if (msgPushRes.getCode() == 200) {
            return "success";
        } else {
            return "医保退费成功，4003自费退费失败：" + msgPushRes.getMessage();
        }
    }

    @Override
    public void manageInsuranceData() {
        // 查询orderInfo表中setlid为空的数据
        LambdaQueryWrapper<RegOnlineGjYiBaoOrderInfo> wrapper = Wrappers.lambdaQuery();
        wrapper.isNull(RegOnlineGjYiBaoOrderInfo::getSetlId);
        List<RegOnlineGjYiBaoOrderInfo> orderInfos = regOnlineGjYiBaoOrderInfoMapper.selectList(wrapper);
        for (RegOnlineGjYiBaoOrderInfo orderInfo : orderInfos) {
            // 查询医保结算数据
            ExtData extData = StandardObjectMapper.readValue(orderInfo.getExtData(), new TypeReference<ExtData>() {
            });
            orderInfo.setSetlId(extData.getSetlinfo().getSetlId());
            orderInfo.setMdtrtId(extData.getSetlinfo().getMdtrtId());
            orderInfo.setPsnNo(extData.getSetlinfo().getPsnNo());
            orderInfo.setInsuplcAdmdvs(extData.getInsuplcAdmdvs());
            regOnlineGjYiBaoOrderInfoMapper.updateById(orderInfo);
        }
    }

    /**
     * 免费服药挂号中chargeType为19或20时，预算报错《患者免费服药请线下结算》
     * @param req
     */
    private void throwErrorForFreeCharge(PreChargeReq req) {
        RegisterListView registerList = AppContext.getInstance(RegisterListViewMapper.class).selectById(req.getRegno());
        if (registerList == null) {
            throw new RuntimeException("挂号序号regNo:" + req.getRegno() + "不正确");
        } else if (registerList.getChargeType() == 19 || registerList.getChargeType() == 20){
            throw new RuntimeException("患者免费服药请线下结算");
        }
    }
}
