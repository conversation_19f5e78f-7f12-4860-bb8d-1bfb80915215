package com.sunhealth.ihhis.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.sunhealth.ihhis.common.JsonResponse;
import com.sunhealth.ihhis.config.HisHospitalProperties;
import com.sunhealth.ihhis.dao.his.*;
import com.sunhealth.ihhis.model.dto.push.*;
import com.sunhealth.ihhis.model.entity.Hospital;
import com.sunhealth.ihhis.model.entity.inpatient.InPatient;
import com.sunhealth.ihhis.model.entity.patient.RTPatientCard;
import com.sunhealth.ihhis.model.entity.patient.RTPatientList;
import com.sunhealth.ihhis.model.entity.register.Appointment;
import com.sunhealth.ihhis.model.entity.register.PreRegisterList;
import com.sunhealth.ihhis.nodered.NodeRedClient;
import com.sunhealth.ihhis.service.MsgPushService;
import com.sunhealth.ihhis.utils.TimeUtils;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.HashMap;
import java.util.Map;
import java.util.UUID;

@Slf4j
@Service
@AllArgsConstructor
public class MsgPushServiceImpl implements MsgPushService {

    private final NodeRedClient nodeRedClient;

    private final PatientListMapper patientListMapper;
    private final PatientCardMapper patientCardMapper;

    private final PreRegisterListMapper preRegisterListMapper;
    private final InPatientMapper inPatientMapper;
    private final HospitalMapper hospitalMapper;
    private final HisHospitalProperties hisHospitalProperties;
    private final AppointmentMapper appointmentMapper;
    @Async
    @Override
    public void pushRegister4010Succeeded(String hospitalCode, String regNo, String payOrderId, String billNo, String traceTime) {
        MsgPushReq pushReq = new MsgPushReq();
        pushReq.setRegno(regNo);
        pushReq.setUser_source("1");
        pushReq.setMsg_type("4010");
        pushReq.setOperation_sn(regNo);
        Map<String, Object> detail = new HashMap<>();
        detail.put("trade_type", "0");
        detail.put("status", "0");
        detail.put("out_trade_no", regNo);
        detail.put("pay_order_id", payOrderId);
        detail.put("bill_no", billNo);
        detail.put("charge_time", traceTime);
        pushReq.setMsg_details(detail);
        MsgPushRes res = pushPatientMsg(pushReq, hospitalCode);
        log.info("发送挂号医保结算成功消息：{}", res); ;
    }

    @Override
    public void pushCharge4010Succeeded(String hospitalCode, String regNo, String chargeNo, String payOrderId, String billNo, String traceTime) {
        MsgPushReq pushReq = new MsgPushReq();
        pushReq.setRegno(regNo);
        pushReq.setUser_source("1");
        pushReq.setMsg_type("4010");
        pushReq.setOperation_sn(chargeNo);
        Map<String, Object> detail = new HashMap<>();
        detail.put("trade_type", "1");
        detail.put("status", "0");
        detail.put("out_trade_no", chargeNo);
        detail.put("pay_order_id", payOrderId);
        detail.put("bill_no", billNo);
        detail.put("charge_time", traceTime);
        pushReq.setMsg_details(detail);
        MsgPushRes res = pushPatientMsg(pushReq, hospitalCode);
        log.info("发送挂号医保结算成功消息：{}", res); ;

    }

    @Override
    public MsgPushRes pushPatientMsg(MsgPushReq req, String hospitalCode) {
        Msg msg = new Msg();
        msg.setRegno(req.getRegno());
        String errMsg;
        switch (req.getMsg_type()) {
            case "1501":
                errMsg = stopSourceMsg(msg,req.getRegno());
                break;
            case "1101":
            case "9001":
            case "9002":
            case "9003":
            case "4003":
                errMsg = outpatient(msg, req.getRegno(), false);
                break;
            case "1301":
            case "4010":
                if ("1".equals(req.getUser_source())) {
                    errMsg = outpatient(msg, req.getRegno(), false);
                    break;
                }
                if ("2".equals(req.getUser_source())) {
                    errMsg = inpatient(msg, req.getRegno());
                    break;
                }
            case "4008":
                errMsg = inpatient(msg, req.getRegno());
                break;
            default:
                log.error("消息类型错误");
                return MsgPushRes.fail("消息类型错误");
        }

        if (StringUtils.isNotEmpty(errMsg)) {
            return MsgPushRes.fail(errMsg);
        }

        if (hospitalCode == null) {
            hospitalCode = hisHospitalProperties.getCode();
        }
        msg.setOrgan_code(hospitalCode);
        Hospital hospital = hospitalByCode(hospitalCode);
        msg.setOrgan_name(hospital == null ? "" : hospital.getHospitalName());
        msg.setMsg_type(req.getMsg_type());
        if (!StringUtils.isEmpty(req.getOperation_sn())) {
            msg.setOperation_sn(req.getOperation_sn());
        } else {
            msg.setOperation_sn(msg.getRegno());
        }
        msg.setTimestamp(TimeUtils.dateStringFormat(new Date(), "yyyyMMddHHmmss"));
        msg.setMsg_details(req.getMsg_details());
        msg.setRequest_id(UUID.randomUUID().toString());
        msg.setHospitalCode(hospitalCode);

        return nodeRedClient.pushPatientMsg(msg);
    }

    /**
     * 根据挂号流水号/门诊号查询门诊患者信息
     * 默认 regNo是挂号流水号，但是东港1301消息拿不到挂号流水号，只能根据就诊流水号去查
     * @param regNo 就诊流水号（门诊号）或挂号流水号（regno）
     * @param jzlsh 是 表示regNo是就诊流水号，否 表示regNo是挂号流水号
     */
    private String outpatient(Msg msg, String regNo, boolean jzlsh) {
        RTPatientList patient;
        if (jzlsh) {
            // regNo是就诊流水号
            patient = patientListMapper.listPatientByJzlsh(regNo).stream().findFirst().orElse(null);
            if (patient == null) {
                return String.format("未查询到该门诊号[%s]的患者信息", regNo);
            }
        } else {
            // regNo是挂号序号
            PreRegisterList preRegisterList = preRegisterListMapper.selectById(regNo);
            if (preRegisterList == null) {
                return String.format("未查询到该挂号流水号[%s]的患者信息",regNo);
            }
            patient = patientListMapper.selectByPatId(preRegisterList.getPatID()+"");
            if (patient == null) {
                throw new RuntimeException("院内无此患者信息或患者信息已停用");
            }
        }
        msg.setUser_source("1");
        RTPatientCard patientCard = patientCard(patient.getPatId());
        msg.setCertificate_no(patient.getCertificateNo());
        msg.setPat_name(patient.getPatName());
        msg.setPatid(patient.getPatId()+"");
        msg.setCard_no(patientCard.getCardNo());
        return null;
    }

    /**
     * 停诊通知处理
     * 根据regno作为appointId查询预约记录
     */
    private String stopSourceMsg(Msg msg, String regNo) {
        RTPatientList patient;

        Appointment appointment = appointmentMapper.selectById(regNo);
        if (appointment == null) {
            throw new RuntimeException("根据预约ID" + regNo + "未查询到预约信息");
        }

        patient = patientListMapper.selectByPatId(appointment.getPatNo()+"");
        if (patient == null) {
            throw new RuntimeException("院内无此患者信息或患者信息已停用");
        }
        msg.setUser_source("1");
        RTPatientCard patientCard = patientCard(patient.getPatId());
        msg.setCertificate_no(patient.getCertificateNo());
        msg.setPat_name(patient.getPatName());
        msg.setPatid(patient.getPatId()+"");
        msg.setCard_no(patientCard.getCardNo());
        return null;
    }

    /**
     * 根据住院号查询住院患者信息
     * @param regNo 住院号
     */
    private String inpatient(Msg msg, String regNo) {
        LambdaQueryWrapper<InPatient> wrapper = Wrappers.lambdaQuery();
        wrapper.eq(InPatient::getHospNo, regNo);
        InPatient inpatient = inPatientMapper.selectList(wrapper).stream().findFirst().orElse(null);
        if (inpatient == null) {
            return String.format("未查询到该住院号[%s]的患者信息",regNo);
        }
        msg.setUser_source("2");
        msg.setCertificate_no(inpatient.getCertificateNo());
        msg.setPat_name(inpatient.getPatName());
        msg.setPatid(inpatient.getPatId()+"");
        msg.setCard_no(inpatient.getCardNo());
        return null;
    }

    /**
     * 查询就诊卡
     * @param patId
     * @return
     */
    private RTPatientCard patientCard(Long patId) {
        LambdaQueryWrapper<RTPatientCard> wrapper = Wrappers.lambdaQuery();
        wrapper.eq(RTPatientCard::getPatId, patId);
        return patientCardMapper.selectList(wrapper).stream().findFirst().orElse(null);
    }

    private Hospital hospitalByCode(String hospitalCode) {
        LambdaQueryWrapper<Hospital> wrapper = Wrappers.lambdaQuery();
        wrapper.eq(Hospital::getHospitalCode, hospitalCode);
        return hospitalMapper.selectList(wrapper).stream().findFirst().orElse(null);
    }

    @Override
    public JsonResponse<HisQRCodeResponse> bQRCode(HisQRCodeRequest qrCodeRequest) {
        String hospitalCode = hisHospitalProperties.getCode();
        try {
            IHQRCodeRequest ihqrCodeRequest = new IHQRCodeRequest(qrCodeRequest);
            ihqrCodeRequest.setOrgan_code(hospitalCode);
            Hospital hospital = hospitalByCode(hospitalCode);
            ihqrCodeRequest.setOrgan_name(hospital == null ? "" : hospital.getHospitalName());
            ihqrCodeRequest.setTimestamp(TimeUtils.dateStringFormat(new Date(), "yyyyMMddHHmmss"));
            ihqrCodeRequest.setRequest_id(UUID.randomUUID().toString());
            ihqrCodeRequest.setPatName(patientListMapper.selectByPatId(ihqrCodeRequest.getPatid()).getPatName());
            HisQRCodeResponse qrCode = nodeRedClient.getQRCode(hospitalCode, ihqrCodeRequest);
            return new JsonResponse<>("0", qrCode);
        } catch (Exception e) {
            log.error("二维码生成失败", e);
            return new JsonResponse<>("1", e.getMessage());
        }
    }
}
