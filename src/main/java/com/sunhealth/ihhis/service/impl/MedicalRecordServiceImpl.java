package com.sunhealth.ihhis.service.impl;

import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import com.sunhealth.ihhis.dao.his.RegisterListTimeMapper;
import com.sunhealth.ihhis.model.dto.medicalrecord.PatientMedicalRecord;
import com.sunhealth.ihhis.model.dto.medicalrecord.PatientMedicalRecordInfo;
import com.sunhealth.ihhis.model.dto.outpatientcharge.OutpatientRecipeReq;
import com.sunhealth.ihhis.service.MedicalRecordService;
import com.sunhealth.ihhis.utils.MedicalRecordRenderer;
import java.io.ByteArrayInputStream;
import java.io.StringReader;
import java.util.ArrayList;
import java.util.List;
import java.util.Set;
import javax.xml.parsers.DocumentBuilder;
import javax.xml.parsers.DocumentBuilderFactory;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.w3c.dom.Document;
import org.w3c.dom.Element;
import org.w3c.dom.Node;
import org.w3c.dom.NodeList;
import org.xml.sax.InputSource;

@Service
@Slf4j
public class MedicalRecordServiceImpl implements MedicalRecordService {

    private final RegisterListTimeMapper registerListTimeMapper;


    public MedicalRecordServiceImpl(RegisterListTimeMapper registerListTimeMapper) {
        this.registerListTimeMapper = registerListTimeMapper;
    }

    @Override
    public List<PatientMedicalRecord> getOutpatientMedicalRecords(OutpatientRecipeReq req) {
        List<PatientMedicalRecord> records = registerListTimeMapper.selectMedicalRecord(req);
        records.forEach(u -> {

            u.setDocument_infos(getInfosByBL(u.getBl(), u.getBl_html()));
        });
        return records;
    }

    private List<PatientMedicalRecordInfo> getInfosByBL(String blXmlStr, String blHtml) {
        PatientMedicalRecordInfo info = new PatientMedicalRecordInfo();
        if (blXmlStr != null) {
            info.setDocument_content(MedicalRecordRenderer.renderToHtml(blXmlStr));
        } else if (blHtml != null) {
            info.setDocument_content(blHtml);
        }
        return Lists.newArrayList(info);
    }
}
