package com.sunhealth.ihhis.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.google.common.collect.Lists;
import com.sunhealth.ihhis.config.HisHospitalProperties;
import com.sunhealth.ihhis.dao.his.ChargeItemViewMapper;
import com.sunhealth.ihhis.dao.his.ThirdAddAccountMapper;
import com.sunhealth.ihhis.model.dto.outpatientcharge.InsertThirdAddAccountReq;
import com.sunhealth.ihhis.model.dto.outpatientcharge.InsertThirdAddAccountResult;
import com.sunhealth.ihhis.model.dto.outpatientcharge.ThirdAddAccountItem;
import com.sunhealth.ihhis.model.entity.charge.ThirdAddAccount;
import com.sunhealth.ihhis.model.entity.register.RegisterListTime;
import com.sunhealth.ihhis.model.entity.view.ChargeItemView;
import com.sunhealth.ihhis.service.ThirdAddAccountService;
import com.sunhealth.ihhis.service.processer.RecipeDetailNoProcessor;
import com.sunhealth.ihhis.service.processer.RecipeNoProcessor;
import com.sunhealth.ihhis.service.processer.ThirdAddAccountIdProcessor;
import com.sunhealth.ihhis.utils.StandardObjectMapper;
import com.sunhealth.ihhis.utils.StringUtil;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

@Service
@Slf4j
@AllArgsConstructor
public class ThirdAddAccountServiceImpl implements ThirdAddAccountService {

    private final ChargeItemViewMapper chargeItemViewMapper;
    private final RecipeNoProcessor recipeNoProcessor;
    private final RecipeDetailNoProcessor recipeDetailNoProcessor;
    private final ThirdAddAccountIdProcessor thirdAddAccountIdProcessor;
    private final ThirdAddAccountMapper thirdAddAccountMapper;
    private final HisHospitalProperties hisHospitalProperties;

    @Override
    @Transactional
    public InsertThirdAddAccountResult createThirdAddAccount(InsertThirdAddAccountReq req) {
        InsertThirdAddAccountResult result = new InsertThirdAddAccountResult();
        Date date = new Date();
        RegisterListTime register = req.getRegister();
        List<ThirdAddAccountItem> items = req.getItems();
        // 另加一个交通费
        if (hisHospitalProperties.getTrafficItemCode() != null) {
            LambdaQueryWrapper<ChargeItemView> wrapper = Wrappers.lambdaQuery();
            wrapper.eq(ChargeItemView::getItemCode, hisHospitalProperties.getTrafficItemCode());
            List<ChargeItemView> itemViews = chargeItemViewMapper.selectList(wrapper);
            if (!CollectionUtils.isEmpty(itemViews)) {
                ChargeItemView view = itemViews.get(0);
                ThirdAddAccountItem item = new ThirdAddAccountItem();
                item.setItem_code(String.valueOf(view.getItemCode()));
                item.setItem_name(view.getItemName());
                item.setPrice(req.getTrafficAmount());
                item.setQuantity(1);
                item.setAmount(req.getTrafficAmount());
                items.add(item);
            }
        }
        Map<String, List<ThirdAddAccountItem>> itemMapCode = items.stream()
            .collect(Collectors.groupingBy(u -> StringUtils.upperCase(u.getItem_code())));
        LambdaQueryWrapper<ChargeItemView> wrapper = Wrappers.lambdaQuery();
        wrapper.in(ChargeItemView::getCheckCode, items.stream().map(ThirdAddAccountItem::getItem_code).toArray(String[]::new));
        List<ChargeItemView> itemViews = chargeItemViewMapper.selectList(wrapper);
        List<ThirdAddAccount> thirdAddAccounts = Lists.newArrayList();
        Long recipeNo = recipeNoProcessor.execute();
        log.info("护理到家申请电子发票：itemMapCode:{}", StandardObjectMapper.stringify(itemMapCode));
        log.info("护理到家申请电子发票：itemViews:{}", StandardObjectMapper.stringify(itemViews));
        if (itemMapCode.size() != itemViews.size()) {
            log.info("护理到家申请电子发票: 存在收费项目未维护！！！！");
        }
        itemViews.forEach(chargeItemView -> {
            List<ThirdAddAccountItem> itemsList = itemMapCode.get(StringUtils.upperCase(chargeItemView.getCheckCode()));
            log.info("护理到家申请电子发票：checkCode: {} itemsList: {}",chargeItemView.getCheckCode(),
                     null == itemsList ? null : StandardObjectMapper.stringify(itemsList));
            if (CollectionUtils.isEmpty(itemsList)) return;
            ThirdAddAccountItem item = itemsList.get(0);
            if (item != null) {
                Long recipeDetailId = recipeDetailNoProcessor.execute();
                Long thirdId = thirdAddAccountIdProcessor.execute();
                // 收集收费项目写入ThirdAddAccount表
                ThirdAddAccount thirdAddAccount = new ThirdAddAccount(chargeItemView);
                thirdAddAccount.setCardno(register.getCardNo());
                thirdAddAccount.setPatid(register.getPatID());
                thirdAddAccount.setRecipeId(recipeNo);
                thirdAddAccount.setRecipeDetlID(recipeDetailId);
                thirdAddAccount.setQuantiy(BigDecimal.valueOf(item.getQuantity()));
                thirdAddAccount.setExecuteDept(register.getDeptID());
                thirdAddAccount.setPrice(chargeItemView.getExpensePrice().add(chargeItemView.getNonExpensePrice()));
                thirdAddAccount.setTotalAmount(thirdAddAccount.getQuantiy().multiply(thirdAddAccount.getPrice()));
                thirdAddAccount.setDeptId(register.getDeptID());
                thirdAddAccount.setDoctorId(register.getDoctorID());
                thirdAddAccount.setDataFrom("掌上医院-护理到家");
                thirdAddAccount.setZhkfrq(date);
                thirdAddAccount.setIsCharge(1);
                thirdAddAccount.setRegno(register.getRegNo());
                thirdAddAccount.setHospno(register.getHospNo());
                thirdAddAccount.setSqzt(0);
                thirdAddAccount.setItemCategoryName(chargeItemViewMapper.selectCategoryName(thirdAddAccount.getItemCategory(), thirdAddAccount.getHospitalCode()));
                thirdAddAccount.setId(thirdId);
                thirdAddAccountMapper.insert(thirdAddAccount);
                thirdAddAccounts.add(thirdAddAccount);
            }
        });
        if (CollectionUtils.isEmpty(thirdAddAccounts)) {
            log.error("护理到家申请电子发票：收费项目未成功匹配");
        }
        result.setRecipeNo(recipeNo);
        result.setItems(thirdAddAccounts);
        return result;
    }
}
