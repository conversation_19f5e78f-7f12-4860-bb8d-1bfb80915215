package com.sunhealth.ihhis.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.sunhealth.ihhis.common.Constants;
import com.sunhealth.ihhis.config.HisHospitalProperties;
import com.sunhealth.ihhis.dao.his.GJYiBaoPatientAccountMapper;
import com.sunhealth.ihhis.dao.his.PatientCardMapper;
import com.sunhealth.ihhis.dao.his.PatientDetlMapper;
import com.sunhealth.ihhis.dao.his.PatientListMapper;
import com.sunhealth.ihhis.enums.ChargeTypeEnum;
import com.sunhealth.ihhis.enums.Gender;
import com.sunhealth.ihhis.enums.PatientCardStatusEnum;
import com.sunhealth.ihhis.enums.PatientCardTypeEnum;
import com.sunhealth.ihhis.error.ErrorResponseException;
import com.sunhealth.ihhis.model.dto.patient.PatientCardCreateReq;
import com.sunhealth.ihhis.model.dto.patient.PatientCardCreateRes;
import com.sunhealth.ihhis.model.entity.TBDicHisDictionary;
import com.sunhealth.ihhis.model.entity.gjyibao.GjYiBaoPatientAccount;
import com.sunhealth.ihhis.model.entity.patient.RTPatientCard;
import com.sunhealth.ihhis.model.entity.patient.RTPatientDetl;
import com.sunhealth.ihhis.model.entity.patient.RTPatientList;
import com.sunhealth.ihhis.service.DictionaryService;
import com.sunhealth.ihhis.service.PatientCardService;
import com.sunhealth.ihhis.service.processer.HospNoProcessor;
import com.sunhealth.ihhis.service.processer.PatCardNoProcessor;
import com.sunhealth.ihhis.service.processer.PatIDProcessor;
import com.sunhealth.ihhis.service.processer.PatMedicareCardNoProcessor;
import com.sunhealth.ihhis.utils.TimeUtils;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Comparator;
import java.util.Date;
import java.util.List;

@Slf4j
@Service
@AllArgsConstructor
public class PatientCardServiceImpl extends ServiceImpl<PatientCardMapper, RTPatientCard>  implements PatientCardService {

    private final PatIDProcessor patIDProcessor;
    private final PatCardNoProcessor patCardNoProcessor;
    private final PatMedicareCardNoProcessor patMedicareCardNoProcessor;
    private final HospNoProcessor hospNoProcessor;
    private final DictionaryService dictionaryService;
    private final PatientListMapper patientListMapper;
    private final PatientDetlMapper patientDetlMapper;
    private final GJYiBaoPatientAccountMapper gjYiBaoPatientAccountMapper;
    private final HisHospitalProperties hisHospitalProperties;

    @Override
    public RTPatientCard getByPatId(String patId) {
        LambdaQueryWrapper<RTPatientCard> wrapper = Wrappers.lambdaQuery();
        wrapper.eq(RTPatientCard::getPatId, patId);
        return baseMapper.selectList(wrapper).stream().findFirst().orElse(null);
    }

    // 创建就诊卡、就诊人信息
    @Override
    @Transactional
    public PatientCardCreateRes createPatientCard(PatientCardCreateReq request) {
        // 查询最新一张自费卡
        RTPatientCard card = baseMapper.getBySfzAndHospCode(request.getCertificate_no(), request.getHospitalCode());
        if (card != null){
            log.info("就诊人:{},身份证号：{} 已有就诊卡（自费）。卡号：{}", request.getPatname(), request.getCertificate_no(), card.getCardNo());
            return buildCreatePatientCardRes(card.getPatId(), request.getPatname(), card.getCardNo(), card.getCardType());
        }
        // 没有就诊卡判断有没有就诊人
        Long patId = null;
        String hospNo = null;
        List<RTPatientList> patientLists = patientListMapper.listPatientByCertificateNo(request.getCertificate_no(), request.getHospitalCode());
        if (CollectionUtils.isEmpty(patientLists)) {
            // 生成就诊人ID
            patId = patIDProcessor.execute();
            // 生成HospNo
            Long hospNoId = hospNoProcessor.execute();
            String hospNoIdStr = String.valueOf(hospNoId);
            hospNo = "MZ" + hospNoIdStr.substring(hospNoIdStr.length() - 8);
            RTPatientList patientList = convertList(request, patId, request.getHospitalCode(), hospNo);
            patientListMapper.savePatientList(patientList);
            RTPatientDetl patientDetl = convertDetl(request, patId, request.getHospitalCode());
            patientDetlMapper.savePatientDetl(patientDetl);
        } else {
            // 有就诊人历史信息，取最后一条
            patId = patientLists.get(0).getPatId();
            hospNo = patientLists.get(0).getHospNo();
        }

        // 生成自费就诊卡号
        String cardNo = patCardNoProcessor.execute();
        RTPatientCard patientCard = convertCard(patId, cardNo, hospNo);
        baseMapper.savePatientCard(patientCard);

        return buildCreatePatientCardRes(patId, request.getPatname(), cardNo);
    }


    /**
     * 根据医保账号信息创建医保卡
     *  查询就诊人信息时，如果发现没有医保卡，则会判断有没有医保信息，
     *  如果有医保信息，根据医保信息生成一张医保卡
     */
    @Override
    public void createMedicareCard(String certificateNo, Integer hospitalCode) {
        LambdaQueryWrapper<GjYiBaoPatientAccount> wrapper = Wrappers.lambdaQuery();
        wrapper.eq(GjYiBaoPatientAccount::getCertno, certificateNo)
                .eq(GjYiBaoPatientAccount::getHospitalCode, hospitalCode);
        List<GjYiBaoPatientAccount> gjYiBaoPatientAccounts = gjYiBaoPatientAccountMapper.selectList(wrapper);
        if (CollectionUtils.isEmpty(gjYiBaoPatientAccounts)) {
            return;
        }
        List<RTPatientList> patientLists = patientListMapper.listPatientByCertificateNo(certificateNo, hospitalCode);
        if (CollectionUtils.isEmpty(patientLists)) {
            // 有医保信息，但是没有患者信息
            return;
        }
        RTPatientList patientList = patientLists.stream().max(Comparator.comparing(RTPatientList::getPatId)).orElse(null);
        RTPatientCard patientCard = new RTPatientCard();
        patientCard.setPatId(patientList.getPatId());
        // 生成医保卡号
        String cardNo = patMedicareCardNoProcessor.execute(certificateNo);
        patientCard.setCardNo(cardNo);
        patientCard.setCardType(PatientCardTypeEnum.MEDICARE.getValue());
        patientCard.setChargeType(ChargeTypeEnum.MEDICARE.getValue());
        patientCard.setHisCardNo(patientList.getHospNo());
        patientCard.setStatus(PatientCardStatusEnum.NORMAL.getValue());
        patientCard.setCreateBy(hisHospitalProperties.getOpCode());
        patientCard.setCreateDate(new Date());
        patientCard.setUpdateBy(hisHospitalProperties.getOpCode());
        patientCard.setUpdateDate(new Date());
        baseMapper.savePatientCard(patientCard);
    }

    private RTPatientCard convertCard(Long patId, String cardNo, String hisCardNo) {
        RTPatientCard patientCard = new RTPatientCard();
        patientCard.setPatId(patId);
        patientCard.setCardNo(cardNo);
        patientCard.setCardType(PatientCardTypeEnum.SELF_PAY.getValue());
        patientCard.setChargeType(ChargeTypeEnum.SELF_PAY.getValue());
        patientCard.setHisCardNo(hisCardNo);
        patientCard.setStatus(PatientCardStatusEnum.NORMAL.getValue());
        patientCard.setCreateBy(hisHospitalProperties.getOpCode());
        patientCard.setCreateDate(new Date());
        patientCard.setUpdateBy(hisHospitalProperties.getOpCode());
        patientCard.setUpdateDate(new Date());
        return patientCard;
    }

    private RTPatientList convertList(PatientCardCreateReq request, Long patId, Integer hospitalCode, String hospNo) {
        RTPatientList patientList = new RTPatientList();
        patientList.setPatId(patId);
        patientList.setNewPatId(0L);
        patientList.setPatName(request.getPatname());
        patientList.setHospNo(hospNo);
        patientList.setCertificateType(certificateTypeMapping(request.getCertificate_type()));
        patientList.setCertificateNo(request.getCertificate_no());
        patientList.setBirthday(TimeUtils.convert(request.getBirth()));
        Gender gender = Gender.lookupByKey(request.getSex());
        patientList.setSex(gender.getValue());
        patientList.setCreateTime(new Date());
        patientList.setIsDelete(false);
        patientList.setIsUse(1);
        patientList.setOpCode(hisHospitalProperties.getOpCode());
        patientList.setCreatedBy(hisHospitalProperties.getOpCode());
        patientList.setCreatedDate(new Date());
        patientList.setUpdateBy(hisHospitalProperties.getOpCode());
        patientList.setUpdateDate(new Date());
        patientList.setHospitalCode(hospitalCode);
        return patientList;
    }

    private RTPatientDetl convertDetl(PatientCardCreateReq request, Long patId, Integer hospitalCode) {
        RTPatientDetl patientDetl = new RTPatientDetl();
        patientDetl.setPatId(patId);
//        patientDetl.setNewPatID(); TODO Mapper.xml 暂时没写
        patientDetl.setMarriage(request.getMarriage());
        // 获取民族字典
        TBDicHisDictionary nation = dictionaryService.getDictByName(Constants.DICT_TYPE_NATION, hospitalCode, request.getNation());
        if (nation != null) {
            patientDetl.setNation(nation.getHisDictionaryCode());
        }
        // 获取职业字典
        TBDicHisDictionary occupation = dictionaryService.getDictByName(Constants.DICT_TYPE_OCCUPATION, hospitalCode, request.getCareer());
        if (occupation != null){
            patientDetl.setOccupation(occupation.getHisDictionaryCode());

        }
        patientDetl.setPatPhone(request.getTelephone());
        patientDetl.setCreateTime(new Date());
        patientDetl.setIsDelete(false);
        patientDetl.setIsUse(1);
        patientDetl.setOpCode(hisHospitalProperties.getOpCode());
        patientDetl.setCreatedBy(hisHospitalProperties.getOpCode());
        patientDetl.setCreatedDate(new Date());
        patientDetl.setUpdateBy(hisHospitalProperties.getOpCode());
        patientDetl.setUpdateDate(new Date());
        patientDetl.setHospitalCode(hospitalCode);
        patientDetl.setAddress(request.getAddress());
        return patientDetl;
    }

    private PatientCardCreateRes buildCreatePatientCardRes(Long patId, String patName, String cardNo){
        PatientCardCreateRes patientCardCreateRes = new PatientCardCreateRes();
        patientCardCreateRes.setPatid(String.valueOf(patId));
        patientCardCreateRes.setPatname(patName);
        patientCardCreateRes.setCardno(cardNo);
        patientCardCreateRes.setCardtype(String.valueOf(PatientCardTypeEnum.SELF_PAY.getValue()));
        return patientCardCreateRes;
    }

    private PatientCardCreateRes buildCreatePatientCardRes(Long patId, String patName, String cardNo, Integer cardType){
        PatientCardCreateRes patientCardCreateRes = new PatientCardCreateRes();
        patientCardCreateRes.setPatid(String.valueOf(patId));
        patientCardCreateRes.setPatname(patName);
        patientCardCreateRes.setCardno(cardNo);
        patientCardCreateRes.setCardtype(String.valueOf(cardType));
        return patientCardCreateRes;
    }

    /**
     * 证件类型ih->his
     * select * from TB_Dic_HisDictionary where DictionaryTypeID=27
     */
    private String certificateTypeMapping(String certificateType) {
        switch (certificateType){
            case "01":
                return "1";
            case "02":
                return "2";
            case "03":
                return "3";
            case "04":
                return "4";
            case "05":
                return "5";
            case "06":
                return "6";
            case "07":
                return "7";
            case "20":
                return "9"; // 外国人永久居留证
            default:
                return "99";
        }
    }
}
