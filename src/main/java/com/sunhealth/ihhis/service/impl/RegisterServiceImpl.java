package com.sunhealth.ihhis.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.sunhealth.ihhis.config.HisHospitalProperties;
import com.sunhealth.ihhis.config.YiBaoProperties;
import com.sunhealth.ihhis.dao.his.*;
import com.sunhealth.ihhis.enums.BusinessType;
import com.sunhealth.ihhis.enums.ItemCategoryEnum;
import com.sunhealth.ihhis.enums.PayChannelType;
import com.sunhealth.ihhis.error.ErrorResponseException;
import com.sunhealth.ihhis.model.dq.sh.insurance.request.SK01Request;
import com.sunhealth.ihhis.model.dq.sh.insurance.response.*;
import com.sunhealth.ihhis.model.dto.patient.SendPatientInfoRes;
import com.sunhealth.ihhis.model.dto.register.*;
import com.sunhealth.ihhis.model.dto.schedule.ScheduleDetail;
import com.sunhealth.ihhis.model.dto.schedule.SourceNumber;
import com.sunhealth.ihhis.model.dto.schedule.SourceNumberReq;
import com.sunhealth.ihhis.model.entity.Dept;
import com.sunhealth.ihhis.model.entity.MedicalWorker;
import com.sunhealth.ihhis.model.entity.dqyibao.RegInsuranceRegister;
import com.sunhealth.ihhis.model.entity.dqyibao.RegInsuranceReturnCharge;
import com.sunhealth.ihhis.model.entity.gjyibao.GJYiBaoRefundRegister;
import com.sunhealth.ihhis.model.entity.gjyibao.GJYiBaoRegister;
import com.sunhealth.ihhis.model.entity.gjyibao.GjYiBaoPatientAccount;
import com.sunhealth.ihhis.model.entity.invoice.OutpatientInvoiceTime;
import com.sunhealth.ihhis.model.entity.invoice.OutpatientInvoiceView;
import com.sunhealth.ihhis.model.entity.patient.*;
import com.sunhealth.ihhis.model.entity.register.*;
import com.sunhealth.ihhis.model.entity.view.ChargeItemView;
import com.sunhealth.ihhis.model.entity.view.RegisterListView;
import com.sunhealth.ihhis.model.entity.yibao.RegOnlineGjYiBaoOrderInfo;
import com.sunhealth.ihhis.model.entity.yibao.RegOnlineGjYiBaoUploadFeeRecord;
import com.sunhealth.ihhis.model.entity.yibao.RegOnlineSHYiBaoUploadFeeRecord;
import com.sunhealth.ihhis.model.insurance.MedicalInsuranceParam;
import com.sunhealth.ihhis.model.insurance.request.CallBack6302;
import com.sunhealth.ihhis.model.insurance.request.Input6203;
import com.sunhealth.ihhis.model.insurance.request.Input6301;
import com.sunhealth.ihhis.model.insurance.response.Output6201;
import com.sunhealth.ihhis.model.insurance.response.Output6202;
import com.sunhealth.ihhis.model.insurance.response.Output6203;
import com.sunhealth.ihhis.model.insurance.response.Output6301;
import com.sunhealth.ihhis.model.insurance.response.Output6301.ExtData;
import com.sunhealth.ihhis.model.insurance.response.Output6301.ExtData.Setlinfo;
import com.sunhealth.ihhis.model.vm.PushInvoiceMessageParam;
import com.sunhealth.ihhis.model.vm.PushSendPatientInfoMessageParam;
import com.sunhealth.ihhis.model.vm.PushUpdateRegisterOrderMessageParam;
import com.sunhealth.ihhis.service.*;
import com.sunhealth.ihhis.service.dqyb.DQYiBaoClient;
import com.sunhealth.ihhis.service.processer.MzNoProcessor;
import com.sunhealth.ihhis.service.processer.PatIDProcessor;
import com.sunhealth.ihhis.service.processer.RegDtlNoProcessor;
import com.sunhealth.ihhis.service.processer.RegSerialNoProcessor;
import com.sunhealth.ihhis.task.scheduler.InvoiceScheduler;
import com.sunhealth.ihhis.task.scheduler.SendPatientInfoScheduler;
import com.sunhealth.ihhis.task.scheduler.UpdateRegisterOrderScheduler;
import com.sunhealth.ihhis.utils.*;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.time.DateUtils;
import org.jetbrains.annotations.NotNull;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.util.*;

@Service
@Slf4j
@AllArgsConstructor
public class RegisterServiceImpl implements RegisterService {
    private final MsgPushService msgPushService;
    private final YiBaoProperties yiBaoProperties;
    private final PreRegisterListMapper preRegisterListMapper;
    private final PreRegisterDetailMapper preRegisterDetailMapper;
    private final PatientListMapper patientListMapper;
    private final SchedulingMapper schedulingMapper;
    private final ChargeItemViewMapper chargeItemViewMapper;
    private final MedicalWorkerService medicalWorkerService;
    private final RegisterListTimeMapper registerListTimeMapper;
    private final RegisterListMapper registerListMapper;
    private final RegisterDetailTimeMapper registerDetailTimeMapper;
    private final RegisterDetailMapper registerDetailMapper;
    private final RegisterListViewMapper registerListViewMapper;
    private final InvoiceService invoiceService;
    private final RecipeEInvoiceNoMapper recipeEInvoiceNoMapper;
    private final PatientDetlMapper patientDetlMapper;
    private final MzNoProcessor mzNoProcessor;
    private final RegDtlNoProcessor regDtlNoProcessor;
    private final PatientCardMapper patientCardMapper;
    private final PatIDProcessor patIDProcessor;
    private final AppointmentMapper appointmentMapper;
    private final AppointmentLockNumMapper appointmentLockNumMapper;
    private final DeptMapper deptMapper;
    private final PatientListService patientListService;
    private final RegOnlineGjYiBaoUploadFeeRecordMapper regOnlineGjYiBaoUploadFeeRecordMapper;
    private final RegOnlineSHYiBaoUploadFeeRecordMapper regOnlineSHYiBaoUploadFeeRecordMapper;
    private final RegSerialNoProcessor regSerialNoProcessor;
    private final RegOnlineGjYiBaoOrderInfoMapper regOnlineGjYiBaoOrderInfoMapper;
    private final HisHospitalProperties hisHospitalProperties;
    private final GJYiBaoRegisterMapper gjYiBaoRegisterMapper;
    private final GJYiBaoRefundRegisterMapper gjYiBaoRefundRegisterMapper;
    private final RegFeeConfigMapper regFeeConfigMapper;
    private final RegWhiteListMapper regWhiteListMapper;
    private final MedicalWorkerMapper medicalWorkerMapper;
    private final PatientInfoMapper patientInfoMapper;
    private final PatientRegisterMapper patientRegisterMapper;
    private final GJYiBaoPatientAccountMapper gjYiBaoPatientAccountMapper;
    private final RegInsuranceRegisterMapper regInsuranceRegisterMapper;
    private final RegInsuranceReturnChargeMapper regInsuranceReturnChargeMapper;

    @Override
    @Transactional
    public PreRegistrationResult preRegister(PreRegisterReq req) {
        //判断当天已经挂号过就不用再挂了 查询挂号明细信息
        RegisterListTime registerListTime = getTodayDetailByPatId(req);
        if (registerListTime != null) {
            log.info("当天已经挂号过，不用再挂号");
            throw new RuntimeException("当天已经挂号过，不用再挂号");
        }

        // 2024年08月21日15:38:28 新需求70岁以上挂号免费，所以只能挂自费号
        PreRegistrationResult result = new PreRegistrationResult();
        // 就诊人信息
        String patid = req.getPatid();
        RTPatientList patient = patientListMapper.selectByPatId(patid);
        if (patient == null) {
            throw new RuntimeException("院内无此患者信息或患者信息已停用");
        }
        if (!req.getPatname().equals(patient.getPatName())) {
            throw new RuntimeException("患者姓名"+req.getPatname()+"与his内建档姓名"+patient.getPatName()+"不同，请前往就诊人管理中修改");
        }
        // 接下来到生成门诊号之前的内容都是仁和专用
        // 根据科目id代表的医生等级和就诊人年龄判断是否可以免费预约挂号
        // 通过scheduling_id查询排班科目信息
        ScheduleDetail scheduleDetail = schedulingMapper.selectScheduleDetail(req.getScheduling_id(),
                                                                              req.getHospitalCode());
        // 2024年09月02日21:35:28  如果卡类型为身份证 根据年龄判断能否挂号
        if("1".equals(patient.getCertificateType())) {
            Map<String, Object> checkAgeResult = registerListTimeMapper.checkAge(patient.getCertificateNo(),
                                                                                 scheduleDetail.getCourseID());
            if ((Integer) checkAgeResult.get("ret") == 0) {
                throw new RuntimeException("" + checkAgeResult.get("msg"));
            }
        }

        // 免费号判断逻辑
        boolean isFreeReg = false;
        String freeGradeStr = registerListMapper.selectFreeGrade(req.getHospitalCode());
        String age = registerListMapper.selectFreeAge(req.getHospitalCode());
        List<String> levelCodes = Arrays.asList(StringUtils.split(freeGradeStr,","));
        if (!CollectionUtils.isEmpty(levelCodes) && levelCodes.contains(scheduleDetail.getDoctorLevel() + "")) {
            log.info("免费挂号：符合免费挂号科目：{} 等级-{}", scheduleDetail.getCourseID(), scheduleDetail.getDoctorLevel());
            if ((patient.getBirthday() != null && TimeUtils.age(patient.getBirthday()) >= Integer.parseInt(age))
            ) {
                log.info("免费挂号：符合免费挂号年龄：{} 要求-{}", TimeUtils.age(patient.getBirthday()), Integer.parseInt(age));
                isFreeReg = true;
                if ("0".equals(req.getSelf_flag())) {
                    throw new RuntimeException(age + "以上老人可以免费挂自费普通号！");
                }
            }
            // 白名单内患者可以挂免费号
            List<RegWhiteList> regWhiteLists = regWhiteListMapper.selectByPatId(patid);
            if (!CollectionUtils.isEmpty(regWhiteLists) && regWhiteLists.stream().anyMatch(u -> u.getExpiringDate() == null || u.getExpiringDate().getTime() > System.currentTimeMillis())) {
                log.info("免费挂号：符合免费挂号白名单：patId-{} ", patid);
                isFreeReg = true;
            }
        }

        String regno = "";
        String settleId = "";

        // 生成门诊号
        Long mzNo = mzNoProcessor.execute();

        // 保存预挂号信息preRegisterList
        PreRegisterList preRegisterList = new PreRegisterList();
        Date date = new Date();
        ThreadLocalUtils.setNow(date);
        managePreRegisterList(preRegisterList, req, date, mzNo);
        updatePreRegisterList(preRegisterList, req);

        preRegisterListMapper.insert(preRegisterList);

        // 根据预挂号信息保存预挂号明细信息
        List<PreRegisterDetail> details = saveByPreRegister(preRegisterList, req, date, mzNo, isFreeReg);

        regno = preRegisterList.getRegNo().toString();
        // 结算单号暂定为挂号序号
        settleId = preRegisterList.getRegNo().toString();

//        int totalAmount =
//            details.stream().mapToInt(u -> u.getTotalAmount().multiply(new BigDecimal(100)).intValue()).sum();
//        int selfPay = details.stream().mapToInt(u -> u.getSelfCost().multiply(new BigDecimal(100)).intValue()).sum();
        // 上面写法不对mapToInt方法会丢失精度，不能直接转int，应该先转成BigDecimal再转int
        BigDecimal totalAmountBigDecimal = new BigDecimal(0);
        BigDecimal selfPayBigDecimal = new BigDecimal(0);
        for (PreRegisterDetail detail : details) {
            totalAmountBigDecimal = totalAmountBigDecimal.add(detail.getTotalAmount());
            selfPayBigDecimal = selfPayBigDecimal.add(detail.getSelfCost());
        }
        int totalAmount = totalAmountBigDecimal.multiply(new BigDecimal(100)).intValue();
        int selfPay = selfPayBigDecimal.multiply(new BigDecimal(100)).intValue();

        result.setRegno(regno);
        result.setSettle_id(settleId);
        result.setTotal_amount(String.valueOf(totalAmount));
        result.setSelf_pay(String.valueOf(selfPay));
        result.setShould_pay_amount(String.valueOf(totalAmount));

        LambdaQueryWrapper<Dept> deptWrapper = Wrappers.lambdaQuery();
        deptWrapper.eq(Dept::getDeptCode, preRegisterList.getDeptID());
        Dept dept = deptMapper.selectOne(deptWrapper);
        ThreadLocalUtils.setDept(dept);
        if ("0".equals(req.getSelf_flag())) {
            MedicalInsuranceParam insuranceParam = MedicalInsuranceParam.fromHisParam(req.getInsurance_param());
            ThreadLocalUtils.setPreRegisterList(preRegisterList);
            ThreadLocalUtils.setPreRegisterDetail(details);
            ThreadLocalUtils.setPatient(patient);
            InsuranceBeanUtils.readAccount(BusinessType.REGISTER, insuranceParam, PayChannelType.getByValue(req.getPay_type()));
            MedicalWorker medicalWorker = medicalWorkerService.getById(preRegisterList.getDoctorID());
            ThreadLocalUtils.setMedicalWorker(medicalWorker);

            if (insuranceParam.isDqYiBao()) {
                LambdaUpdateWrapper<PreRegisterList> uw = new LambdaUpdateWrapper<>();
                uw.eq(PreRegisterList::getRegNo, preRegisterList.getRegNo());
                uw.set(PreRegisterList::getChargeType, 86);
                preRegisterListMapper.update(uw);
                // 这部分代码先放在这里, 如果有必要的话, 可以在DQYiBaoClient中加个方法, 挪过去
                // 5期医保
                DQYiBaoClient dqYiBaoClient = InsuranceBeanUtils.getDQYiBaoClient();
                // sh01
                SH01Response sh01Response = dqYiBaoClient.postSH01(insuranceParam);
                // se02
                // 医院自行生成的唯一ID，生成规则：医疗机构代码（11 位）+yyyyMMddHHmmssSSS+6位随机数字
                String orderNo = yiBaoProperties.getDqOrgCode() + TimeUtils.dateStringFormat(new Date(), "yyyyMMddHHmmssSSS")
                        + StringUtils.right("00000" + SequenceUtils.getSequence(999999), 6);
                dqYiBaoClient.postSE02Registration(orderNo, insuranceParam);


                RegOnlineSHYiBaoUploadFeeRecord record = new RegOnlineSHYiBaoUploadFeeRecord();
                BeanUtils.copyProperties(sh01Response, record);
                record.setPatName(sh01Response.getPersonname());
                record.setRegNo(mzNo);
                record.setFlag(0);
                record.setHospitalCode(req.getHospitalCode());
                record.setOrderNo(orderNo);
                record.setCreatedDate(date);
                record.setUpdatedDate(date);
                record.setCardType(sh01Response.getCardtype());
                record.setCardNo(sh01Response.getCardid());
                record.setPayway(PayChannelType.getByValue(req.getPay_type()).getByHisValue());
                // 保存请求中的医保参数
                record.setAuthCode(insuranceParam.getAuthCode());
                record.setUserName(insuranceParam.getUserName());
                record.setCityId(insuranceParam.getInsuOrg());
                record.setPayAuthNo(insuranceParam.getPayAuthNo());
                record.setLongitude(insuranceParam.getUserLongitudeLatitude().getLongitude());
                record.setLatitude(insuranceParam.getUserLongitudeLatitude().getLatitude());
                record.setEcQrcode(insuranceParam.getEcQrcode());
                record.setUserCardNo(insuranceParam.getUserCardNo());
                record.setEcToken(insuranceParam.getEcToken());
                regOnlineSHYiBaoUploadFeeRecordMapper.insert(record);
                BigDecimal total = sh01Response.getYbjsfwfyze().add(sh01Response.getFybjsfwfyze());
                BigDecimal self = sh01Response.getZfdxjzfs().add(sh01Response.getTcdxjzfs()).add(sh01Response.getFjdxjzfs())
                        .add(sh01Response.getFybjsfwfyze());
                BigDecimal pubAccount = sh01Response.getCuraccountpay().add(sh01Response.getHisaccountpay());
                BigDecimal pub = sh01Response.getTcdzhzfs().add(sh01Response.getTczfs()).add(sh01Response.getFjdzhzfs())
                        .add(sh01Response.getFjzfs());

                result.setShould_pay_amount(self.multiply(new BigDecimal(100)).intValue() + "");
                result.setPub_account_pay(pubAccount.multiply(new BigDecimal(100)).intValue() + "");
                result.setPub_pay(pub.multiply(new BigDecimal(100)).intValue() + "");
                result.setTotal_amount(total.multiply(new BigDecimal(100)).intValue() + "");
                result.setSelf_pay(self.multiply(new BigDecimal(100)).intValue() + "");
                result.setMedical_order_no(mzNo.toString());
                result.setPay_order_id(null);
                result.setBill_no(orderNo);
                result.setGmt_out_create(TimeUtils.getHisDateStr(date));
            } else {
                // 6期医保
                InsuranceBeanUtils.getGJYiBaoClient().post1101(insuranceParam);
                InsuranceBeanUtils.getGJYiBaoClient().post2201A(insuranceParam, regno);
                Output6201 output6201 = InsuranceBeanUtils.getGJYiBaoClient().post6201Register(insuranceParam,
                        PayChannelType.getByValue(req.getPay_type()), req.getHospitalCode());
                Output6202 output6202 = InsuranceBeanUtils.getGJYiBaoClient().post6202Register(insuranceParam,
                        PayChannelType.getByValue(req.getPay_type()), req.getHospitalCode());

                result.setShould_pay_amount(output6202.getOwnPayAmt().multiply(new BigDecimal(100)).intValue() + "");
                result.setPub_account_pay(output6202.getPsnAcctPay().multiply(new BigDecimal(100)).intValue() + "");
                result.setPub_pay(output6202.getFundPay().multiply(new BigDecimal(100)).intValue() + "");
                result.setTotal_amount(output6202.getFeeSumamt().multiply(new BigDecimal(100)).intValue() + "");
                result.setSelf_pay(output6202.getOwnPayAmt().multiply(new BigDecimal(100)).intValue() + "");
                // DONE 6202返回数据入库
                RegOnlineGjYiBaoUploadFeeRecord record = new RegOnlineGjYiBaoUploadFeeRecord();
                BeanUtils.copyProperties(output6201, record);
                BeanUtils.copyProperties(output6202, record);
                record.setPatName(patient.getPatName());
                record.setRegNo(mzNo);
                record.setFlag(0);
                record.setHospitalCode(req.getHospitalCode());
                record.setCreatedDate(date);
                record.setUpdatedDate(date);
                record.setCardType(InsuranceBeanUtils.certificateTypeMapping(patient.getCertificateType()));
                record.setCardNo(patient.getCertificateNo());
                record.setPayway(PayChannelType.getByValue(req.getPay_type()).getByHisValue());
                // 保存请求中的医保参数
                record.setAuthCode(insuranceParam.getAuthCode());
                record.setUserName(insuranceParam.getUserName());
                record.setCityId(insuranceParam.getCityId());
                record.setPayAuthNo(insuranceParam.getPayAuthNo());
                record.setLongitude(insuranceParam.getUserLongitudeLatitude().getLongitude());
                record.setLatitude(insuranceParam.getUserLongitudeLatitude().getLatitude());
                record.setEcQrcode(insuranceParam.getEcQrcode());
                record.setUserCardNo(insuranceParam.getUserCardNo());
                regOnlineGjYiBaoUploadFeeRecordMapper.insert(record);
                // 医保特有字段返回
                result.setMedical_order_no(preRegisterList.getRegNo() + "");
                result.setPay_order_id(output6201.getPayOrdId());
                result.setGmt_out_create(TimeUtils.getHisDateStr(date));
            }
        }

        return result;

    }

    /**
     * 2024年09月23日10:43:26 因护理到家需求 实现一个自动挂免费号的功能
     */
    @Override
    public FreeRegisterResult freeRegister(FreeRegisterReq req) {
        FreeRegisterResult result = new FreeRegisterResult();
        // 就诊人信息
        String patid = req.getPatid();
        RTPatientList patient = patientListMapper.selectByPatId(patid);
        if (patient == null) {
            throw new RuntimeException("院内无此患者信息或患者信息已停用");
        }
        if (!req.getPatname().equals(patient.getPatName())) {
            throw new RuntimeException("患者姓名"+req.getPatname()+"与his内建档姓名"+patient.getPatName()+"不同，请前往就诊人管理中修改");
        }
        String regno;
        // 生成门诊号
        Long mzNo = mzNoProcessor.execute();

        // 保存预挂号信息preRegisterList
        RegisterListTime list = new RegisterListTime();
        Date date = new Date();
        list.setRegNo(mzNo);
        list.setPatID(patient.getPatId());
        list.setNewPatID(patient.getNewPatId());

        // 查询卡信息
        // 卡表里：0 自费，1 医保
        Integer cardType = 0;
        // 医保后续处理医保相关字段值
        if ("1".equals(req.getCardtype())) { // 医保，查医保卡
            cardType = 1;
        } else { // 自费，查自费卡
            cardType = 0;
        }
        RTPatientCard card;
        card = patientCardMapper.getByCardNoAndPatIdAndCardType(
            req.getCardno(), patient.getPatId(), cardType);
        if (card == null) {
            card = patientCardMapper.getByCardNoAndPatId(req.getCardno(),
                                                         patient.getPatId());
        }
        if (card == null) {
            log.error("未查询到卡信息，patId:{} cardNo:{} cardType:{}", patid, req.getCardno(),req.getCardtype());
            throw new RuntimeException("未查询到卡信息，patId: "+ patid +" cardNo: "+req.getCardno()+" cardType:" + req.getCardtype());
        }
        list.setCardNo(card.getCardNo());
        list.setHospNo(patient.getHospNo());
        list.setPatName(req.getPatname());
        list.setOutPatientNo(patient.getHospNo());
        list.setBlanceWay(1);
        list.setRegistType(0);
        // 号序信息
        list.setRegistOrder(0);
        list.setRegistMode(0);
        // 预约信息
        list.setAppointmentNo("0");
        list.setStatus(0);
        list.setRegistTime(date);
        if (null != req.getBegin_time()) {
            list.setRegistTime(TimeUtils.convert(req.getBegin_time()));
        }
        list.setComputerNo("999999");
        list.setOpCode(hisHospitalProperties.getOpCode());
        list.setCreateTime(date);
//        preRegisterList.setCureCode("");
        list.setVisitFlag(2);
        list.setIsDelete(false);
        list.setCreatedBy(hisHospitalProperties.getOpCode());
        list.setCreatedDate(date);
        list.setUpdateDate(date);
        list.setUpdateby(hisHospitalProperties.getOpCode());
        list.setReferralFlag(0);
        list.setDeptKind((short) 2);
        list.setFzFlag(0);
        list.setHospitalCode(Integer.valueOf(hisHospitalProperties.getCode()));
        // 医保结算类型，0，1自费或者五期医保，2国家医保
        list.setJsFlag(0);
        // 收费类型，10自费，30医保，45大病
        list.setChargeType(10);
        // 医保后续处理医保相关字段值
//        preRegisterList.setReservationType("");
        // 预约标识，预约记录表TB_Appointment字段RemarkA1
//        preRegisterList.setAppointmentFlag("DTZH");
        list.setIsGreenChannel(false);
        list.setIsDebitPay(false);

        // 就诊信息
        list.setDeptID(Integer.valueOf(req.getDeptid()));
        // 根据工号和医院编码查出医生id
        MedicalWorker medicalWorker = medicalWorkerMapper.selectByWorkNoAndHospitalCode(req.getWorkid(), req.getHospitalCode());
        list.setDoctorID(medicalWorker.getWorkerId());
        registerListTimeMapper.insert(list);


        result.setRegister(list);
        return result;
    }

    @Override
    @Transactional
    public boolean cancelPreRegister(CancelPreRegistReq req) {
        // settleId是必给的通过这个查数据，挂号序号
        Long settleId = Long.valueOf(req.getSettle_id());
        // 查询预挂号明细信息
        LambdaQueryWrapper<PreRegisterDetail> wrapper = Wrappers.lambdaQuery();
        wrapper.eq(PreRegisterDetail::getRegNo, settleId);
        wrapper.eq(PreRegisterDetail::getIsDelete, false);
        List<PreRegisterDetail> preRegisterDetail = preRegisterDetailMapper.selectList(wrapper);
        if (CollectionUtils.isEmpty(preRegisterDetail)) {
            log.error("未查询到挂号明细信息，settleId:{}", settleId);
            throw new ErrorResponseException("未找到预挂号信息或已挂号，撤销失败");
        }
        //查询未删除的挂号信息
        RegisterListView registerListView = registerListViewMapper.selectByRegNo(settleId);
        if (registerListView != null) {
            log.error("查询到已挂号信息，regNo:{}", settleId);
            throw new ErrorResponseException("未找到预挂号信息或已挂号，撤销失败");
        }

        // 删除预挂号明细信息
        preRegisterDetail.forEach(detail -> {
            detail.setIsDelete(true);
            preRegisterDetailMapper.updateById(detail);
        });
        // 删除预挂号信息
        PreRegisterList preRegisterList = preRegisterListMapper.selectById(settleId);
        preRegisterList.setIsDelete(true);
        preRegisterListMapper.updateById(preRegisterList);
        return true;
    }

    @Override
    @Transactional
    public ConfirmRegistResult confirmRegister(ConfirmRegistReq req, RegOnlineSHYiBaoUploadFeeRecord feeRecord) {
        // settleId是必给的通过这个查数据，目前认为是明细序号
        // 结算时需要将传入数据与预算时保存的数据进行比对，如果不一致则返回错误信息
        Long settleId = Long.valueOf(req.getSettle_id());
        // 查询挂号预算明细信息
        LambdaQueryWrapper<PreRegisterDetail> wrapper = Wrappers.lambdaQuery();
        wrapper.eq(PreRegisterDetail::getRegNo, settleId);
        wrapper.eq(PreRegisterDetail::getIsDelete, false);
        List<PreRegisterDetail> detailList = preRegisterDetailMapper.selectList(wrapper);
        if (CollectionUtils.isEmpty(detailList)) {
            log.error("未查询到预挂号明细信息或预算已作废，regNo:{} settleId:{}", req.getRegno(), settleId);
            throw new ErrorResponseException("未查询到预挂号明细信息或预算已作废");
        }
        // 查询挂号预算信息
        LambdaQueryWrapper<PreRegisterList> preRegisterListWrapper = Wrappers.lambdaQuery();
        preRegisterListWrapper.eq(PreRegisterList::getRegNo, req.getRegno());
        preRegisterListWrapper.eq(PreRegisterList::getPatID, req.getPatid());
        preRegisterListWrapper.eq(PreRegisterList::getIsDelete, false);
        PreRegisterList preRegister = preRegisterListMapper.selectOne(preRegisterListWrapper);
        if (preRegister == null) {
            log.error("未查询到预挂号信息或预算已作废，regNo:{} settleId:{}", req.getRegno(), settleId);
            throw new ErrorResponseException("未查询到预挂号信息或预算已作废");
        }
        // 校验数据
        ConfirmRegistResult result = checkRegisterReqData(req, preRegister, detailList);
        if (!result.isSuccess()) {
            return result;
        }

        //逻辑顺序：
        //ih 		------------> 		his
        //支付成功					1.保存挂号信息（list+detail）
        //						            2.保存发票信息（OutpatientInvoice + OpInvoiceSupply + OpInvoicePayway）
        //						            3.保存医生出诊信息 （Tbt_Recipe_eInvoiceNo）
        //						            4.更新患者初复诊信息 （Reg_Tb_InvoiceInfo）
        //						            5.发票号跳号 （Reg_Tb_InvoiceInfo）
        Date date = new Date();
        result.setCharge_time(TimeUtils.getHisDateStr(date));
        // 1.保存挂号信息（list+detail）
        // 挂号信息
        RegisterListTime registerListTime = new RegisterListTime(preRegister);
        registerListTime.setCreateTime(date);
        registerListTime.setUpdateDate(date);
        registerListTime.setUpdateby(hisHospitalProperties.getOpCode());
        registerListTime.setCreatedBy(hisHospitalProperties.getOpCode());
        registerListTime.setOpCode(hisHospitalProperties.getOpCode());
        registerListTime.setAppointmentNo(req.getLock_number_id());
        registerListTime.setHospNo(preRegister.getHospNo());
        registerListTime.setRegistTime(preRegister.getRegistTime());
        registerListTime.setVisitTime(TimeUtils.dateStringFormat(registerListTime.getRegistTime(), "yyyy-MM-dd "
            + "HH:mm:ss"));
        registerListTime.setOutPatientNo(preRegister.getOutPatientNo());
        // 2024年07月29日14:13:04 根据norris提供群聊记录，需要赋值GHDOCTOR字段
        registerListTime.setGhDoctor(registerListTime.getDoctorID());
        registerListTime.setJsFlag(req.getJsFlag());
        registerListTimeMapper.insert(registerListTime);

        List<RegisterDetailTime> registerDetails = Lists.newArrayList();
        // 判断挂号费是否为0  免费挂号
        BigDecimal totalAmount = BigDecimal.ZERO;
        int totalAmountInt =
            detailList.stream().mapToInt(u -> u.getTotalAmount().multiply(new BigDecimal(100)).intValue()).sum();
        // 金额部分
        BigDecimal totalAmountBigDecimal = new BigDecimal(totalAmountInt);
        boolean isFreeReg = totalAmount.compareTo(totalAmountBigDecimal) == 0;
        // 挂号明细
        detailList.forEach(detail -> {
            RegisterDetailTime registerDetailTime = new RegisterDetailTime(detail);
            registerDetailTime.setUpdateDate(date);
            registerDetailTime.setCreatedBy(hisHospitalProperties.getOpCode());
            registerDetailTime.setOpCode(hisHospitalProperties.getOpCode());
            registerDetailTimeMapper.insert(registerDetailTime);
            registerDetails.add(registerDetailTime);
        });

        // 2.发票信息
        OutpatientInvoiceTime invoiceTime = invoiceService.saveRegisterInvoiceInfo(req, registerListTime,
                                                                                 registerDetails);

        // 医保挂号保存国家医保数据
        if ("0".equals(req.getSelf_flag())) {
            if (req.getJsFlag() == 2) {
                // 国家医保
                GJYiBaoRegister gjYiBaoRegister = new GJYiBaoRegister();
                Output6301 output6301 = req.getOutput6301();
                ExtData extData = output6301.getExtData();
                Setlinfo setlinfo = extData.getSetlinfo();
                BeanUtils.copyProperties(setlinfo, gjYiBaoRegister);
                gjYiBaoRegister.setRegNo(registerListTime.getRegNo());
                gjYiBaoRegister.setFlag(4);
                gjYiBaoRegister.setCreateTime(date);
                gjYiBaoRegister.setUpdateTime(date);
                gjYiBaoRegister.setAge(BigDecimal.valueOf(setlinfo.getAge()));
                gjYiBaoRegister.setInsuplcAdmdvs(extData.getInsuplcAdmdvs());
                gjYiBaoRegister.setMsgID(setlinfo.getMedinsSetlId());
                gjYiBaoRegister.setOperationCode(hisHospitalProperties.getOpCode() + "");
                gjYiBaoRegister.setSetlTime(TimeUtils.convert(setlinfo.getSetlTime()));
                gjYiBaoRegister.setHospitalCode(Integer.parseInt(req.getHospitalCode()));
                gjYiBaoRegister.setIptOtpNo(registerListTime.getOutPatientNo());
                gjYiBaoRegisterMapper.insert(gjYiBaoRegister);

                // 添加或更新医保账户信息
                GjYiBaoPatientAccount ac = gjYiBaoPatientAccountMapper.selectOneByPsnNoAndPatIdAndCardNo(
                        setlinfo.getPsnNo(),
                        registerListTime.getPatID().toString(),
                        registerListTime.getCardNo());
                boolean acExit = true;
                if (ac == null) {
                    ac = new GjYiBaoPatientAccount();
                    acExit = false;
                }
                ac.setPsnNo(setlinfo.getPsnNo());
                ac.setPsnName(setlinfo.getPsnName());
                ac.setPatId(registerListTime.getPatID().intValue());
                ac.setCardNo(registerListTime.getCardNo());
                ac.setPsnCertType(setlinfo.getPsnCertType());
                ac.setCertno(setlinfo.getCertno());
                ac.setPsnName(setlinfo.getPsnName());
                ac.setGend(setlinfo.getGend());
//            ac.setNaty();
                ac.setBrdy(setlinfo.getBrdy());
                ac.setAge(setlinfo.getAge() + "");
                ac.setBalc(setlinfo.getBalc().toString());
                ac.setInsutype(setlinfo.getInsutype());
                ac.setPsnType(setlinfo.getPsnType());
//            ac.setPsnInsuStas();
                ac.setCvlservFlag(setlinfo.getCvlservFlag());
                ac.setInsuplcAdmdvs(extData.getInsuplcAdmdvs());
                ac.setHospitalCode(Integer.parseInt(req.getHospitalCode()));
                if (acExit) {
                    LambdaQueryWrapper<GjYiBaoPatientAccount> acWrapper = Wrappers.lambdaQuery();
                    acWrapper.eq(GjYiBaoPatientAccount::getPatId, ac.getPatId());
                    acWrapper.eq(GjYiBaoPatientAccount::getCardNo, ac.getCardNo());
                    acWrapper.eq(GjYiBaoPatientAccount::getPsnNo, ac.getPsnNo());
                    gjYiBaoPatientAccountMapper.update(ac, acWrapper);
                } else {
                    gjYiBaoPatientAccountMapper.insert(ac);
                }
            } else {
                // 上海医保
                SH02Response sh02Response = req.getSe04Response().getSh02Response();
                RegInsuranceRegister regInsuranceRegister = new RegInsuranceRegister();
                BeanUtils.copyProperties(registerListTime, regInsuranceRegister);
                regInsuranceRegister.setCardNo(sh02Response.getCardid());
                regInsuranceRegister.setCardType(sh02Response.getCardtype());
                regInsuranceRegister.setSerialNo(regSerialNoProcessor.executeInsurance());
//                regInsuranceRegister.setChargeType = param.ChargeType.ToString();
                regInsuranceRegister.setTradeTotal(sh02Response.getTotalexpense());
                regInsuranceRegister.setInsuranceTotal(sh02Response.getYbjsfwfyze());
                regInsuranceRegister.setNonInsuranceTotal(sh02Response.getFybjsfwfyze());
                regInsuranceRegister.setCalculationSerial(sh02Response.getJssqxh());
                regInsuranceRegister.setRecordNo(sh02Response.getJlc());
                regInsuranceRegister.setJyFlag(2);
                regInsuranceRegister.setTradeSerialNo(sh02Response.getLsh());
                regInsuranceRegister.setCurrentAccountPay(sh02Response.getCuraccountpay());
                regInsuranceRegister.setLastAccountPay(sh02Response.getHisaccountpay());
                regInsuranceRegister.setCurrentAccountMoney(sh02Response.getCuraccountamt());
                regInsuranceRegister.setLastAccountMoney(sh02Response.getHisaccountamt());
                regInsuranceRegister.setPubPay(sh02Response.getTczfs());
                regInsuranceRegister.setPubCashPay(sh02Response.getTcdxjzfs());
                regInsuranceRegister.setPubAccountPay(sh02Response.getTcdzhzfs());
                regInsuranceRegister.setAppendCashPay(sh02Response.getFjdxjzfs());
                regInsuranceRegister.setAppendPay(sh02Response.getFjzfs());
                regInsuranceRegister.setUploadTime(new Date());
                regInsuranceRegister.setReturnTime(new Date());
                regInsuranceRegister.setCashPayTotal(sh02Response.getZfdxjzfs());
                regInsuranceRegister.setCashLastAccountPay(sh02Response.getZfdlnzhzfs());
                regInsuranceRegister.setAppendAccountPay(sh02Response.getFjdzhzfs());
                regInsuranceRegister.setUnitNo(sh02Response.getJzdyh()); // 就诊单元号
                regInsuranceRegister.setIsDeleted(false);
                regInsuranceRegister.setCreateTime(new Date());
                regInsuranceRegister.setWflag(2);
                regInsuranceRegister.setOpTime(new Date());
                regInsuranceRegister.setOpCode(hisHospitalProperties.getOpCode());
                regInsuranceRegister.setAccountFlag(feeRecord.getAccountattr());

                RTPatientCard card = patientCardMapper.getByCardNoAndPatId(registerListTime.getCardNo(), registerListTime.getPatID());
                regInsuranceRegister.setCardData(card.getCardData());
                regInsuranceRegister.setChargeType(registerListTime.getChargeType() + "");

                LambdaQueryWrapper<Dept> deptWrapper = Wrappers.lambdaQuery();
                deptWrapper.eq(Dept::getDeptCode, registerListTime.getDeptID());
                Dept dept = deptMapper.selectOne(deptWrapper);
                regInsuranceRegister.setDeptId(dept.getInsureDeptCode() + "");
                regInsuranceRegister.setDeptNum(1);
                regInsuranceRegister.setNewPatId(registerListTime.getNewPatID());
                regInsuranceRegister.setPatType(1);
                if (StringUtils.isNotBlank(feeRecord.getPersonspectag())) {
                    regInsuranceRegister.setSpecialFlag(Integer.parseInt(feeRecord.getPersonspectag()));
                }
                BigDecimal consultationFee = registerDetails.stream()
                        .filter(p -> ItemCategoryEnum.DisFee.getCode().equals(p.getItemCatetory()))
                        .map(p -> DecimalUtil.isNullAsZero(p.getTotalAmount()))
                        .reduce(new BigDecimal(0), BigDecimal::add).setScale(2, RoundingMode.HALF_UP);
                regInsuranceRegister.setDiagAmount(consultationFee);

                BigDecimal total = registerDetails.stream()
                        .map(p -> DecimalUtil.isNullAsZero(p.getTotalAmount()))
                        .reduce(new BigDecimal(0), BigDecimal::add).setScale(2, RoundingMode.HALF_UP);
                regInsuranceRegister.setTotalAmount(total);

                regInsuranceRegister.setIsRemission("0");
                regInsuranceRegister.setOnLineType("1");
                regInsuranceRegister.setJyFlag(20);
                if (sh02Response.getGjzhzfs() != null) {
                    regInsuranceRegister.setAcctMulaidPay(sh02Response.getGjzhzfs());
                    regInsuranceRegister.setSelfMulaidPay(sh02Response.getZfdgjzhzfs());
                    regInsuranceRegister.setPubMulaidPay(sh02Response.getTcdgjzhzfs());
                    regInsuranceRegister.setAppendMulaidPay(sh02Response.getFjdgjzhzfs());
                    regInsuranceRegister.setMulaidPayTotal(regInsuranceRegister.getAcctMulaidPay().add(regInsuranceRegister.getSelfMulaidPay())
                            .add(regInsuranceRegister.getPubMulaidPay()).add(regInsuranceRegister.getAppendMulaidPay()));
                    regInsuranceRegister.setQfdMulaidPay(BigDecimal.ZERO);
                    regInsuranceRegister.setGyMulaidPay(BigDecimal.ZERO);
                    regInsuranceRegister.setInsuranceType(1);
                }
                regInsuranceRegisterMapper.insert(regInsuranceRegister);
            }
        }


        // 3.保存医生出诊信息
        RecipeEInvoiceNo recipeEInvoiceNo = new RecipeEInvoiceNo();
        recipeEInvoiceNo.setWorkerId(preRegister.getDoctorID());
        recipeEInvoiceNo.setHospitalCode(preRegister.getHospitalCode());
        recipeEInvoiceNo.setInvoId(Math.toIntExact(invoiceTime.getInvoiceID()));
        recipeEInvoiceNo.setCurrentKey(0);
        recipeEInvoiceNo.setFpNum(1);
        recipeEInvoiceNo.setOpTime(date);

        recipeEInvoiceNoMapper.insert(recipeEInvoiceNo);
        // 4.更新患者初复诊信息
        patientDetlMapper.updatePatientVisitInfo(preRegister.getPatID());

        PreRegisterReq preRegisterReq = new PreRegisterReq();
        preRegisterReq.setHospitalCode(req.getHospitalCode());
        preRegisterReq.setScheduling_id(String.valueOf(req.getScheduling_id()));
        preRegisterReq.setSource_number(String.valueOf(preRegister.getRegistOrder()));
        preRegisterReq.setPatid(req.getPatid());
        preRegisterReq.setLock_number_id(req.getLock_number_id());
        confirmLockNumber(preRegisterReq, registerListTime, req);
        // 异步调接口生成电子发票
        // 发票类型 1 收费 3 挂号
        // Flag=1  门诊挂号 ； Flag=2 红冲； Flag=3 门诊收费
        // 调用接口生成电子发票
        if (!isFreeReg) {
            InvoiceScheduler.delayTask(new PushInvoiceMessageParam(invoiceTime.getInvoiceID(), 3, 1,
                                                                   registerListTime.getHospitalCode() + "",
                                                                   registerListTime.getOpCode() + "", invoiceTime.getInvoiceInfo()));
        }
        SendPatientInfoScheduler.delayTask(new PushSendPatientInfoMessageParam(registerListTime.getRegNo(), req.getSelf_flag()));
        // 更新排队序号
        UpdateRegisterOrderScheduler.delayTask(new PushUpdateRegisterOrderMessageParam(registerListTime.getRegNo(),
                                                                                       registerListTime.getDeptID(),
                                                                                       registerListTime.getDoctorID()));

        return result;
    }
    @Override
    public void sendPatientInfo(Long regNo, String selfFlag) {
        RegisterListTime registerListTime = registerListTimeMapper.selectById(regNo);
        PatientInfo patientInfo = new PatientInfo();
        PatientRegister patientRegister = new PatientRegister();

        // 调用wjw接口传输信息
        HisAppApiClient instance = AppContext.getInstance(HisAppApiClient.class);
        Map<String, String> fieldMap = new HashMap<>();

        RTPatientList patient = patientListMapper.selectByPatId(registerListTime.getPatID().toString());
        RTPatientDetl rtPatientDetl = patientDetlMapper.selectByPatId(registerListTime.getPatID().toString());

        // 设置字段映射
        fieldMap.put("orgCode", "4205011");
        patientRegister.setOrgCode("4205011");

        fieldMap.put("busType", "8");
        patientRegister.setBusType("8");

        fieldMap.put("clinicNo", registerListTime.getRegNo().toString());
        patientRegister.setClinicNo(registerListTime.getRegNo().toString());

        String patfeeType = "0".equals(selfFlag) ? "2" : "1";
        fieldMap.put("patfeeType", patfeeType);
        patientRegister.setPatfeeType(patfeeType);

        fieldMap.put("name", registerListTime.getPatName());
        patientRegister.setName(registerListTime.getPatName());

        fieldMap.put("sex", patient.getSex() + "");
        patientRegister.setSex(patient.getSex() + "");

        String birthday = TimeUtils.dateStringFormat(patient.getBirthday(), "yyyy-MM-dd");
        fieldMap.put("birthday", birthday);
        patientRegister.setBirthday(birthday);

        String age = String.valueOf(TimeUtils.age(patient.getBirthday()));
        fieldMap.put("age", age);
        patientRegister.setAge(age);

        fieldMap.put("ageUnit", "岁");
        patientRegister.setAgeUnit("岁");

        String idType = InsuranceBeanUtils.certificateTypeMapping(patient.getCertificateType());
        fieldMap.put("idType", idType);
        patientRegister.setIdType(idType); // 补全设置

        fieldMap.put("idCard", patient.getCertificateNo());
        patientRegister.setIdCard(patient.getCertificateNo()); // 补全设置

        fieldMap.put("address", "");
        patientRegister.setAddress(""); // 补全设置
        String nation;
        if (rtPatientDetl.getNation() == null) {
            nation = "01";
        } else if (99 == rtPatientDetl.getNation()) {
            nation = "99";
        } else {
            nation = "0" + rtPatientDetl.getNation();
        }
        fieldMap.put("nation", nation);
        patientRegister.setNation(nation); // 补全设置

        fieldMap.put("phone", rtPatientDetl.getPatPhone() != null ? rtPatientDetl.getPatPhone() : "");
        patientRegister.setPhone(rtPatientDetl.getPatPhone()); // 补全设置

        if (registerListTime.getRegistTime() != null) {
            String clinicDate = TimeUtils.dateStringFormat(registerListTime.getRegistTime(), "yyyy-MM-dd HH:mm:ss");
            fieldMap.put("clinicDate", clinicDate);
            patientRegister.setClinicDate(clinicDate); // 补全设置
        } else {
            fieldMap.put("clinicDate", null);
            patientRegister.setClinicDate(null); // 补全设置
        }

        Dept dept = deptMapper.selectById(registerListTime.getDeptID().toString());
        MedicalWorker doctor = medicalWorkerService.getById(registerListTime.getDoctorID());

        fieldMap.put("deptCode", dept.getDeptCode());
        patientRegister.setDeptCode(dept.getDeptCode()); // 补全设置

        fieldMap.put("deptName", dept.getDeptName());
        patientRegister.setDeptName(dept.getDeptName()); // 补全设置

        fieldMap.put("wardCode", "");
        patientRegister.setWardCode(""); // 补全设置

        fieldMap.put("wardName", "");
        patientRegister.setWardName(""); // 补全设置

        fieldMap.put("doctorCode", registerListTime.getDoctorID().toString());
        patientRegister.setDoctorCode(registerListTime.getDoctorID().toString()); // 补全设置

        fieldMap.put("doctorName", doctor.getName());
        patientRegister.setDoctorName(doctor.getName()); // 补全设置

        String schedulingName = schedulingMapper.selectSchedulingTypeByCourceId(registerListTime.getCourseID()).get(0);
        fieldMap.put("category", matchType(schedulingName));
        patientRegister.setCategory(matchType(schedulingName)); // 补全设置
        patientRegister.setUploadTime(new Date());

        SendPatientInfoRes sendPatientInfoRes = instance.sendPatientInfo(fieldMap);
        if (sendPatientInfoRes != null) {
            patientRegister.setState(sendPatientInfoRes.getState());
            patientRegister.setMessage(sendPatientInfoRes.getMessage());
        }
        patientRegister.setReturnTime(new Date());

        Date date = new Date();
        patientInfo.setRegno(regNo);
        patientInfo.setFlag(1);
        patientInfo.setCreateTime(date);
        patientInfoMapper.insert(patientInfo);

        patientRegister.setFlag(1);
        patientRegister.setRegno(regNo);
        patientRegister.setPatinfoId(patientInfo.getId());

        patientRegisterMapper.insert(patientRegister);
    }

    @Override
    @Transactional
    public ReturnRegistResult cancelRegister(CancelRegisterReq req) {
        ReturnRegistResult returnRegistResult = new ReturnRegistResult();
        returnRegistResult.setSuccess(true);
        // 通过 pid regno 查到发票信息
        OutpatientInvoiceView invoice = invoiceService.getNormalOutpatientInvoice(req.getPatid(),
                                                                                  req.getRegno(),
                                                                                  req.getHospitalCode(), 1);
        // 通过发票信息判断是否能退号
        if (invoice == null) {
            log.error("挂号信息无效或已退号，patId:{}，regNo:{}", req.getPatid(), req.getRegno());
            if (tryReturnYbRegister(req)) {
                returnRegistResult.setMessage("退款成功");
                returnRegistResult.setSuccess(false);
                return returnRegistResult;
            } else {
                returnRegistResult.setMessage("挂号信息无效或已退号");
                returnRegistResult.setSuccess(false);
                return returnRegistResult;
            }
        }

        Map<String, String> canReturnRegisterResult = canReturnRegister(Long.valueOf(req.getRegno()));
        log.info("退号：regno-{}，检测能否退号结果-{}", req.getRegno(), canReturnRegisterResult);

        // 执行退费逻辑1 - 挂号部分
        // 1.插入一条退费挂号信息
        Long mzNo = mzNoProcessor.execute();
        Date date = TimeUtils.convert(req.getRefund_time());
        RegisterListView registerView = registerListViewMapper.selectByRegNo(invoice.getRegNo());
        RegisterListTime returnRegister = new RegisterListTime();
        BeanUtils.copyProperties(registerView, returnRegister);
        returnRegister.setRegNo(mzNo);
        returnRegister.setReturnRegNo(invoice.getRegNo());
        returnRegister.setCreatedDate(date);
        returnRegister.setUpdateDate(date);
        returnRegister.setCreateTime(date);
        returnRegister.setUpdateby(hisHospitalProperties.getOpCode());
        returnRegister.setCreatedBy(hisHospitalProperties.getOpCode());
        returnRegister.setOpCode(hisHospitalProperties.getOpCode());
        returnRegister.setComputerNo("999999");
        returnRegister.setStatus(120);

        registerListTimeMapper.insert(returnRegister);
        // 2.拿到并更新原挂号信息状态
        RegisterListTime registerTime = registerListTimeMapper.selectById(invoice.getRegNo());
        RegisterList register = registerListMapper.selectById(invoice.getRegNo());
        if (registerTime != null) {
            registerTime.setReturnRegNo(mzNo);
            registerTime.setUpdateDate(date);
            registerTime.setUpdateby(hisHospitalProperties.getOpCode());
            registerTime.setStatus(80);
            registerListTimeMapper.updateById(registerTime);
            List<RegisterDetailTime> detailTimes = registerDetailTimeMapper.selectListByRegNo(
                invoice.getRegNo());
            // 2.1.插入退费挂号明细
            detailTimes.forEach(detail -> {
                RegisterDetailTime returnDetail = new RegisterDetailTime();
                BeanUtils.copyProperties(detail, returnDetail);
                Long detailRegNo = regDtlNoProcessor.execute();
                returnDetail.setRegNo(mzNo);
                returnDetail.setRegDtlNo(detailRegNo);
                returnDetail.setCreatedDate(date);
                returnDetail.setUpdateDate(date);
                returnDetail.setCreatedBy(hisHospitalProperties.getOpCode());
                returnDetail.setOpCode(hisHospitalProperties.getOpCode());
                returnDetail.setTotalAmount(detail.getTotalAmount().negate());
                returnDetail.setExpenseAmout(detail.getExpenseAmout().negate());
                returnDetail.setNonExpenseAmount(detail.getNonExpenseAmount().negate());
                registerDetailTimeMapper.insert(returnDetail);

                detail.setReturnRegNo(mzNo);
                detail.setReturnRegDtlNo(detailRegNo);
                returnDetail.setUpdateDate(date);
                returnDetail.setOpCode(hisHospitalProperties.getOpCode());
                registerDetailTimeMapper.updateById(detail);
            });

        }
        if (register != null) {
            register.setReturnRegNo(mzNo);
            register.setUpdateDate(date);
            register.setUpdateby(hisHospitalProperties.getOpCode());
            register.setStatus(80);
            registerListMapper.updateById(register);
            List<RegisterDetail> detailTimes = registerDetailMapper.selectListByRegNo(
                invoice.getRegNo());
            // 2.1.插入退费挂号明细
            detailTimes.forEach(detail -> {
                RegisterDetailTime returnDetail = new RegisterDetailTime();
                BeanUtils.copyProperties(detail, returnDetail);
                Long detailRegNo = regDtlNoProcessor.execute();
                returnDetail.setRegNo(mzNo);
                returnDetail.setRegDtlNo(detailRegNo);
                returnDetail.setCreatedDate(date);
                returnDetail.setUpdateDate(date);
                returnDetail.setCreatedBy(hisHospitalProperties.getOpCode());
                returnDetail.setOpCode(hisHospitalProperties.getOpCode());
                returnDetail.setTotalAmount(detail.getTotalAmount().negate());
                returnDetail.setExpenseAmout(detail.getExpenseAmout().negate());
                returnDetail.setNonExpenseAmount(detail.getNonExpenseAmount().negate());
                registerDetailTimeMapper.insert(returnDetail);

                detail.setReturnRegNo(mzNo);
                detail.setReturnRegDtlNo(detailRegNo);
                returnDetail.setUpdateDate(date);
                returnDetail.setOpCode(hisHospitalProperties.getOpCode());
                registerDetailMapper.updateById(detail);
            });
        }

        // 2.门诊状态信息
        registerListMapper.updateSpecialSourceRelation(invoice.getRegNo(), 80);
        // 3.就诊人就诊信息
        Long newPatId = patIDProcessor.execute();
        patientDetlMapper.updatePatientVisitInfoAndNewPatId(invoice.getPatID(), newPatId);
        // 4.解锁预约号源
        Integer deptId = null;
        PreRegisterReq returnAppointReq = new PreRegisterReq();
        if (registerTime != null) {
            returnAppointReq.setLock_number_id(registerTime.getAppointmentNo());
            deptId = registerTime.getDeptID();
        } else if (register != null) {
            returnAppointReq.setLock_number_id(register.getAppointmentNo());
            deptId = register.getDeptID();
        }
        returnAppointment(returnAppointReq);

        // 如果是医保挂号，需要先调用医保退费接口退医保部分
        if (invoice.getChargeType() != null && (invoice.getChargeType() == 30 || invoice.getChargeType() == 86)) {
            MedicalInsuranceParam insuranceParam = MedicalInsuranceParam.fromHisParam(req.getInsurance_param());
            if (null == insuranceParam) {
                throw new ErrorResponseException("医保退费失败 退款需要重新授权insuranceParam中传入PayAuthNo");
            }

            LambdaQueryWrapper<Dept> deptWrapper = Wrappers.lambdaQuery();
            deptWrapper.eq(Dept::getDeptCode, deptId);
            Dept dept = deptMapper.selectOne(deptWrapper);
            ThreadLocalUtils.setDept(dept);
            DQYiBaoClient dqYiBaoClient = InsuranceBeanUtils.getDQYiBaoClient();
            if (dqYiBaoClient != null) {
                dqYiBaoClient.getDecodeQuery(BusinessType.REGISTER, insuranceParam, PayChannelType.getByValue(req.getPay_type()));
            }
            // 查询上传明细
            RegOnlineGjYiBaoUploadFeeRecord uploadRecord = regOnlineGjYiBaoUploadFeeRecordMapper.selectByRegNo(
                req.getRegno());
            if (uploadRecord == null) {
                // 上海医保
                RegOnlineSHYiBaoUploadFeeRecord shUploadRecord = regOnlineSHYiBaoUploadFeeRecordMapper.selectByRegNo(
                        req.getRegno());
                if (shUploadRecord != null) {
                    RegInsuranceRegister regInsuranceRegister = regInsuranceRegisterMapper.getByRegNo(Long.parseLong(req.getRegno()));
                    SK01Request sk01Request = new SK01Request();
                    sk01Request.setCardtype("3");
                    sk01Request.setCarddata(insuranceParam.getEcToken());
                    sk01Request.setTranslsh(regInsuranceRegister.getTradeSerialNo());
                    sk01Request.setTotalexpense(regInsuranceRegister.getTradeTotal());
                    sk01Request.setXsywlx("1");
                    SHYBBasicResponse<SK01Response> response = InsuranceBeanUtils.getDQYiBaoClient().postSK01(sk01Request, insuranceParam);
                    if (!Objects.equals("P001", response.getXxfhm()) && !Objects.equals("P510", response.getXxfhm())) {
                        throw new ErrorResponseException("医保退费失败 outData:" + response.getSourceString());
                    }
                    SK01Response sk01Response = response.getXxnr();
                    RegInsuranceReturnCharge retcharge = new RegInsuranceReturnCharge();
                    retcharge.setFlag(1);
                    retcharge.setSerialNo(regInsuranceRegister.getSerialNo());
                    retcharge.setMzNo(regInsuranceRegister.getRegNo());
                    retcharge.setCurrentAccountRefund(sk01Response.getCuraccount());
                    retcharge.setCalendarYearAccountRefund(sk01Response.getHisaccount());
                    retcharge.setSelfCashRefund(sk01Response.getZfcash());
                    retcharge.setOverallPlanningAccountRefund(sk01Response.getTchisaccount());
                    retcharge.setOverallPlanningCashRefund(sk01Response.getTccash());
                    retcharge.setOverallPlanningRefund(sk01Response.getTc());
                    retcharge.setAdditionalAccountRefund(sk01Response.getDffjhisaccount());
                    retcharge.setAdditionalCashRefund(sk01Response.getDffjcash());
                    retcharge.setAdditionalRefund(sk01Response.getDffj());
                    retcharge.setCurrentAccountBalance(sk01Response.getCuraccountamt());
                    retcharge.setCalendarYearAccountBalance(sk01Response.getHisaccountamt());
                    retcharge.setReturnCenterFlowNumber(sk01Response.getTranslsh());
                    retcharge.setCenterFlowNumber(regInsuranceRegister.getTradeSerialNo());
                    retcharge.setReturnTime(new Date());
                    retcharge.setUploadTime(new Date());
                    retcharge.setCreateTime(new Date());
                    if (regInsuranceReturnChargeMapper.getBySerialNoAndFlag(retcharge.getSerialNo(), retcharge.getFlag()) == null) {
                        regInsuranceReturnChargeMapper.insert(retcharge);
                    }
                }
            } else {
                // 国家医保
                Input6203 input6203 = new Input6203();
                input6203.setPayOrdId(uploadRecord.getPayOrdId());
                input6203.setAppRefdSn(mzNo.toString());
                input6203.setAppRefdTime(TimeUtils.getHisDateStr(date));
                input6203.setTotlRefdAmt(invoice.getTotalAmount());
                input6203.setPsnAcctRefdAmt(uploadRecord.getPsnAcctPay());
                input6203.setFundRefdAmt(uploadRecord.getFundPay());
                input6203.setCashRefdAmt(uploadRecord.getOwnPayAmt());
                // ALL:全部 CASH:只退现金 HI:只退医保
                input6203.setRefdType("HI");
                // 退号时医保需要重新授权传入
                input6203.setPayAuthNo(insuranceParam.getPayAuthNo());

                GJYiBaoRefundRegister gjYiBaoRefundRegister = new GJYiBaoRefundRegister();
                Output6203 output6203 = InsuranceBeanUtils.getGJYiBaoClient().post6203(input6203, insuranceParam,
                        PayChannelType.getByValue(req.getPay_type()), req.getHospitalCode());
                if (!"SUCC".equals(output6203.getRefStatus())) {
                    throw new ErrorResponseException("医保退费失败 outData:" + StandardObjectMapper.stringify(output6203));
                }
                // 退费成功，把医保退费返回的数据写入医保退号表
                Output6203.ExtData.Setlinfo setlInfo = output6203.getExtData().getSetlInfo();
                BeanUtils.copyProperties(setlInfo, gjYiBaoRefundRegister);
                gjYiBaoRefundRegister.setSetlTime(TimeUtils.convert(setlInfo.getSetlTime()));
                gjYiBaoRefundRegister.setRegNo(invoice.getRegNo());
                gjYiBaoRefundRegister.setFlag(6);
                gjYiBaoRefundRegister.setCreateTime(date);
                gjYiBaoRefundRegister.setUpdateTime(date);
                gjYiBaoRefundRegister.setOperationCode(hisHospitalProperties.getOpCode() + "");
                gjYiBaoRefundRegisterMapper.insert(gjYiBaoRefundRegister);
            }
        }


        // 执行退费逻辑2 - 发票部分
        // 1.发票信息
        invoiceService.iteratorRegisterInvoiceInfo(invoice.getInvoiceID(), mzNo);
        // 5.返回结果
        returnRegistResult.setRefund_settle_id(mzNo.toString());
        returnRegistResult.setRefund_time(TimeUtils.getHisDateStr(new Date()));
        returnRegistResult.setMessage("退号成功");
        return returnRegistResult;
    }

    /**
     * 尝试医保退款
     * @param req
     * @return
     */
    private boolean tryReturnYbRegister(CancelRegisterReq req) {
        MedicalInsuranceParam insuranceParam = MedicalInsuranceParam.fromHisParam(req.getInsurance_param());
        if (null == insuranceParam) {
            throw new ErrorResponseException("医保退费失败 退款需要重新授权insuranceParam中传入PayAuthNo");
        }
        // 查询上传明细
        RegOnlineGjYiBaoUploadFeeRecord uploadRecord = regOnlineGjYiBaoUploadFeeRecordMapper.selectByRegNo(
                req.getRegno());
        if (uploadRecord == null) {
            // 上海医保
            RegOnlineSHYiBaoUploadFeeRecord shUploadRecord = regOnlineSHYiBaoUploadFeeRecordMapper.selectByRegNo(req.getRegno());
            if (shUploadRecord != null) {
                SE04Response se04Response = InsuranceBeanUtils.getDQYiBaoClient().postSE04(shUploadRecord.getEcToken(), shUploadRecord.getCityId());
                if (se04Response != null) {
                    SK01Request sk01Request = new SK01Request();
                    sk01Request.setCardtype("3");
                    sk01Request.setCarddata(insuranceParam.getEcToken());
                    sk01Request.setTranslsh(se04Response.getSh02Response().getLsh());
                    sk01Request.setTotalexpense(shUploadRecord.getTotalexpense());
                    sk01Request.setXsywlx("1");
                    SHYBBasicResponse<SK01Response> response = InsuranceBeanUtils.getDQYiBaoClient().postSK01(sk01Request, insuranceParam);
                    return Objects.equals("P001", response.getXxfhm()) || Objects.equals("P510", response.getXxfhm());
                } else {
                    return false;
                }
            } else {
                return false;
            }
        } else {
            Long mzNo = mzNoProcessor.execute();
            // 国家医保
            Input6203 input6203 = new Input6203();
            input6203.setPayOrdId(uploadRecord.getPayOrdId());
            input6203.setAppRefdSn(mzNo.toString());
            input6203.setAppRefdTime(TimeUtils.getHisDateStr(new Date()));

            LambdaQueryWrapper<PreRegisterDetail> wrapper = Wrappers.lambdaQuery();
            wrapper.eq(PreRegisterDetail::getRegNo, req.getRegno());
            wrapper.eq(PreRegisterDetail::getIsDelete, false);
            List<PreRegisterDetail> detailList = preRegisterDetailMapper.selectList(wrapper);
            BigDecimal totalAmount = detailList.stream().map(PreRegisterDetail::getTotalAmount)
                    .filter(Objects::nonNull).reduce(BigDecimal.ZERO, BigDecimal::add);
            input6203.setTotlRefdAmt(totalAmount);
            input6203.setPsnAcctRefdAmt(uploadRecord.getPsnAcctPay());
            input6203.setFundRefdAmt(uploadRecord.getFundPay());
            input6203.setCashRefdAmt(uploadRecord.getOwnPayAmt());
            // ALL:全部 CASH:只退现金 HI:只退医保
            input6203.setRefdType("HI");
            // 退号时医保需要重新授权传入
            input6203.setPayAuthNo(insuranceParam.getPayAuthNo());
            Output6203 output6203 = InsuranceBeanUtils.getGJYiBaoClient().post6203(input6203, insuranceParam,
                    PayChannelType.getByValue(req.getPay_type()), req.getHospitalCode());
            return "SUCC".equals(output6203.getRefStatus());
        }

    }

    @Override
    public ReturnRegistResult cancelShYb(CancelRegisterReq req) {
        LambdaQueryWrapper<Dept> deptWrapper = Wrappers.lambdaQuery();
        deptWrapper.eq(Dept::getDeptCode, req.getDeptId());
        Dept dept = deptMapper.selectOne(deptWrapper);
        ThreadLocalUtils.setDept(dept);
        MedicalInsuranceParam insuranceParam = MedicalInsuranceParam.fromHisParam(req.getInsurance_param());
        if (null == insuranceParam) {
            throw new ErrorResponseException("医保退费失败 退款需要重新授权insuranceParam中传入PayAuthNo");
        }

        DQYiBaoClient dqYiBaoClient = InsuranceBeanUtils.getDQYiBaoClient();
        if (dqYiBaoClient != null) {
            dqYiBaoClient.getDecodeQuery(req.getBusinessType(), insuranceParam, PayChannelType.getByValue(req.getPay_type()));
        }

        SK01Request sk01Request = new SK01Request();
        sk01Request.setCardtype("3");
        sk01Request.setCarddata(insuranceParam.getEcToken());
        sk01Request.setTranslsh(req.getLsh());
        sk01Request.setTotalexpense(req.getTotalexpense());
        sk01Request.setXsywlx("1");
        SHYBBasicResponse<SK01Response> response = InsuranceBeanUtils.getDQYiBaoClient().postSK01(sk01Request, insuranceParam);
        if (!Objects.equals("P001", response.getXxfhm()) && !Objects.equals("P510", response.getXxfhm())) {
            throw new ErrorResponseException("医保退费失败 outData:" + response.getSourceString());
        }
        ReturnRegistResult returnRegistResult = new ReturnRegistResult();
        // 5.返回结果
        returnRegistResult.setRefund_settle_id("9999999");
        returnRegistResult.setRefund_time(TimeUtils.getHisDateStr(new Date()));
        returnRegistResult.setMessage(response.getSourceString());
        return returnRegistResult;
    }


    private RegisterListTime getTodayDetailByPatId(PreRegisterReq req) {
        // 排班上线，替换非空数据
        // 通过scheduling_id查询排班信息
        ScheduleDetail scheduleDetail = schedulingMapper.selectScheduleDetail(req.getScheduling_id(),
                                                                              req.getHospitalCode());
        // 查询患者当天挂号信息
        LambdaQueryWrapper<RegisterListTime> listWrapper = Wrappers.lambdaQuery();
        listWrapper.eq(RegisterListTime::getPatID, req.getPatid());
        listWrapper.eq(RegisterListTime::getIsDelete, false);
        listWrapper.eq(RegisterListTime::getStatus, 0);
        listWrapper.eq(RegisterListTime::getCourseID, scheduleDetail.getCourseID());
        listWrapper.orderByDesc(RegisterListTime::getRegistTime);
        Date date = TimeUtils.convert(req.getBegin_time());
        listWrapper.between(RegisterListTime::getRegistTime, TimeUtils.getStartOfDay(date),
                            TimeUtils.getEndOfDay(date));
        List<RegisterListTime> preRegisterList = registerListTimeMapper.selectList(listWrapper);
        if (CollectionUtils.isEmpty(preRegisterList)) {
            return null;
        }

        return preRegisterList.get(0);
    }

    @Transactional
    public void confirmLockNumber(PreRegisterReq req, RegisterListTime registerListTime, ConfirmRegistReq confirmRegistReq) {
        // 确认锁号做的事情
        // 1.如果是预约转挂号，只需要更新状态
        // 2.如果是直接挂号，需要锁号+更新状态
        if (confirmRegistReq.getAppointment_id() != null) {
            Appointment appointment = appointmentMapper.selectById(confirmRegistReq.getAppointment_id());
            if (appointment != null) {
                appointmentLockNumMapper.updateFlagBySqh(1, appointment.getSqh());
                registerListTime.setAppointmentNo(appointment.getSqh().toString());
                registerListTimeMapper.updateById(registerListTime);
                appointment.setAppointmentStatus(5);
                appointmentMapper.updateById(appointment);
            }
        } else {
            Integer status = 2;
            SourceNumberReq sourceNumberReq = new SourceNumberReq();
            sourceNumberReq.setHospitalCode(req.getHospitalCode());
            sourceNumberReq.setScheduling_id(req.getScheduling_id());
            sourceNumberReq.setSource_number(req.getSource_number());
            // 通过scheduling_id， source_number查询排班信息
            List<SourceNumber> sourceNumbers = schedulingMapper.getSchedulingSourceNumber(sourceNumberReq);
            SourceNumber sourceNumber = sourceNumbers.stream().filter(u -> u.getStatus() == 0 || u.getStatus() == 1).findFirst()
                .orElse(null);
            if (CollectionUtils.isEmpty(sourceNumbers) || sourceNumber == null) {
                log.error("锁号：未查询到排班号源信息，schedulingId:{}，sourceNumber:{} status: {}", req.getScheduling_id(),
                          req.getSource_number(), status);
                throw new ErrorResponseException("未查询到排班号源信息或号已被锁，请重新挂号");
            }
            // 查询TB_APPOINTMENT表，如果存在则直接返回成功，消息提示不需要锁号，可直接挂号
            LambdaQueryWrapper<Appointment> wrapper = Wrappers.lambdaQuery();
            wrapper.eq(Appointment::getSqh, StringUtils.trim(req.getLock_number_id()));
            wrapper.eq(Appointment::getCheckDate, sourceNumber.getDutyDate());
            List<Appointment> appointments = appointmentMapper.selectList(wrapper);
            if (!CollectionUtils.isEmpty(appointments)) {
                log.info("锁号：已存在预约信息，无需锁号  schedulingId:{}，sourceNumber:{}", req.getScheduling_id(),
                         req.getSource_number());
                sourceNumber.setStatus(status);
                schedulingMapper.updateSourceNumberStatus(sourceNumber);
                appointmentLockNumMapper.updateFlagBySqh(1, appointments.get(0).getSqh());
                return;
            }
            sourceNumber.setStatus(status);
            schedulingMapper.updateSourceNumberStatus(sourceNumber);
            AppointmentLockNum appointmentLockNum = new AppointmentLockNum();
            // 通过patid查询患者信息
            RTPatientList patientList = patientListMapper.selectByPatId(req.getPatid());
            if (patientList == null) {
                throw new RuntimeException("院内无此患者信息或患者信息已停用");
            }

            // 锁号的同时判断在同一个排班下（SubjectId，DutyDate相同）该患者是否有锁号，如果有锁号，先将原本的锁号号源恢复
            LambdaUpdateWrapper<AppointmentLockNum> updateWrapper = Wrappers.lambdaUpdate();
            updateWrapper.set(AppointmentLockNum::getFlag, 2);
            updateWrapper.eq(AppointmentLockNum::getSubjectID, sourceNumber.getSubjectID());
            updateWrapper.eq(AppointmentLockNum::getDutyDate, sourceNumber.getDutyDate());
            updateWrapper.eq(AppointmentLockNum::getCertificateNo, patientList.getCertificateNo());
            updateWrapper.eq(AppointmentLockNum::getFlag, 0);
            appointmentLockNumMapper.update(updateWrapper);

            // 保存预占号信息TB_APPOINTMENT_LOCKNUM
            log.info("锁号：保存预占号信息  schedulingId:{}，sourceNumber:{}", req.getScheduling_id(), sourceNumber);
            appointmentLockNum.setSubjectID(sourceNumber.getSubjectID());
            appointmentLockNum.setDutyDate(sourceNumber.getDutyDate());
            appointmentLockNum.setTimeSpanID(sourceNumber.getTimespanId());
            appointmentLockNum.setSourceId(sourceNumber.getSchedulingId());
            if (appointmentLockNum.getSourceId() == null ) {
                appointmentLockNum.setSourceId(Integer.valueOf(req.getScheduling_id()));
            }
            appointmentLockNum.setSeqNum(Integer.valueOf(sourceNumber.getSeqNum()));
            appointmentLockNum.setCertificateNo(patientList.getCertificateNo());
            appointmentLockNum.setLockTime(new Date());
            appointmentLockNum.setNumId(sourceNumber.getNumId());
            appointmentLockNum.setHospitalCode(req.getHospitalCode());
            appointmentLockNum.setFlag(0);
            appointmentLockNum.setVisitFlag(0);
            appointmentLockNum.setIsCZF(0);

            appointmentLockNumMapper.insertAppointmentLockNum(appointmentLockNum);
            appointmentLockNum = appointmentLockNumMapper.selectAppointmentLockNumBySqh(appointmentLockNum.getSqh());
            RTPatientDetl patientDetl = patientDetlMapper.selectById(req.getPatid());
            // 保存预约信息
            Appointment appointment = new Appointment();
            appointment.setPatName(patientList.getPatName());
            appointment.setCertificateNo(StringUtils.trim(patientList.getCertificateNo()));
            appointment.setTelephone(patientDetl.getPatPhone());
            appointment.setSubjectID(sourceNumber.getSubjectID());
            appointment.setVisitDate(TimeUtils.dateStringFormat(sourceNumber.getDutyDate(), "yyyy-MM-dd") + " " + sourceNumber.getStartTime() + "-" + sourceNumber.getEndTime());
            appointment.setAppointmentStatus(5);
            appointment.setCheckDate(sourceNumber.getDutyDate());
            appointment.setTimeSpanID(sourceNumber.getTimespanId());
            appointment.setSqh(appointmentLockNum.getSqh());
            appointment.setHospitalCode(hisHospitalProperties.getCode());
            appointment.setAppointmentNum(registerListTime.getRegistOrder());
            appointment.setAppointmentSeqNum(registerListTime.getRegistOrder());
            Integer visiteCount = registerListTimeMapper.checkVisitFlag(appointment.getCertificateNo(),
                                                                        Integer.valueOf(appointment.getHospitalCode()));
            if (visiteCount > 0) {
                appointment.setAppointmentType(2);
                appointment.setCfz(Short.valueOf("2"));
            } else {
                appointment.setAppointmentType(1);
                appointment.setCfz(Short.valueOf("1"));
            }
            appointment.setAppointmentOrderID(appointmentLockNum.getLockNumOrderID());
            appointment.setDeptId(registerListTime.getDeptID());
            appointment.setDoctorId(registerListTime.getDoctorID());
            appointment.setZtStatus(0);
            appointment.setCreatedBy(hisHospitalProperties.getOpCode());
            appointment.setOperator(hisHospitalProperties.getOpCode());
            appointment.setCreatedDate(registerListTime.getCreatedDate());
            appointment.setUpdatedBy(hisHospitalProperties.getOpCode());
            appointment.setUpdatedDate(registerListTime.getUpdateDate());
            appointment.setIsDelete(0);
            appointment.setIsUse(1);
            appointment.setSourceID(appointmentLockNum.getSourceId());
            appointment.setBeginTime(sourceNumber.getStartTime());
            appointment.setEndTime(sourceNumber.getEndTime());
            appointment.setBmbh("xiaochengxu");
            appointment.setSex(patientList.getSex() + "");
            // 时段名称
            appointment.setTimsMs(sourceNumber.getName());
            appointment.setRemarkA1("GTYY");
            appointment.setCardNo(registerListTime.getCardNo());
            appointment.setPatSfz(patientList.getCertificateNo());
            appointment.setMovePhone(patientDetl.getPatPhone());
            appointment.setPatNo(Math.toIntExact(patientList.getPatId()));
            appointmentMapper.insert(appointment);
            appointmentLockNumMapper.updateFlagBySqh(1, appointment.getSqh());
            registerListTime.setAppointmentNo(appointment.getSqh().toString());
            registerListTimeMapper.updateById(registerListTime);
        }
    }

    @Transactional
    public void returnAppointment(PreRegisterReq req) {
        // 查询预占号信息
        AppointmentLockNum appointmentLockNum =
            appointmentLockNumMapper.selectAppointmentLockNumBySqh(Integer.valueOf(StringUtils.trim(req.getLock_number_id())));
        if (appointmentLockNum == null) {
            log.error("未查询到预占号信息，lock_number_id:{}", req.getLock_number_id());
            return;
        }

        // 退号后将TB_Appointment_LockNum表中flag变成2
        appointmentLockNumMapper.updateFlagBySqh(2, Integer.valueOf(StringUtils.trim(req.getLock_number_id())));

        // 查询TB_APPOINTMENT表，如果存在则直接返回成功，消息提示不需要锁号，可直接挂号
        LambdaQueryWrapper<Appointment> wrapper = Wrappers.lambdaQuery();
        wrapper.eq(Appointment::getSqh, req.getLock_number_id());
        List<Appointment> appointments = appointmentMapper.selectList(wrapper);
        if (!CollectionUtils.isEmpty(appointments)) {
            log.info("锁号解锁：已存在预约信息，更改预约状态为退号  lock_number_id(SQH):{}", req.getLock_number_id());
            for (Appointment appointment : appointments) {
                schedulingMapper.updateAppointmentStatus(appointment.getAppointmentID().toString(), 3);
            }
        }

        // 退号后将TB_Config_SubjectNumDetail的Status变成0
        SourceNumber sourceNumber = new SourceNumber();
        sourceNumber.setNumId(appointmentLockNum.getNumId());
        sourceNumber.setStatus(0);
        schedulingMapper.updateSourceNumberStatus(sourceNumber);
    }

    @Override
    public Map<String, String> canReturnRegister(Long regNo) {
        Map<String, String> map = Maps.newHashMap();
        // 通过挂号序号查询挂号信息
        RegisterListView registerView = registerListViewMapper.selectByRegNo(regNo);
        if (registerView == null) {
            map.put("msg", "未查询到挂号记录");
            map.put("success", "0");
            return map;
        }
        if (0 != registerView.getFzFlag() && 1 != registerView.getFzFlag()) {
            map.put("msg", "当前患者已就诊不能退号，请让接诊医生取消就诊再退号");
            map.put("success", "0");
            return map;
        }
        // 验证是否是特需号，如果是特需号判断当前有没有同医生的配药是否退掉
        if (registerView.getDoctorLevel() == 1 || registerView.getDoctorLevel() == 6) {
            RegisterListTime registerListTime = registerListTimeMapper.selectById(regNo);
            if (registerListTime != null) {
                LambdaQueryWrapper<RegisterListTime> timeWrapper = Wrappers.lambdaQuery();
                timeWrapper.in(RegisterListTime::getDeptID, 1078, 1083);
                timeWrapper.eq(RegisterListTime::getStatus, 0);
                timeWrapper.eq(RegisterListTime::getDoctorID, registerView.getDoctorID());
                timeWrapper.between(RegisterListTime::getRegistTime, registerView.getRegistTime(),
                                    DateUtils.addDays(registerView.getRegistTime(), 1));
                if (registerListTimeMapper.exists(timeWrapper)) {
                    map.put("msg", "该特需存在有效配药号!");
                    map.put("success", "0");
                    return map;
                }
            } else {
                LambdaQueryWrapper<RegisterList> timeWrapper = Wrappers.lambdaQuery();
                timeWrapper.in(RegisterList::getDeptID, 1078, 1083);
                timeWrapper.eq(RegisterList::getStatus, 0);
                timeWrapper.eq(RegisterList::getDoctorID, registerView.getDoctorID());
                timeWrapper.between(RegisterList::getRegistTime, registerView.getRegistTime(),
                                    DateUtils.addDays(registerView.getRegistTime(), 1));
                if (registerListMapper.exists(timeWrapper)) {
                    map.put("msg", "该特需存在有效配药号!");
                    map.put("success", "0");
                    return map;
                }
            }
        }

        Long aLong = chargeItemViewMapper.selectCountChargeList(regNo);
        if (aLong > 0) {
            map.put("msg", "存在收费项目无法退号");
            map.put("success", "0");
            return map;
        }

        OutpatientInvoiceView invoice = invoiceService.getNormalOutpatientInvoice(
            registerView.getPatID().toString(), registerView.getRegNo().toString(),
            registerView.getHospitalCode().toString(), 1);
        if (invoice != null && "一站".equals(invoice.getInvoicePrefix())) {
            map.put("msg", "未打印自助机发票, 不能退号");
            map.put("success", "0");
            return map;
        }

        map.put("msg", "允许退号");
        map.put("success", "1");
        return map;
    }

    private ConfirmRegistResult checkRegisterReqData(ConfirmRegistReq req, PreRegisterList register,
                                                     List<PreRegisterDetail> details) {
        ConfirmRegistResult result = new ConfirmRegistResult();

        boolean success = true;
        String message = "挂号成功";
        String memo = "";

        String settleId = req.getSettle_id();
        if (StringUtils.isBlank(req.getRegno())) {
            log.info("挂号序号不能为空");
            success = false;
            message = "挂号序号不能为空";
        }
        if (StringUtils.isBlank(req.getSettle_id())) {
            log.info("结算单号不能为空");
            success = false;
            message = "结算单号不能为空";
        }
        if (StringUtils.isBlank(req.getTotal_amount())) {
            log.info("总金额不能为空");
            success = false;
            message = "总金额不能为空";
        }
        if (StringUtils.isBlank(req.getShould_pay_amount())) {
            log.info("应付金额不能为空");
            success = false;
            message = "应付金额不能为空";
        }

        // 号源部分
        if (!register.getRegNo().equals(Long.valueOf(req.getRegno()))) {
            log.error("挂号序号不一致，settleId:{}，regNo:{}", settleId, req.getRegno());
            success = false;
            message = "挂号序号不一致";
        }
        // 排班部分 Scheduling_id是明细序号，根据这个查询排班号做对比
        Integer courseId = schedulingMapper.selectCourseIdBySchedulingId(req.getScheduling_id(), req.getHospitalCode());
        if (!register.getCourseID().equals(courseId)) {
            log.error("排班序号不一致，detail-courseID:{}，req-schedulingId:{}，req-courseId:{}", register.getCourseID(),
                      req.getScheduling_id(), courseId);
            success = false;
            message = "排班序号不一致";
        }
        if ("0".equals(req.getSelf_flag())) {
            if (req.getJsFlag() == 2) {
                RegOnlineGjYiBaoUploadFeeRecord record = regOnlineGjYiBaoUploadFeeRecordMapper.selectByRegNo(register.getRegNo().toString());
                BigDecimal totalPay = record.getFeeSumamt().multiply(new BigDecimal(100));
                if (totalPay.compareTo(new BigDecimal(req.getTotal_amount())) != 0) {
                    log.error("挂号总金额不一致，settleId:{}，detail-totalAmount:{} req-totalAmount:{}",
                            settleId, totalPay, req.getTotal_amount());
                    success = false;
                    message = "挂号总金额不一致";
                }
                BigDecimal shouldPay = record.getOwnPayAmt().multiply(new BigDecimal(100));
                if (shouldPay.compareTo(new BigDecimal(req.getShould_pay_amount())) != 0) {
                    log.error("应付金额不一致，settleId:{}，detail-shouldPayAmount:{}  req-shouldPayAmount:{}", settleId,
                            shouldPay, req.getShould_pay_amount());
                    success = false;
                    message = "应付金额不一致";
                }
            } else {
                RegOnlineSHYiBaoUploadFeeRecord record = regOnlineSHYiBaoUploadFeeRecordMapper.selectByRegNo(register.getRegNo().toString());
                BigDecimal totalPay = record.getTotalexpense().add(record.getFybjsfwfyze()).multiply(new BigDecimal(100));
                if (totalPay.compareTo(new BigDecimal(req.getTotal_amount())) != 0) {
                    log.error("挂号总金额不一致，settleId:{}，detail-totalAmount:{} req-totalAmount:{}",
                            settleId, totalPay, req.getTotal_amount());
                    success = false;
                    message = "挂号总金额不一致";
                }

                BigDecimal shouldPay = record.getZfdxjzfs().add(record.getTcdxjzfs()).add(record.getFybjsfwfyze())
                        .multiply(new BigDecimal(100));
                if (shouldPay.compareTo(new BigDecimal(req.getShould_pay_amount())) != 0) {
                    log.error("应付金额不一致，settleId:{}，detail-shouldPayAmount:{}  req-shouldPayAmount:{}", settleId,
                            shouldPay, req.getShould_pay_amount());
                    success = false;
                    message = "应付金额不一致";
                }
            }
        } else {
            int totalAmount =
                details.stream().mapToInt(u -> u.getTotalAmount().multiply(new BigDecimal(100)).intValue()).sum();
            // 金额部分
            BigDecimal totalAmountBigDecimal = new BigDecimal(totalAmount);
            if (!totalAmountBigDecimal.equals(new BigDecimal(req.getTotal_amount()))) {
                log.error("挂号总金额不一致，settleId:{}，detail-totalAmount:{} req-totalAmount:{}",
                          settleId, totalAmount, req.getTotal_amount());
                success = false;
                message = "挂号总金额不一致";
            }
            if (!totalAmountBigDecimal.equals(new BigDecimal(req.getShould_pay_amount()))) {
                log.error("应付金额不一致，settleId:{}，detail-shouldPayAmount:{}  req-shouldPayAmount:{}", settleId,
                          totalAmount, req.getShould_pay_amount());
                success = false;
                message = "应付金额不一致";
            }
        }


        result.setSuccess(success);
        result.setMessage(message);
        result.setSettle_id(req.getSettle_id());
        result.setMemo(memo);
        return result;
    }

    private void managePreRegisterList(PreRegisterList list, PreRegisterReq req, Date date, Long mzNo)  {
        list.setRegNo(mzNo);
        list.setReturnRegNo(0L);
        // 就诊人信息
        String patid = req.getPatid();
        RTPatientList patient = patientListMapper.selectByPatId(patid);
        if (patient == null) {
            throw new RuntimeException("院内无此患者信息或患者信息已停用");
        }
        if (!req.getPatname().equals(patient.getPatName())) {
            throw new RuntimeException("患者姓名"+req.getPatname()+"与his内建档姓名"+patient.getPatName()+"不同，请前往就诊人管理中修改");
        }
        list.setPatID(patient.getPatId());
        list.setNewPatID(patient.getNewPatId());
        // 查询卡信息
        RTPatientCard card = null;
        // 卡表里：0 自费，1 医保
        Integer cardType = 0;
        // 医保后续处理医保相关字段值
        if ("0".equals(req.getSelf_flag())) { // 医保，查医保卡
            cardType = 1;
        } else { // 自费，查自费卡
            cardType = 0;
        }
        if (StringUtils.isNotBlank(req.getCardno())) {
            card = patientCardMapper.getByCardNoAndPatIdAndCardType(req.getCardno(), patient.getPatId(), cardType);
            if (card == null) {
                card = patientCardMapper.getByCardNoAndPatId(req.getCardno(), patient.getPatId());
                if (card == null) {
                    log.error("根据卡号未查询到就诊卡信息，patId:{}，cardNo:{}", patid, req.getCardno());
                    throw new RuntimeException("根据卡号未查询到就诊卡信息:" + req.getCardno());
                }
            }
        }
        list.setCardNo(req.getCardno());
//        preRegisterList.setOutPatientNo(patient.getOutPatientNo());
        list.setHospNo(patient.getHospNo());
        list.setPatName(req.getPatname());
        list.setOutPatientNo(patient.getHospNo());

        // 查询患者当天预占号信息，保存sql到list中
        LambdaQueryWrapper<AppointmentLockNum> wrapper = Wrappers.lambdaQuery();
        wrapper.eq(AppointmentLockNum::getCertificateNo, patient.getCertificateNo());
        wrapper.eq(AppointmentLockNum::getSourceId, req.getScheduling_id());
        wrapper.eq(AppointmentLockNum::getSeqNum, Integer.valueOf(req.getSource_number()));
        /*
         * 如果只根据身份证号、排班ID、号序来查Lock表，有可能会查出多条
         * 前提：
         *    1.多次挂号再退号，并且拿到的是固定的号序
         *    2.锁号后未支付，等30分钟后，这个号序又可以被预约
         *
         * 所以这里要判断Flag是不是0（确号之后Flag会变成1，退号之后Flag会变成2，并且根据锁号时间排序取最后一个
         *
         * 注：有其他地方有使用selectOne的地方，但是如果条件中有SQH就不用考虑那么多条件，因为多条锁号记录的SQH不同
         */
        wrapper.eq(AppointmentLockNum::getFlag, 0);
        wrapper.orderByDesc(AppointmentLockNum::getLockTime);
        List<AppointmentLockNum> appointmentLockNums = appointmentLockNumMapper.selectList(wrapper);
        AppointmentLockNum appointmentLockNum = appointmentLockNums.stream().findFirst().orElse(null);
        if (appointmentLockNum == null) {
            SourceNumberReq sourceNumberReq = new SourceNumberReq();
            sourceNumberReq.setHospitalCode(req.getHospitalCode());
            sourceNumberReq.setScheduling_id(req.getScheduling_id());
            sourceNumberReq.setSource_number(req.getSource_number());
            // 通过scheduling_id， source_number查询排班信息
            List<SourceNumber> sourceNumbers = schedulingMapper.getSchedulingSourceNumber(sourceNumberReq);

            SourceNumber sourceNumber = sourceNumbers.stream().filter(u -> u.getStatus() == 0 || u.getStatus() == 1).findFirst()
                    .orElse(null);
            if (CollectionUtils.isEmpty(sourceNumbers) || sourceNumber == null) {
                log.error("锁号：未查询到排班号源信息，schedulingId:{}，sourceNumber:{} status: {}", req.getScheduling_id(),
                        req.getSource_number(), 1);
                throw new ErrorResponseException("未查询到排班号源信息或号已被锁，请重新挂号");
            }
            sourceNumber.setStatus(1);
            schedulingMapper.updateSourceNumberStatus(sourceNumber);
            appointmentLockNum = new AppointmentLockNum();

            // 保存预占号信息TB_APPOINTMENT_LOCKNUM
            log.info("锁号：保存预占号信息  schedulingId:{}，sourceNumber:{}", req.getScheduling_id(), sourceNumber);
            appointmentLockNum.setSubjectID(sourceNumber.getSubjectID());
            appointmentLockNum.setDutyDate(sourceNumber.getDutyDate());
            appointmentLockNum.setTimeSpanID(sourceNumber.getTimespanId());
            appointmentLockNum.setSourceId(sourceNumber.getSchedulingId());
            if (appointmentLockNum.getSourceId() == null ) {
                appointmentLockNum.setSourceId(Integer.valueOf(req.getScheduling_id()));
            }
            appointmentLockNum.setSeqNum(Integer.valueOf(sourceNumber.getSeqNum()));
            appointmentLockNum.setCertificateNo(patient.getCertificateNo());
            appointmentLockNum.setLockTime(new Date());
            appointmentLockNum.setNumId(sourceNumber.getNumId());
            appointmentLockNum.setHospitalCode(req.getHospitalCode());
            appointmentLockNum.setFlag(0);
            appointmentLockNum.setVisitFlag(0);
            appointmentLockNum.setIsCZF(0);

            appointmentLockNumMapper.insertAppointmentLockNum(appointmentLockNum);
            list.setAppointmentNo(appointmentLockNum.getSqh().toString());
        } else {
            list.setAppointmentNo(appointmentLockNum.getSqh().toString());
        }

        list.setBlanceWay(1);
        list.setRegistType(0);
//        preRegisterList.setAppointmentWay(0);
//        preRegisterList.setAppointmentNo("");
        // 号序信息
        if (StringUtils.isNotBlank(req.getSource_number())) {
            list.setRegistOrder(Integer.valueOf(req.getSource_number()));
        }
        list.setRegistMode(0);
//        preRegisterList.setVisitTime("2024-03-23 08:45:56");
        list.setStatus(0);
        list.setRegistTime(date);
        if (null != req.getBegin_time()) {
            list.setRegistTime(TimeUtils.convert(req.getBegin_time()));
        }
        list.setComputerNo("999999");
        list.setOpCode(hisHospitalProperties.getOpCode());
        list.setCreateTime(date);
//        preRegisterList.setCureCode("");
        list.setVisitFlag(2);
        list.setIsDelete(false);
        list.setCreatedBy(hisHospitalProperties.getOpCode());
        list.setCreatedDate(date);
        list.setUpdateDate(date);
        list.setUpdateby(hisHospitalProperties.getOpCode());
        list.setReferralFlag(0);
        list.setDeptKind((short) 2);
        list.setFzFlag(0);
        list.setHospitalCode(Integer.valueOf(hisHospitalProperties.getCode()));
        // 医保结算类型，0，1自费或者五期医保，2国家医保
        list.setJsFlag(0);
        // 收费类型，10自费，30医保，45大病
        list.setChargeType(10);
        list.setAcctUsedFlag("1");
        // 医保后续处理医保相关字段值
        if ("0".equals(req.getSelf_flag())) { // 走医保
            list.setJsFlag(2);
            list.setChargeType(30);
            list.setAcctUsedFlag("1");
        }
//        preRegisterList.setReservationType("");
        // 预约标识，预约记录表TB_Appointment字段RemarkA1
//        preRegisterList.setAppointmentFlag("DTZH");
        list.setIsGreenChannel(false);
        list.setIsDebitPay(false);
    }

    private void updatePreRegisterList(PreRegisterList list, PreRegisterReq req) {
        // 排班上线，替换非空数据
        // 通过scheduling_id查询排班信息
        ScheduleDetail scheduleDetail = schedulingMapper.selectScheduleDetail(req.getScheduling_id(),
                                                                              req.getHospitalCode());
        if (scheduleDetail != null) {
            if (null != scheduleDetail.getCourseID()) {
                list.setCourseID(scheduleDetail.getCourseID());
            }
            if (StringUtils.isNotBlank(scheduleDetail.getCourseName())) {
                list.setCourseName(scheduleDetail.getCourseName());
            }
            if (null != scheduleDetail.getDeptID()) {
                list.setDeptID(scheduleDetail.getDeptID());
            }
            if (null != scheduleDetail.getDoctorID()) {
                list.setDoctorID(scheduleDetail.getDoctorID());
            }
            if (null != scheduleDetail.getDoctorLevel()) {
                list.setDoctorLevel(scheduleDetail.getDoctorLevel());
            }
            if (null != scheduleDetail.getChargeType()) {
                list.setChargeType(scheduleDetail.getChargeType());
            }
            if (null != scheduleDetail.getBlanceWay()) {
                list.setBlanceWay(scheduleDetail.getBlanceWay());
            }
            if (null != scheduleDetail.getRegistType()) {
                list.setRegistType(scheduleDetail.getRegistType());
            }
            if (null != scheduleDetail.getAppointmentWay()) {
                list.setAppointmentWay(scheduleDetail.getAppointmentWay());
            }
            if (StringUtils.isNotBlank(scheduleDetail.getAppointmentNo())) {
                list.setAppointmentNo(scheduleDetail.getAppointmentNo());
            }
            if (null != scheduleDetail.getRegistOrder()) {
                list.setRegistOrder(scheduleDetail.getRegistOrder());
            }
            if (null != scheduleDetail.getRegistMode()) {
                list.setRegistMode(scheduleDetail.getRegistMode());
            }
            if (StringUtils.isNotBlank(scheduleDetail.getVisitTime())) {
                list.setVisitTime(scheduleDetail.getVisitTime());
            }
            if (null != scheduleDetail.getStatus()) {
                list.setStatus(scheduleDetail.getStatus());
            }
            if (StringUtils.isNotBlank(scheduleDetail.getComputerNo())) {
                list.setComputerNo(scheduleDetail.getComputerNo());
            }
            if (null != scheduleDetail.getOpCode()) {
                list.setOpCode(scheduleDetail.getOpCode());
            }
            if (StringUtils.isNotBlank(scheduleDetail.getCureCode())) {
                list.setCureCode(scheduleDetail.getCureCode());
            }
            if (null != scheduleDetail.getVisitFlag()) {
                list.setVisitFlag(scheduleDetail.getVisitFlag());
            }
            if (null != scheduleDetail.getIsDelete()) {
                list.setIsDelete(scheduleDetail.getIsDelete());
            }
            if (null != scheduleDetail.getCreatedBy()) {
                list.setCreatedBy(scheduleDetail.getCreatedBy());
            }
            if (null != scheduleDetail.getUpdateby()) {
                list.setUpdateby(scheduleDetail.getUpdateby());
            }
            if (null != scheduleDetail.getReferralFlag()) {
                list.setReferralFlag(scheduleDetail.getReferralFlag());
            }
            if (null != scheduleDetail.getDeptKind()) {
                list.setDeptKind(scheduleDetail.getDeptKind());
            }
            if (null != scheduleDetail.getFzFlag()) {
                list.setFzFlag(scheduleDetail.getFzFlag());
            }
            if (null != scheduleDetail.getHospitalCode()) {
                list.setHospitalCode(scheduleDetail.getHospitalCode());
            }
            if (null != scheduleDetail.getJsFlag()) {
                list.setJsFlag(scheduleDetail.getJsFlag());
            }
            if (StringUtils.isNotBlank(scheduleDetail.getAcctUsedFlag())) {
                list.setAcctUsedFlag(scheduleDetail.getAcctUsedFlag());
            }
            if (StringUtils.isNotBlank(scheduleDetail.getReservationType())) {
                list.setReservationType(scheduleDetail.getReservationType());
            }
            if (StringUtils.isNotBlank(scheduleDetail.getAppointmentFlag())) {
                list.setAppointmentFlag(scheduleDetail.getAppointmentFlag());
            }
            if (null != scheduleDetail.getIsGreenChannel()) {
                list.setIsGreenChannel(scheduleDetail.getIsGreenChannel());
            }
            if (null != scheduleDetail.getIsDebitPay()) {
                list.setIsDebitPay(scheduleDetail.getIsDebitPay());
            }
        }
    }

    private List<PreRegisterDetail> saveByPreRegister(PreRegisterList list, PreRegisterReq req, Date date, Long mzNo,
                                                      boolean isFeeReg) {
        List<PreRegisterDetail> details = Lists.newArrayList();

        // 保存预挂号明细信息preRegisterDetail
        PreRegisterDetail detail = new PreRegisterDetail();
        detail.setRegNo(mzNo);
//        // 生成明细序号
//        Long regDtlNo = regDtlNoProcessor.execute();
//        detail.setRegDtlNo(regDtlNo);
        detail.setReturnRegNo(0L);
        detail.setReturnRegDtlNo(0L);

        // 项目费用信息
        detail.setOpCode(hisHospitalProperties.getOpCode());
        detail.setOpTime(date);
        detail.setIsDelete(false);
        detail.setOrder(0);
        detail.setIsUse(0);
        detail.setCreatedBy(hisHospitalProperties.getOpCode());
        detail.setCreatedDate(date);
        detail.setUpdateBy(hisHospitalProperties.getOpCode());
        detail.setUpdateDate(date);
        detail.setDiscountAmount(BigDecimal.valueOf(0));
        detail.setHospitalCode(list.getHospitalCode());
        detail.setDoctorId(list.getDoctorID());
        MedicalWorker doctor = medicalWorkerService.getById(list.getDoctorID());
        detail.setDoctorName(doctor.getName());
        detail.setJzPay(BigDecimal.valueOf(0));
        detail.setIsDebitPay(list.getIsDebitPay());
        detail.setDebitPay(null);
        detail.setDebitStatus(null);

        // 费用计算
        List<ChargeItemView> views = Lists.newArrayList();
        views.addAll(listChargeItems(req.getRegistration_fee_code(), list.getDoctorLevel()));
        if (StringUtils.isBlank(req.getTreatment_fee_code())) {
            log.info("未查询到诊疗费用信息，itemCode:{}", req.getTreatment_fee_code());
        } else {
            views.addAll(listChargeItems(req.getTreatment_fee_code(), list.getDoctorLevel()));
        }
        if (StringUtils.isBlank(req.getChildren_treatment_fee_code())) {
            log.info("未查询到儿童诊疗费用信息，itemCode:{}", req.getChildren_treatment_fee_code());
        } else {
            views.addAll(listChargeItems(req.getChildren_treatment_fee_code(), list.getDoctorLevel()));
        }
        // 这里只适用于挂号费的预算！！！
        views.stream().filter(Objects::nonNull).forEach(view -> {
            // 一个新的detail
            PreRegisterDetail preRegisterDetail = new PreRegisterDetail();
            BeanUtils.copyProperties(detail, preRegisterDetail);
            // 明细编号
            Long regDtlNo = regDtlNoProcessor.execute();
            preRegisterDetail.setRegDtlNo(regDtlNo);

            BigDecimal expensePrice = view.getExpensePrice();
            BigDecimal nonExpensePrice = view.getNonExpensePrice();
            BigDecimal price = view.getRegFee();

            preRegisterDetail.setItemID(view.getItemCode().intValue());
            preRegisterDetail.setItemName(view.getItemName());
            preRegisterDetail.setItemCatetory(view.getItemCategory());
            preRegisterDetail.setExpensePrice(expensePrice);
            preRegisterDetail.setNonExpensePrice(nonExpensePrice);
            preRegisterDetail.setPrice(price);
            preRegisterDetail.setNationCode(view.getNationCode());
            preRegisterDetail.setDosageUnit(view.getDosageUnit());
            // 计算项目数量，计算总金额
            // 项目数量挂号数量为1
            int qty = 1;
            preRegisterDetail.setQty(qty);
            preRegisterDetail.setExpenseAmout(expensePrice.multiply(new BigDecimal(qty)));
            preRegisterDetail.setNonExpenseAmount(nonExpensePrice.multiply(new BigDecimal(qty)));
            preRegisterDetail.setTotalAmount(price);
            preRegisterDetail.setSelfCost(price);

            // 免费号同职工号免费并记账，2023.12.04
            // 本院职工自费患者，挂号费用为 0
//            detail.NonExpenseAmount = 0;
//            detail.ExpenseAmout = 0;
//            detail.Price = 0;
//            detail.ExpensePrice = 0;
//            detail.NonExpensePrice = 0;
//            //职工挂号不付钱，记账
//            detail.JzPay = detail.TotalAmount;
//            if (isRegFree)
//            {
//                //免费号不记账
//                detail.JzPay = 0;
//                detail.TotalAmount = 0;
//            }
//            detail.DebitPay = 0;
            if (isFeeReg) {
                preRegisterDetail.setNonExpenseAmount(BigDecimal.ZERO);
                preRegisterDetail.setExpenseAmout(BigDecimal.ZERO);
                preRegisterDetail.setPrice(BigDecimal.ZERO);
                preRegisterDetail.setExpensePrice(BigDecimal.ZERO);
                preRegisterDetail.setNonExpensePrice(BigDecimal.ZERO);
                preRegisterDetail.setJzPay(BigDecimal.ZERO);
                preRegisterDetail.setTotalAmount(BigDecimal.ZERO);
                preRegisterDetail.setSelfCost(BigDecimal.ZERO);
            }
            preRegisterDetailMapper.insert(preRegisterDetail);
            details.add(preRegisterDetail);
        });
        return details;
    }

    private List<ChargeItemView> listChargeItems(String code, Integer doctorLevel) {
        LambdaQueryWrapper<ChargeItemView> wrapper = Wrappers.lambdaQuery();
        wrapper.in(ChargeItemView::getItemCode, code.split(","));
        List<ChargeItemView> itemViews = chargeItemViewMapper.selectList(wrapper);
        itemViews.forEach(u -> {
            RegFeeConfig feeConfig = regFeeConfigMapper.selectOneByItemId(u.getItemCode().intValue(),
                                                                          u.getHospitalId() + "", doctorLevel);
            if (feeConfig == null) {
                log.error("未查询到对应项目的价格, code=" + u.getItemCode());
            } else {
                u.setRegFee(feeConfig.getPrice());
            }
        });
        return itemViews;
    }

    // TODO: 现在只做了查询医保挂号是否结算成功，非医保的如果有需求再做
//    public GetOutpatientPayResult queryGJYiBaoPayResult(String hospitalCode, long regNo) {
//        RegisterListView rList = registerListViewMapper.selectByRegNo(regNo);
//        if (rList != null) {
//            // 已挂号
//        } else {
//            // 需要判断是不是医保的，医保的，调用queryGJYiBaoPayResult非医保的返回未结算
//        }
//    }

    @Override
    @Transactional
    public GetOutpatientPayResult queryYiBaoPayResult(String hospitalCode, long regNo, boolean push) {
        PreRegisterList preRegister = preRegisterListMapper.selectById(regNo);
        if (preRegister == null) {
            throw new RuntimeException("未预算，regNo: " + regNo);
        }
        Integer payway;
        RegOnlineGjYiBaoUploadFeeRecord feeRecord = regOnlineGjYiBaoUploadFeeRecordMapper.selectByRegNo(regNo + "");
        RegOnlineSHYiBaoUploadFeeRecord shFeeRecord = null;
        if (feeRecord == null) {
            shFeeRecord = regOnlineSHYiBaoUploadFeeRecordMapper.selectByRegNo(regNo + "");
            if (shFeeRecord == null) {
                throw new RuntimeException("这个不是线上挂号医保支付，regNo: " + regNo);
            } else {
                payway = shFeeRecord.getPayway();
            }
        } else {
            payway = feeRecord.getPayway();
        }
        RTPatientList patient = patientListService.getById(preRegister.getPatID());
        // 支付渠道 11-支付宝小程序 17-微信小程序 13-微信公众号
        PayChannelType payChannelType = null;
        switch (payway) {
            case 11:
                payChannelType = PayChannelType.ALIPAY_MINI_PROGRAM;
                break;
            case 17:
                payChannelType = PayChannelType.WECHAT_MINI_PROGRAM;
                break;
            case 13:
                payChannelType = PayChannelType.WECHAT_OFFICIAL_ACCOUNT;
                break;
            default:
                throw new RuntimeException("不支持的支付方式: regNo: " + preRegister.getRegNo() + ", payWay: " + payway);
        }
        LambdaQueryWrapper<AppointmentLockNum> appointmentWrapper = Wrappers.lambdaQuery();
        appointmentWrapper.eq(AppointmentLockNum::getSqh, StringUtils.trim(preRegister.getAppointmentNo()));
        AppointmentLockNum appointment = appointmentLockNumMapper.selectOne(appointmentWrapper);
        if (appointment == null) {
            throw new RuntimeException("没有预占号记录，regNo: " + regNo);
        }
        Integer schedulingId =
            schedulingMapper.selectSchedulingIdBySubjectidAndDutyDate(appointment.getSubjectID(),
                                                                      appointment.getDutyDate());
        if (feeRecord == null) {
            return handlerDqYiBao(hospitalCode, push, shFeeRecord, patient, payChannelType, preRegister, schedulingId);
        } else {
            return handlerGJYiBao(hospitalCode, push, feeRecord, patient, payChannelType, preRegister, schedulingId);
        }

    }

    /**
     * 处理国家医保支付结果
     * @param hospitalCode
     * @param push
     * @param feeRecord
     * @param patient
     * @param payChannelType
     * @param preRegister
     * @param schedulingId
     * @return
     */
    @NotNull
    private GetOutpatientPayResult handlerGJYiBao(String hospitalCode, boolean push, RegOnlineGjYiBaoUploadFeeRecord feeRecord,
                                                  RTPatientList patient, PayChannelType payChannelType, PreRegisterList preRegister, Integer schedulingId) {
        long regNo = feeRecord.getRegNo();
        GetOutpatientPayResult payResult = new GetOutpatientPayResult();
        payResult.setTrade_type("0");
        payResult.setOut_trade_no(regNo + "");
        payResult.setPay_order_id(feeRecord.getPayOrdId());

        Input6301 input6301 = new Input6301();
        input6301.setIdNo(patient.getCertificateNo());
        input6301.setUserName(patient.getPatName());
        input6301.setIdType("01");
        input6301.setOrgCodg(yiBaoProperties.getOrgCode());
        input6301.setPayOrdId(feeRecord.getPayOrdId());
        input6301.setPayToken(feeRecord.getPayToken());

        // 现在没有锁，在发请求前和发请求后都判断一次
        RegisterListView rList = registerListViewMapper.selectByRegNo(regNo);
        if (rList != null) {
            // 已挂号，不做后续处理
            payResult.setStatus("0");
            payResult.setCharge_time(TimeUtils.getHisDateStr(rList.getRegistTime()));
            return payResult;
        }

        Output6301 output6301 = InsuranceBeanUtils.getGJYiBaoClient().post6301(input6301, payChannelType, hospitalCode);
        log.info("医保支付结果: regNo: " + regNo + ", result: " + StandardObjectMapper.stringify(output6301));
        if (output6301 != null && ("4".equals(output6301.getOrdStas()) || "6".equals(output6301.getOrdStas()))) {
            // 现在没有锁，在发请求前和发请求后都判断一次
            rList = registerListViewMapper.selectByRegNo(regNo);
            if (rList != null) {
                // 已挂号，不做后续处理
                payResult.setStatus("0");
                payResult.setCharge_time(TimeUtils.getHisDateStr(rList.getRegistTime()));
                return payResult;
            }
            log.info("医保已支付: regNo: " + regNo);
//            saveRegOnlineGjYiBaoOrderInfo(patient, input6301, output6301, feeRecord);
            // 0	已保存
            // 1	预结算完成
            // 2	结算中
            // 3	自费完成
            // 4	医保支付完成
            // 5	院内结算完成
            // 6	结算完成
            // 7	已退款
            // 8	已医保全部退款
            // 9	仅自费全部退款
            // 10	仅自费部分退款
            // 11	医保全部退自费部分退款
            // 12	已撤销
            // 13	医保已撤销
            // 14	异常
            // 15	结算失败
            // 16	医保结算失败自费冲正失败
            ConfirmRegistReq req = new ConfirmRegistReq();
            req.setSettle_id(preRegister.getRegNo() + "");
            req.setRegno(preRegister.getRegNo() + "");
            req.setPatid(preRegister.getPatID() + "");
            req.setHospitalCode(hospitalCode);
            req.setLock_number_id(StringUtils.trim(preRegister.getAppointmentNo()));
            req.setScheduling_id(schedulingId + "");
            req.setTotal_amount(output6301.getFeeSumamt().multiply(new BigDecimal(100)).intValue() + "");
            req.setShould_pay_amount(output6301.getOwnPayAmt().multiply(new BigDecimal(100)).intValue() + "");
            req.setPay_amount(output6301.getOwnPayAmt().multiply(new BigDecimal(100)).intValue() + "");
            req.setPay_type(payChannelType.getValue());
            // 医保结算不是ih调用his，所以ih的订单id和流水号拿不到，这里保存挂号流水号
            req.setTrade_no(preRegister.getRegNo() + "");
            req.setSerial_no(output6301.getMedOrgOrd());
            req.setSelf_flag("0");
            req.setJsFlag(2);
            // 6301
            req.setOutput6301(output6301);
            ConfirmRegistResult registResult = confirmRegister(req, null);
            if (registResult != null && registResult.isSuccess()) {
                log.info("医保已支付，业务处理成功: regNo: " + regNo);
                payResult.setStatus("0");
                payResult.setCharge_time(TimeUtils.getHisDateStr(new Date()));
                if (push) {
                    log.info("医保已支付，推送挂号成功给IH: regNo: " + regNo);
                    msgPushService.pushRegister4010Succeeded(hospitalCode, regNo + "", feeRecord.getPayOrdId(), "", output6301.getTraceTime());
                }
            } else {
                log.error("医保结算成功业务处理失败: result: " + StandardObjectMapper.stringify(registResult));
                payResult.setStatus("2");
            }
        } else {
            log.info("医保未结算: regNo: " + regNo);
            payResult.setStatus("2");
        }

        return payResult;
    }


    /**
     * 处理国家医保支付结果
     * @param hospitalCode
     * @param push
     * @param shFeeRecord
     * @param patient
     * @param payChannelType
     * @param preRegister
     * @param schedulingId
     * @return
     */
    @NotNull
    private GetOutpatientPayResult handlerDqYiBao(String hospitalCode, boolean push, RegOnlineSHYiBaoUploadFeeRecord shFeeRecord,
                                                  RTPatientList patient, PayChannelType payChannelType, PreRegisterList preRegister, Integer schedulingId) {
        long regNo = shFeeRecord.getRegNo();
        GetOutpatientPayResult payResult = new GetOutpatientPayResult();
        payResult.setTrade_type("0");
        payResult.setOut_trade_no(regNo + "");
        payResult.setBill_no(shFeeRecord.getOrderNo());
        // 现在没有锁，在发请求前和发请求后都判断一次
        RegisterListView rList = registerListViewMapper.selectByRegNo(regNo);
        if (rList != null) {
            // 已挂号，不做后续处理
            payResult.setStatus("0");
            payResult.setCharge_time(TimeUtils.getHisDateStr(rList.getRegistTime()));
            return payResult;
        }

        SE04Response se04Response = InsuranceBeanUtils.getDQYiBaoClient().postSE04(shFeeRecord.getEcToken(), shFeeRecord.getCityId());
        if (se04Response != null) {
            // 现在没有锁，在发请求前和发请求后都判断一次
            rList = registerListViewMapper.selectByRegNo(regNo);
            if (rList != null) {
                // 已挂号，不做后续处理
                payResult.setStatus("0");
                payResult.setCharge_time(TimeUtils.getHisDateStr(rList.getRegistTime()));
                return payResult;
            }
            log.info("医保已支付: regNo: " + regNo);
            ConfirmRegistReq req = new ConfirmRegistReq();
            req.setSettle_id(preRegister.getRegNo() + "");
            req.setRegno(preRegister.getRegNo() + "");
            req.setPatid(preRegister.getPatID() + "");
            req.setHospitalCode(hospitalCode);
            req.setLock_number_id(StringUtils.trim(preRegister.getAppointmentNo()));
            req.setScheduling_id(schedulingId + "");
            SH02Response sh02Response = se04Response.getSh02Response();

            BigDecimal total = sh02Response.getTotalexpense().add(sh02Response.getFybjsfwfyze());
            BigDecimal self = sh02Response.getZfdxjzfs().add(sh02Response.getTcdxjzfs()).add(sh02Response.getFjdxjzfs())
                    .add(sh02Response.getFybjsfwfyze());

            req.setTotal_amount(total.multiply(new BigDecimal(100)).intValue() + "");
            req.setShould_pay_amount(self.multiply(new BigDecimal(100)).intValue() + "");
            req.setPay_amount(self.multiply(new BigDecimal(100)).intValue() + "");
            req.setPay_type(payChannelType.getValue());
            // 医保结算不是ih调用his，所以ih的订单id和流水号拿不到，这里保存挂号流水号
            req.setTrade_no(preRegister.getRegNo() + "");
            req.setSerial_no(preRegister.getRegNo() + "");
            req.setSelf_flag("0");
            req.setJsFlag(1);
            req.setSe04Response(se04Response);
            ConfirmRegistResult registResult = confirmRegister(req, shFeeRecord);
            if (registResult != null && registResult.isSuccess()) {
                log.info("医保已支付，业务处理成功: regNo: " + regNo);
                payResult.setStatus("0");
                payResult.setCharge_time(TimeUtils.getHisDateStr(new Date()));
                if (push) {
                    log.info("医保已支付，推送挂号成功给IH: regNo: " + regNo);
                    msgPushService.pushRegister4010Succeeded(hospitalCode, regNo + "", "", shFeeRecord.getOrderNo(),
                            TimeUtils.getHisDateStr(TimeUtils.convert(se04Response.getGmt())));
                }
            } else {
                log.error("医保结算成功业务处理失败: result: " + StandardObjectMapper.stringify(registResult));
                payResult.setStatus("2");
            }
        } else {
            log.info("医保未结算: regNo: " + regNo);
            payResult.setStatus("2");
        }

        return payResult;
    }


    /**
     * 国家医保挂号支付成功回调
     * @param callBack6302
     * @param hospitalCode
     * @param feeRecord
     */
    @Override
    @Transactional
    public void registerGJCallBackPaySuccess(CallBack6302 callBack6302, String hospitalCode, RegOnlineGjYiBaoUploadFeeRecord feeRecord) {
        Long regNo = feeRecord.getRegNo();
        // 现在没有锁，在发请求前和发请求后都判断一次
        RegisterListView rList = registerListViewMapper.selectByRegNo(regNo);
        if (rList != null) {
            // 已挂号，不做后续处理
            return;
        }

        PreRegisterList preRegister = preRegisterListMapper.selectById(regNo);
        RTPatientList patient = patientListService.getById(preRegister.getPatID());
        // 支付渠道 11-支付宝小程序 17-微信小程序 13-微信公众号
        PayChannelType payChannelType = null;
        switch (feeRecord.getPayway()) {
            case 11:
                payChannelType = PayChannelType.ALIPAY_MINI_PROGRAM;
                break;
            case 17:
                payChannelType = PayChannelType.WECHAT_MINI_PROGRAM;
                break;
            case 13:
                payChannelType = PayChannelType.WECHAT_OFFICIAL_ACCOUNT;
                break;
            default:
                throw new RuntimeException("不支持的支付方式: regNo: " + preRegister.getRegNo() + ", payWay: " + feeRecord.getPayway());
        }

        LambdaQueryWrapper<AppointmentLockNum> appointmentWrapper = Wrappers.lambdaQuery();
        appointmentWrapper.eq(AppointmentLockNum::getSqh, StringUtils.trim(preRegister.getAppointmentNo()));
        AppointmentLockNum appointment = appointmentLockNumMapper.selectOne(appointmentWrapper);
        if (appointment == null) {
            throw new RuntimeException("没有预占号记录，regNo: " + preRegister.getRegNo());
        }
        Integer schedulingId =
                schedulingMapper.selectSchedulingIdBySubjectidAndDutyDate(appointment.getSubjectID(),
                        appointment.getDutyDate());
        Input6301 input6301 = new Input6301();
        input6301.setIdNo(patient.getCertificateNo());
        input6301.setUserName(patient.getPatName());
        input6301.setIdType("01");
        input6301.setOrgCodg(yiBaoProperties.getOrgCode());
        input6301.setPayOrdId(feeRecord.getPayOrdId());
        input6301.setPayToken(feeRecord.getPayToken());
        Output6301 output6301 = new Output6301(callBack6302);
        output6301.setOrgCodg(yiBaoProperties.getOrgCode());

        saveRegOnlineGjYiBaoOrderInfo(patient, input6301, output6301, feeRecord);

        ConfirmRegistReq req = new ConfirmRegistReq();
        req.setSettle_id(preRegister.getRegNo() + "");
        req.setRegno(preRegister.getRegNo() + "");
        req.setPatid(preRegister.getPatID() + "");
        req.setHospitalCode(hospitalCode);
        req.setLock_number_id(StringUtils.trim(preRegister.getAppointmentNo()));
        req.setScheduling_id(schedulingId + "");
        req.setTotal_amount(callBack6302.getFeeSumamt().multiply(new BigDecimal(100)).intValue() + "");
        req.setShould_pay_amount(callBack6302.getOwnpayAmt().multiply(new BigDecimal(100)).intValue() + "");
        req.setPay_amount(callBack6302.getOwnpayAmt().multiply(new BigDecimal(100)).intValue() + "");
        req.setPay_type(payChannelType.getValue());
        // 医保结算不是ih调用his，所以ih的订单id和流水号拿不到，这里保存挂号流水号
        req.setTrade_no(preRegister.getRegNo() + "");
        req.setSerial_no(callBack6302.getMedOrgOrd());
        req.setSelf_flag("0");
        req.setOutput6301(output6301);
        req.setJsFlag(2);
        ConfirmRegistResult registResult = confirmRegister(req, null);
        GetOutpatientPayResult payResult = new GetOutpatientPayResult();
        payResult.setTrade_type("0");
        payResult.setOut_trade_no(feeRecord.getRegNo() + "");
        payResult.setPay_order_id(feeRecord.getPayOrdId());
        if (registResult != null && registResult.isSuccess()) {
            log.info("医保已支付，业务处理成功: regNo: " + regNo);
            payResult.setStatus("0");
            payResult.setCharge_time(TimeUtils.getHisDateStr(new Date()));
            log.info("医保已支付，推送挂号成功给IH: regNo: " + regNo);
            msgPushService.pushRegister4010Succeeded(hospitalCode, regNo + "", feeRecord.getPayOrdId(), "", output6301.getTraceTime());
        } else {
            log.error("医保结算成功业务处理失败: result: " + StandardObjectMapper.stringify(registResult));
            payResult.setStatus("2");
        }
    }

    /**
     * 地区医保挂号支付成功回调
     * @param callBackSH03
     * @param hospitalCode
     * @param shFeeRecord
     */
    @Transactional
    @Override
    public void registerSHCallBackPaySuccess(CallBackSH03 callBackSH03, String hospitalCode, RegOnlineSHYiBaoUploadFeeRecord shFeeRecord) {
        Long regNo = shFeeRecord.getRegNo();
        // 现在没有锁，在发请求前和发请求后都判断一次
        RegisterListView rList = registerListViewMapper.selectByRegNo(regNo);
        if (rList != null) {
            // 已挂号，不做后续处理
            return;
        }

        PreRegisterList preRegister = preRegisterListMapper.selectById(regNo);
        RTPatientList patient = patientListService.getById(preRegister.getPatID());
        // 支付渠道 11-支付宝小程序 17-微信小程序 13-微信公众号
        PayChannelType payChannelType = null;
        switch (shFeeRecord.getPayway()) {
            case 11:
                payChannelType = PayChannelType.ALIPAY_MINI_PROGRAM;
                break;
            case 17:
                payChannelType = PayChannelType.WECHAT_MINI_PROGRAM;
                break;
            case 13:
                payChannelType = PayChannelType.WECHAT_OFFICIAL_ACCOUNT;
                break;
            default:
                throw new RuntimeException("不支持的支付方式: regNo: " + preRegister.getRegNo() + ", payWay: " + shFeeRecord.getPayway());
        }

        LambdaQueryWrapper<AppointmentLockNum> appointmentWrapper = Wrappers.lambdaQuery();
        appointmentWrapper.eq(AppointmentLockNum::getSqh, StringUtils.trim(preRegister.getAppointmentNo()));
        AppointmentLockNum appointment = appointmentLockNumMapper.selectOne(appointmentWrapper);
        if (appointment == null) {
            throw new RuntimeException("没有预占号记录，regNo: " + preRegister.getRegNo());
        }
        Integer schedulingId =
                schedulingMapper.selectSchedulingIdBySubjectidAndDutyDate(appointment.getSubjectID(),
                        appointment.getDutyDate());

        GetOutpatientPayResult payResult = new GetOutpatientPayResult();
        payResult.setTrade_type("0");
        payResult.setOut_trade_no(regNo + "");
        payResult.setBill_no(shFeeRecord.getOrderNo());

        if (callBackSH03 != null && "1".equals(callBackSH03.getPayStatus())) {
            // 现在没有锁，在发请求前和发请求后都判断一次
            rList = registerListViewMapper.selectByRegNo(regNo);
            if (rList != null) {
                // 已挂号，不做后续处理
                payResult.setStatus("0");
                payResult.setCharge_time(TimeUtils.getHisDateStr(rList.getRegistTime()));
                return;
            }
            log.info("医保已支付: regNo: " + regNo);
            ConfirmRegistReq req = new ConfirmRegistReq();
            req.setSettle_id(preRegister.getRegNo() + "");
            req.setRegno(preRegister.getRegNo() + "");
            req.setPatid(preRegister.getPatID() + "");
            req.setHospitalCode(hospitalCode);
            req.setLock_number_id(StringUtils.trim(preRegister.getAppointmentNo()));
            req.setScheduling_id(schedulingId + "");
            SH02Response sh02Response = callBackSH03.getSh02Response();

            BigDecimal total = sh02Response.getTotalexpense().add(sh02Response.getFybjsfwfyze());
            BigDecimal self = sh02Response.getZfdxjzfs().add(sh02Response.getTcdxjzfs()).add(sh02Response.getFjdxjzfs())
                    .add(sh02Response.getFybjsfwfyze());

            req.setTotal_amount(total.multiply(new BigDecimal(100)).intValue() + "");
            req.setShould_pay_amount(self.multiply(new BigDecimal(100)).intValue() + "");
            req.setPay_amount(self.multiply(new BigDecimal(100)).intValue() + "");
            req.setPay_type(payChannelType.getValue());

            // 医保结算不是ih调用his，所以ih的订单id和流水号拿不到，这里保存挂号流水号
            req.setTrade_no(preRegister.getRegNo() + "");
            req.setSerial_no(preRegister.getRegNo() + "");
            req.setSelf_flag("0");
            req.setJsFlag(1);
            req.setSe04Response(callBackSH03);
            ConfirmRegistResult registResult = confirmRegister(req, shFeeRecord);
            if (registResult != null && registResult.isSuccess()) {
                log.info("医保已支付，业务处理成功: regNo: " + regNo);
                payResult.setStatus("0");
                payResult.setCharge_time(TimeUtils.getHisDateStr(new Date()));
                log.info("医保已支付，推送挂号成功给IH: regNo: " + regNo);
                msgPushService.pushRegister4010Succeeded(hospitalCode, regNo + "", shFeeRecord.getOrderNo(), "",
                        TimeUtils.getHisDateStr(TimeUtils.convert(callBackSH03.getGmt())));
            } else {
                log.error("医保结算成功业务处理失败: result: " + StandardObjectMapper.stringify(registResult));
                payResult.setStatus("2");
            }
        } else {
            log.info("医保未结算: regNo: " + regNo);
            payResult.setStatus("2");
        }
    }



    /**
     * 保存医保结算数据
     * @param patient
     * @param input6301
     * @param output6301
     * @param feeRecord
     */
    @Transactional(propagation = Propagation.REQUIRES_NEW)
    public void saveRegOnlineGjYiBaoOrderInfo(RTPatientList patient, Input6301 input6301, Output6301 output6301,
                                                                     RegOnlineGjYiBaoUploadFeeRecord feeRecord) {
        LambdaQueryWrapper<RegOnlineGjYiBaoOrderInfo> wrapper = Wrappers.lambdaQuery();
        wrapper.eq(RegOnlineGjYiBaoOrderInfo::getPayOrdId, output6301.getPayOrdId());

        RegOnlineGjYiBaoOrderInfo gjYiBaoOrderInfo = regOnlineGjYiBaoOrderInfoMapper.selectOne(wrapper);
        if (gjYiBaoOrderInfo != null) {
            return;
        }
        gjYiBaoOrderInfo = new RegOnlineGjYiBaoOrderInfo();
        gjYiBaoOrderInfo.setCreatedDate(new Date());
        gjYiBaoOrderInfo.setPatName(patient.getPatName());
        gjYiBaoOrderInfo.setCardType(input6301.getIdType());
        gjYiBaoOrderInfo.setCardNo(input6301.getIdNo());
        gjYiBaoOrderInfo.setChargeNo(feeRecord.getChargeNo());
        gjYiBaoOrderInfo.setRegNo(feeRecord.getRegNo());
        gjYiBaoOrderInfo.setFlag(feeRecord.getFlag());
        gjYiBaoOrderInfo.setHospitalCode(patient.getHospitalCode() + "");
        gjYiBaoOrderInfo.setOrdStas(output6301.getOrdStas());
        gjYiBaoOrderInfo.setPayOrdId(output6301.getPayOrdId());
        gjYiBaoOrderInfo.setCallType(output6301.getCallType());
        gjYiBaoOrderInfo.setMedOrgOrd(output6301.getMedOrgOrd());
        gjYiBaoOrderInfo.setTraceTime(output6301.getTraceTime());
        gjYiBaoOrderInfo.setOrgCode(output6301.getOrgCodg());
        gjYiBaoOrderInfo.setOrgName(output6301.getOrgName());
        gjYiBaoOrderInfo.setSetlType(output6301.getSetlType());
        gjYiBaoOrderInfo.setFeeSumamt(output6301.getFeeSumamt());
        gjYiBaoOrderInfo.setOwnPayAmt(output6301.getOwnPayAmt());
        gjYiBaoOrderInfo.setPsnAcctPay(output6301.getPsnAcctPay());
        gjYiBaoOrderInfo.setFundPay(output6301.getFundPay());
        gjYiBaoOrderInfo.setRevsToken(output6301.getRevsToken());
        gjYiBaoOrderInfo.setExtData(StandardObjectMapper.stringify(output6301.getExtData()));
        gjYiBaoOrderInfo.setDeposit(new BigDecimal(0));
        gjYiBaoOrderInfo.setHiChrgTime(output6301.getHiChrgTime());
        gjYiBaoOrderInfo.setHiDocSn(output6301.getHiDocSn());
        gjYiBaoOrderInfo.setHiRgstSn(output6301.getHiRgstSn());
        if (output6301.getExtData() != null) {
            gjYiBaoOrderInfo.setSetlId(output6301.getExtData().getSetlinfo().getSetlId());
            gjYiBaoOrderInfo.setMdtrtId(output6301.getExtData().getSetlinfo().getMdtrtId());
            gjYiBaoOrderInfo.setPsnNo(output6301.getExtData().getSetlinfo().getPsnNo());
            gjYiBaoOrderInfo.setInsuplcAdmdvs(output6301.getExtData().getInsuplcAdmdvs());
        }
        regOnlineGjYiBaoOrderInfoMapper.insert(gjYiBaoOrderInfo);
    }

    /**
     * 更新指定挂号的就诊号
     * @param regNo 挂号序号
     * @param deptId 科室编码
     * @param doctorId 医生编码
     */
    @Override
    public void processRegistrationUpdate(Long regNo, Integer deptId, Integer doctorId) {
        long ghxh; // 对应存储过程中的 @ghxh
        LocalDateTime registTimeStartBoundary;
        LocalDate today = LocalDate.now(); // 获取当前日期
        // 这部分非常关键，取决于你SP片段中缺失的IF条件
        // if (/* 你SP中 "begin set @ghxh..." 之前的某个条件 */) {
        // 例如，如果原始SP的IF是基于当前时间：
        if (LocalTime.now().isAfter(LocalTime.NOON)) { // 例子：如果当前时间是中午之后
            registTimeStartBoundary = LocalDateTime.of(today, LocalTime.NOON); // yyyy-MM-dd 12:00:00
        } else {
            registTimeStartBoundary = LocalDateTime.of(today, LocalTime.MIDNIGHT); // yyyy-MM-dd 00:00:00
        }

        QueryWrapper<RegisterListTime> countWrapper = new QueryWrapper<>();
        countWrapper.ge("Registtime", registTimeStartBoundary) // Registtime >= ?
            .eq("GhDoctor", doctorId)                  // and GhDoctor = ?
            .eq("DeptID", deptId)                      // and DeptID = ?
            .le("RegNo", regNo)                        // and RegNo <= ? (假设RegNo可以按字典序比较，或者它是数字型的字符串)
            .le("Status", 80);                         // and Status <= 80
        // selectCount 返回 Long，所以 ISNULL(...,0) 的逻辑通过初始化 ghxh 或检查 null 来处理
        Long countResult = registerListTimeMapper.selectCount(countWrapper);
        ghxh = (countResult != null) ? countResult : 0L;
        log.info("regNo: {}, 就诊号为：{}", regNo, ghxh);
        RegisterListTime registerListTime = registerListTimeMapper.selectById(regNo);
        registerListTime.setRegistNum(registerListTime.getRegistOrder());
        registerListTime.setRegistOrder(Math.toIntExact(ghxh));
        registerListTimeMapper.updateById(registerListTime);
    }

    /**
     * 证件类型his->医保
     */
    private String certificateTypeMapping(String certificateType) {
        if (certificateType == null) {
            return "01";
        }
        switch (certificateType) {
            case "1":
            case "2":
                return "01";
            case "3":
                return "08";
            case "4":
                return "02";
            case "6":
                return "04";
            case "7":
                return "06";
            case "5":
            default:
                return "99";
        }
    }

    @Override
    public List<PatientRegistInfo> getRegistInfos(PatientRegistInfoReq req) {
        Date convertDate = TimeUtils.convert(req.getBegin_date());
        Date initDate = new Date(0);
        if (convertDate != null && (initDate.getTime() - convertDate.getTime() <= 0) ) {
            req.setBeginDate(convertDate);
            req.setEndDate(TimeUtils.convert(req.getEnd_date()));
        }
        List<PatientRegistInfo> registers = registerListTimeMapper.selectRegisterInfos(req);
        registers.forEach(register -> {
            Date time = TimeUtils.convert(register.getVisit_time());
            register.setVisit_time(TimeUtils.dateStringFormat(time, "yyyyMMdd"));
            register.setVisit_time_span(TimeUtils.dateStringFormat(time, "HH:mm"));
        });
        return registers;
    }
    private String matchType(String typeStr) {
        if (org.apache.commons.lang.StringUtils.isEmpty(typeStr)) {
            return "";
        }
        if (typeStr.contains("普通")) {
            return "01";
        }
        if (typeStr.contains("主任")) {
            return "03";
        }
        if (typeStr.contains("急诊")) {
            return "05";
        }
        return "";
    }
}
