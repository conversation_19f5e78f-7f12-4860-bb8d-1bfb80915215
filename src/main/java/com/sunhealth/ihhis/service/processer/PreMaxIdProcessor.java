package com.sunhealth.ihhis.service.processer;

import com.sunhealth.ihhis.dao.his.PreMaxIdMapper;
import com.sunhealth.ihhis.model.entity.sequences.PreMaxId;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Component;

/**
 * 预交金流水号生成器
 */
@Component
@AllArgsConstructor
public class PreMaxIdProcessor {

    private final PreMaxIdMapper preMaxIdMapper;

    public Long execute() {
        PreMaxId preMaxId = new PreMaxId(0);
        preMaxIdMapper.insert(preMaxId);
        return preMaxId.getId();
    }

}
