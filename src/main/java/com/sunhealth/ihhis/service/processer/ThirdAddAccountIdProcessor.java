package com.sunhealth.ihhis.service.processer;

import com.sunhealth.ihhis.dao.his.SequencesMzNoMapper;
import com.sunhealth.ihhis.dao.his.SequencesThirdAddAccountIdMapper;
import com.sunhealth.ihhis.model.entity.sequences.SequencesMzNo;
import com.sunhealth.ihhis.model.entity.sequences.SequencesThirdAddAccountId;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Component;

@Component
@AllArgsConstructor
public class ThirdAddAccountIdProcessor {

    private final SequencesThirdAddAccountIdMapper sequencesThirdAddAccountIdMapper;

    public Long execute() {
        SequencesThirdAddAccountId sequencesMzNo = new SequencesThirdAddAccountId(1);
        sequencesThirdAddAccountIdMapper.insert(sequencesMzNo);
        return sequencesMzNo.getId();
    }
}
