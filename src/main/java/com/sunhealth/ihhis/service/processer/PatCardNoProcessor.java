package com.sunhealth.ihhis.service.processer;

import com.sunhealth.ihhis.dao.his.YBJSKHIDMapper;
import com.sunhealth.ihhis.model.entity.sequences.TbtRecipeYBJSKHID;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Component;

@Component
@AllArgsConstructor
public class PatCardNoProcessor {

    private final YBJSKHIDMapper recipeYBJSKHIDMapper;

    // 获取自费卡卡号
    public String execute() {
        String cardNo = "";
        //插入KHID
        TbtRecipeYBJSKHID khid = new TbtRecipeYBJSKHID();
        khid.setZValue(0);
        recipeYBJSKHIDMapper.insert(khid);

        cardNo = cardNoFormat(khid.getLsh());
        return cardNo;
    }

    private String cardNoFormat(int lsh) {
        return "KH" + String.format("%09d", lsh);
    }
}

