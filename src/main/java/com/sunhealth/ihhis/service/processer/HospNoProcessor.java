package com.sunhealth.ihhis.service.processer;

import com.sunhealth.ihhis.dao.his.SequencesHospNoMapper;
import com.sunhealth.ihhis.model.entity.sequences.SequencesHospNo;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

@Component
@AllArgsConstructor
public class HospNoProcessor {

    private final SequencesHospNoMapper sequencesHospNoMapper;

    @Transactional
    public Long execute() {
        SequencesHospNo sequencesHospNo = new SequencesHospNo(1,1);
        sequencesHospNoMapper.insert(sequencesHospNo);
        return sequencesHospNo.getId();
    }
}
