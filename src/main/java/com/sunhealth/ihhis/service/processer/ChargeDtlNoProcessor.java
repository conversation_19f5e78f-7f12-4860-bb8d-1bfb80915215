package com.sunhealth.ihhis.service.processer;

import com.sunhealth.ihhis.dao.his.SequencesChargeDetlMapper;
import com.sunhealth.ihhis.model.entity.sequences.SequencesChargeDetl;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

@Component
@AllArgsConstructor
public class ChargeDtlNoProcessor {
    private final SequencesChargeDetlMapper sequencesChargeDetlMapper;

    @Transactional
    public Long execute() {
        SequencesChargeDetl sequencesMzNo = new SequencesChargeDetl(1);
        sequencesChargeDetlMapper.insert(sequencesMzNo);
        return sequencesMzNo.getId();
    }

    public Long executeInsurance() {
        return sequencesChargeDetlMapper.getInsuranceSequencesSerialNo();
    }
}
