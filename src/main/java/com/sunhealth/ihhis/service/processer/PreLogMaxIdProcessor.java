package com.sunhealth.ihhis.service.processer;

import com.sunhealth.ihhis.dao.his.PreLogMaxIdMapper;
import com.sunhealth.ihhis.model.entity.sequences.PreLogMaxId;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Component;

/**
 * 预交金日志流水号生成器
 */
@Component
@AllArgsConstructor
public class PreLogMaxIdProcessor {

    private final PreLogMaxIdMapper preLogMaxIdMapper;

    public Long execute() {
        PreLogMaxId preMaxId = new PreLogMaxId(0);
        preLogMaxIdMapper.insert(preMaxId);
        return preMaxId.getId();
    }

}
