package com.sunhealth.ihhis.service.processer;

import com.sunhealth.ihhis.dao.his.SequencesInvoiceIDMapper;
import com.sunhealth.ihhis.model.entity.sequences.SequencesInvoiceID;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Component;

@Component
@AllArgsConstructor
public class RegInvoiceIDProcessor {

    private final SequencesInvoiceIDMapper sequencesInvoiceIDMapper;

    public Long execute() {
        SequencesInvoiceID sequencesInvoiceID = new SequencesInvoiceID(1);
        sequencesInvoiceIDMapper.insert(sequencesInvoiceID);
        return sequencesInvoiceID.getId();
    }


}
