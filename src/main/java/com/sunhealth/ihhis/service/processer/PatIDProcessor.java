package com.sunhealth.ihhis.service.processer;

import com.sunhealth.ihhis.dao.his.SequencesPatIdMapper;
import com.sunhealth.ihhis.model.entity.sequences.SequencesPatId;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Component;

@Component
@AllArgsConstructor
public class PatIDProcessor {

    private final SequencesPatIdMapper sequencesPatIdMapper;

    public Long execute() {
        SequencesPatId sequencesPatId = new SequencesPatId(1);
        sequencesPatIdMapper.insert(sequencesPatId);
        return sequencesPatId.getId();
    }
}
