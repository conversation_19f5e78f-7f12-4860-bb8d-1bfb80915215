package com.sunhealth.ihhis.service.processer;

import com.sunhealth.ihhis.dao.his.SequencesRegDtlNoMapper;
import com.sunhealth.ihhis.model.entity.sequences.SequencesRegDtlNo;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Component;

@Component
@AllArgsConstructor
public class RegDtlNoProcessor {

    private final SequencesRegDtlNoMapper sequencesRegDtlNoMapper;

    public Long execute() {
        SequencesRegDtlNo sequencesRegDtlNo = new SequencesRegDtlNo(1);
        sequencesRegDtlNoMapper.insert(sequencesRegDtlNo);
        return sequencesRegDtlNo.getId();
    }
}
