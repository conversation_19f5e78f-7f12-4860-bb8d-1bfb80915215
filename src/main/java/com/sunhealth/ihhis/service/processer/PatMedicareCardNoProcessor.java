package com.sunhealth.ihhis.service.processer;

import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.sunhealth.ihhis.dao.his.YBJSKHIDMapper;
import com.sunhealth.ihhis.dao.his.YBJSPatSfzToKHMapper;
import com.sunhealth.ihhis.model.entity.sequences.TbtRecipeYBJSKHID;
import com.sunhealth.ihhis.model.entity.sequences.TbtRecipeYBJSPatSfzToKH;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Component;

import java.util.Date;

@Component
@AllArgsConstructor
public class PatMedicareCardNoProcessor {

    private final YBJSKHIDMapper recipeYBJSKHIDMapper;
    private final YBJSPatSfzToKHMapper patSfzToKHMapper;

    // 生成医保卡号
    public String execute(String certificateNo) {
        String cardNo = "";

        cardNo = patSfzToKHMapper.getKHBySfz(certificateNo);
        if (StringUtils.isNotEmpty(cardNo)) {
            return cardNo;
        }

        //插入KHID
        TbtRecipeYBJSKHID khid = new TbtRecipeYBJSKHID();
        khid.setZValue(0);
        recipeYBJSKHIDMapper.insert(khid);

        cardNo = cardNoFormat(khid.getLsh());
        //插入Tbt_Recipe_YBJS_PatSfzToKH
        TbtRecipeYBJSPatSfzToKH patSfzToKH = new TbtRecipeYBJSPatSfzToKH();
        patSfzToKH.setPatSfz(certificateNo);
        patSfzToKH.setKh(cardNo);
        patSfzToKH.setWriteTime(new Date());
        patSfzToKHMapper.insert(patSfzToKH);
        return cardNo;
    }

    private String cardNoFormat(int lsh) {
        return "KH" + String.format("%07d", lsh);
    }
}

