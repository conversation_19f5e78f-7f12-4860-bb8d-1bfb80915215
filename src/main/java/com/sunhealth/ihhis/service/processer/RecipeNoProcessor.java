package com.sunhealth.ihhis.service.processer;

import com.sunhealth.ihhis.dao.his.SequencesMzNoMapper;
import com.sunhealth.ihhis.dao.his.SequencesRecipeNoMapper;
import com.sunhealth.ihhis.model.entity.sequences.SequencesMzNo;
import com.sunhealth.ihhis.model.entity.sequences.SequencesRecipeNo;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Component;

@Component
@AllArgsConstructor
public class RecipeNoProcessor {

    private final SequencesRecipeNoMapper sequencesRecipeNoMapper;

    public Long execute() {
        SequencesRecipeNo sequencesMzNo = new SequencesRecipeNo(1);
        sequencesRecipeNoMapper.insert(sequencesMzNo);
        return sequencesMzNo.getId();
    }
}
