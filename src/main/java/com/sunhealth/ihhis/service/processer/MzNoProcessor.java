package com.sunhealth.ihhis.service.processer;

import com.sunhealth.ihhis.dao.his.SequencesMzNoMapper;
import com.sunhealth.ihhis.model.entity.sequences.SequencesMzNo;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Component;

@Component
@AllArgsConstructor
public class MzNoProcessor {

    private final SequencesMzNoMapper sequencesMzNoMapper;

    public Long execute() {
        SequencesMzNo sequencesMzNo = new SequencesMzNo(1);
        sequencesMzNoMapper.insert(sequencesMzNo);
        return sequencesMzNo.getId();
    }
}
