package com.sunhealth.ihhis.service.processer;

import com.sunhealth.ihhis.dao.his.SequencesPreChargeNoMapper;
import com.sunhealth.ihhis.dao.his.SequencesRegDtlNoMapper;
import com.sunhealth.ihhis.model.entity.sequences.SequencesPreChargeNo;
import com.sunhealth.ihhis.model.entity.sequences.SequencesRegDtlNo;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Component;

@Component
@AllArgsConstructor
public class PreChargeNoProcessor {

    private final SequencesPreChargeNoMapper sequencesPreChargeNoMapper;

    public Long execute() {
        SequencesPreChargeNo sequencesPreChargeNo = new SequencesPreChargeNo(1);
        sequencesPreChargeNoMapper.insert(sequencesPreChargeNo);
        return sequencesPreChargeNo.getId();
    }
}
