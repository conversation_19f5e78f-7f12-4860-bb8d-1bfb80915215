package com.sunhealth.ihhis.service.processer;

import com.sunhealth.ihhis.dao.his.SequencesSerialNoMapper;
import com.sunhealth.ihhis.model.entity.sequences.SequencesSerialNo;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Component;

@Component
@AllArgsConstructor
public class RegSerialNoProcessor {

    private final SequencesSerialNoMapper sequencesSerialNoMapper;

    public Long execute() {
        SequencesSerialNo sequencesSerialNo = new SequencesSerialNo(1);
        sequencesSerialNoMapper.insert(sequencesSerialNo);
        return sequencesSerialNo.getId();
    }

    public Long executeInsurance() {
        return sequencesSerialNoMapper.getInsuranceSequencesSerialNo();
    }

}
