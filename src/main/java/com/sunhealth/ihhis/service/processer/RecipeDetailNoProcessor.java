package com.sunhealth.ihhis.service.processer;

import com.sunhealth.ihhis.dao.his.SequencesRecipeDetailNoMapper;
import com.sunhealth.ihhis.model.entity.sequences.SequencesRecipeDetailNo;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Component;

@Component
@AllArgsConstructor
public class RecipeDetailNoProcessor {

    private final SequencesRecipeDetailNoMapper sequencesRecipeDetailNoMapper;

    public Long execute() {
        SequencesRecipeDetailNo sequencesMzNo = new SequencesRecipeDetailNo(0);
        sequencesRecipeDetailNoMapper.insertRecipeDetail(sequencesMzNo);
        return sequencesMzNo.getId();
    }
}
