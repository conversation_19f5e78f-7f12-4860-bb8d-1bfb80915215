package com.sunhealth.ihhis.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.sunhealth.ihhis.model.dto.patient.PatientCardCreateReq;
import com.sunhealth.ihhis.model.dto.patient.PatientCardCreateRes;
import com.sunhealth.ihhis.model.entity.patient.RTPatientCard;

public interface PatientCardService extends IService<RTPatientCard> {

    RTPatientCard getByPatId(String patId);

    /**
     * 创建就诊卡
     */
    PatientCardCreateRes createPatientCard(PatientCardCreateReq request);

    void createMedicareCard(String certificateNo, Integer hospitaoCode);
}
