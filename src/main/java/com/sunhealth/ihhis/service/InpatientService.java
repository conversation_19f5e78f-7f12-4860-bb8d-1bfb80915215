package com.sunhealth.ihhis.service;

import com.sunhealth.ihhis.model.dto.inpatient.*;

import java.util.List;

public interface InpatientService {

    /**
     * 查询患者住院就诊信息
     * @param req
     * @return
     */
    List<InpatientRecordRes> getInpatientRecord(InpatientRecordReq req);

    /**
     * 查询住院患者预交金明细
     * @param req
     * @return
     */
    List<AdvanceChargeDetailRes> getInpatientAdvanceChargeDetail(AdvanceChargeDetailReq req);

    /**
     * 住院预交金预充值
     * @param req
     * @return
     */
    HospCardPreChargeRes inpatientHospCardPreCharge(HospCardPreChargeReq req);

    /**
     * 住院预交金充值
     * @param req
     * @return
     */
    HospCardChargeRes inpatientHospCardCharge(HospCardChargeReq req);
}
