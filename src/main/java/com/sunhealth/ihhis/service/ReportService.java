package com.sunhealth.ihhis.service;

import com.sunhealth.ihhis.model.dto.report.LisReport;
import com.sunhealth.ihhis.model.dto.report.LisReportResult;
import com.sunhealth.ihhis.model.dto.report.ReportListReq;
import com.sunhealth.ihhis.model.dto.report.ReportResultReq;
import com.sunhealth.ihhis.model.dto.report.RisReport;
import com.sunhealth.ihhis.model.dto.report.RisReportResult;
import java.util.List;

public interface ReportService  {


    List<RisReport> getRisReportsByPatId(ReportListReq req);


    List<RisReportResult> getRisReportResult(ReportResultReq req);



    List<LisReport> getLisReportsByPatId(ReportListReq req);


    List<LisReportResult> getLisReportResult(ReportResultReq req);

}
