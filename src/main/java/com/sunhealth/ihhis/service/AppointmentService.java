package com.sunhealth.ihhis.service;


import com.sunhealth.ihhis.model.dto.register.CancelAppointmentReq;
import com.sunhealth.ihhis.model.dto.register.LockNumberResult;
import com.sunhealth.ihhis.model.dto.register.PreRegisterReq;
import com.sunhealth.ihhis.model.dto.register.SaveAppointmentReq;
import com.sunhealth.ihhis.model.dto.register.SaveAppointmentResult;
import org.springframework.transaction.annotation.Transactional;

public interface AppointmentService {

    /**
     * 门诊预约登记
     *
     * @param req
     * @return
     */
    SaveAppointmentResult appointment(SaveAppointmentReq req);

    LockNumberResult returnAppointment(CancelAppointmentReq req);
}
