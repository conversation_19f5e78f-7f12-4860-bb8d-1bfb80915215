package com.sunhealth.ihhis.service;

import com.fasterxml.jackson.core.type.TypeReference;
import com.google.common.collect.Maps;
import com.sunhealth.ihhis.config.HisHospitalProperties;
import com.sunhealth.ihhis.error.ErrorType;
import com.sunhealth.ihhis.model.dto.patient.SendPatientInfoRes;
import com.sunhealth.ihhis.model.vm.PushInpatientGuanKongMessageParam;
import com.sunhealth.ihhis.model.vm.PushInvoiceMessageParam;
import com.sunhealth.ihhis.utils.StandardObjectMapper;
import com.sunhealth.ihhis.nodered.data.NodeRedResponse;
import com.sunhealth.ihhis.utils.OkHttpUtils;
import com.sunhealth.ihhis.utils.UrlUtils;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import okhttp3.Headers;
import okhttp3.Response;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import java.io.IOException;
import java.util.Map;
import java.util.Objects;

@Slf4j
@Component
@AllArgsConstructor
public class HisAppApiClient {

    private final HisHospitalProperties hisHospitalProperties;
    /**
     * 推送电子发票号给his应用生成电子发票
     */
    public void pushInvoiceInfo(Map<String, Object> data) {
        String path = "/Registration/Invoice/CreateOrReturnInvoice";
        Map<String, Object> body = Maps.newHashMap();
        body.put("Keys", data);
        postForHis(path, null, body, null);
    }

    /**
     * 调用his接口刷新住院患者预交金管控状态
     */
    public void pushInpatientFlushGuanKong(Map<String, Object> data) {
        String path = "/io/account/patientControl/refresh";
        Map<String, Object> body = Maps.newHashMap();
        body.put("Keys", data);
        postForHis(path, null, body, null);
    }

    public void pushInpatientFlushGuanKong(PushInpatientGuanKongMessageParam param) {
        Map<String, Object> params =  Maps.newHashMap();
        params.put("regNoArr", param.getRegNoArr());
        params.put("hospId", param.getHospId());
        params.put("workerId", param.getWorkerId());
        pushInpatientFlushGuanKong(params);
    }

    public void pushInvoiceMessage(PushInvoiceMessageParam param) {
        Map<String, Object> params =  Maps.newHashMap();
        params.put("InvoiceId", param.getInvoiceId());
        params.put("InvoiceType", param.getInvoiceType());
        params.put("Flag", param.getFlag());
        params.put("HospCode", param.getHospitalCode());
        params.put("OpCode", param.getOpCode());
        if (StringUtils.isNotBlank(param.getInvoiceInfo())) {
            params.put("InvoiceInfo", param.getInvoiceInfo());
        }
        pushInvoiceInfo(params);
    }
    public SendPatientInfoRes sendPatientInfo(Map<String, String> params) {
       return getForWJWByUrl(hisHospitalProperties.getWjwHisApiUrl(), params, null, new TypeReference<SendPatientInfoRes>() {
        });
    }


    private <T> T postForHis(String path, Map<String, String> headers,
                             Object body, TypeReference<T> returnTypeReference){
        if (StringUtils.isBlank(hisHospitalProperties.getHisApiBaseUrl())) {
            log.info("未配置hisApiBaseUrl");
            return null;
        }
        String url = UrlUtils.concatSegments(hisHospitalProperties.getHisApiBaseUrl(), path);
        if (headers == null) {
            headers = Maps.newHashMap();
        }
        String bodyStr = null;
        if (body != null) {
            bodyStr = (body instanceof String) ? (String) body : StandardObjectMapper.stringify(body);
        }
        log.info("调用his服务 接口:url:{},headers:{},params:{},body:{}", url, headers, null, body);
        try (Response response = OkHttpUtils.post(url, Headers.of(headers), null, bodyStr)) {
            if (response.isSuccessful()) {
                if (response.body() == null) {
                    return null;
                }
                if (returnTypeReference == null) {
                    return null;
                }
                String responseString = OkHttpUtils.getResponseBody(response).orElse(null);
                log.info("调用his服务 接口 返回：{}",responseString);
                NodeRedResponse nodeRedResponse = StandardObjectMapper.readValue(responseString, new TypeReference<NodeRedResponse>() {});
                if (nodeRedResponse.isSuccess()) {
                    Object data = nodeRedResponse.getData();
                    if (Objects.isNull(data)) {
                        return null;
                    }
                    return StandardObjectMapper.getInstance().convertValue(data, returnTypeReference);
                } else {
                    log.info("his服务调用成功-但返回了业务错误：[code]: " + nodeRedResponse.getCode() + " [msg]:" + nodeRedResponse.getMessage());
                    throw new IOException(nodeRedResponse.getMessage());
                }
            } else {
                log.info("his服务调用失败：" + response.body().string());
                throw new Exception(response.body().string());
            }
        } catch (Exception e) {
            log.error("his服务调用失败", e);
            throw ErrorType.INTERNAL_SERVER_ERROR.toProblem(e.getMessage());
        }
    }

    private <T> T getForWJWByUrl(String url, Map<String, String> params,
                                 Map<String, String> headers,TypeReference<T> returnTypeReference){
        if (StringUtils.isBlank(hisHospitalProperties.getWjwHisApiUrl())) {
            log.info("未配置wjwHisApiUrl");
            return null;
        }
        if (headers == null) {
            headers = Maps.newHashMap();
        }

        log.info("调用wjwhis服务 接口:url:{},params:{},headers:{}", url, params,headers);
        try (Response response = OkHttpUtils.get(url, params, Headers.of(headers))){
            if (response.isSuccessful()) {
                if (response.body() == null) {
                    log.info("调用wjwhis服务 response.body() 为null");
                    return null;
                }
                if (returnTypeReference == null) {
                    return null;
                }
                String responseString = OkHttpUtils.getResponseBody(response).orElse(null);
                log.info("wjw调用his服务 接口 返回：{}",responseString);
                return StandardObjectMapper.readValue(responseString, returnTypeReference);
            } else {
                log.info("wjwhis服务调用失败：" + response.body().string());
                return null;
            }
        } catch (Exception e) {
            log.error("wjwhis服务调用失败", e);
            return null;
        }
    }
}
