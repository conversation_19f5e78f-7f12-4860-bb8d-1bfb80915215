package com.sunhealth.ihhis.service;

import com.sunhealth.ihhis.model.dto.outpatientcharge.ConfirmChargeReq;
import com.sunhealth.ihhis.model.dto.register.ConfirmRegistReq;
import com.sunhealth.ihhis.model.entity.charge.ChargeDetailTime;
import com.sunhealth.ihhis.model.entity.charge.ChargeListTime;
import com.sunhealth.ihhis.model.entity.invoice.OutpatientInvoiceTime;
import com.sunhealth.ihhis.model.entity.invoice.OutpatientInvoiceView;
import com.sunhealth.ihhis.model.entity.register.RegisterDetailTime;
import com.sunhealth.ihhis.model.entity.register.RegisterListTime;

import java.util.List;


public interface InvoiceService {

    OutpatientInvoiceTime saveRegisterInvoiceInfo(ConfirmRegistReq req, RegisterListTime register,
                                                  List<RegisterDetailTime> registerDetails);


    OutpatientInvoiceTime saveChargeInvoiceInfo(ConfirmChargeReq req, ChargeListTime charge, List<ChargeDetailTime> chargeDetails);

    Long iteratorRegisterInvoiceInfo(Long sourceInvoiceId, Long returnRegNo);


    OutpatientInvoiceView getNormalOutpatientInvoice(String patId, String regNo, String hospitalCode,
                                                     Integer flag);
}
