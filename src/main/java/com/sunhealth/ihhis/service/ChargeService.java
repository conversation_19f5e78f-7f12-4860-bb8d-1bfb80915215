package com.sunhealth.ihhis.service;

import com.sunhealth.ihhis.model.dq.sh.insurance.response.CallBackSH03;
import com.sunhealth.ihhis.model.dto.outpatientcharge.*;
import com.sunhealth.ihhis.model.dto.register.GetOutpatientPayResult;
import com.sunhealth.ihhis.model.entity.charge.ThirdAddAccount;
import com.sunhealth.ihhis.model.entity.register.RegisterListTime;
import com.sunhealth.ihhis.model.entity.yibao.RegOnlineGjYiBaoUploadFeeRecord;
import com.sunhealth.ihhis.model.entity.yibao.RegOnlineSHYiBaoUploadFeeRecord;
import com.sunhealth.ihhis.model.insurance.request.CallBack6302;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

public interface ChargeService {
    List<OutpatientCharge> getOutpatientChargeList(UnChargeReq req);
    List<OutpatientChargeRecipeInfo> getChargeRecipeList(ChargeDetailReq req);

    OutpatientChargeDetail getOutpatientChargeDetail(ChargeDetailReq req);

    List<OutpatientUnChargeRecipeInfo> getOutpatientUnChargeRecipe(UnChargeReq req);
    PreChargeResult preCharge(PreChargeReq req);

    /**
     *
     * @param req
     * @param feeRecord 5期医保用
     * @return
     */
    ConfirmChargeResult confirmCharge(ConfirmChargeReq req, RegOnlineSHYiBaoUploadFeeRecord feeRecord);

    @Transactional
    ConfirmChargeResult confirmChargeForHuLiDaoJia(ConfirmChargeReq req);

    /**
     * 结算取消确认
     * @param req
     * @return
     */
    ConfirmedRefundedRes confirmedRefunded(ConfirmedRefundedReq req);

    /**
     * 结算取消结果查询
     * @param req
     * @return
     */
    OrderRefundedRes orderRefunded(OrderRefundedReq req);

    /**
     * 查询医保结算状态，如果已结算，写入结算信息并返回
     * @param hospitalCode
     * @param chargeNo
     * @param push 是否需要推送
     * @return
     */
    GetOutpatientPayResult queryYiBaoPayResult(String hospitalCode, long chargeNo, boolean push);

    /**
     * 缴费医保结算回调成功逻辑
     * @param callBack6302
     * @param hospitalCode
     * @param feeRecord
     */
    void chargeCallBackPaySuccess(CallBack6302 callBack6302, String hospitalCode, RegOnlineGjYiBaoUploadFeeRecord feeRecord);

    /**
     * 缴费医保结算回调成功逻辑
     * @param callBackSH03
     * @param hospitalCode
     * @param feeRecord
     */
    void chargeCallBackPaySuccess(CallBackSH03 callBackSH03, String hospitalCode, RegOnlineSHYiBaoUploadFeeRecord feeRecord);

    PreChargeResult preChargeForHuLi(List<ThirdAddAccount> items, RegisterListTime register);

    String returnOutPatientInsuranceFee(OutpatientChargeRefundedReq req);
    void manageInsuranceData();
}
