package com.sunhealth.ihhis.service;

import com.sunhealth.ihhis.utils.AppContext;
import java.util.Optional;

public interface CurrentHospital {

//    Optional<Hospital> get();

    String getHospitalCode();

//    String getRequestItem();

    static String getCode() {
        return AppContext.getInstance(CurrentHospital.class).getHospitalCode();
    }

//    static String getItem() {
//        return AppContext.getInstance(CurrentHospital.class).getRequestItem();
//    }

//    static Hospital getOrNull() {
//        return AppContext.getInstance(CurrentHospital.class).get().orElse(null);
//    }

//    static Hospital getOrThrow() {
//        Hospital hospital = AppContext.getInstance(CurrentHospital.class).get()
//            .orElseThrow(ErrorType.NO_CURRENT_HOSPITAL_HOSPITAL_ERROR::toProblem);
//        if (hospital.getEnabled()) {
//            return hospital;
//        }
//        throw ErrorType.NO_CURRENT_HOSPITAL_HOSPITAL_ERROR.toProblem("当前医院不可用");
//    }

//    static Hospital getOrDefault() {
//        return AppContext.getInstance(CurrentHospital.class).get().orElseGet(() ->
//            AppContext.getInstance(HospitalRepository.class).findOneByCode("default").orElse(null)
//        );
//    }
//
//    static boolean isPlatform() {
//        return Constants.HEALTH_HEAD_CODE.equalsIgnoreCase(AppContext.getInstance(CurrentHospital.class).getHospitalCode());
//    }

}
