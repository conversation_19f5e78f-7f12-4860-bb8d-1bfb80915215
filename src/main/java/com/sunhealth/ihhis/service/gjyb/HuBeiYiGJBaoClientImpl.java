package com.sunhealth.ihhis.service.gjyb;

import com.fasterxml.jackson.core.type.TypeReference;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.sunhealth.ihhis.config.YiBaoProperties;
import com.sunhealth.ihhis.config.YiBaoProperties.ChannelData;
import com.sunhealth.ihhis.dao.his.ChargeItemViewMapper;
import com.sunhealth.ihhis.dao.his.PreChargeAmtMapper;
import com.sunhealth.ihhis.enums.PayChannelType;
import com.sunhealth.ihhis.model.entity.Dept;
import com.sunhealth.ihhis.model.entity.Diagnose;
import com.sunhealth.ihhis.model.entity.MedicalWorker;
import com.sunhealth.ihhis.model.entity.charge.PreChargeAmt;
import com.sunhealth.ihhis.model.entity.charge.PreChargeDetail;
import com.sunhealth.ihhis.model.entity.patient.RTPatientList;
import com.sunhealth.ihhis.model.entity.register.PreRegisterDetail;
import com.sunhealth.ihhis.model.entity.register.PreRegisterList;
import com.sunhealth.ihhis.model.entity.view.ChargeItemView;
import com.sunhealth.ihhis.model.insurance.MedicalInsuranceParam;
import com.sunhealth.ihhis.model.insurance.UserLongitudeLatitude;
import com.sunhealth.ihhis.model.insurance.request.Input6201;
import com.sunhealth.ihhis.model.insurance.request.Input6202;
import com.sunhealth.ihhis.model.insurance.request.Input6203;
import com.sunhealth.ihhis.model.insurance.request.Input6301;
import com.sunhealth.ihhis.model.insurance.response.Output6201;
import com.sunhealth.ihhis.model.insurance.response.Output6202;
import com.sunhealth.ihhis.model.insurance.response.Output6203;
import com.sunhealth.ihhis.model.insurance.response.Output6301;
import com.sunhealth.ihhis.utils.InsuranceBeanUtils;
import com.sunhealth.ihhis.utils.ThreadLocalUtils;
import com.sunhealth.ihhis.utils.TimeUtils;
import com.sunhealth.ihhis.utils.UrlUtils;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.Date;
import java.util.List;
import java.util.Map;

@Service("huBeiGJYiBaoClient")
@Slf4j
@AllArgsConstructor
public class HuBeiYiGJBaoClientImpl implements GJYiBaoClient {

    private final YiBaoProperties yiBaoProperties;
    private final ChargeItemViewMapper chargeItemViewMapper;
    private final PreChargeAmtMapper preChargeAmtMapper;

    @Override
    public void post1101(MedicalInsuranceParam insuranceParam) {}

    @Override
    public void post2201A(MedicalInsuranceParam insuranceParam, String regNo) {}

    @Override
    public Output6201 post6201Register(MedicalInsuranceParam insuranceParam, PayChannelType type, String hospitalCode) {
        // 调用医保6201 6202接口返回分账信息
        Date date = ThreadLocalUtils.getNow();
        RTPatientList patientList = ThreadLocalUtils.getPatient();
        Dept dept = ThreadLocalUtils.getDept();
        PreRegisterList preRegisterList = ThreadLocalUtils.getPreRegisterList();
        List<PreRegisterDetail> details = ThreadLocalUtils.getPreRegisterDetail();
        String mzNo = preRegisterList.getRegNo() + "";
        Input6201 input6201 = new Input6201();
        input6201.setOrgCodg(yiBaoProperties.getOrgCode());
        input6201.setMedOrgOrd(mzNo);
        input6201.setBegntime(TimeUtils.dateStringFormat(date, "yyyy-MM-dd HH:mm:ss"));
        if (patientList == null) {
            throw new RuntimeException("院内无此患者信息或患者信息已停用");
        }
        input6201.setIdNo(patientList.getCertificateNo());
        input6201.setUserName(patientList.getPatName());
        // 查询字典表，把数据与国家医保数据做map
        input6201.setIdType(InsuranceBeanUtils.certificateTypeMapping(patientList.getCertificateType()));
//        input6201.setEcToken(patientList.getEcToken());
        input6201.setIptOtpNo(mzNo);
        input6201.setDeptCode(dept.getDeptCode());
        input6201.setDeptName(dept.getDeptName());
        input6201.setCaty(dept.getNationDeptCode());
        input6201.setChrgBchno(mzNo);
        // 就诊ID 通过2201接口获得
        input6201.setMdtrtId("");
        // 医疗类别 12门诊挂号
        input6201.setMedType("12");
        // 费用类别 01门诊就诊
        input6201.setFeeType("01");
        // 个人账户使用标志 0不使用
        input6201.setAcctUsedFlag("1");
        input6201.setPayAuthNo(insuranceParam.getPayAuthNo());
        UserLongitudeLatitude userLongitudeLatitude = insuranceParam.getUserLongitudeLatitude();
        if (StringUtils.isNotBlank(userLongitudeLatitude.getLongitude()) && StringUtils.isNotBlank(userLongitudeLatitude.getLatitude())) {
            // 经纬度(经度,纬度)
            String uldLatInt = userLongitudeLatitude.getLongitude() + "," + userLongitudeLatitude.getLatitude();
            input6201.setUldLatlnt(uldLatInt);
        }
        // 就诊凭证类型 02身份证
        input6201.setMdtrtCertType("02");
        input6201.setAtddrNo("D420503003011");
        input6201.setDrName("黄园");
        // 医生信息 暂时固定陈点点 D420503002920
        if (preRegisterList.getDoctorID() != null) {
            MedicalWorker doctor = ThreadLocalUtils.getMedicalWorker();
            if (doctor != null && !"普通号".equals(doctor.getName()) && doctor.getNationCode() != null) {
                input6201.setAtddrNo(doctor.getNationCode());
                input6201.setDrName(doctor.getName());
            }
        }
        List<Input6201.Diseinfo> diseinfos = Lists.newArrayList();
        Input6201.Diseinfo diseinfo = new Input6201.Diseinfo();
        diseinfo.setDiagCode(StringUtils.trim("Z00.001"));
        diseinfo.setDiagName("健康查体");
        diseinfo.setDiagDept(dept.getDeptCode());
        diseinfo.setDiagTime(TimeUtils.localDateTime2String(LocalDateTime.now(), "yyyy-MM-dd HH:mm:ss"));
        diseinfo.setDiagSrtNo(0);
        diseinfo.setDiagType("1");
        diseinfo.setDiseDorName("黄园");
        diseinfo.setDiseDorNo("D420503003011");
        diseinfos.add(diseinfo);
        BigDecimal totalAmount = BigDecimal.ZERO;
        List<Input6201.Feedetail> feedetails = Lists.newArrayList();
        for (PreRegisterDetail detail : details) {
            if (detail.getPrice().compareTo(BigDecimal.ZERO) == 0) {
                continue;
            }
            totalAmount = totalAmount.add(detail.getTotalAmount());
            Input6201.Feedetail feedetail = new Input6201.Feedetail();
            feedetail.setFeedetlSn(detail.getRegDtlNo().toString());
            feedetail.setChrgBchno(mzNo);
            feedetail.setMedListCodg(detail.getNationCode());
            feedetail.setMedinsListCodg(detail.getItemID().toString());
            feedetail.setDetItemFeeSumamt(detail.getTotalAmount());
            feedetail.setCnt(new BigDecimal(detail.getQty()));
            feedetail.setPric(detail.getPrice());
            // 医院审批标志 0无需审批 2不通过 1通过
            feedetail.setHospApprFlag("0");
            // 医生信息 暂时固定陈点点 D420503002920
            feedetail.setBilgDrCodg("D420503003011");
            feedetail.setBilgDrName("黄园");
            // 开单科室编码
            feedetail.setBilgDeptName(dept.getDeptName());
            feedetail.setBilgDeptCodg(dept.getDeptCode());
            feedetail.setMedType(input6201.getMedType());
            feedetail.setRxCircFlag("0");
            feedetails.add(feedetail);
        }
        input6201.setMedfeeSumamt(totalAmount);
        input6201.setFeedetailList(feedetails);
        input6201.setDiseinfoList(diseinfos);
        String path = "org/local/api/hos/uldFeeInfo";
        String url = UrlUtils.concatSegments(yiBaoProperties.getBaseUrl1(), path);
        ChannelData channelData = getChannelData(type);
        Map<String, String> maps = Maps.newHashMap();
        maps.put("hospitalCode", hospitalCode);
        Output6201 output6201 = post(url, null, maps, getEncryptStr(input6201, channelData),
                new TypeReference<Output6201>() {}, channelData);
        ThreadLocalUtils.setOutput6201(output6201);
        return output6201;
    }

    @Override
    public Output6202 post6202Register(MedicalInsuranceParam insuranceParam, PayChannelType type, String hospitalCode) {
        PreRegisterList preRegisterList = ThreadLocalUtils.getPreRegisterList();
        Output6201 output6201 = ThreadLocalUtils.getOutput6201();
        Input6202 input = new Input6202();
        input.setOrgCodg(yiBaoProperties.getOrgCode());
        input.setPayAuthNo(insuranceParam.getPayAuthNo());
        input.setChrgBchno(preRegisterList.getRegNo() + "");
        input.setPayOrdId(output6201.getPayOrdId());
        input.setPayToken(output6201.getPayToken());
        input.setOrgBizSer(System.currentTimeMillis() + "");

        String path = "org/local/api/hos/pay_order";
        String url = UrlUtils.concatSegments(yiBaoProperties.getBaseUrl2(), path);
        ChannelData channelData = getChannelData(type);
        Map<String, String> maps = Maps.newHashMap();
        maps.put("hospitalCode", hospitalCode);
        return post(url, null, maps, getEncryptStr(input, channelData), new TypeReference<Output6202>() {
        }, channelData);
    }


    @Override
    public Output6201 post6201Charge(Long chargeNo, MedicalInsuranceParam insuranceParam, PayChannelType type, String hospitalCode) {
        Date date = ThreadLocalUtils.getNow();
        RTPatientList patientList = ThreadLocalUtils.getPatient();
        Dept dept = ThreadLocalUtils.getDept();
        List<PreChargeDetail> preChargeDetailList = ThreadLocalUtils.getPreChargeDetails();
        List<Diagnose> diagnoses = ThreadLocalUtils.getDiagnoses();


        Input6201 input = new Input6201();
        input.setOrgCodg(yiBaoProperties.getOrgCode());
        input.setMedOrgOrd(chargeNo.toString());
        input.setBegntime(TimeUtils.dateStringFormat(date, "yyyy-MM-dd HH:mm:ss"));
        input.setIdNo(patientList.getCertificateNo());
        input.setUserName(patientList.getPatName());
        // 查询字典表，把数据与国家医保数据做map
        input.setIdType(InsuranceBeanUtils.certificateTypeMapping(patientList.getCertificateType(), patientList.getCertificateNo()));
//        input6201.setEcToken(patientList.getEcToken());
        input.setIptOtpNo(chargeNo.toString());
        input.setDeptCode(dept.getDeptCode());
        input.setDeptName(dept.getDeptName());
        input.setCaty(dept.getNationDeptCode());
        input.setChrgBchno(chargeNo.toString());
        // 就诊ID 通过2201接口获得
        input.setMdtrtId("");
        // 医疗类别 12门诊挂号
        input.setMedType(medTypeMapping(preChargeDetailList.get(0).getRegistType()));
        // 费用类别 01门诊就诊
        input.setFeeType("01");
        // 个人账户使用标志 0不使用
        input.setAcctUsedFlag("1");
        input.setPayAuthNo(insuranceParam.getPayAuthNo());
        UserLongitudeLatitude userLongitudeLatitude = insuranceParam.getUserLongitudeLatitude();
        if (StringUtils.isNotBlank(userLongitudeLatitude.getLongitude()) && StringUtils.isNotBlank(userLongitudeLatitude.getLatitude())) {
            // 经纬度(经度,纬度)
            String uldLatInt = userLongitudeLatitude.getLongitude() + "," + userLongitudeLatitude.getLatitude();
            input.setUldLatlnt(uldLatInt);
        }
        input.setMdtrtCertType("02");
        // 医师写死陈点点
        input.setAtddrNo("D420503002920");
        // 疾病列表
        List<Input6201.Diseinfo> diseinfos = Lists.newArrayList();
        Input6201.Diseinfo diseinfo = new Input6201.Diseinfo();
        diagnoses.forEach(diagnose -> {
            diseinfo.setDiagCode(StringUtils.trim(diagnose.getZdbm()));
            diseinfo.setDiagName(diagnose.getZdmc());
            diseinfo.setDiagDept(dept.getDeptCode());
            diseinfo.setDiagTime(TimeUtils.localDateTime2String(diagnose.getCjrq(), "yyyy-MM-dd HH:mm:ss"));
            diseinfo.setDiagSrtNo(diagnose.getXh());
            diseinfo.setDiagType(diagTypeMapping(diagnose.getZdlx()));
            diseinfo.setDiseDorName("陈点点");
            diseinfo.setDiseDorNo("D420503002920");
            diseinfos.add(diseinfo);
        });
        input.setDiseinfoList(diseinfos);
        List<Input6201.Feedetail> feedetails = Lists.newArrayList();
        // 6.返回预收费号
        preChargeDetailList.forEach(detail -> {
            // 6201费用上传详情
            Input6201.Feedetail feedetail = new Input6201.Feedetail();
            feedetail.setFeedetlSn(detail.getRecipeDetlID().toString());
            feedetail.setChrgBchno(chargeNo.toString());
            feedetail.setRxno(detail.getRecipeID().toString());
            feedetail.setRxCircFlag("0");
            ChargeItemView chargeItemView = chargeItemViewMapper.selectChargeItemByItemCode(detail.getItemID().toString());
            feedetail.setMedListCodg(chargeItemView.getNationCode());
            feedetail.setMedinsListCodg(detail.getItemID().toString());
            feedetail.setDetItemFeeSumamt(detail.getTotalAmount());
            // 查出的预收费数量不能乘以副数，但是医保上传需要
            feedetail.setCnt(detail.getQuantity().multiply(BigDecimal.valueOf(detail.getTimes())));
            feedetail.setPric(detail.getPrice());
            feedetail.setBilgDeptCodg(dept.getDeptCode());
            feedetail.setBilgDeptName(dept.getDeptName());
            feedetail.setBilgDrCodg("D420503002920");
            feedetail.setBilgDrName("陈点点");
            feedetail.setHospApprFlag("0");
            feedetail.setMedType(input.getMedType());
            feedetails.add(feedetail);

            BigDecimal totalAmount = detail.getTotalAmount();

            PreChargeAmt preChargeAmt = new PreChargeAmt();
            preChargeAmt.setChargeNo(chargeNo);
            preChargeAmt.setRegNo(detail.getRegNo());
            preChargeAmt.setRecipeID(detail.getPreChargeNo());
            preChargeAmt.setRecipeDetlID(detail.getRecipeDetlID());
            preChargeAmt.setDiscountAmount(detail.getDiscountAmount());
            preChargeAmt.setInsuranceTradeAmount(new BigDecimal(0));
            preChargeAmt.setSelfAmount(totalAmount);
            // [ClassifyTotal], [InsuranceCashAmount], [CreditAmt]
            preChargeAmt.setClassifyTotal(new BigDecimal(0));
            preChargeAmt.setInsuranceCashAmount(new BigDecimal(0));
            preChargeAmt.setCreditAmt(new BigDecimal(0));
            // [RealAmt], [OtherAmt], [JzAmount], FeeType
            preChargeAmt.setRealAmt(totalAmount);
            preChargeAmt.setOtherAmt(new BigDecimal(0));
            preChargeAmt.setJzAmount(new BigDecimal(0));
            preChargeAmt.setFeeType(detail.getFeeType());

            int i = preChargeAmtMapper.countPreChargeAmt(chargeNo, detail.getRecipeDetlID());
            if (i > 0) {
                preChargeAmtMapper.updateById(preChargeAmt);
            } else {
                preChargeAmtMapper.insert(preChargeAmt);
            }
        });
        input.setMedfeeSumamt(feedetails.stream().map(Input6201.Feedetail::getDetItemFeeSumamt).reduce(BigDecimal.ZERO, BigDecimal::add));
        input.setFeedetailList(feedetails);

        String path = "org/local/api/hos/uldFeeInfo";
        String url = UrlUtils.concatSegments(yiBaoProperties.getBaseUrl1(), path);
        ChannelData channelData = getChannelData(type);
        Map<String, String> maps = Maps.newHashMap();
        maps.put("hospitalCode", hospitalCode);
        Output6201 output6201 = post(url, null, maps, getEncryptStr(input, channelData),
                new TypeReference<Output6201>() {}, channelData);
        ThreadLocalUtils.setOutput6201(output6201);
        return output6201;
    }

    @Override
    public Output6202 post6202Charge(Long chargeNo, MedicalInsuranceParam insuranceParam, PayChannelType type, String hospitalCode) {
        Output6201 output6201 = ThreadLocalUtils.getOutput6201();
        Input6202 input = new Input6202();
        input.setOrgCodg(yiBaoProperties.getOrgCode());
        input.setPayAuthNo(insuranceParam.getPayAuthNo());
        input.setChrgBchno(chargeNo.toString());
        input.setPayOrdId(output6201.getPayOrdId());
        input.setPayToken(output6201.getPayToken());
        input.setOrgBizSer(System.currentTimeMillis() + "");

        String path = "org/local/api/hos/pay_order";
        String url = UrlUtils.concatSegments(yiBaoProperties.getBaseUrl2(), path);
        ChannelData channelData = getChannelData(type);
        Map<String, String> maps = Maps.newHashMap();
        maps.put("hospitalCode", hospitalCode);
        return post(url, null, maps, getEncryptStr(input, channelData), new TypeReference<Output6202>() {
        }, channelData);
    }

    @Override
    public Output6203 post6203(Input6203 input, MedicalInsuranceParam insuranceParam, PayChannelType type, String hospitalCode) {
        String path = "org/local/api/hos/refund_Order";
        String url = UrlUtils.concatSegments(yiBaoProperties.getBaseUrl2(), path);
        ChannelData channelData = getChannelData(type);
        Map<String, String> maps = Maps.newHashMap();
        maps.put("hospitalCode", hospitalCode);
        return post(url, null, maps, getEncryptStr(input, channelData), new TypeReference<Output6203>() {
        }, channelData);
    }

    @Override
    public Output6301 post6301(Input6301 input, PayChannelType type, String hospitalCode) {
        String path = "org/local/api/hos/query_order_info";
        String url = UrlUtils.concatSegments(yiBaoProperties.getBaseUrl2(), path);
        ChannelData channelData = getChannelData(type);
        Map<String, String> maps = Maps.newHashMap();
        maps.put("hospitalCode", hospitalCode);
        return post(url, null, maps, getEncryptStr(input, channelData), new TypeReference<Output6301>() {
        }, channelData);
    }

    @Override
    public String signIn(String operatorId) {
        return null;
    }

}
