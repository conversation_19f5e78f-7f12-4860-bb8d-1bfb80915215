package com.sunhealth.ihhis.service.gjyb;

import com.alibaba.fastjson.JSONObject;
import com.fasterxml.jackson.core.type.TypeReference;
import com.google.common.collect.Maps;
import com.sunhealth.ihhis.config.YiBaoProperties;
import com.sunhealth.ihhis.enums.PayChannelType;
import com.sunhealth.ihhis.error.ErrorType;
import com.sunhealth.ihhis.model.insurance.MedicalInsuranceParam;
import com.sunhealth.ihhis.model.insurance.request.Input6203;
import com.sunhealth.ihhis.model.insurance.request.Input6301;
import com.sunhealth.ihhis.model.insurance.response.*;
import com.sunhealth.ihhis.utils.*;
import okhttp3.Headers;
import okhttp3.Response;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.IOException;
import java.util.List;
import java.util.Map;


/**
 * 封装okhttpClient与国家医保的api交互
 */
public interface GJYiBaoClient {

    Logger logger =  LoggerFactory.getLogger(GJYiBaoClient.class);

    void post1101(MedicalInsuranceParam insuranceParam);

    /**
     * 2201接口
     * @param insuranceParam
     */
    void post2201A(MedicalInsuranceParam insuranceParam, String regNo);

    Output6201 post6201Register(MedicalInsuranceParam insuranceParam, PayChannelType type, String hospitalCode);
    Output6202 post6202Register(MedicalInsuranceParam insuranceParam, PayChannelType type, String hospitalCode);

    /**
     * 6201接口
     * @param chargeNo
     * @param insuranceParam
     * @param type
     * @param hospitalCode
     * @return
     */
    Output6201 post6201Charge(Long chargeNo, MedicalInsuranceParam insuranceParam, PayChannelType type, String hospitalCode);

    /**
     * 6202接口
     * @param chargeNo
     * @param insuranceParam
     * @param type
     * @param hospitalCode
     * @return
     */
    Output6202 post6202Charge(Long chargeNo, MedicalInsuranceParam insuranceParam, PayChannelType type, String hospitalCode);

    Output6203 post6203(Input6203 input, MedicalInsuranceParam insuranceParam, PayChannelType type, String hospitalCode);
    Output6301 post6301(Input6301 input, PayChannelType type, String hospitalCode);

    /**
     * 医保签到（9001）
     *
     * @return
     */
    String signIn(String operatorId);

    /**
     * 加密
     * @param data
     * @param channelData
     * @return
     */
    default String getEncryptStr(Object data, YiBaoProperties.ChannelData channelData) {
        try {
            String rawData;
            if (data instanceof String) {
                rawData = (String) data;
            } else {
                rawData = JSONObject.toJSONString(data);
            }
            return GJYBSm4Utils.jm(rawData, channelData.getAppId(), channelData.getApiVersion(),
                    channelData.getSm4Key(), channelData.getAppKey(), channelData.getPubKey());
        } catch (Exception e) {
            logger.error("医保入参加密失败", e);
            throw new RuntimeException(e);
        }
    }

    default String medTypeMapping(Integer registType) {
        switch (registType){
            case 1:
                return "13";
            case 0:
            default:
                return "11";
        }
    }

    default String diagTypeMapping(Integer zdlx) {
        switch (zdlx){
            case 1:
                return "1";
            case 2:
            default:
                return "3";
        }
    }

    default YiBaoProperties.ChannelData getChannelData(PayChannelType type) {
        List<YiBaoProperties.ChannelData> channelDataList = AppContext.getInstance(YiBaoProperties.class).getChannelData();
        return channelDataList.stream().filter(c -> c.getChannelCode().equals(type.name())).findFirst()
                .orElseThrow(() -> ErrorType.INTERNAL_SERVER_ERROR.toProblem("未找到对应的支付渠道"));
    }


    /**
     * 调用node-red接口，返回的是content
     *
     * @param url                 请求地址
     * @param headers             请求头
     * @param params              请求参数
     * @param body                请求体
     * @param returnTypeReference 要求返回类型
     * @param <T>                 返回类型
     * @return
     */
    default  <T> T post(String url, Map<String, String> headers, Map<String, String> params,
                       Object body, TypeReference<T> returnTypeReference, YiBaoProperties.ChannelData data) {
        if (headers == null) {
            headers = Maps.newHashMap();
        }
        String bodyStr = null;
        if (body != null) {
            bodyStr = (body instanceof String) ? (String) body : StandardObjectMapper.stringify(body);
        }
        logger.info("调用医保 接口:url:{}, data: {}", url, bodyStr);
        try (Response response = OkHttpUtils.post(url, Headers.of(headers), null, bodyStr)) {
            if (response.isSuccessful()) {
                if (response.body() == null) {
                    logger.info("调用医保 接口 返回:null");
                    return null;
                }
                if (returnTypeReference == null) {
                    return null;
                }
                String responseString = OkHttpUtils.getResponseBody(response).orElse(null);
                logger.info("调用医保 接口url:{} 返回：{}",url, responseString);
                GJYiBaoBaseEncryptResponse<T> basicResponse = StandardObjectMapper.getInstance().readValue(
                        responseString, new TypeReference<GJYiBaoBaseEncryptResponse<T>>() {
                        });

                if (0 == basicResponse.getCode() || basicResponse.getSuccess()) {
                    // 这里进行验签/解密工作，获取data (默认签名方式SM2，加密方式为SM4)
                    String encData = basicResponse.getEncData();
                    String dataStr;
                    try {
                        dataStr = HseEncAndDecUtil.sm4Decrypt(data.getAppId(), data.getSm4Key(),
                                encData);
                        logger.info("调用医保 接口url:{}，调用成功，返回解析后data：{}", url, dataStr);
                    } catch (Exception e) {
                        logger.error("医保返回数据解密失败", e);
                        throw new RuntimeException(e);
                    }
                    return StandardObjectMapper.getInstance().readValue(dataStr, returnTypeReference);
                } else {
                    logger.info("国家医保 API调用成功-但返回了业务错误：[code]: " + basicResponse.getCode() + " [msg]:"
                            + basicResponse.getMessage());
                    throw ErrorType.INTERNAL_SERVER_ERROR.toProblem(basicResponse.getMessage());
                }
            } else {
                throw ErrorType.INTERNAL_SERVER_ERROR.toProblem(
                        "国家医保 API调用失败：" + OkHttpUtils.getResponseBody(response).orElse(null));
            }
        } catch (IOException e) {
            logger.error(e.getMessage(), e);
            throw ErrorType.INTERNAL_SERVER_ERROR.toProblem(e.getMessage());
        }
    }
}