package com.sunhealth.ihhis.service.gjyb;

import com.fasterxml.jackson.core.type.TypeReference;
import com.google.common.collect.ImmutableMap;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.sunhealth.ihhis.config.HisHospitalProperties;
import com.sunhealth.ihhis.config.YiBaoProperties;
import com.sunhealth.ihhis.dao.his.ChargeItemViewMapper;
import com.sunhealth.ihhis.dao.his.PreChargeAmtMapper;
import com.sunhealth.ihhis.enums.PayChannelType;
import com.sunhealth.ihhis.error.ErrorType;
import com.sunhealth.ihhis.model.entity.Dept;
import com.sunhealth.ihhis.model.entity.Diagnose;
import com.sunhealth.ihhis.model.entity.MedicalWorker;
import com.sunhealth.ihhis.model.entity.charge.PreChargeAmt;
import com.sunhealth.ihhis.model.entity.charge.PreChargeDetail;
import com.sunhealth.ihhis.model.entity.patient.RTPatientList;
import com.sunhealth.ihhis.model.entity.register.PreRegisterDetail;
import com.sunhealth.ihhis.model.entity.register.PreRegisterList;
import com.sunhealth.ihhis.model.entity.view.ChargeItemView;
import com.sunhealth.ihhis.model.insurance.MedicalInsuranceParam;
import com.sunhealth.ihhis.model.insurance.UserLongitudeLatitude;
import com.sunhealth.ihhis.model.insurance.request.*;
import com.sunhealth.ihhis.model.insurance.response.*;
import com.sunhealth.ihhis.utils.*;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import okhttp3.Headers;
import okhttp3.Response;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.io.IOException;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

@Service("shangHaiGJYiBaoClient")
@Slf4j
@RequiredArgsConstructor
public class ShangHaiGJYiBaoClientImpl implements GJYiBaoClient {

    private static final Map<String, Output9001> SIGN_IN_RESTLT_MAP = new HashMap<>();

    private final YiBaoProperties yiBaoProperties;
    private final HisHospitalProperties hospitalProperties;
    private final ChargeItemViewMapper chargeItemViewMapper;
    private final PreChargeAmtMapper preChargeAmtMapper;

    // 老数据库中zxhis.dbo.Tbt_Recipe_GJYB_GhXmxx,新的库没有这个表
    private static final Map<Double, String> GJYBMM_MAP = ImmutableMap.<Double, String>builder()
            .put(10.00, "001102000010000-S110200001a0010")
            .put(18.00, "001102000010000-S110200001b0010")
            .put(19.00, "001102000030000-S110200003a0010")
            .put(25.00, "001102000010000-S110200001c0010")
            .put(27.00, "001102000030000-S110200003b0010")
            .put(31.00, "001102000020200-S110200002a0010")
            .put(41.00, "001102000020100-S110200002b0010")
            .put(40.00, "001102000020200-S110200002c0010")
            .put(50.00, "001102000020100-S110200002b0010")
            .put(300.00, "001102000010000-SZZZZZ000000090")
            .put(500.00, "001102000010000-SZZZZZ000000090").build();

    @Override
    public void post1101(MedicalInsuranceParam insuranceParam) {
        String signNo = signIn(hospitalProperties.getOpCode() + "");
        RTPatientList patient = ThreadLocalUtils.getPatient();
        Input1101 input1101 = new Input1101();
        input1101.setMdtrtCertType("01");
        input1101.setMdtrtCertNo(insuranceParam.getEcToken());
        input1101.setPsnCertType("01");
        input1101.setCertNo(patient.getCertificateNo());
        input1101.setPsnName(patient.getPatName());
        GJYBBasicRequest<InputData<Input1101>> input1101r = new GJYBBasicRequest<>("1101", new InputData<>(input1101)
            , insuranceParam.getEcToken(), signNo, insuranceParam.getInsuOrg());
        GJYBBaseResponse<Output1101> output1101 = post(input1101r, new TypeReference<GJYBBaseResponse<Output1101>>() {});
        if (output1101 == null || output1101.getOutput() == null) {
            throw new RuntimeException("1101获取数据失败");
        }
        ThreadLocalUtils.setOutput1101(output1101.getOutput());
    }

    @Override
    public void post2201A(MedicalInsuranceParam insuranceParam, String regNo) {
        // 这个接口不确定需不需要调用
        Date now = ThreadLocalUtils.getNow();
        String signNo = signIn(hospitalProperties.getOpCode() + "");
        Output1101 output1101 = ThreadLocalUtils.getOutput1101();
        Dept dept = ThreadLocalUtils.getDept();

        List<Output1101.InsuInfo> insuInfoRess = output1101.getInsuinfo();

        String accountAttr = insuranceParam.getAccountAttr();
        String yblx12 = accountAttr.substring(11, 12);

        List<String> insutypes;
        if (!"Y".equals(yblx12)) {
            if ("B".equals(yblx12) || "C".equals(yblx12) || "D".equals(yblx12) || "E".equals(yblx12)
                    || "F".equals(yblx12) || "H".equals(yblx12)) {
                // 城乡居民基本医疗保险
                insutypes = Lists.newArrayList("390");
            } else {
                // 职工基本医疗保险
                insutypes = Lists.newArrayList("310");
            }
        } else {
            insutypes = Lists.newArrayList("310", "390");
        }
        //非异地医保只能使用310或者390险种类型
        if (!yblx12.equals("Y")) {
            insuInfoRess = insuInfoRess.stream().filter(p -> insutypes.contains(p.getInsutype())).collect(Collectors.toList());
        }
        if (CollectionUtils.isEmpty(insuInfoRess)) {
            throw new RuntimeException("国家医保账户查询失败，不能使用国家医保结算");
        }

        Output1101.InsuInfo insuInfo = insuInfoRess.stream().filter(p -> "1".equals(p.getPsnInsuStas())).findFirst().orElse(null);
        if (insuInfo == null) {
            //取暂停但是还未到停止日期的险种类型
            insuInfo = insuInfoRess.stream().filter(p -> Objects.equals(p.getPsnInsuStas(), "2") && p.getPausInsuDate().compareTo(now) > 0)
                    .findFirst().orElse(null);
        }
        if (insuInfo == null) {
            //取暂停险种类型
            insuInfo = insuInfoRess.stream().filter(p -> Objects.equals(p.getPsnInsuStas(), "2"))
                    .findFirst().orElse(null);
        }
        if (insuInfo == null) {
            throw new RuntimeException("未查询到患者国家医保参保信息");
        }
        ThreadLocalUtils.setInsuInfo(insuInfo);

        Input2201 input2201 = new Input2201();
        input2201.setPsnNo(output1101.getBaseinfo().getPsnNo());
        input2201.setInsutype(insuInfo.getInsutype());
        input2201.setBegntime(now);
        input2201.setMdtrtCertType("01");
        input2201.setMdtrtCertNo(insuranceParam.getEcToken());
        input2201.setIptOtpNo(regNo);

        MedicalWorker medicalWorker = ThreadLocalUtils.getMedicalWorker();
        input2201.setAtddrNo(medicalWorker.getNationCode());
        input2201.setDrName(medicalWorker.getName().trim());
        input2201.setDeptCode(dept.getNationDeptCode());
        input2201.setDeptName(dept.getDeptName().trim());
        input2201.setCaty("A15");

        GJYBBasicRequest<InputData<Input2201>> input2201r = new GJYBBasicRequest<>("2201", new InputData<>(input2201),
                insuranceParam.getEcToken(), signNo, insuInfo.getInsuplcAdmdvs());
        GJYBBaseResponse<Output2201> output2201 = post(input2201r, new TypeReference<GJYBBaseResponse<Output2201>>() {});
        if (output2201 == null || output2201.getOutput() == null) {
            throw new RuntimeException("2201获取数据失败");
        }
        ThreadLocalUtils.setOutput2201(output2201.getOutput().getData());
    }

    public static void main(String[] args) {
        BigDecimal a1 = new BigDecimal("18").setScale(2, RoundingMode.HALF_UP);
        Double a2 = 18d;
        System.out.println(a1);
        System.out.println(a2);
        System.out.println(Objects.equals(a1.doubleValue(), a2));
    }
    @Override
    public Output6201 post6201Register(MedicalInsuranceParam insuranceParam, PayChannelType type, String hospitalCode) {
        Date now = ThreadLocalUtils.getNow();
        PreRegisterList preRegisterList = ThreadLocalUtils.getPreRegisterList();
        List<PreRegisterDetail> details = ThreadLocalUtils.getPreRegisterDetail();
        Dept dept = ThreadLocalUtils.getDept();

        String mzNo = preRegisterList.getRegNo() + "";
        Input6201 input6201 = new Input6201();
        input6201.setOrgCodg(yiBaoProperties.getOrgCode());
        input6201.setMedOrgOrd(mzNo);
        input6201.setBegntime(TimeUtils.dateStringFormat(now, "yyyy-MM-dd HH:mm:ss"));
        RTPatientList patient = ThreadLocalUtils.getPatient();
        if (patient == null) {
            throw new RuntimeException("院内无此患者信息或患者信息已停用");
        }
        input6201.setIdNo(patient.getCertificateNo());
        input6201.setUserName(patient.getPatName());
        // 查询字典表，把数据与国家医保数据做map
        input6201.setIdType(InsuranceBeanUtils.certificateTypeMapping(patient.getCertificateType()));
        input6201.setEcToken(insuranceParam.getEcToken());
        input6201.setIptOtpNo(mzNo);
        input6201.setDeptCode(dept.getNationDeptCode());
        input6201.setDeptName(dept.getDeptName());
        input6201.setCaty(dept.getNationDeptCode());
        input6201.setChrgBchno(mzNo);
        Output2201.Data2201 output2201 = ThreadLocalUtils.getOutput2201();
        input6201.setPsnNo(output2201.getPsnNo());
        Output1101.InsuInfo insuInfo = ThreadLocalUtils.getInsuInfo();
        input6201.setInsutype(insuInfo.getInsutype());
        // 就诊ID 通过2201接口获得
        input6201.setMdtrtId(output2201.getMdtrtId());
        // 医疗类别 12门诊挂号
        input6201.setMedType("12");
        // 费用类别 01门诊就诊
        input6201.setFeeType("01");
        // 个人账户使用标志 0不使用
        input6201.setAcctUsedFlag("1");
        input6201.setPsnSetlway("1");
        input6201.setChrgBchno(mzNo);
        input6201.setPayAuthNo(insuranceParam.getPayAuthNo());
        input6201.setInsuplcAdmdvs(insuInfo.getInsuplcAdmdvs());
        input6201.setInsuCode(yiBaoProperties.getMdtrtareaAdmvs());
        UserLongitudeLatitude userLongitudeLatitude = insuranceParam.getUserLongitudeLatitude();
        if (StringUtils.isNotBlank(userLongitudeLatitude.getLongitude()) && StringUtils.isNotBlank(userLongitudeLatitude.getLatitude())) {
            // 经纬度(经度,纬度)
            String uldLatInt = userLongitudeLatitude.getLongitude() + "," + userLongitudeLatitude.getLatitude();
            input6201.setUldLatlnt(uldLatInt);
        }
        // 就诊凭证类型 02身份证
        input6201.setMdtrtCertType("02");
//        MedicalWorker medicalWorker = ThreadLocalUtils.getMedicalWorker();
//        input6201.setAtddrNo(yiBaoProperties.getDrNo());
//        input6201.setDrName(yiBaoProperties.getDrName());
        if (preRegisterList.getDoctorID() != null) {
            MedicalWorker doctor = ThreadLocalUtils.getMedicalWorker();
            if (doctor != null) {
                if ("普通号".equals(doctor.getName())) {
                    input6201.setAtddrNo(yiBaoProperties.getDrNo());
                    input6201.setDrName(yiBaoProperties.getDrName());
                } else {
                    input6201.setAtddrNo(doctor.getNationCode());
                    input6201.setDrName(doctor.getName());
                }
            }
        }
        List<Input6201.Diseinfo> diseinfos = Lists.newArrayList();
        Input6201.Diseinfo diseinfo = new Input6201.Diseinfo();
        diseinfo.setDiagCode(StringUtils.isBlank(yiBaoProperties.getDiagCode()) ? "Z71.900" : yiBaoProperties.getDiagCode());
        diseinfo.setDiagName(StringUtils.isBlank(yiBaoProperties.getDiagName()) ? "咨询" : yiBaoProperties.getDiagName());
        diseinfo.setDiagDept(dept.getNationDeptCode());
        diseinfo.setDiagTime(TimeUtils.localDateTime2String(LocalDateTime.now(), "yyyy-MM-dd HH:mm:ss"));
        diseinfo.setDiagSrtNo(0);
        diseinfo.setDiagType("1");
//        diseinfo.setDiseDorName(yiBaoProperties.getDiagName());
//        diseinfo.setDiseDorNo(yiBaoProperties.getDrNo());
        diseinfo.setDiseDorName(input6201.getDrName());
        diseinfo.setDiseDorNo(input6201.getAtddrNo());
        diseinfos.add(diseinfo);

        BigDecimal totalAmount = BigDecimal.ZERO;
        List<Input6201.Feedetail> feedetails = Lists.newArrayList();
        for (PreRegisterDetail detail : details) {
            if (detail.getPrice().compareTo(BigDecimal.ZERO) == 0) {
                continue;
            }
            totalAmount = totalAmount.add(detail.getTotalAmount());
            Input6201.Feedetail feedetail = new Input6201.Feedetail();
            feedetail.setFeedetlSn(detail.getRegDtlNo().toString());
            feedetail.setChrgBchno(mzNo);
            feedetail.setMedListCodg(detail.getNationCode());
            feedetail.setMedinsListCodg(detail.getItemID().toString());
            feedetail.setDetItemFeeSumamt(detail.getTotalAmount().setScale(2, RoundingMode.HALF_UP));
            feedetail.setCnt(new BigDecimal(detail.getQty()).setScale(4, RoundingMode.HALF_UP));
            feedetail.setPric(detail.getPrice().setScale(2, RoundingMode.HALF_UP));
            // 医院审批标志 0无需审批 2不通过 1通过
            feedetail.setHospApprFlag("0");
//            feedetail.setBilgDrCodg(yiBaoProperties.getDrNo());
//            feedetail.setBilgDrName(yiBaoProperties.getDrName());
            feedetail.setBilgDrCodg(input6201.getAtddrNo());
            feedetail.setBilgDrName(input6201.getDrName());
            // 开单科室编码
            feedetail.setBilgDeptName(dept.getDeptName());
            feedetail.setBilgDeptCodg(dept.getNationDeptCode());
            feedetail.setMedType(input6201.getMedType());
            feedetail.setRxCircFlag("0");
            feedetails.add(feedetail);
        }
        // 普精卫一段奇怪的逻辑, 最终的国家医保编码需要根据价格固定编码
        if (GJYBMM_MAP.containsKey(totalAmount.doubleValue())) {
            Input6201.Feedetail feedetail = feedetails.get(0);
            feedetail.setMedListCodg(GJYBMM_MAP.get(totalAmount.doubleValue()));
            BigDecimal detItemFeeSumamt = BigDecimal.ZERO;
            BigDecimal pric = BigDecimal.ZERO;
            for (Input6201.Feedetail detail : feedetails) {
                detItemFeeSumamt = detItemFeeSumamt.add(detail.getDetItemFeeSumamt());
                pric = pric.add(detail.getPric());
            }
            feedetail.setDetItemFeeSumamt(detItemFeeSumamt.setScale(2, RoundingMode.HALF_UP));
            feedetail.setPric(pric.setScale(2, RoundingMode.HALF_UP));
            feedetails = Lists.newArrayList(feedetail);
        }
        input6201.setMedfeeSumamt(totalAmount.setScale(2, RoundingMode.HALF_UP));
        input6201.setFeedetailList(feedetails);
        input6201.setDiseinfoList(diseinfos);
        if ("Y".equals(StringUtils.substring(insuranceParam.getAccountAttr(),11,12)) ) {
            input6201.setExpContent("**********");
        }
        YiBaoProperties.ChannelData channelData = getChannelData(type);
        String url = UrlUtils.concatSegments(yiBaoProperties.getBaseUrl1(), "hsa-pmc", "6201");

        Output6201 output6201 = post(url, getHeader(), null, getEncryptStr(input6201, channelData),
                new TypeReference<Output6201>() {}, channelData);
        ThreadLocalUtils.setOutput6201(output6201);
        return output6201;

    }

    @Override
    public Output6202 post6202Register(MedicalInsuranceParam insuranceParam, PayChannelType type, String hospitalCode) {
        PreRegisterList preRegisterList = ThreadLocalUtils.getPreRegisterList();
        Output6201 output6201 = ThreadLocalUtils.getOutput6201();
        Input6202 input6202 = new Input6202();
        input6202.setOrgCodg(yiBaoProperties.getOrgCode());
        input6202.setPayAuthNo(insuranceParam.getPayAuthNo());
        input6202.setChrgBchno(preRegisterList.getRegNo() + "");
        input6202.setPayOrdId(output6201.getPayOrdId());
        input6202.setPayToken(output6201.getPayToken());
        input6202.setOrgBizSer(System.currentTimeMillis() + "");

        YiBaoProperties.ChannelData channelData = getChannelData(type);
        String url = UrlUtils.concatSegments(yiBaoProperties.getBaseUrl1(), "hsa-pmc", "6202");
        Output6202 output6202 = post(url, getHeader(), null, getEncryptStr(input6202, channelData),
                new TypeReference<Output6202>() {}, channelData);
        return output6202;
    }

    @Override
    public Output6201 post6201Charge(Long chargeNo, MedicalInsuranceParam insuranceParam, PayChannelType type, String hospitalCode) {
        Date date = ThreadLocalUtils.getNow();
        RTPatientList patientList = ThreadLocalUtils.getPatient();
        Dept dept = ThreadLocalUtils.getDept();
        List<PreChargeDetail> preChargeDetailList = ThreadLocalUtils.getPreChargeDetails();
        List<Diagnose> diagnoses = ThreadLocalUtils.getDiagnoses();
        MedicalWorker medicalWorker = ThreadLocalUtils.getMedicalWorker();

        Input6201 input6201 = new Input6201();
        input6201.setOrgCodg(yiBaoProperties.getOrgCode());
        input6201.setMedOrgOrd(chargeNo.toString());
        input6201.setBegntime(TimeUtils.dateStringFormat(date, "yyyy-MM-dd HH:mm:ss"));
        input6201.setIdNo(patientList.getCertificateNo());
        input6201.setUserName(patientList.getPatName());
        // 查询字典表，把数据与国家医保数据做map
        input6201.setIdType(InsuranceBeanUtils.certificateTypeMapping(patientList.getCertificateType(), patientList.getCertificateNo()));
        input6201.setEcToken(insuranceParam.getEcToken());
        input6201.setIptOtpNo(chargeNo.toString());
        input6201.setDeptCode(dept.getNationDeptCode());
        input6201.setDeptName(dept.getDeptName());
        input6201.setCaty(dept.getNationDeptCode());
        input6201.setChrgBchno(chargeNo.toString());
        Output2201.Data2201 output2201 = ThreadLocalUtils.getOutput2201();
        input6201.setPsnNo(output2201.getPsnNo());
        Output1101.InsuInfo insuInfo = ThreadLocalUtils.getInsuInfo();
        input6201.setInsutype(insuInfo.getInsutype());
        // 就诊ID 通过2201接口获得
        input6201.setMdtrtId(output2201.getMdtrtId());
        // 医疗类别 12门诊挂号
        input6201.setMedType(medTypeMapping(preChargeDetailList.get(0).getRegistType()));
        // 费用类别 01门诊就诊
        input6201.setFeeType("01");
        // 个人账户使用标志 0不使用
        input6201.setAcctUsedFlag("1");
        input6201.setPayAuthNo(insuranceParam.getPayAuthNo());
        input6201.setInsuplcAdmdvs(insuInfo.getInsuplcAdmdvs());
        input6201.setInsuCode(yiBaoProperties.getMdtrtareaAdmvs());

        UserLongitudeLatitude userLongitudeLatitude = insuranceParam.getUserLongitudeLatitude();
        if (StringUtils.isNotBlank(userLongitudeLatitude.getLongitude()) && StringUtils.isNotBlank(userLongitudeLatitude.getLatitude())) {
            // 经纬度(经度,纬度)
            String uldLatInt = userLongitudeLatitude.getLongitude() + "," + userLongitudeLatitude.getLatitude();
            input6201.setUldLatlnt(uldLatInt);
        }
        input6201.setMdtrtCertType("02");
        String doctorCode = medicalWorker.getNationCode();
        String doctorName = medicalWorker.getName().trim();
        input6201.setAtddrNo(doctorCode);
        // 疾病列表
        List<Input6201.Diseinfo> diseinfos = Lists.newArrayList();
        Input6201.Diseinfo diseinfo = new Input6201.Diseinfo();
        diagnoses.forEach(diagnose -> {
            diseinfo.setDiagCode(StringUtils.trim(diagnose.getZdbm()));
            diseinfo.setDiagName(diagnose.getZdmc());
            diseinfo.setDiagDept(dept.getNationDeptCode());
            diseinfo.setDiagTime(TimeUtils.localDateTime2String(diagnose.getCjrq(), "yyyy-MM-dd HH:mm:ss"));
            diseinfo.setDiagSrtNo(diagnose.getXh());
            diseinfo.setDiagType(diagTypeMapping(diagnose.getZdlx()));
            diseinfo.setDiseDorName(doctorName);
            diseinfo.setDiseDorNo(doctorCode);
            diseinfos.add(diseinfo);
        });
        input6201.setDiseinfoList(diseinfos);
        List<Input6201.Feedetail> feedetails = Lists.newArrayList();
        // 6.返回预收费号
        preChargeDetailList.forEach(detail -> {
            // 6201费用上传详情
            Input6201.Feedetail feedetail = new Input6201.Feedetail();
            feedetail.setFeedetlSn(detail.getRecipeDetlID().toString());
            feedetail.setChrgBchno(chargeNo.toString());
            feedetail.setRxno(detail.getRecipeID().toString());
            feedetail.setRxCircFlag("0");
            ChargeItemView chargeItemView = chargeItemViewMapper.selectChargeItemByItemCode(detail.getItemID().toString());
            feedetail.setMedListCodg(chargeItemView.getNationCode());
            feedetail.setMedinsListCodg(detail.getItemID().toString());
            feedetail.setDetItemFeeSumamt(detail.getTotalAmount());
            // 查出的预收费数量不能乘以副数，但是医保上传需要
            feedetail.setCnt(detail.getQuantity().multiply(BigDecimal.valueOf(detail.getTimes())));
            feedetail.setPric(detail.getPrice());
            feedetail.setBilgDeptCodg(dept.getNationDeptCode());
            feedetail.setBilgDeptName(dept.getDeptName());
            feedetail.setBilgDrCodg(doctorCode);
            feedetail.setBilgDrName(doctorName);
            feedetail.setHospApprFlag("0");
            feedetail.setMedType(input6201.getMedType());
            feedetails.add(feedetail);

            BigDecimal totalAmount = detail.getTotalAmount();

            PreChargeAmt preChargeAmt = new PreChargeAmt();
            preChargeAmt.setChargeNo(chargeNo);
            preChargeAmt.setRegNo(detail.getRegNo());
            preChargeAmt.setRecipeID(detail.getPreChargeNo());
            preChargeAmt.setRecipeDetlID(detail.getRecipeDetlID());
            preChargeAmt.setDiscountAmount(detail.getDiscountAmount());
            preChargeAmt.setInsuranceTradeAmount(new BigDecimal(0));
            preChargeAmt.setSelfAmount(totalAmount);
            // [ClassifyTotal], [InsuranceCashAmount], [CreditAmt]
            preChargeAmt.setClassifyTotal(new BigDecimal(0));
            preChargeAmt.setInsuranceCashAmount(new BigDecimal(0));
            preChargeAmt.setCreditAmt(new BigDecimal(0));
            // [RealAmt], [OtherAmt], [JzAmount], FeeType
            preChargeAmt.setRealAmt(totalAmount);
            preChargeAmt.setOtherAmt(new BigDecimal(0));
            preChargeAmt.setJzAmount(new BigDecimal(0));
            preChargeAmt.setFeeType(detail.getFeeType());

            int i = preChargeAmtMapper.countPreChargeAmt(chargeNo, detail.getRecipeDetlID());
            if (i > 0) {
                preChargeAmtMapper.updateById(preChargeAmt);
            } else {
                preChargeAmtMapper.insert(preChargeAmt);
            }
        });
        input6201.setMedfeeSumamt(feedetails.stream().map(Input6201.Feedetail::getDetItemFeeSumamt).reduce(BigDecimal.ZERO, BigDecimal::add));
        input6201.setFeedetailList(feedetails);
        if ("Y".equals(StringUtils.substring(insuranceParam.getAccountAttr(),11,12)) ) {
            input6201.setExpContent("**********");
        }
        YiBaoProperties.ChannelData channelData = getChannelData(type);
        String url = UrlUtils.concatSegments(yiBaoProperties.getBaseUrl1(), "hsa-pmc", "6201");
        Output6201 output6201 = post(url, getHeader(), null, getEncryptStr(input6201, channelData),
                new TypeReference<Output6201>() {}, channelData);
        ThreadLocalUtils.setOutput6201(output6201);
        return output6201;
    }

    @Override
    public Output6202 post6202Charge(Long chargeNo, MedicalInsuranceParam insuranceParam, PayChannelType type, String hospitalCode) {
        Output6201 output6201 = ThreadLocalUtils.getOutput6201();
        Input6202 input6202 = new Input6202();
        input6202.setOrgCodg(yiBaoProperties.getOrgCode());
        input6202.setPayAuthNo(insuranceParam.getPayAuthNo());
        input6202.setChrgBchno(chargeNo.toString());
        input6202.setPayOrdId(output6201.getPayOrdId());
        input6202.setPayToken(output6201.getPayToken());
        input6202.setOrgBizSer(System.currentTimeMillis() + "");

        YiBaoProperties.ChannelData channelData = getChannelData(type);
        String url = UrlUtils.concatSegments(yiBaoProperties.getBaseUrl1(), "hsa-pmc", "6202");
        Output6202 output6202 = post(url, getHeader(), null, getEncryptStr(input6202, channelData),
                new TypeReference<Output6202>() {}, channelData);
        return output6202;
    }

    @Override
    public Output6203 post6203(Input6203 input, MedicalInsuranceParam insuranceParam, PayChannelType type, String hospitalCode) {
        input.setEcToken(insuranceParam.getEcToken());

        YiBaoProperties.ChannelData channelData = getChannelData(type);
        String url = UrlUtils.concatSegments(yiBaoProperties.getBaseUrl1(), "hsa-pmc", "6203");
        Output6203 output6203 = post(url, getHeader(), null, getEncryptStr(input, channelData),
                new TypeReference<Output6203>() {}, channelData);
        return output6203;
    }

    @Override
    public Output6301 post6301(Input6301 input, PayChannelType type, String hospitalCode) {
        YiBaoProperties.ChannelData channelData = getChannelData(type);
        String url = UrlUtils.concatSegments(yiBaoProperties.getBaseUrl1(), "hsa-pmc", "6301");
        Output6301 output6301 = post(url, getHeader(), null, getEncryptStr(input, channelData),
                new TypeReference<Output6301>() {}, channelData);
        return output6301;
    }

    @Override
    public synchronized String signIn(String operatorId) {
        try {
            Date time = new Date();
            String today = TimeUtils.dateToString(time, "yyyyMMdd");
            if (SIGN_IN_RESTLT_MAP.containsKey(operatorId)) {
                Output9001 old = SIGN_IN_RESTLT_MAP.get(operatorId);
                if (old != null && today.equals(old.getDate())) {
                    return old.getSigninoutb().getSignNo();
                }
            }
            Input9001.SignIn signIn = new Input9001.SignIn();
            signIn.setOpterNo(hospitalProperties.getOpCode() + "");
            signIn.setMac(IpUtils.getMacAddress(null));
            signIn.setIp(IpUtils.getIpAddress(null));
            GJYBBasicRequest<Input9001> request = new GJYBBasicRequest<>("9001", new Input9001(signIn), null, null);
            GJYBBaseResponse<Output9001> response = post(request, new TypeReference<GJYBBaseResponse<Output9001>>() {});
            Output9001 output = response.getOutput();
            output.setDate(TimeUtils.dateToString(TimeUtils.convert(output.getSigninoutb().getSignTime()), "yyyyMMdd"));
            SIGN_IN_RESTLT_MAP.put(operatorId, output);
            return output.getSigninoutb().getSignNo();
        } catch (Exception e) {
            log.error("9001请求异常", e);
            throw new RuntimeException(e);
        }
    }

    /**
     * 调用node-red接口，返回的是content
     *
     * @param body                请求体
     * @param <T>                 返回类型
     * @return
     */
    private <T, E> GJYBBaseResponse<T> post(GJYBBasicRequest<E> body, TypeReference<GJYBBaseResponse<T>> returnTypeReference) {
        String url = yiBaoProperties.getBaseUrl1();
        String bodyStr = null;
        if (body != null) {
            bodyStr = StandardObjectMapper.stringify(body);
            log.info("调用医保 {} 接口:url:{}, data: {}", body.getInfno(), url, bodyStr);
        } else {
            log.info("调用医保 接口:url:{}, data: {}", url, bodyStr);
        }
        try (Response response = OkHttpUtils.post(url, Headers.of("apikey", yiBaoProperties.getApiKey()), null, bodyStr)) {
            if (response.isSuccessful()) {
                if (response.body() == null) {
                    log.info("调用医保 接口 返回:null");
                    response.close();
                    return null;
                }
                String result = OkHttpUtils.getResponseBody(response).orElse(null);
                log.info("调用医保 接口url:{} 返回：{}", url, result);
                GJYBBaseResponse<T> responseType = StandardObjectMapper.getInstance().readValue(
                        result, returnTypeReference);

                responseType.setSourceString(result);
                // 有些接口需要加密,有些接口不需要加密
                if (responseType.getInfcode() == 0) {
                    return responseType;
                } else {
                    log.info("国家医保 API调用成功-但返回了业务错误：[code]: " + responseType.getInfcode() + " [msg]:"
                            + responseType.getErrMsg());
                    throw ErrorType.INTERNAL_SERVER_ERROR.toProblem(responseType.getErrMsg());
                }
            } else {
                throw ErrorType.INTERNAL_SERVER_ERROR.toProblem(
                        "国家医保 API调用失败：" + OkHttpUtils.getResponseBody(response).orElse(null));
            }
        } catch (IOException e) {
            log.error(e.getMessage(), e);
            throw ErrorType.INTERNAL_SERVER_ERROR.toProblem(e.getMessage());
        }
    }

    private Map<String, String> getHeader() {
        Map<String, String> header = Maps.newHashMap();
        header.put("apikey", yiBaoProperties.getApiKey());
        return header;
    }
}
