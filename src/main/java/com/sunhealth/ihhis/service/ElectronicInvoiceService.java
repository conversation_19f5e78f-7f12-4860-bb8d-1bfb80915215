package com.sunhealth.ihhis.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.sunhealth.ihhis.model.dto.electronicInvoice.ElectronicInvoice;
import com.sunhealth.ihhis.model.dto.electronicInvoice.ElectronicInvoiceFileReq;
import com.sunhealth.ihhis.model.dto.electronicInvoice.ElectronicInvoiceFile;
import com.sunhealth.ihhis.model.dto.electronicInvoice.ElectronicInvoiceReq;

/**
 * <AUTHOR> jzs
 * @Date : 2024-07-23
 */
public interface ElectronicInvoiceService {
    ElectronicInvoiceFile getElectronicInvoiceFile(ElectronicInvoiceFileReq req);

    Page<ElectronicInvoice> getElectronicInvoiceList(ElectronicInvoiceReq req);
}
