package com.sunhealth.ihhis.service;

import com.sunhealth.ihhis.model.dq.sh.insurance.response.CallBackSH03;
import com.sunhealth.ihhis.model.dto.register.*;
import com.sunhealth.ihhis.model.entity.yibao.RegOnlineGjYiBaoUploadFeeRecord;
import com.sunhealth.ihhis.model.entity.yibao.RegOnlineSHYiBaoUploadFeeRecord;
import com.sunhealth.ihhis.model.insurance.request.CallBack6302;

import java.util.List;
import java.util.Map;
import org.springframework.transaction.annotation.Transactional;

public interface RegisterService {

    /**
     * 门诊挂号预算
     *
     * @param req
     * @return
     */
    PreRegistrationResult preRegister(PreRegisterReq req);

    /**
     * 免费挂号
     *
     * @param req
     * @return
     */
    FreeRegisterResult freeRegister(FreeRegisterReq req);

    /**
     * 门诊挂号预算撤销
     *
     * @param req
     */
    boolean cancelPreRegister(CancelPreRegistReq req);

    /**
     * 门诊挂号结算
     *
     * @param req
     * @param feeRecord 上海医保使用
     * @return
     */
    ConfirmRegistResult confirmRegister(ConfirmRegistReq req, RegOnlineSHYiBaoUploadFeeRecord feeRecord);
    void sendPatientInfo(Long regNo, String selfFlag);

    /**
     * 门诊挂号结算撤销
     *
     * @param req
     * @return
     */
    ReturnRegistResult cancelRegister(CancelRegisterReq req);

    /**
     * 上海医保退款
     * @param req
     * @return
     */
    ReturnRegistResult cancelShYb(CancelRegisterReq req);

    Map<String, String> canReturnRegister(Long regNo);

    /**
     * 查询医保结算状态，如果已结算，写入结算信息并返回
     * @param hospitalCode
     * @param regNo
     * @param push 是否需要推送
     * @return
     */
    GetOutpatientPayResult queryYiBaoPayResult(String hospitalCode, long regNo, boolean push);

    /**
     * 挂号医保结算回调成功逻辑
     * @param callBack6302
     * @param hospitalCode
     * @param feeRecord
     */
    void registerGJCallBackPaySuccess(CallBack6302 callBack6302, String hospitalCode, RegOnlineGjYiBaoUploadFeeRecord feeRecord);

    /**
     * 地区医保挂号支付成功回调
     * @param callBackSH03
     * @param hospitalCode
     * @param shFeeRecord
     */
    void registerSHCallBackPaySuccess(CallBackSH03 callBackSH03, String hospitalCode, RegOnlineSHYiBaoUploadFeeRecord shFeeRecord);

    void processRegistrationUpdate(Long regNo, Integer deptId, Integer doctorId);

    List<PatientRegistInfo> getRegistInfos(PatientRegistInfoReq req);
}
