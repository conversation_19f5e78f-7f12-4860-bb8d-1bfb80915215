package com.sunhealth.ihhis.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.sunhealth.ihhis.common.PageBean;
import com.sunhealth.ihhis.model.entity.OperationLog;
import com.sunhealth.ihhis.model.vm.SelectOperationLogParam;
import org.aspectj.lang.ProceedingJoinPoint;

public interface LogService extends IService<OperationLog> {
    void saveLog(ProceedingJoinPoint point, Boolean result);

    PageBean<OperationLog> getLog(SelectOperationLogParam param);
}
