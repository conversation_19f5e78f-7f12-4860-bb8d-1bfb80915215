package com.sunhealth.ihhis.task.job;

import com.fasterxml.jackson.core.type.TypeReference;
import com.sunhealth.ihhis.model.vm.PushSendPatientInfoMessageParam;
import com.sunhealth.ihhis.service.RegisterService;
import com.sunhealth.ihhis.task.scheduler.SchedulerPersistence;
import com.sunhealth.ihhis.utils.AppContext;
import com.sunhealth.ihhis.utils.StandardObjectMapper;
import org.quartz.Job;
import org.quartz.JobDataMap;
import org.quartz.JobExecutionContext;
import org.quartz.JobExecutionException;

/**
 * quartz demo
 */
public class PushSendPatientInfoMessageJob implements Job {

    @Override
    public void execute(JobExecutionContext jobExecutionContext) throws JobExecutionException {
        // 获取传入参数
        JobDataMap map = jobExecutionContext.getJobDetail().getJobDataMap();
        String id = map.getString("id");
        String param = map.getString("param");
        PushSendPatientInfoMessageParam pushSendPatientInfoMessageParam = StandardObjectMapper.readValue(param,
                                                                                                 new TypeReference<PushSendPatientInfoMessageParam>() {});

        AppContext.getInstance(RegisterService.class).sendPatientInfo(pushSendPatientInfoMessageParam.getRegno(),
                                                                      pushSendPatientInfoMessageParam.getSelfFlag());

        SchedulerPersistence.removeSendPatientInfoScheduler(id);
    }

}
