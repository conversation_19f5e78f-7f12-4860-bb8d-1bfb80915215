package com.sunhealth.ihhis.task.job;

import com.fasterxml.jackson.core.type.TypeReference;
import com.sunhealth.ihhis.model.vm.PushInvoiceMessageParam;
import com.sunhealth.ihhis.utils.StandardObjectMapper;
import com.sunhealth.ihhis.service.HisAppApiClient;
import com.sunhealth.ihhis.task.scheduler.SchedulerPersistence;
import com.sunhealth.ihhis.utils.AppContext;
import org.quartz.Job;
import org.quartz.JobDataMap;
import org.quartz.JobExecutionContext;
import org.quartz.JobExecutionException;

/**
 * quartz demo
 */
public class PushInvoiceMessageJob implements Job {

    @Override
    public void execute(JobExecutionContext jobExecutionContext) throws JobExecutionException {
        // 获取传入参数
        JobDataMap map = jobExecutionContext.getJobDetail().getJobDataMap();
        String id = map.getString("id");
        String param = map.getString("param");
        PushInvoiceMessageParam pushInvoiceMessageParam = StandardObjectMapper.readValue(param,
                new TypeReference<PushInvoiceMessageParam>() {});
        AppContext.getInstance(HisAppApiClient.class).pushInvoiceMessage(pushInvoiceMessageParam);
        SchedulerPersistence.removeInvoiceScheduler(id);
    }

}
