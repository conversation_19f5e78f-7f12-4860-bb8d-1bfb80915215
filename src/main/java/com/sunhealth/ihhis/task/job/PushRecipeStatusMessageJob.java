package com.sunhealth.ihhis.task.job;

import com.fasterxml.jackson.core.type.TypeReference;
import com.sunhealth.ihhis.dao.his.ChargeItemViewMapper;
import com.sunhealth.ihhis.model.vm.PushInvoiceMessageParam;
import com.sunhealth.ihhis.model.vm.RecipeStatusMessageParam;
import com.sunhealth.ihhis.service.HisAppApiClient;
import com.sunhealth.ihhis.task.scheduler.SchedulerPersistence;
import com.sunhealth.ihhis.utils.AppContext;
import com.sunhealth.ihhis.utils.StandardObjectMapper;
import lombok.extern.java.Log;
import lombok.extern.slf4j.Slf4j;
import org.quartz.Job;
import org.quartz.JobDataMap;
import org.quartz.JobExecutionContext;
import org.quartz.JobExecutionException;

/**
 * quartz demo
 */
@Slf4j
public class PushRecipeStatusMessageJob implements Job {

    @Override
    public void execute(JobExecutionContext jobExecutionContext) throws JobExecutionException {

        // 获取传入参数
        JobDataMap map = jobExecutionContext.getJobDetail().getJobDataMap();
        String id = map.getString("id");
        String param = map.getString("param");
        RecipeStatusMessageParam pushInvoiceMessageParam = StandardObjectMapper.readValue(param,
                                                                                          new TypeReference<RecipeStatusMessageParam>() {});
        log.info("更新处方状态为0，任务执行，{}", pushInvoiceMessageParam);
        ChargeItemViewMapper chargeItemViewMapper = AppContext.getInstance(ChargeItemViewMapper.class);
        chargeItemViewMapper.updateRecipeDetailStatusWhereStatus(pushInvoiceMessageParam.getIds(), 0,-1);
        chargeItemViewMapper.updateApplyDetailCostStatusWhereStatus(pushInvoiceMessageParam.getIds(), 0,-1);
        chargeItemViewMapper.updateApplyDetailStatusWhereStatus(pushInvoiceMessageParam.getIds(), 0,-1);
        SchedulerPersistence.removeRecipeStatusScheduler(id);
    }

}
