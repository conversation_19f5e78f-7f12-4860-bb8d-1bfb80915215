package com.sunhealth.ihhis.task.scheduler;

import com.fasterxml.jackson.core.type.TypeReference;
import com.sunhealth.ihhis.config.HisHospitalProperties;
import com.sunhealth.ihhis.model.vm.PushInpatientGuanKongMessageParam;
import com.sunhealth.ihhis.model.vm.PushInvoiceMessageParam;
import com.sunhealth.ihhis.model.vm.PushSendPatientInfoMessageParam;
import com.sunhealth.ihhis.model.vm.PushUpdateRegisterOrderMessageParam;
import com.sunhealth.ihhis.model.vm.RecipeStatusMessageParam;
import com.sunhealth.ihhis.task.scheduler.vm.InpatientFlushGuanKongSchedulerVM;
import com.sunhealth.ihhis.task.scheduler.vm.RecipeStatusSchedulerVM;
import com.sunhealth.ihhis.task.scheduler.vm.SendPatientInfoSchedulerVM;
import com.sunhealth.ihhis.task.scheduler.vm.UpdateRegisterOrderSchedulerVM;
import com.sunhealth.ihhis.utils.StandardObjectMapper;
import com.sunhealth.ihhis.task.scheduler.vm.InvoiceSchedulerVM;
import com.sunhealth.ihhis.utils.UrlUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

import java.io.File;
import java.io.IOException;
import java.util.Date;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

@Slf4j
public class SchedulerPersistence {

    private static String schedulerStateFile = "scheduler_state.dat";
    private static ConcurrentHashMap<String, ConcurrentHashMap<String, Object>> schedulers = new ConcurrentHashMap<>();
    private static ConcurrentHashMap<String, ConcurrentHashMap<String, RecipeStatusSchedulerVM>> recipeSchedulers =
        new ConcurrentHashMap<>();
    private static ConcurrentHashMap<String, ConcurrentHashMap<String, InpatientFlushGuanKongSchedulerVM>> inpatientFlushGuanKongSchedulers =
        new ConcurrentHashMap<>();
    private static ConcurrentHashMap<String, ConcurrentHashMap<String, SendPatientInfoSchedulerVM>> sendPatientInfoSchedulers =
        new ConcurrentHashMap<>();

    private static ConcurrentHashMap<String, ConcurrentHashMap<String, UpdateRegisterOrderSchedulerVM>> updateRegisterOrderSchedulers =
        new ConcurrentHashMap<>();
    public static final String INVOICE_SCHEDULER_KEY =  "invoiceScheduler"; // 对应InvoiceSchedulerVM
    public static final String INPATIENT_FLUSH_GUANKONG_SCHEDULER_KEY =  "inpatientFlushGuanKongScheduler";
    public static final String RECIPE_STATUS_SCHEDULER_KEY =  "recipeStatusScheduler";
    public static final String SEND_PATIENT_INFO_SCHEDULER_KEY =  "sendPatientInfoScheduler";
    public static final String UPDATE_REGISTER_ORDER_SCHEDULER_KEY =  "updateRegisterOrderScheduler";

    public static void init(HisHospitalProperties hisHospitalProperties) {
        String path = hisHospitalProperties.getQuartzSchedulerFilePath();
        if (StringUtils.isNotBlank(path)) {
            schedulerStateFile = UrlUtils.concatSegments(path, schedulerStateFile);
        }
    }

    /**
     * 加载任务数据
     * @return
     */
    public static ConcurrentHashMap<String, ConcurrentHashMap<String, Object>> load() {
        try {
            File file = new File(SchedulerPersistence.schedulerStateFile);
            if (!file.exists()) {
                if (file.getParentFile() != null && !file.getParentFile().exists()) {
                    file.getParentFile().mkdirs();
                }
                file.createNewFile();
            } else {
                schedulers = StandardObjectMapper.getInstance().readValue(file,
                        new TypeReference<ConcurrentHashMap<String, ConcurrentHashMap<String, Object>>>() {});
            }
        } catch (IOException e) {
            log.error("读取保存的任务失败", e);
        }
        return schedulers;
    }

    /**
     * 添加发票任务,并保存未执行的任务到文件
     * @param id
     * @param doTime
     * @param param
     */
    public static synchronized void addInvoiceScheduler(String id, Date doTime, PushInvoiceMessageParam param) {
        Map<String, Object> invoiceSchedulerVMMap = schedulers.computeIfAbsent(INVOICE_SCHEDULER_KEY,
                k -> new ConcurrentHashMap<>());
        if (!invoiceSchedulerVMMap.containsKey(id)) {
            invoiceSchedulerVMMap.put(id, new InvoiceSchedulerVM(id, doTime, param));
            save();
        }
    }

    /**
     * 添加刷新住院管控任务,并保存未执行的任务到文件
     * @param id
     * @param doTime
     * @param param
     */
    public static synchronized void addInpatientFlushGuanKongScheduler(String id, Date doTime,
                                                                 PushInpatientGuanKongMessageParam param) {
        Map<String, InpatientFlushGuanKongSchedulerVM> inpatientFlushGuanKongSchedulerVMMap
            = inpatientFlushGuanKongSchedulers.computeIfAbsent(INPATIENT_FLUSH_GUANKONG_SCHEDULER_KEY,
                                                                                           k -> new ConcurrentHashMap<>());
        if (!inpatientFlushGuanKongSchedulerVMMap.containsKey(id)) {
            inpatientFlushGuanKongSchedulerVMMap.put(id, new InpatientFlushGuanKongSchedulerVM(id, doTime, param));
            save();
        }
    }

    /**
     * 添加上报就诊信息任务,并保存未执行的任务到文件
     * @param id
     * @param doTime
     * @param param
     */
    public static synchronized void addSendPatientInfoScheduler(String id, Date doTime,
                                                                       PushSendPatientInfoMessageParam param) {
        Map<String, SendPatientInfoSchedulerVM> inpatientFlushGuanKongSchedulerVMMap
            = sendPatientInfoSchedulers.computeIfAbsent(SEND_PATIENT_INFO_SCHEDULER_KEY,
                                                               k -> new ConcurrentHashMap<>());
        if (!inpatientFlushGuanKongSchedulerVMMap.containsKey(id)) {
            inpatientFlushGuanKongSchedulerVMMap.put(id, new SendPatientInfoSchedulerVM(id, doTime, param));
            save();
        }
    }

    /**
     * 添加处方状态更新任务,并保存未执行的任务到文件
     * @param id
     * @param doTime
     * @param param
     */
    public static synchronized void addRecipeStatusScheduler(String id, Date doTime, RecipeStatusMessageParam param) {
        ConcurrentHashMap<String, RecipeStatusSchedulerVM> schedulerVMConcurrentHashMap = recipeSchedulers.computeIfAbsent(
            RECIPE_STATUS_SCHEDULER_KEY,
            k -> new ConcurrentHashMap<>());
        if (!schedulerVMConcurrentHashMap.containsKey(id)) {
            schedulerVMConcurrentHashMap.put(id, new RecipeStatusSchedulerVM(id, doTime, param));
            save();
        }
    }

    /**
     * 添加更新挂号序号任务,并保存未执行的任务到文件
     * @param id
     * @param doTime
     * @param param
     */
    public static synchronized void addUpdateRegisterOrderScheduler(String id, Date doTime,
                                                                 PushUpdateRegisterOrderMessageParam param) {
        Map<String, Object> invoiceSchedulerVMMap = schedulers.computeIfAbsent(UPDATE_REGISTER_ORDER_SCHEDULER_KEY,
                                                                               k -> new ConcurrentHashMap<>());
        if (!invoiceSchedulerVMMap.containsKey(id)) {
            invoiceSchedulerVMMap.put(id, new UpdateRegisterOrderSchedulerVM(id, doTime, param));
            save();
        }
    }

    /**
     * 移除发票任务,并保存未执行的任务到文件
     * @param id
     */
    public static synchronized void removeInvoiceScheduler(String id) {
        Map<String, Object> invoiceSchedulerVMMap = schedulers.computeIfAbsent(INVOICE_SCHEDULER_KEY, k -> new ConcurrentHashMap<>());
        if (invoiceSchedulerVMMap.containsKey(id)) {
            invoiceSchedulerVMMap.remove(id);
            save();
        }
    }

    /**
     * 移除住院病人预交金管控刷新任务,并保存未执行的任务到文件
     * @param id
     */
    public static synchronized void removeInpatientFlushGuanKongScheduler(String id) {
        Map<String, InpatientFlushGuanKongSchedulerVM> invoiceSchedulerVMMap =
            inpatientFlushGuanKongSchedulers.computeIfAbsent(INPATIENT_FLUSH_GUANKONG_SCHEDULER_KEY,
                                                                          k -> new ConcurrentHashMap<>());
        if (invoiceSchedulerVMMap.containsKey(id)) {
            invoiceSchedulerVMMap.remove(id);
            save();
        }
    }

    /**
     * 移除更新处方状态任务
     * @param id
     */
    public static synchronized void removeRecipeStatusScheduler(String id) {
        ConcurrentHashMap<String, RecipeStatusSchedulerVM> schedulerVMConcurrentHashMap = recipeSchedulers.computeIfAbsent(
            RECIPE_STATUS_SCHEDULER_KEY,
            k -> new ConcurrentHashMap<>());
        if (schedulerVMConcurrentHashMap.containsKey(id)) {
            schedulerVMConcurrentHashMap.remove(id);
            save();
        }
    }

    /**
     * 移除上报就诊信息任务
     * @param id
     */
    public static synchronized void removeSendPatientInfoScheduler(String id) {
        ConcurrentHashMap<String, SendPatientInfoSchedulerVM> schedulerVMConcurrentHashMap = sendPatientInfoSchedulers.computeIfAbsent(
            SEND_PATIENT_INFO_SCHEDULER_KEY,
            k -> new ConcurrentHashMap<>());
        if (schedulerVMConcurrentHashMap.containsKey(id)) {
            schedulerVMConcurrentHashMap.remove(id);
            save();
        }
    }

    /**
     * 移除更新挂号序号任务
     * @param id
     */
    public static synchronized void removeUpdateRegisterOrderScheduler(String id) {
        ConcurrentHashMap<String, UpdateRegisterOrderSchedulerVM> schedulerVMConcurrentHashMap = updateRegisterOrderSchedulers.computeIfAbsent(
            UPDATE_REGISTER_ORDER_SCHEDULER_KEY,
            k -> new ConcurrentHashMap<>());
        if (schedulerVMConcurrentHashMap.containsKey(id)) {
            schedulerVMConcurrentHashMap.remove(id);
            save();
        }
    }

    /**
     * 保存未执行的任务到文件
     */
    public static synchronized void save() {
        try {
            StandardObjectMapper.getInstance().writeValue(new File(schedulerStateFile), schedulers);
        } catch (IOException e) {
            log.error("quartz任务文件写入异常");
        }
    }
}
