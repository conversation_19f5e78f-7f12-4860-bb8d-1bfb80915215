package com.sunhealth.ihhis.task.scheduler.vm;

import com.sunhealth.ihhis.model.vm.PushInpatientGuanKongMessageParam;
import com.sunhealth.ihhis.model.vm.PushSendPatientInfoMessageParam;
import java.io.Serializable;
import java.util.Date;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Getter
@Setter
@NoArgsConstructor
public class SendPatientInfoSchedulerVM implements Serializable {

    private String id;
    private Date doTime;
    private PushSendPatientInfoMessageParam param;

    public SendPatientInfoSchedulerVM(String id, Date doTime, PushSendPatientInfoMessageParam param) {
        this.id = id;
        this.doTime = doTime;
        this.param = param;
    }
}
