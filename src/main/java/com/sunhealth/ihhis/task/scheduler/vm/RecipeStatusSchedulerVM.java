package com.sunhealth.ihhis.task.scheduler.vm;

import com.sunhealth.ihhis.model.vm.PushInvoiceMessageParam;
import com.sunhealth.ihhis.model.vm.RecipeStatusMessageParam;
import java.io.Serializable;
import java.util.Date;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Getter
@Setter
@NoArgsConstructor
public class RecipeStatusSchedulerVM implements Serializable {

    private String id;
    private Date doTime;
    private RecipeStatusMessageParam param;

    public RecipeStatusSchedulerVM(String id, Date doTime, RecipeStatusMessageParam param) {
        this.id = id;
        this.doTime = doTime;
        this.param = param;
    }
}
