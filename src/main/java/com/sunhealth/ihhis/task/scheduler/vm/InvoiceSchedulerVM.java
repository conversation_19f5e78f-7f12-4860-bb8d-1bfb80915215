package com.sunhealth.ihhis.task.scheduler.vm;

import com.sunhealth.ihhis.model.vm.PushInvoiceMessageParam;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.io.Serializable;
import java.util.Date;

@Getter
@Setter
@NoArgsConstructor
public class InvoiceSchedulerVM implements Serializable {

    private String id;
    private Date doTime;
    private PushInvoiceMessageParam param;

    public InvoiceSchedulerVM(String id, Date doTime, PushInvoiceMessageParam param) {
        this.id = id;
        this.doTime = doTime;
        this.param = param;
    }
}
