package com.sunhealth.ihhis.task.scheduler;

import com.sunhealth.ihhis.model.vm.PushInvoiceMessageParam;
import com.sunhealth.ihhis.utils.StandardObjectMapper;
import com.sunhealth.ihhis.task.job.PushInvoiceMessageJob;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.quartz.*;
import org.quartz.impl.StdSchedulerFactory;

import java.util.Date;
import java.util.UUID;

@Slf4j
public class InvoiceScheduler {

    /**
     * 创建发票任务, 2秒后执行1次
     * @param param 参数
     */
    public static void delayTask(PushInvoiceMessageParam param) {
        log.info("发票任务创建，" + StandardObjectMapper.stringify(param));
        try {
            delayTask(param, null, null);
        } catch (SchedulerException e) {
            log.error("发票任务创建失败", e);
        }
    }

    /**
     * 创建发票任务, 2秒后执行1次
     * @param param 参数
     * @param doTime 执行时间，如果不传，默认为2秒后
     * @param id 任务id,不传时自动生成，用于重启后启动未执行完成的任务，正常不需要传值
     * @throws SchedulerException
     */
    public static void delayTask(PushInvoiceMessageParam param, Date doTime, String id) throws SchedulerException {
        Scheduler scheduler = StdSchedulerFactory.getDefaultScheduler();
        id = StringUtils.isBlank(id) ? UUID.randomUUID().toString() : id;
        String name = "invoice." + id;
        JobDetail jobDetail = scheduler.getJobDetail(JobKey.jobKey(name, "invoiceGroup"));
        Date date = doTime == null ? new Date(System.currentTimeMillis() + 2_000) : doTime;
        log.info("发票任务创建，任务将在 {} 执行", date);

        SchedulerPersistence.addInvoiceScheduler(id, date, param);
        // 1. 已经存在的,跳过
        if (jobDetail == null) {
            // 2. 创建JobDetail实例，并与DemoJob类绑定(Job执行内容)
            jobDetail = JobBuilder
                .newJob(PushInvoiceMessageJob.class).withIdentity(name, "invoiceGroup").build();
            // 3. 构建Trigger实例,30秒之后执行1次
            Trigger trigger = TriggerBuilder
                .newTrigger()
                .withIdentity("invoice.trigger." + name, "invoiceTriggerGroup")
                .startAt(date)
                .withSchedule(SimpleScheduleBuilder.simpleSchedule())
                .build();
            // 4. 添加参数
            jobDetail.getJobDataMap().put("id", id);
            jobDetail.getJobDataMap().put("param", StandardObjectMapper.stringify(param));
            // 5. 添加调度任务
            scheduler.scheduleJob(jobDetail, trigger);
        } else {
            log.info("发票任务重复，已存在相同的任务");
        }
    }

}
