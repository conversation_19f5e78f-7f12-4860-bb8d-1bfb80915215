package com.sunhealth.ihhis.task.scheduler.vm;

import com.sunhealth.ihhis.model.vm.PushInpatientGuanKongMessageParam;
import com.sunhealth.ihhis.model.vm.PushInvoiceMessageParam;
import java.io.Serializable;
import java.util.Date;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Getter
@Setter
@NoArgsConstructor
public class InpatientFlushGuanKongSchedulerVM implements Serializable {

    private String id;
    private Date doTime;
    private PushInpatientGuanKongMessageParam param;

    public InpatientFlushGuanKongSchedulerVM(String id, Date doTime, PushInpatientGuanKongMessageParam param) {
        this.id = id;
        this.doTime = doTime;
        this.param = param;
    }
}
