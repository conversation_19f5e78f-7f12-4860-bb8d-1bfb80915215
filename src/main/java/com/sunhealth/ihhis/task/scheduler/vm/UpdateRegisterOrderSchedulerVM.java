package com.sunhealth.ihhis.task.scheduler.vm;

import com.sunhealth.ihhis.model.vm.PushSendPatientInfoMessageParam;
import com.sunhealth.ihhis.model.vm.PushUpdateRegisterOrderMessageParam;
import java.io.Serializable;
import java.util.Date;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Getter
@Setter
@NoArgsConstructor
public class UpdateRegisterOrderSchedulerVM implements Serializable {

    private String id;
    private Date doTime;
    private PushUpdateRegisterOrderMessageParam param;

    public UpdateRegisterOrderSchedulerVM(String id, Date doTime, PushUpdateRegisterOrderMessageParam param) {
        this.id = id;
        this.doTime = doTime;
        this.param = param;
    }
}
