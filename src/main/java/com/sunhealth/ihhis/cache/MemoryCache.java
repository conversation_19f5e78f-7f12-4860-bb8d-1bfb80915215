package com.sunhealth.ihhis.cache;

import com.google.common.cache.Cache;
import com.google.common.cache.CacheBuilder;
import com.sunhealth.ihhis.model.dq.sh.insurance.EcTokenDTO;
import com.sunhealth.ihhis.model.entity.Dept;
import org.springframework.stereotype.Component;

import java.util.Map;
import java.util.concurrent.TimeUnit;

@Component
public class MemoryCache {

    private final Cache<String, Map<Integer, Dept>> hospitalDepts;
    private final Cache<String, EcTokenDTO> ectokens;
    private final Cache<String, Integer> onlineRecipeAmount;

    public MemoryCache() {
        this.hospitalDepts = CacheBuilder.newBuilder().expireAfterWrite(30, TimeUnit.SECONDS).build();
        this.ectokens = CacheBuilder.newBuilder().expireAfterWrite(2, TimeUnit.HOURS).build();
        this.onlineRecipeAmount = CacheBuilder.newBuilder().expireAfterWrite(2, TimeUnit.HOURS).build();
    }

    public void putHospitalDepts(String hospitalCode, Map<Integer, Dept> value){
        if (value == null) {
            this.hospitalDepts.cleanUp();
        } else {
            this.hospitalDepts.put(hospitalCode, value);
        }
    }

    public Map<Integer, Dept> getHospitalDepts(String hospitalCode) {
        return this.hospitalDepts.getIfPresent(hospitalCode);
    }

    public void putEctoken(String ecQrCode, EcTokenDTO value){
        if (value != null) {
            this.ectokens.put(ecQrCode, value);
        }
    }

    public EcTokenDTO getEctoken(String ecQrCode) {
        return this.ectokens.getIfPresent(ecQrCode);
    }

    public void putOnlineRecipeAmount(String recipeNo, Integer amount){
        this.onlineRecipeAmount.put(recipeNo, amount);
    }

    public Integer getOnlineRecipeAmount(String recipeNo){
        return this.onlineRecipeAmount.getIfPresent(recipeNo);
    }

}
