package com.sunhealth.ihhis.enums;

public enum PayChannelType {

    WECHAT_MINI_PROGRAM("微信小程序", "1", 17),
    ALIPAY_MINI_PROGRAM("支付宝小程序", "2", 11),
    WECHAT_OFFICIAL_ACCOUNT("微信公众号", "3", 13);

    private final String key;
    private final String value;
    private final Integer hisValue;

    PayChannelType(String key, String value, Integer hisValue) {
        this.key = key;
        this.value = value;
        this.hisValue = hisValue;
    }

    public String getValue() {
        return value;
    }
    public Integer getByHisValue() {
        return hisValue;
    }

    public String getKey() {
        return key;
    }

    public static PayChannelType getByValue(String value) {
        for (PayChannelType payChannelType : PayChannelType.values()) {
            if (payChannelType.getValue().equals(value)) {
                return payChannelType;
            }
        }
        return null;
    }

    public static PayChannelType getByHisValue(Integer value) {
        for (PayChannelType payChannelType : PayChannelType.values()) {
            if (payChannelType.getByHisValue().equals(value)) {
                return payChannelType;
            }
        }
        return null;
    }
}
