package com.sunhealth.ihhis.enums;

import lombok.Getter;

@Getter
public enum ItemCategoryEnum {
    ConsultationFee(1, "诊疗费"),
    TreatmentFee(2, "治疗费"),
    OperationFee(3, "手材费"),
    ExaminationFee(4, "检查费"),
    LaboratoryFee(5, "化验费"),
    FilmFee(6, "摄片费"),
    PerspectiveFee(7, "透视费"),
    WesternMedFee(8, "西药费"),
    ChinesePatMedFee(9, "中成药费"),
    ChineseHerbMedFee(10, "中草药费"),
    OtherFee(11, "其它"),

    RegistFee(27, "挂号费"),
    DisFee(2, "诊疗费(挂号用)"),
    CardFee(22, "磁卡费");

    private final Integer code;

    private final String msg;

    /**
     * @param code
     * @param msg
     */
    ItemCategoryEnum(Integer code, String msg) {
        this.code = code;
        this.msg = msg;
    }

}
