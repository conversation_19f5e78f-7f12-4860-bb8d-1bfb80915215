package com.sunhealth.ihhis.enums;

public enum Gender {
    /**
     * 未知
     */
    UNKNOWN("未知", 0),
    /**
     * 男性
     */
    MALE("男性", 1),
    /**
     * 女性
     */
    FEMALE("女性", 2);

    private final String key;
    private final Integer value;

    private Gender(String key, Integer value) {
        this.key = key;
        this.value = value;
    }

    public String getKey() {return key;}
    public Integer getValue() {
        return value;
    }

    public static Gender lookupByKey(String key) {
        switch (key) {
            case "男":
            case "男性":
                return MALE;
            case "女":
            case "女性":
                return FEMALE;
            default:
                return UNKNOWN;
        }
    }

    public static Gender lookupByValue(Integer value) {
        switch (value) {
            case 1:
                return MALE;
            case 2:
                return FEMALE;
            default:
                return UNKNOWN;
        }
    }
}
