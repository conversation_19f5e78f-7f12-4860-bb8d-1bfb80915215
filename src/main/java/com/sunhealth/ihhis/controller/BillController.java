package com.sunhealth.ihhis.controller;

import com.alibaba.fastjson.JSON;
import com.sunhealth.ihhis.annotations.HisLog;
import com.sunhealth.ihhis.model.dto.bill.HisBillReq;
import com.sunhealth.ihhis.model.dto.bill.HisBillRes;
import com.sunhealth.ihhis.model.vm.BaseResponse;
import com.sunhealth.ihhis.service.BillService;
import com.sunhealth.ihhis.service.CurrentHospital;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@Slf4j
@RestController
@RequestMapping()
@Api(tags = "账单")
@AllArgsConstructor
public class BillController {

    private final BillService billService;

    @PostMapping("/B_TradeHisBill")
    @ApiOperation("查询HIS交易账单")
    @HisLog("查询HIS交易账单")
    public BaseResponse<HisBillRes> getTradeHisBill(@RequestBody HisBillReq req) {
        req.setHospitalCode(CurrentHospital.getCode());
        log.info("查询HIS交易账单Req：{}", JSON.toJSONString(req));
        HisBillRes tradeHisBill = billService.getTradeHisBill(req);
        return BaseResponse.success(tradeHisBill);
    }
}
