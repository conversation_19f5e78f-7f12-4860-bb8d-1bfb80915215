package com.sunhealth.ihhis.controller;

import com.alibaba.fastjson.JSON;
import com.sunhealth.ihhis.annotations.HisLog;
import com.sunhealth.ihhis.model.dto.medicalrecord.PatientMedicalRecord;
import com.sunhealth.ihhis.model.dto.outpatientcharge.OutpatientRecipeReq;
import com.sunhealth.ihhis.model.vm.BaseResponse;
import com.sunhealth.ihhis.service.CurrentHospital;
import com.sunhealth.ihhis.service.MedicalRecordService;
import com.sunhealth.ihhis.utils.TimeUtils;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import java.util.List;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping()
@Api(tags = "患者病历相关api")
@Slf4j
@AllArgsConstructor
public class OutPatientMedicalRecordController {

    private final MedicalRecordService medicalRecordService;

    @PostMapping("/Q_GetPatientMedicalRecord")
    @ApiOperation("查询患者病历列表")
    @HisLog("查询患者病历列表")
    public BaseResponse<List<PatientMedicalRecord>> getOutpatientRecipeList(@RequestBody OutpatientRecipeReq req) {
        req.setHospitalCode(CurrentHospital.getCode());
        log.info("查询患者病历列表req：{}", JSON.toJSONString(req));
        req.setBeginDate(TimeUtils.convert(req.getBegin_date()));
        req.setEndDate(TimeUtils.getEndOfDay(TimeUtils.convert(req.getEnd_date())));
        return BaseResponse.success(medicalRecordService.getOutpatientMedicalRecords(req));
    }
}