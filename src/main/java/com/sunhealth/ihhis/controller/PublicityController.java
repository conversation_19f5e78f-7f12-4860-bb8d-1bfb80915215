package com.sunhealth.ihhis.controller;

import com.alibaba.fastjson.JSON;
import com.sunhealth.ihhis.model.dto.publicity.PricePublicityReq;
import com.sunhealth.ihhis.model.dto.publicity.PricePublicityRes;
import com.sunhealth.ihhis.model.vm.BaseResponse;
import com.sunhealth.ihhis.service.CurrentHospital;
import com.sunhealth.ihhis.service.PublicityService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

@Slf4j
@RestController
@RequestMapping()
@Api(tags = "公示相关API")
@AllArgsConstructor
public class PublicityController {

    private final PublicityService publicityService;

    @ApiOperation(value = "查询药品和项目价格公示列表")
    @PostMapping("/Q_GetPricePublicityList")
    public BaseResponse<List<PricePublicityRes>> getPricePublicityList(@RequestBody PricePublicityReq request) {
        request.setHospitalCode(CurrentHospital.getCode());
        log.info("查询药品和项目价格公示列表req：{}", JSON.toJSONString(request));
        List<PricePublicityRes> pricePublicityList = publicityService.getPricePublicityList(request);
        return BaseResponse.success(pricePublicityList);
    }
}
