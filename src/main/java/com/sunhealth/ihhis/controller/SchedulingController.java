package com.sunhealth.ihhis.controller;

import com.alibaba.fastjson.JSON;
import com.sunhealth.ihhis.annotations.HisLog;
import com.sunhealth.ihhis.model.dto.schedule.*;
import com.sunhealth.ihhis.model.vm.BaseResponse;
import com.sunhealth.ihhis.service.CurrentHospital;
import com.sunhealth.ihhis.service.SchedulingService;
import com.sunhealth.ihhis.utils.TimeUtils;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.Date;
import java.util.List;

@Slf4j
@RestController
@RequestMapping()
@AllArgsConstructor
@Api(tags = "排班相关Api")
public class SchedulingController {

    private final SchedulingService schedulingService;

    @PostMapping("/Q_GetCurrentDaySchedulingSourceNumber")
    @ApiOperation("获取指定当天排班的号序信息(包括普通号和专家号)")
    @HisLog("查询指定排班的号序信息")
    public BaseResponse<List<SourceNumberRes>> getCurrentDaySchedulingSourceNumber(@RequestBody SourceNumberReq req) {
        req.setHospitalCode(CurrentHospital.getCode());
        log.info("获取指定当天排班的号序信息Req：{}", JSON.toJSONString(req));
        return BaseResponse.success(schedulingService.getCurrentDaySchedulingSourceNumber(req));
    }

    @PostMapping("/Q_GetSourceDetails")
    @ApiOperation("查询全院预约号源信息（含科室号源和医生号源）")
    @HisLog("查询全院预约号源信息")
    public BaseResponse<List<SourceDetailsRes>> getSourceDetails(@RequestBody SourceDetailsReq req) {
        req.setHospitalCode(CurrentHospital.getCode());
        log.info("查询全院预约号源信息Req：{}", JSON.toJSONString(req));
        req.setBeginDate(TimeUtils.convert(req.getBegin_date()));
        req.setEndDate(TimeUtils.convert(req.getEnd_date()));
        return BaseResponse.success(schedulingService.getSourceDetails(req));
    }

    @PostMapping("/Q_GetSchedulingDeptList")
    @ApiOperation("查询时间区间内的有排班的科室信息（包含专家出诊的科室）")
    @HisLog("查询时间区间内的有排班的科室信息")
    public BaseResponse<List<DeptListRes>> getSchedulingDeptList(@RequestBody DeptListReq req) {
        req.setHospitalCode(CurrentHospital.getCode());
        log.info("查询时间区间内的有排班的科室信息Req：{}", JSON.toJSONString(req));
        return BaseResponse.success(schedulingService.getSchedulingDeptList(req));
    }

    @PostMapping("/Q_GetSchedulingDeptSourceDetails")
    @ApiOperation("查询预约科室号源信息")
    @HisLog("查询预约科室号源信息")
    public BaseResponse<List<DeptSourceDetailsRes>> getSchedulingDeptSourceDetails(@RequestBody DeptSourceDetailsReq req) {
        req.setHospitalCode(CurrentHospital.getCode());
        log.info("查询预约科室号源信息Req：{}", JSON.toJSONString(req));
        return BaseResponse.success(schedulingService.getSchedulingDeptSourceDetails(req));
    }

    @PostMapping("/Q_GetSchedulingDoctorSourceDetails")
    @ApiOperation("查询预约医生号源信息")
    @HisLog("查询预约医生号源信息")
    public BaseResponse<List<DoctorSourceDetailsRes>> getSchedulingDoctorSourceDetails(@RequestBody DoctorSourceDetailsReq req) {
        req.setHospitalCode(CurrentHospital.getCode());
        log.info("查询预约医生号源信息Req：{}", JSON.toJSONString(req));
        return BaseResponse.success(schedulingService.getSchedulingDoctorSourceDetails(req));
    }

    @PostMapping("/Q_GetCurrentDayAppointmentList")
    @ApiOperation("查询全院当天号源信息(含科室号源和医生号源)")
    @HisLog("查询全院当天号源信息(含科室号源和医生号源)")
    public BaseResponse<List<CurrentDayAppointmentRes>> getGetCurrentDayAppointmentList(@RequestBody SourceDetailsReq req) {
        req.setHospitalCode(CurrentHospital.getCode());
        log.info("查询全院当天号源信息(含科室号源和医生号源)：{}", JSON.toJSONString(req));
        req.setDept_id(null);
        req.setBeginDate(TimeUtils.getStartOfDay(new Date()));
        req.setEndDate(TimeUtils.getStartOfDay(new Date()));
        return BaseResponse.success(schedulingService.getGetCurrentDayAppointmentList(req));
    }

    @PostMapping("/Q_GetCurrentDayDoctorSourceDetail")
    @ApiOperation("查询全院当天号源信息（含科室号源和医生号源）")
    @HisLog("查询全院当天号源信息（含科室号源和医生号源）")
    public BaseResponse<List<CurrentDoctorSourceDetailsRes>> getCurrentDayDoctorSourceDetail(@RequestBody CurrentDoctorSourceDetailsReq req) {
        req.setHospitalCode(CurrentHospital.getCode());
        log.info("查询全院当天号源信息（含科室号源和医生号源）Req：{}", JSON.toJSONString(req));
        req.setBeginDate(TimeUtils.getStartOfDay(new Date()));
        req.setEndDate(TimeUtils.getStartOfDay(new Date()));
        return BaseResponse.success(schedulingService.getCurrentDayDoctorSourceDetail(req));
    }

}
