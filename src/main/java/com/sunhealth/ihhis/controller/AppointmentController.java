package com.sunhealth.ihhis.controller;

import com.alibaba.fastjson.JSON;
import com.sunhealth.ihhis.annotations.HisLog;
import com.sunhealth.ihhis.model.dto.register.CancelAppointmentReq;
import com.sunhealth.ihhis.model.dto.register.CancelPreRegistReq;
import com.sunhealth.ihhis.model.dto.register.CancelRegisterReq;
import com.sunhealth.ihhis.model.dto.register.ConfirmRegistReq;
import com.sunhealth.ihhis.model.dto.register.ConfirmRegistResult;
import com.sunhealth.ihhis.model.dto.register.LockNumberResult;
import com.sunhealth.ihhis.model.dto.register.PreRegisterReq;
import com.sunhealth.ihhis.model.dto.register.PreRegistrationResult;
import com.sunhealth.ihhis.model.dto.register.ReturnRegistResult;
import com.sunhealth.ihhis.model.dto.register.SaveAppointmentReq;
import com.sunhealth.ihhis.model.dto.register.SaveAppointmentResult;
import com.sunhealth.ihhis.model.vm.BaseResponse;
import com.sunhealth.ihhis.model.vm.ResponseStatus;
import com.sunhealth.ihhis.service.AppointmentService;
import com.sunhealth.ihhis.service.CurrentHospital;
import com.sunhealth.ihhis.service.RegisterService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping()
@Api(tags = "预约相关api")
@Slf4j
@AllArgsConstructor
public class AppointmentController {

    private final AppointmentService appointmentService;

    @PostMapping("/B_SaveAppointment")
    @ApiOperation("门诊预约登记")
    @HisLog("门诊预约登记")
    public BaseResponse<SaveAppointmentResult> preRegist(
        @RequestBody SaveAppointmentReq req) {
        req.setHospitalCode(CurrentHospital.getCode());
        log.info("门诊预约登记req：{}", JSON.toJSONString(req));
        return BaseResponse.success(appointmentService.appointment(req));
    }

    @PostMapping("/B_CancelAppointment")
    @ApiOperation("门诊预约登记撤销")
    @HisLog("门诊预约登记撤销")
    public BaseResponse<LockNumberResult> cancelAppointment(
        @RequestBody CancelAppointmentReq req) {
        req.setHospitalCode(CurrentHospital.getCode());
        log.info("门诊预约登记撤销req：{}", JSON.toJSONString(req));
        return BaseResponse.success(appointmentService.returnAppointment(req));
    }

}

