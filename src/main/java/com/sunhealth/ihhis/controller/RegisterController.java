package com.sunhealth.ihhis.controller;

import com.alibaba.fastjson.JSON;
import com.sunhealth.ihhis.annotations.HisLog;
import com.sunhealth.ihhis.model.dto.register.*;
import com.sunhealth.ihhis.model.vm.BaseResponse;
import com.sunhealth.ihhis.model.vm.ResponseStatus;
import com.sunhealth.ihhis.service.CurrentHospital;
import com.sunhealth.ihhis.service.RegisterService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

@RestController
@RequestMapping()
@Api(tags = "挂号相关api")
@Slf4j
@AllArgsConstructor
public class RegisterController {

    private final RegisterService registerService;

    @PostMapping("/B_PreRegist")
    @ApiOperation("门诊挂号预算")
    @HisLog("门诊挂号预算")
    public BaseResponse<PreRegistrationResult> preRegist(
        @RequestBody PreRegisterReq req) {
        req.setHospitalCode(CurrentHospital.getCode());
        log.info("门诊挂号预算req：{}", JSON.toJSONString(req));
//        throw new RuntimeException("暂不支持挂号");
        return BaseResponse.success(registerService.preRegister(req));
    }

    @PostMapping("/B_CancelPreRegist")
    @ApiOperation("门诊挂号预算撤销")
    @HisLog("门诊挂号预算撤销")
    public BaseResponse<ResponseStatus> cancelPreRegist(
        @RequestBody CancelPreRegistReq req) {
        req.setHospitalCode(CurrentHospital.getCode());
        log.info("门诊挂号预算撤销req：{}", JSON.toJSONString(req));

        registerService.cancelPreRegister(req);
        return BaseResponse.success(new ResponseStatus(true, "成功"));
    }

    @PostMapping("/B_ConfirmRegist")
    @ApiOperation("门诊挂号结算")
    @HisLog("门诊挂号结算")
    public BaseResponse<ConfirmRegistResult> confirmRegist(
        @RequestBody ConfirmRegistReq req) {
        req.setHospitalCode(CurrentHospital.getCode());
        log.info("门诊挂号结算req：{}", JSON.toJSONString(req));
        return BaseResponse.success(registerService.confirmRegister(req, null));
    }

    @PostMapping("/B_ReturnRegist")
    @ApiOperation("门诊挂号取消结算")
    @HisLog("门诊挂号取消结算")
    public BaseResponse<ReturnRegistResult> returnRegist(
        @RequestBody CancelRegisterReq req) {
        req.setHospitalCode(CurrentHospital.getCode());
        log.info("门诊挂号取消结算req：{}", JSON.toJSONString(req));
        return BaseResponse.success(registerService.cancelRegister(req));
    }


    @PostMapping("/Q_GetRegistListByPatId")
    @ApiOperation("查询患者门诊挂号记录")
    @HisLog("查询患者门诊挂号记录")
    public BaseResponse<List<PatientRegistInfo>> getRegistList(
        @RequestBody PatientRegistInfoReq req) {
        req.setHospitalCode(CurrentHospital.getCode());
        log.info("查询患者门诊挂号记录：{}", JSON.toJSONString(req));
        return BaseResponse.success(registerService.getRegistInfos(req));
    }

//    @PostMapping("/B_ReturnShYb")
//    @ApiOperation("上海医保退款,测试用")
//    @HisLog("上海医保退款,测试用")
//    public BaseResponse<ReturnRegistResult> returnShYb(@RequestBody CancelRegisterReq req) {
//        req.setHospitalCode(CurrentHospital.getCode());
//        log.info("上海医保退款req：{}", JSON.toJSONString(req));
//        return BaseResponse.success(registerService.cancelShYb(req));
//    }

}

