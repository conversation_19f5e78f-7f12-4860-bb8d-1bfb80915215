package com.sunhealth.ihhis.controller;

import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.sunhealth.ihhis.annotations.HisLog;
import com.sunhealth.ihhis.model.dto.DeptInfo;
import com.sunhealth.ihhis.model.dto.DictDataQueryReq;
import com.sunhealth.ihhis.model.dto.DoctorInfo;
import com.sunhealth.ihhis.model.vm.BaseResponse;
import com.sunhealth.ihhis.model.vm.BaseResponseData;
import com.sunhealth.ihhis.service.CurrentHospital;
import com.sunhealth.ihhis.service.DeptService;
import com.sunhealth.ihhis.service.MedicalWorkerService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

@RestController
@RequestMapping
@Api(tags = "医生，科室数据")
@Slf4j
public class DictDeptDoctorController {

    @Autowired
    private DeptService deptService;
    @Autowired
    private MedicalWorkerService medicalWorkerService;

    @PostMapping("/Q_GetDoctorList")
    @ApiOperation("医生字典信息查询")
    @HisLog("医生字典信息查询")
    public BaseResponse<List<DoctorInfo>> getDoctorList(
        @RequestBody DictDataQueryReq req) {
        req.setHospitalCode(CurrentHospital.getCode());
        log.info("医生字典信息查询req：{}", JSON.toJSONString(req));
        Page<DoctorInfo> doctorInfoPage = medicalWorkerService.findAll(req);
        return BaseResponse.success(new BaseResponseData<>(doctorInfoPage));
    }

    @PostMapping("/Q_GetDeptList")
    @ApiOperation("科室字典信息查询")
    @HisLog("科室字典信息查询")
    public BaseResponse<List<DeptInfo>> getDeptList(
        @RequestBody DictDataQueryReq req) {
        req.setHospitalCode(CurrentHospital.getCode());
        log.info("科室字典信息查询req：{}", JSON.toJSONString(req));
        Page<DeptInfo> deptPage = deptService.findAll(req);
        return BaseResponse.success(new BaseResponseData<>(deptPage));
    }
}
