package com.sunhealth.ihhis.controller;

import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.sunhealth.ihhis.annotations.HisLog;
import com.sunhealth.ihhis.config.HisHospitalProperties;
import com.sunhealth.ihhis.model.dto.electronicInvoice.ElectronicInvoice;
import com.sunhealth.ihhis.model.dto.electronicInvoice.ElectronicInvoiceFileReq;
import com.sunhealth.ihhis.model.dto.electronicInvoice.ElectronicInvoiceFile;
import com.sunhealth.ihhis.model.dto.electronicInvoice.ElectronicInvoiceReq;
import com.sunhealth.ihhis.model.dto.outpatientcharge.CreateHuLiDaoJiaChargeReq;
import com.sunhealth.ihhis.model.dto.outpatientcharge.CreateHuLiDaoJiaChargeResult;
import com.sunhealth.ihhis.model.vm.BaseResponse;
import com.sunhealth.ihhis.model.vm.BaseResponseData;
import com.sunhealth.ihhis.service.CurrentHospital;
import com.sunhealth.ihhis.service.ElectronicInvoiceService;
import com.sunhealth.ihhis.service.HuLiDaoJiaBusinessService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

@Slf4j
@RestController
@RequestMapping()
@Api(tags = "电子发票查询")
@AllArgsConstructor
public class ElectronicInvoiceController {

    private final ElectronicInvoiceService electronicInvoiceService;
    private final HisHospitalProperties hisHospitalProperties;
    private final HuLiDaoJiaBusinessService huliDaoJiaBusinessService;

    @PostMapping("/Q_GetElectronicInvoiceFile")
    @ApiOperation("电子发票文件查询")
    @HisLog("电子发票文件查询")
    public BaseResponse<ElectronicInvoiceFile> getElectronicInvoiceFile(@RequestBody ElectronicInvoiceFileReq req) {
        req.setHospitalCode(CurrentHospital.getCode());
        req.setOpCode(hisHospitalProperties.getOpCode());
        log.info("电子发票文件查询req：{}", JSON.toJSONString(req));
        return BaseResponse.success(electronicInvoiceService.getElectronicInvoiceFile(req));
    }

    @PostMapping("/Q_GetElectronicInvoiceList")
    @ApiOperation("电子发票列表查询")
    @HisLog("电子发票列表查询")
    public BaseResponse<List<ElectronicInvoice>> getElectronicInvoiceList(@RequestBody ElectronicInvoiceReq req) {
        req.setHospitalCode(CurrentHospital.getCode());
        req.setOpCode(hisHospitalProperties.getOpCode());
        log.info("电子发票列表查询req：{}", JSON.toJSONString(req));
        Page<ElectronicInvoice> electronicInvoicePage = electronicInvoiceService.getElectronicInvoiceList(req);
        return BaseResponse.success(new BaseResponseData<>(electronicInvoicePage));
    }

    @PostMapping("/B_ApplyElectronicInvoiceSync")
    @ApiOperation("护理到家电子发票申请")
    @HisLog("护理到家电子发票申请")
    public BaseResponse<CreateHuLiDaoJiaChargeResult> applyE(@RequestBody CreateHuLiDaoJiaChargeReq req) {
        req.setHospitalCode(CurrentHospital.getCode());
        log.info("护理到家电子发票申请req：{}", JSON.toJSONString(req));
        return BaseResponse.success(new BaseResponseData<>(huliDaoJiaBusinessService.applyElectronicInvoice(req)));
    }
}
