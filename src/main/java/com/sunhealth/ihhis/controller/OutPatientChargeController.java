package com.sunhealth.ihhis.controller;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.sunhealth.ihhis.annotations.HisLog;
import com.sunhealth.ihhis.model.dto.outpatientcharge.*;
import com.sunhealth.ihhis.model.dto.register.GetOutpatientPayReq;
import com.sunhealth.ihhis.model.dto.register.GetOutpatientPayResult;
import com.sunhealth.ihhis.model.vm.BaseResponse;
import com.sunhealth.ihhis.service.ChargeService;
import com.sunhealth.ihhis.service.CurrentHospital;
import com.sunhealth.ihhis.service.InvoiceService;
import com.sunhealth.ihhis.service.RegisterService;
import com.sunhealth.ihhis.utils.TimeUtils;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Description;
import org.springframework.web.bind.annotation.*;

import java.util.Date;
import java.util.List;

@RestController
@RequestMapping()
@Api(tags = "收费相关api")
@Slf4j
@AllArgsConstructor
public class OutPatientChargeController {

    private final ChargeService chargeService;
    private final RegisterService registerService;
    private final InvoiceService invoiceService;

    @PostMapping("/B_PreCharge")
    @ApiOperation("门诊收费预算")
    @HisLog("门诊收费预算")
    public BaseResponse<PreChargeResult> getRisReportsByPatId(
        @RequestBody PreChargeReq req) {
        req.setHospitalCode(CurrentHospital.getCode());
        log.info("门诊收费预算req：{}", JSON.toJSONString(req));
//        return reportService.getRisReportsByPatId(req);
        return BaseResponse.success(chargeService.preCharge(req));
    }
    @PostMapping("/B_ConfirmCharge")
    @ApiOperation("门诊收费结算")
    @HisLog("门诊收费结算")
    public BaseResponse<ConfirmChargeResult> getLisReportsByPatId(
        @RequestBody ConfirmChargeReq req) {
        req.setHospitalCode(CurrentHospital.getCode());
        log.info("门诊收费结算req：{}", JSON.toJSONString(req));
        return BaseResponse.success(chargeService.confirmCharge(req, null));
    }

    @PostMapping("/B_CancelOutpatientSettle")
    @ApiOperation("门诊收费结算取消")
    @HisLog("门诊收费结算取消")
    public BaseResponse<RefundResponse> cancelOutpatientSettle(@RequestBody RefundRequest req) {
        req.setHospitalCode(CurrentHospital.getCode());
        log.info("门诊收费结算取消req：{}", JSON.toJSONString(req));
        // 用于在线处方虚拟退款-临时
        RefundResponse response = new RefundResponse();
        response.setSuccess(true);
        response.setRefund_settle_id(req.getSettle_id());
        response.setRefund_time(TimeUtils.getHisDateStr(new Date()));

        return BaseResponse.success(response);
    }


    @PostMapping("/Q_GetOutpatientUnChargeRecipe")
    @ApiOperation("查询门诊患者待缴费项目信息")
    @HisLog("查询门诊患者待缴费项目信息")
    public BaseResponse<List<OutpatientUnChargeRecipeInfo>> getOutpatientUnChargeRecipe(
        @RequestBody UnChargeReq req) {
        req.setHospitalCode(CurrentHospital.getCode());
        log.info("查询门诊患者待缴费项目信息req：{}", JSON.toJSONString(req));
        return BaseResponse.success(chargeService.getOutpatientUnChargeRecipe(req));
    }

    @PostMapping("/Q_GetOutpatientChargeListByPatId")
    @ApiOperation("查询门诊患者缴费记录 ")
    @HisLog("查询门诊患者缴费记录 ")
    public BaseResponse<List<OutpatientCharge>> getOutpatientChargeListByPatId(
        @RequestBody UnChargeReq req) {
        req.setHospitalCode(CurrentHospital.getCode());
        log.info("查询门诊患者缴费记录req：{}", JSON.toJSONString(req));
        req.setBeginDate(TimeUtils.convert(req.getBegin_date()));
        req.setEndDate(TimeUtils.getEndOfDay(TimeUtils.convert(req.getEnd_date())));
//        return reportService.getLisReportsByPatId(req);
        return BaseResponse.success(chargeService.getOutpatientChargeList(req));
    }


    @PostMapping("/Q_GetOutpatientChargeDetails")
    @ApiOperation("查询门诊患者缴费详细信息")
    @HisLog("查询门诊患者缴费详细信息")
    public BaseResponse<List<OutpatientChargeDetail>> getOutpatientChargeDetails(
        @RequestBody ChargeDetailReq req) {
        req.setHospitalCode(CurrentHospital.getCode());
        log.info("查询门诊患者缴费详细信息req：{}", JSON.toJSONString(req));
//        List<OutpatientChargeRecipeInfo> chargeRecipeList = chargeService.getChargeRecipeList(req);
//        if (CollectionUtils.isEmpty(chargeRecipeList)) {
//            return new BaseResponse<>(new BaseResponseData<>());
//        }
//        OutpatientChargeDetail outpatientChargeDetail = new OutpatientChargeDetail();
//        outpatientChargeDetail.setSettle_id(req.getSettle_id());
//        outpatientChargeDetail.setRecipe_infos(chargeRecipeList);
//        return reportService.getLisReportsByPatId(req);
        OutpatientChargeDetail outpatientChargeDetail = chargeService.getOutpatientChargeDetail(req);
        if (outpatientChargeDetail == null) {
            return BaseResponse.success(Lists.newArrayList());
        }
        return BaseResponse.success(Lists.newArrayList(outpatientChargeDetail));
    }

    @PostMapping("/B_ConfirmedRefunded")
    @ApiOperation("结算取消确认")
    @HisLog("结算取消确认")
    public BaseResponse<ConfirmedRefundedRes> confirmedRefunded(
            @RequestBody ConfirmedRefundedReq req) {
        // 该接口在HIS实现
        req.setHospitalCode(CurrentHospital.getCode());
        log.info("结算取消确认req：{}", JSON.toJSONString(req));
        return BaseResponse.success(chargeService.confirmedRefunded(req));
    }

    @PostMapping("/B_OrderRefunded")
    @ApiOperation("查询结算取消结果")
    @HisLog("查询结算取消结果")
    public BaseResponse<OrderRefundedRes> orderRefunded(
            @RequestBody OrderRefundedReq req) {
        req.setHospitalCode(CurrentHospital.getCode());
        log.info("查询结算取消结果req：{}", JSON.toJSONString(req));
        return BaseResponse.success(chargeService.orderRefunded(req));
    }

    @PostMapping("/Q_GetOutpatientPayResult")
    @ApiOperation("3.3.4.门诊住院支付结果查询（含医保）")
    @Description("第三方交易对应的订单结果状态查询是否在HIS交易成功。在订单类交易发生失败时，如网络中断等，通过外部订单号查询结果状态（包含门诊预约挂号、门诊缴费结算、住院预交金充值、住院结算、医保支付结果查询）")
    @HisLog("门诊挂号解锁")
    public BaseResponse<GetOutpatientPayResult> getOutpatientPayResult(
            @RequestBody GetOutpatientPayReq req) {
        log.info("门诊住院支付结果查询req：{}", JSON.toJSONString(req));
        // 0门诊预约挂号 1门诊缴费结算 2住院预交金充值 3住院结算 4互联网医院挂号 5互联网医院处方
        GetOutpatientPayResult payResult = null;
        switch (req.getTrade_type()) {
            case "0":
                payResult = registerService.queryYiBaoPayResult(CurrentHospital.getCode(), Long.parseLong(req.getSettle_id()), false);
                break;
            case "1":
                payResult = chargeService.queryYiBaoPayResult(CurrentHospital.getCode(), Long.parseLong(req.getSettle_id()), false);
                break;
            default:
                throw new RuntimeException("未实现的业务，trade_type: " + req.getTrade_type());
        }
        payResult.setTrade_type(req.getTrade_type());
        return BaseResponse.success(payResult);
    }


//    @PostMapping("/outpatient/charge/return")
//    @ApiOperation("门诊缴费退费")
//    @HisLog("门诊缴费退费")
//    public String orderReturn(
//        @RequestBody OutpatientChargeRefundedReq req) {
//        log.info("门诊缴费退费req：{}", JSON.toJSONString(req));
//        return chargeService.returnOutPatientInsuranceFee(req);
//    }

    @GetMapping("/outpatient/yibao/manage")
    @ApiOperation("医保结算数据对齐")
    @HisLog("门诊缴费退费")
    public void orderReturn1() {
        log.info("医保结算数据对齐");
        chargeService.manageInsuranceData();
    }

}