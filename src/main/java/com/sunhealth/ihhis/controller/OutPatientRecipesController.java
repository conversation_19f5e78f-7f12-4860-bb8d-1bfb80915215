package com.sunhealth.ihhis.controller;

import com.alibaba.fastjson.JSON;
import com.sunhealth.ihhis.annotations.HisLog;
import com.sunhealth.ihhis.model.dto.outpatientcharge.OutpatientRecipeInfo;
import com.sunhealth.ihhis.model.dto.outpatientcharge.OutpatientRecipeReq;
import com.sunhealth.ihhis.model.dto.recipe.OnlineRecipeDeleteRequest;
import com.sunhealth.ihhis.model.dto.recipe.OnlineRecipeDeleteResponse;
import com.sunhealth.ihhis.model.dto.recipe.OnlineRecipeRequest;
import com.sunhealth.ihhis.model.dto.recipe.OnlineRecipeResponse;
import com.sunhealth.ihhis.model.vm.BaseResponse;
import com.sunhealth.ihhis.service.*;
import com.sunhealth.ihhis.utils.TimeUtils;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@RestController
@RequestMapping()
@Api(tags = "患者处方相关api")
@Slf4j
@AllArgsConstructor
public class OutPatientRecipesController {

    private final RecipeService recipeService;

    @PostMapping("/Q_GetOutpatientRecipes")
    @ApiOperation("查询患者处方列表")
    @HisLog("查询患者处方列表")
    public BaseResponse<List<OutpatientRecipeInfo>> getOutpatientRecipeList(@RequestBody OutpatientRecipeReq req) {
        req.setHospitalCode(CurrentHospital.getCode());
        log.info("查询患者处方列表req：{}", JSON.toJSONString(req));
        req.setBeginDate(TimeUtils.convert(req.getBegin_date()));
        req.setEndDate(TimeUtils.getEndOfDay(TimeUtils.convert(req.getEnd_date())));
        return BaseResponse.success(recipeService.getOutpatientRecipes(req));
    }

    @PostMapping("/B_SaveRecipeOnline")
    @ApiOperation("在线处方保存 ")
    @HisLog("在线处方保存 ")
    public BaseResponse<OnlineRecipeResponse> saveRecipeOnline(@RequestBody OnlineRecipeRequest req) {
        req.setHospitalCode(CurrentHospital.getCode());
        log.info("在线处方保存：{}", JSON.toJSONString(req));
        return BaseResponse.success(recipeService.saveRecipeOnline(req));
    }

    @PostMapping("/B_DeleteRecipeOnline")
    @ApiOperation("在线处方撤销")
    @HisLog("在线处方撤销 ")
    public BaseResponse<OnlineRecipeDeleteResponse> deleteRecipeOnline(@RequestBody OnlineRecipeDeleteRequest req) {
        req.setHospitalCode(CurrentHospital.getCode());
        log.info("在线处方撤销：{}", JSON.toJSONString(req));
        return BaseResponse.success(recipeService.deleteRecipeOnline(req));
    }

}