package com.sunhealth.ihhis.controller;

import com.alibaba.fastjson.JSON;
import com.sunhealth.ihhis.annotations.HisLog;
import com.sunhealth.ihhis.model.dto.inpatient.*;
import com.sunhealth.ihhis.model.vm.BaseResponse;
import com.sunhealth.ihhis.service.CurrentHospital;
import com.sunhealth.ihhis.service.InpatientService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

@Slf4j
@RestController
@RequestMapping()
@Api(tags = "住院相关api")
@AllArgsConstructor
public class InpatientController {

    private final InpatientService inpatientService;

    @PostMapping("/Q_GetInpatientRecord")
    @ApiOperation("按入院日期倒排返回患者住院就诊记录")
    @HisLog("按入院日期倒排返回患者住院就诊记录")
    public BaseResponse<List<InpatientRecordRes>> getInpatientRecord(@RequestBody InpatientRecordReq req) {
        req.setHospitalCode(CurrentHospital.getCode());
        log.info("按入院日期倒排返回患者住院就诊记录Req：{}", JSON.toJSONString(req));
        return BaseResponse.success(inpatientService.getInpatientRecord(req));
    }

    @PostMapping("/Q_GetInpatientAdvanceChargeDetail")
    @ApiOperation("查询住院患者预交金明细")
    @HisLog("查询住院患者预交金明细")
    public BaseResponse<List<AdvanceChargeDetailRes>> getInpatientAdvanceChargeDetail(@RequestBody AdvanceChargeDetailReq req) {
        req.setHospitalCode(CurrentHospital.getCode());
        log.info("查询住院患者预交金明细Req：{}", JSON.toJSONString(req));
        return BaseResponse.success(inpatientService.getInpatientAdvanceChargeDetail(req));
    }

    @PostMapping("/B_InpatientHospCardPreCharge")
    @ApiOperation("住院预交金预充值")
    @HisLog("住院预交金预充值")
    public BaseResponse<HospCardPreChargeRes> inpatientHospCardPreCharge(@RequestBody HospCardPreChargeReq req) {
        req.setHospitalCode(CurrentHospital.getCode());
        log.info("住院预交金预充值Req：{}", JSON.toJSONString(req));
        return BaseResponse.success(inpatientService.inpatientHospCardPreCharge(req));
    }

    @PostMapping("/B_InpatientHospCardCharge")
    @ApiOperation("住院预交金充值")
    @HisLog("住院预交金充值")
    public BaseResponse<HospCardChargeRes> inpatientHospCardCharge(@RequestBody HospCardChargeReq req) {
        req.setHospitalCode(CurrentHospital.getCode());
        log.info("住院预交金充值Req：{}", JSON.toJSONString(req));
        HospCardChargeRes hospCardChargeRes = inpatientService.inpatientHospCardCharge(req);
        if (hospCardChargeRes != null && hospCardChargeRes.isSuccess()) {
            return BaseResponse.success(hospCardChargeRes);
        } else {
            if (hospCardChargeRes == null) {
                hospCardChargeRes = new HospCardChargeRes();
                hospCardChargeRes.setSuccess(false);
                hospCardChargeRes.setMessage("预交金充值失败");
            }
            return BaseResponse.failed(hospCardChargeRes.getMessage(), hospCardChargeRes);
        }
    }
}
