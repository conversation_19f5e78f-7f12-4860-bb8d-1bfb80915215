package com.sunhealth.ihhis.controller;

import com.alibaba.fastjson.JSON;
import com.sunhealth.ihhis.model.dto.patient.*;
import com.sunhealth.ihhis.model.vm.BaseResponse;
import com.sunhealth.ihhis.service.CurrentHospital;
import com.sunhealth.ihhis.service.CurrentRequestId;
import com.sunhealth.ihhis.service.PatientCardService;
import com.sunhealth.ihhis.service.PatientListService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@RestController
@RequestMapping()
@Api(tags = "门诊患者信息类相关API")
@AllArgsConstructor
@Slf4j
public class PatientInfoController {

    private final PatientCardService patientCardService;

    private final PatientListService patientListService;

    @ApiOperation(value = "查询患者基本信息")
    @PostMapping("/Q_GetOutpatientInfoByCertNo")
    public BaseResponse<List<HisPatientInfoRes>> getPatientInfo(@RequestBody HisPatientInfoReq request) {
        request.setHospitalCode(Integer.valueOf(CurrentHospital.getCode()));
        log.info("查询患者基本信息Req：{}", JSON.toJSONString(request));
        return BaseResponse.success(patientListService.getPatientInfo(request));
    }

    @ApiOperation(value = "创建就诊卡")
    @PostMapping("/B_CreateMedicalCard")
    public BaseResponse<PatientCardCreateRes> createPatientCard(@RequestBody PatientCardCreateReq request) {
        request.setHospitalCode(Integer.valueOf(CurrentHospital.getCode()));
        log.info("创建就诊卡Req：{}", JSON.toJSONString(request));
        return BaseResponse.success(patientCardService.createPatientCard(request));
    }

    @ApiOperation(value = "更新患者基本信息")
    @PostMapping("/B_UpdatePatientInfo")
    public PatientInfoUpdateRes updatePatientInfo(@RequestBody PatientInfoUpdateReq request) {
        request.setHospitalCode(Integer.valueOf(CurrentHospital.getCode()));
        request.setRequestId(CurrentRequestId.getId());
        log.info("更新患者基本信息Req：{}", JSON.toJSONString(request));
        return patientListService.updatePatientInfo(request);
    }
}
