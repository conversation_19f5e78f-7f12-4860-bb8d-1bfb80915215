package com.sunhealth.ihhis.controller;

import com.alibaba.fastjson.JSON;
import com.sunhealth.ihhis.annotations.HisLog;
import com.sunhealth.ihhis.model.dto.report.*;
import com.sunhealth.ihhis.model.vm.BaseResponse;
import com.sunhealth.ihhis.service.CurrentHospital;
import com.sunhealth.ihhis.service.ReportService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

@RestController
@RequestMapping()
@Api(tags = "报告相关api")
@Slf4j
@AllArgsConstructor
public class ReportController {

    private final ReportService reportService;

    @PostMapping("/Q_GetRisReportsByPatId")
    @ApiOperation("查询患者所有检查报告列表，包括未出结果和已出结果")
    @HisLog("查询患者所有检查报告列表")
    public BaseResponse<List<RisReport>> getRisReportsByPatId(
        @RequestBody ReportListReq req) {
        req.setHospitalCode(CurrentHospital.getCode());
        log.info("查询患者所有检查报告列表req：{}", JSON.toJSONString(req));
        return BaseResponse.success(reportService.getRisReportsByPatId(req));
    }

    @PostMapping("/Q_GetRisReportResult")
    @ApiOperation("查询检查报告结果")
    @HisLog("查询检查报告结果")
    public BaseResponse<List<RisReportResult>> getRisReportResult(
        @RequestBody ReportResultReq req) {
        log.info("查询检查报告结果req：{}", JSON.toJSONString(req));
        return BaseResponse.success(reportService.getRisReportResult(req));
    }


    @PostMapping("/Q_GetLisReportsByPatId")
    @ApiOperation("查询患者所有检验报告列表，包括未出结果和已出结果")
    @HisLog("查询患者所有检验报告列表")
    public BaseResponse<List<LisReport>> getLisReportsByPatId(
        @RequestBody ReportListReq req) {
        req.setHospitalCode(CurrentHospital.getCode());
        log.info("查询患者所有检验报告列表req：{}", JSON.toJSONString(req));
        return BaseResponse.success(reportService.getLisReportsByPatId(req));
    }

    @PostMapping("/Q_GetLisReportResult")
    @ApiOperation("查询实验室检验报告结果")
    @HisLog("查询实验室检验报告结果")
    public BaseResponse<List<LisReportResult>> getLisReportResult(
        @RequestBody ReportResultReq req) {
        log.info("查询实验室检验报告结果req：{}", JSON.toJSONString(req));
        return BaseResponse.success(reportService.getLisReportResult(req));
    }

}