package com.sunhealth.ihhis.controller;

import com.fasterxml.jackson.core.type.TypeReference;
import com.sunhealth.ihhis.config.HisHospitalProperties;
import com.sunhealth.ihhis.dao.his.RegOnlineGjYiBaoUploadFeeRecordMapper;
import com.sunhealth.ihhis.dao.his.RegOnlineSHYiBaoUploadFeeRecordMapper;
import com.sunhealth.ihhis.model.dq.sh.insurance.response.CallBackSH03;
import com.sunhealth.ihhis.model.entity.yibao.RegOnlineGjYiBaoUploadFeeRecord;
import com.sunhealth.ihhis.model.entity.yibao.RegOnlineSHYiBaoUploadFeeRecord;
import com.sunhealth.ihhis.model.insurance.request.CallBack6302;
import com.sunhealth.ihhis.model.insurance.response.CallBackResult6302;
import com.sunhealth.ihhis.service.ChargeService;
import com.sunhealth.ihhis.service.RegisterService;
import com.sunhealth.ihhis.utils.AppContext;
import com.sunhealth.ihhis.utils.StandardObjectMapper;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.Map;

@Slf4j
@RestController
@RequestMapping()
@Api(tags = "接收api")
@AllArgsConstructor
public class CallBackController {

    private final RegisterService registerService;
    private final ChargeService chargeService;
    private final HisHospitalProperties hisHospitalProperties;

    @PostMapping("/insurance/order_pay_success/callback")
    @ApiOperation("接收医保支付成功回调")
    public CallBackResult6302 pushPatientMsg(@RequestBody Map<String, Object> req) {
        log.info("接收医保支付成功回调Req：{}", StandardObjectMapper.stringify(req));
        CallBack6302 callBack6302 = StandardObjectMapper.getInstance().convertValue(req.get("data"),
                new TypeReference<CallBack6302>() {});
        String hospitalCode = hisHospitalProperties.getCode();
        CallBackResult6302.Data6302 data = new CallBackResult6302.Data6302();
        try {
            RegOnlineGjYiBaoUploadFeeRecordMapper regOnlineGjYiBaoUploadFeeRecordMapper = AppContext.getInstance(RegOnlineGjYiBaoUploadFeeRecordMapper.class);
            RegOnlineGjYiBaoUploadFeeRecord feeRecord = regOnlineGjYiBaoUploadFeeRecordMapper.selectByPayOrdId(callBack6302.getPayOrdId());
            if (feeRecord == null) {
                data.setSuccess(false);
                data.setMessage("不是有效的医保订单");
                return new CallBackResult6302(data);
            }
            if (feeRecord.getFlag() == null) {
                data.setSuccess(false);
                data.setMessage("医保订单类型错误");
                return new CallBackResult6302(data);
            }
            // 0 挂号 1 缴费
            switch (feeRecord.getFlag()) {
                case 0:
                    registerService.registerGJCallBackPaySuccess(callBack6302, hospitalCode, feeRecord);
                    break;
                case 1:
                    chargeService.chargeCallBackPaySuccess(callBack6302, hospitalCode, feeRecord);
                    break;
                default:
            }
            data.setSuccess(true);
        } catch (Exception e) {
            log.error("医保业务处理失败", e);
            data.setSuccess(false);
            data.setMessage(e.getMessage());
        }
        return new CallBackResult6302(data);
    }


    @PostMapping("/insurance/sh/order_pay_success/callback")
    @ApiOperation("接收上海医保支付成功回调")
    public void pushSHPatientMsg(@RequestBody Map<String, Object> req) {
        log.info("接收医保支付成功回调Req：{}", StandardObjectMapper.stringify(req));
        CallBackSH03 callBackSH03 = StandardObjectMapper.getInstance().convertValue(req,
                new TypeReference<CallBackSH03>() {});
        String hospitalCode = hisHospitalProperties.getCode();
//        try {
            RegOnlineSHYiBaoUploadFeeRecordMapper regOnlineSHYiBaoUploadFeeRecordMapper = AppContext.getInstance(RegOnlineSHYiBaoUploadFeeRecordMapper.class);
            RegOnlineSHYiBaoUploadFeeRecord feeRecord = regOnlineSHYiBaoUploadFeeRecordMapper.selectByOrderNo(callBackSH03.getOrderNo());
            if (feeRecord == null) {
                return;
            }
            if (feeRecord.getFlag() == null) {
                return;
            }
            // 0 挂号 1 缴费
            switch (feeRecord.getFlag()) {
                case 0:
                    registerService.registerSHCallBackPaySuccess(callBackSH03, hospitalCode, feeRecord);
                    break;
                case 1:
                    chargeService.chargeCallBackPaySuccess(callBackSH03, hospitalCode, feeRecord);
                    break;
                default:
            }
//        } catch (Exception e) {
//            log.error("医保业务处理失败", e);
//        }
    }


}
