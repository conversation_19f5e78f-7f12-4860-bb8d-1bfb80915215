package com.sunhealth.ihhis.controller;

import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.sunhealth.ihhis.annotations.HisLog;
import com.sunhealth.ihhis.common.JsonResponse;
import com.sunhealth.ihhis.model.dto.push.*;
import com.sunhealth.ihhis.service.CurrentHospital;
import com.sunhealth.ihhis.service.MsgPushService;
import com.sunhealth.ihhis.utils.StandardObjectMapper;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.Arrays;
import java.util.List;

@Slf4j
@RestController
@RequestMapping()
@Api(tags = "消息推送api")
@AllArgsConstructor
public class MsgPushController {

    private final MsgPushService msgPushService;

    @PostMapping("/B_PushPatientMsg")
    @ApiOperation("推送医疗信息消息")
    @HisLog("推送医疗信息消息")
    public MsgPushRes pushPatientMsg(@RequestBody MsgPushReq req) {
        log.info("接收到需要转发的推送医疗信息消息Req：{}", JSON.toJSONString(req));
        if (StringUtils.isEmpty(req.getRegno())) {
            log.error("regno门诊号/住院号不能为空");
            return MsgPushRes.fail("门诊号/住院号不能为空");
        }
        if (StringUtils.isEmpty(req.getMsg_type())) {
            log.error("msg_type消息类型不能为空");
            return MsgPushRes.fail("消息类型不能为空");
        }
        List<String> userSourceNoNull = Arrays.asList("1301", "4010");
        if (userSourceNoNull.contains(req.getMsg_type())) {
            if (StringUtils.isEmpty(req.getUser_source())) {
                log.error("user_source消息类型："+req.getMsg_type() + ",就诊类型不能为空");
                return MsgPushRes.fail("消息类型："+req.getMsg_type() + ",就诊类型不能为空");
            }
        }
        List<String> snNoNull = Arrays.asList("1301", "1501", "4003", "4008");
        if (snNoNull.contains(req.getMsg_type())) {
            if (StringUtils.isEmpty(req.getOperation_sn())){
                log.error("消息类型："+req.getMsg_type() + ",业务流水号不能为空");
                return MsgPushRes.fail("消息类型："+req.getMsg_type() + ",业务流水号不能为空");
            }
            if (req.getMsg_details() == null || req.getMsg_details().isEmpty()) {
                log.error("消息类型："+req.getMsg_type() + ",消息详情不能为空");
                return MsgPushRes.fail("消息类型："+req.getMsg_type() + ",消息详情不能为空");
            }
        }
        return msgPushService.pushPatientMsg(req, CurrentHospital.getCode());
    }

    @PostMapping("/B_QRCode")
    @ApiOperation(value = "生成患者服务平台页面二维码")
    public JsonResponse<HisQRCodeResponse> bQRCode(@RequestBody HisQRCodeRequest qrCodeRequest) {
        log.info("接收到his生成患者服务平台页面二维码: qrCodeRequest = " + StandardObjectMapper.stringify(qrCodeRequest));
        if (StringUtils.isEmpty(qrCodeRequest.getQr_code_type())) {
            return new JsonResponse<>("1", "二维码类型不能为空");
        }
        if (StringUtils.isEmpty(qrCodeRequest.getType())) {
            return new JsonResponse<>("1", "业务类型不能为空");
        }
        if ("2".equals(qrCodeRequest.getType())) {
            // 住院催款单，patid、regno、amount必传
            if (StringUtils.isEmpty(qrCodeRequest.getPatid()) ||
                    StringUtils.isEmpty(qrCodeRequest.getRegno())) {
                return new JsonResponse<>("1","住院催款单patid、regno(住院号)必传");
            }
        }
        return msgPushService.bQRCode(qrCodeRequest);
    }
}
