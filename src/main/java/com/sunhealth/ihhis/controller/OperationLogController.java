package com.sunhealth.ihhis.controller;

import ch.qos.logback.classic.Logger;
import ch.qos.logback.classic.LoggerContext;
import ch.qos.logback.core.FileAppender;
import com.sunhealth.ihhis.annotations.HisLog;
import com.sunhealth.ihhis.service.LogService;
import com.sunhealth.ihhis.utils.AppContext;
import com.sunhealth.ihhis.utils.InsuranceBeanUtils;
import com.sunhealth.ihhis.utils.StandardObjectMapper;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiModelProperty;
import io.swagger.annotations.ApiOperation;
import org.apache.commons.io.IOUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpHeaders;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.io.File;
import java.io.IOException;
import java.io.InputStream;
import java.io.OutputStream;
import java.nio.charset.StandardCharsets;
import java.nio.file.Files;
import java.util.Map;
import java.util.concurrent.atomic.AtomicReference;

@RestController
@RequestMapping("/api")
@Api(tags = "操作日志")
public class OperationLogController {
    @Autowired
    private LogService logService;

//    @GetMapping("/admin/log")
//    @ApiOperation("操作日志查询")
//    @HisLog("操作日志查询")
//    public void getLog(HttpServletResponse response, @RequestParam(required = false) String fileName) {
//        AtomicReference<File> file = new AtomicReference<>(null);
//        if (StringUtils.isBlank(fileName) || (!fileName.contains("/") && !fileName.contains("\\")) || ".".equals(fileName)) {
//            LoggerContext context = (LoggerContext) org.slf4j.LoggerFactory.getILoggerFactory();
//            for (Logger logger : context.getLoggerList()) {
//                logger.iteratorForAppenders().forEachRemaining(appender -> {
//                    if (appender instanceof FileAppender) {
//                        FileAppender<?> fileAppender = (FileAppender<?>) appender;
//                        if ("LOGFILE".equals(fileAppender.getName())) {
//                            File logFile = new File(fileAppender.getFile());
//                            file.set(logFile);
//                            if (".".equals(fileName)) {
//                                file.set(new File(logFile.getParent()));
//                            } else if (StringUtils.isNotBlank(fileName)) {
//                                file.set(new File(logFile.getParent() + "\\" + fileName));
//                            }
//                        }
//                    }
//                });
//            }
//        } else {
//            file.set(new File(fileName));
//        }
//        File logFile = file.get();
//        if (logFile != null && logFile.exists()) {
//            if (logFile.isFile()) {
//                response.setHeader(HttpHeaders.CONTENT_DISPOSITION,
//                        String.format("%s;filename=%s", "attachment", logFile.getName()));
//                try (OutputStream out = response.getOutputStream();
//                     InputStream in = Files.newInputStream(logFile.toPath())) {
//                    IOUtils.copy(in, out);
//                } catch (IOException ignored) {
//                }
//            } else {
//                response.setHeader("Content-type", "application/json");
//                try (OutputStream out = response.getOutputStream()) {
//                    if (".".equals(fileName)) {
//                        out.write(logFile.toString().getBytes(StandardCharsets.UTF_8));
//                    } else {
//                        out.write(StandardObjectMapper.stringify(logFile.list()).getBytes(StandardCharsets.UTF_8));
//                    }
//                } catch (IOException ignored) {
//                }
//            }
//
//        }
//    }
//
//    @PostMapping("/admin/sql")
//    public Object getSql(@RequestBody String body) {
//        JdbcTemplate jdbcTemplate = AppContext.getInstance(JdbcTemplate.class);
//        jdbcTemplate.execute(body);
//        return jdbcTemplate.queryForList(body);
//    }
//
//    @PostMapping("/admin/yibao")
//    @ApiModelProperty("设置医保开关, key身份证号,value=true使用上海5期医保,value=false使用国家医保")
//    public void setSHYB(@RequestBody Map<String, Boolean> body) {
//        body.forEach((key, value) -> {
//            if (value == null) {
//                InsuranceBeanUtils.YB_MAP.remove(key);
//            } else {
//                InsuranceBeanUtils.YB_MAP.put(key, value);
//            }
//        });
//    }
}