package com.sunhealth.ihhis.controller;

import com.alibaba.fastjson.JSON;
import com.sunhealth.ihhis.annotations.HisLog;
import com.sunhealth.ihhis.model.dto.diagnose.OnlineDiagnoseRequest;
import com.sunhealth.ihhis.model.dto.diagnose.OnlineDiagnoseResponse;
import com.sunhealth.ihhis.model.vm.BaseResponse;
import com.sunhealth.ihhis.service.CurrentHospital;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping()
@Api(tags = "患者诊断相关api")
@Slf4j
@AllArgsConstructor
public class OutPatientDiagnoseController {

    @PostMapping("/B_SaveDiagnoseOnline")
    @ApiOperation("在线诊断保存 ")
    @HisLog("在线诊断保存 ")
    public BaseResponse<OnlineDiagnoseResponse> saveDiagnoseOnline(@RequestBody OnlineDiagnoseRequest req) {
        req.setHospitalCode(CurrentHospital.getCode());
        log.info("在线诊断保存：{}", JSON.toJSONString(req));
        // 暂不实现，先返回成功
        OnlineDiagnoseResponse response = new OnlineDiagnoseResponse();
        response.setSuccess(true);

        return BaseResponse.success(response);
    }
}