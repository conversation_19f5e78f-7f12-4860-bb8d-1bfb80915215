package com.sunhealth.ihhis.controller;

import com.sunhealth.ihhis.config.YiBaoProperties;
import com.sunhealth.ihhis.utils.StandardObjectMapper;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping
@Api(tags = "健康检查")
public class AppController {

    @Autowired
    private YiBaoProperties yiBaoProperties;
    @GetMapping("/actuator/health")
    @ApiOperation("应用的健康检查")
    public String healthCheck() {
        return "ok";
    }

//    @GetMapping("/yibao")
//    @ApiOperation("获取医保配置")
//    public String getYiBaoProperties() {
//        return StandardObjectMapper.stringify(yiBaoProperties);
//    }
}
