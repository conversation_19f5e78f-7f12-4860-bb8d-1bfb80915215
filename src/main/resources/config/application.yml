spring:
  datasource:
    type: com.alibaba.druid.pool.DruidDataSource

    druid:
      his:
        initialSize: 10
        minIdle: 10
        maxActive: 100
        maxWait: 60000
        timeBetweenEvictionRunsMillis: 60000
        minEvictableIdleTimeMillis: 300000
        validationQuery: SELECT 'x'
        testWhileIdle: true
        testOnBorrow: false
        testOnReturn: false
        poolPreparedStatements: true
        maxPoolPreparedStatementPerConnectionSize: 20
        filters: stat,slf4j,config
        useGlobalDataSourceStat: true
        stat:
          log-slow-sql: true
          merge-sql: true
          slow-sql-millis: 10000
      lis:
        initialSize: 5
        minIdle: 5
        maxActive: 30
        maxWait: 60000
        timeBetweenEvictionRunsMillis: 60000
        minEvictableIdleTimeMillis: 300000
        validationQuery: SELECT 'x'
        testWhileIdle: true
        testOnBorrow: false
        testOnReturn: false
        poolPreparedStatements: true
        maxPoolPreparedStatementPerConnectionSize: 20
        filters: stat,slf4j,config
        useGlobalDataSourceStat: true
        stat:
          log-slow-sql: true
          merge-sql: true
          slow-sql-millis: 10000
  mvc:
    pathmatch:
      matching-strategy: ant_path_matcher
  task:
    execution:
      pool:
        core-size: 100

logging:
  level:
    com.sunhealth.ihhis: DEBUG

mybatis:
  mapper-locations: classpath:com/sunhealth/ihhis/dao/*.xml
  type-aliases-package: com/sunhealth/ihhis/model/entity
  configuration:
    log-impl: org.apache.ibatis.logging.slf4j.Slf4jImpl
mybatis-plus:
  configuration:
    #开启sql日志
    log-impl: org.apache.ibatis.logging.slf4j.Slf4jImpl
  global-config:
    # 是否打印 Logo banner
    banner: true
    # 是否初始化 SqlRunner
    #不打开，使用SqlRunner会报错：Cause: java.lang.IllegalArgumentException: Mapped
    # Statements collection does not contain value
    #for com.baomidou.mybatisplus.core.mapper.SqlRunner.SelectList
    enableSqlRunner: true
    db-config:
      update-strategy: IGNORED

server:
  port: 12082
  servlet:
    context-path: /hisapi
hishospital:
  normalDoctorId: 208