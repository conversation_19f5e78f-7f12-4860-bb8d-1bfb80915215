/*
 Navicat Premium Data Transfer

 Source Server         : *********
 Source Server Type    : SQL Server
 Source Server Version : 15002000 (15.00.2000)
 Source Host           : *********:1435
 Source Catalog        : HISDB
 Source Schema         : dbo

 Target Server Type    : SQL Server
 Target Server Version : 15002000 (15.00.2000)
 File Encoding         : 65001

 Date: 30/05/2024 09:18:10
*/


-- ----------------------------
-- Table structure for Reg_Online_GjYiBao_OrderInfo
-- ----------------------------
IF EXISTS (SELECT * FROM sys.all_objects WHERE object_id = OBJECT_ID(N'[dbo].[Reg_Online_GjYiBao_OrderInfo]') AND type IN ('U'))
	DROP TABLE [dbo].[Reg_Online_GjYiBao_OrderInfo]
GO

CREATE TABLE [dbo].[Reg_Online_GjYiBao_OrderInfo] (
  [ID] bigint  NOT NULL,
  [PatName] varchar(200) COLLATE Chinese_PRC_CI_AS  NULL,
  [CardType] varchar(10) COLLATE Chinese_PRC_CI_AS  NULL,
  [CardNo] varchar(50) COLLATE Chinese_PRC_CI_AS  NULL,
  [ChargeNo] bigint  NULL,
  [RegNo] bigint  NULL,
  [Flag] int  NULL,
  [HospitalCode] varchar(50) COLLATE Chinese_PRC_CI_AS  NULL,
  [OrdStas] varchar(40) COLLATE Chinese_PRC_CI_AS  NULL,
  [PayOrdId] varchar(50) COLLATE Chinese_PRC_CI_AS  NULL,
  [CallType] varchar(50) COLLATE Chinese_PRC_CI_AS  NULL,
  [MedOrgOrd] varchar(50) COLLATE Chinese_PRC_CI_AS  NULL,
  [TraceTime] varchar(50) COLLATE Chinese_PRC_CI_AS  NULL,
  [OrgCode] varchar(50) COLLATE Chinese_PRC_CI_AS  NULL,
  [OrgName] varchar(50) COLLATE Chinese_PRC_CI_AS  NULL,
  [SetlType] varchar(50) COLLATE Chinese_PRC_CI_AS  NULL,
  [FeeSumamt] decimal(16,2)  NULL,
  [OwnPayAmt] decimal(16,2)  NULL,
  [PsnAcctPay] decimal(16,2)  NULL,
  [FundPay] decimal(16,2)  NULL,
  [RevsToken] varchar(50) COLLATE Chinese_PRC_CI_AS  NULL,
  [ExtData] text COLLATE Chinese_PRC_CI_AS  NULL,
  [Deposit] decimal(16,2)  NULL,
  [HiChrgTime] varchar(50) COLLATE Chinese_PRC_CI_AS  NULL,
  [HiDocSn] varchar(50) COLLATE Chinese_PRC_CI_AS  NULL,
  [HiRgstSn] varchar(50) COLLATE Chinese_PRC_CI_AS  NULL,
  [EcCode] varchar(200) COLLATE Chinese_PRC_CI_AS  NULL,
  [CreatedDate] datetime  NULL
)
GO

ALTER TABLE [dbo].[Reg_Online_GjYiBao_OrderInfo] SET (LOCK_ESCALATION = TABLE)
GO

EXEC sp_addextendedproperty
'MS_Description', N'就诊人姓名',
'SCHEMA', N'dbo',
'TABLE', N'Reg_Online_GjYiBao_OrderInfo',
'COLUMN', N'PatName'
GO

EXEC sp_addextendedproperty
'MS_Description', N'证件类型',
'SCHEMA', N'dbo',
'TABLE', N'Reg_Online_GjYiBao_OrderInfo',
'COLUMN', N'CardType'
GO

EXEC sp_addextendedproperty
'MS_Description', N'证件号码',
'SCHEMA', N'dbo',
'TABLE', N'Reg_Online_GjYiBao_OrderInfo',
'COLUMN', N'CardNo'
GO

EXEC sp_addextendedproperty
'MS_Description', N'结算单号',
'SCHEMA', N'dbo',
'TABLE', N'Reg_Online_GjYiBao_OrderInfo',
'COLUMN', N'ChargeNo'
GO

EXEC sp_addextendedproperty
'MS_Description', N'门诊序号',
'SCHEMA', N'dbo',
'TABLE', N'Reg_Online_GjYiBao_OrderInfo',
'COLUMN', N'RegNo'
GO

EXEC sp_addextendedproperty
'MS_Description', N'0 挂号 1 缴费',
'SCHEMA', N'dbo',
'TABLE', N'Reg_Online_GjYiBao_OrderInfo',
'COLUMN', N'Flag'
GO

EXEC sp_addextendedproperty
'MS_Description', N'医院编码',
'SCHEMA', N'dbo',
'TABLE', N'Reg_Online_GjYiBao_OrderInfo',
'COLUMN', N'HospitalCode'
GO

EXEC sp_addextendedproperty
'MS_Description', N'医保-订单状态',
'SCHEMA', N'dbo',
'TABLE', N'Reg_Online_GjYiBao_OrderInfo',
'COLUMN', N'OrdStas'
GO

EXEC sp_addextendedproperty
'MS_Description', N'医保-支付订单号',
'SCHEMA', N'dbo',
'TABLE', N'Reg_Online_GjYiBao_OrderInfo',
'COLUMN', N'PayOrdId'
GO

EXEC sp_addextendedproperty
'MS_Description', N'医保-回调类型',
'SCHEMA', N'dbo',
'TABLE', N'Reg_Online_GjYiBao_OrderInfo',
'COLUMN', N'CallType'
GO

EXEC sp_addextendedproperty
'MS_Description', N'医保-医院订单',
'SCHEMA', N'dbo',
'TABLE', N'Reg_Online_GjYiBao_OrderInfo',
'COLUMN', N'MedOrgOrd'
GO

EXEC sp_addextendedproperty
'MS_Description', N'医保-交易时间',
'SCHEMA', N'dbo',
'TABLE', N'Reg_Online_GjYiBao_OrderInfo',
'COLUMN', N'TraceTime'
GO

EXEC sp_addextendedproperty
'MS_Description', N'医保-两定机构编码',
'SCHEMA', N'dbo',
'TABLE', N'Reg_Online_GjYiBao_OrderInfo',
'COLUMN', N'OrgCode'
GO

EXEC sp_addextendedproperty
'MS_Description', N'医保-两定机构名称',
'SCHEMA', N'dbo',
'TABLE', N'Reg_Online_GjYiBao_OrderInfo',
'COLUMN', N'OrgName'
GO

EXEC sp_addextendedproperty
'MS_Description', N'医保-结算类型：ALL:医保自费全部，CASH:只结现金 HI:只结医保',
'SCHEMA', N'dbo',
'TABLE', N'Reg_Online_GjYiBao_OrderInfo',
'COLUMN', N'SetlType'
GO

EXEC sp_addextendedproperty
'MS_Description', N'医保-费用总额',
'SCHEMA', N'dbo',
'TABLE', N'Reg_Online_GjYiBao_OrderInfo',
'COLUMN', N'FeeSumamt'
GO

EXEC sp_addextendedproperty
'MS_Description', N'医保-现金支付',
'SCHEMA', N'dbo',
'TABLE', N'Reg_Online_GjYiBao_OrderInfo',
'COLUMN', N'OwnPayAmt'
GO

EXEC sp_addextendedproperty
'MS_Description', N'医保-个人账户支出',
'SCHEMA', N'dbo',
'TABLE', N'Reg_Online_GjYiBao_OrderInfo',
'COLUMN', N'PsnAcctPay'
GO

EXEC sp_addextendedproperty
'MS_Description', N'医保-医保基金支付',
'SCHEMA', N'dbo',
'TABLE', N'Reg_Online_GjYiBao_OrderInfo',
'COLUMN', N'FundPay'
GO

EXEC sp_addextendedproperty
'MS_Description', N'医保-用于院内结算失败对医保的冲正授权',
'SCHEMA', N'dbo',
'TABLE', N'Reg_Online_GjYiBao_OrderInfo',
'COLUMN', N'RevsToken'
GO

EXEC sp_addextendedproperty
'MS_Description', N'医保-扩展数据',
'SCHEMA', N'dbo',
'TABLE', N'Reg_Online_GjYiBao_OrderInfo',
'COLUMN', N'ExtData'
GO

EXEC sp_addextendedproperty
'MS_Description', N'医保-住院押金',
'SCHEMA', N'dbo',
'TABLE', N'Reg_Online_GjYiBao_OrderInfo',
'COLUMN', N'Deposit'
GO

EXEC sp_addextendedproperty
'MS_Description', N'医保-收费时间',
'SCHEMA', N'dbo',
'TABLE', N'Reg_Online_GjYiBao_OrderInfo',
'COLUMN', N'HiChrgTime'
GO

EXEC sp_addextendedproperty
'MS_Description', N'医保-交易流水号',
'SCHEMA', N'dbo',
'TABLE', N'Reg_Online_GjYiBao_OrderInfo',
'COLUMN', N'HiDocSn'
GO

EXEC sp_addextendedproperty
'MS_Description', N'医保-挂号流水号',
'SCHEMA', N'dbo',
'TABLE', N'Reg_Online_GjYiBao_OrderInfo',
'COLUMN', N'HiRgstSn'
GO

EXEC sp_addextendedproperty
'MS_Description', N'医保-电子凭证码值',
'SCHEMA', N'dbo',
'TABLE', N'Reg_Online_GjYiBao_OrderInfo',
'COLUMN', N'EcCode'
GO

EXEC sp_addextendedproperty
'MS_Description', N'创建时间',
'SCHEMA', N'dbo',
'TABLE', N'Reg_Online_GjYiBao_OrderInfo',
'COLUMN', N'CreatedDate'
GO


-- ----------------------------
-- Primary Key structure for table Reg_Online_GjYiBao_OrderInfo
-- ----------------------------
ALTER TABLE [dbo].[Reg_Online_GjYiBao_OrderInfo] ADD CONSTRAINT [PK_Reg_Online_GjYiBao_OrderInfo_1289EC2735F7B9FA] PRIMARY KEY CLUSTERED ([ID])
WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON)  
ON [PRIMARY]
GO

alter TABLE Reg_Online_GjYiBao_OrderInfo ADD
    setl_id VARCHAR(30) NULL,
mdtrt_id VARCHAR(30) NULL,
psn_no VARCHAR(30) NULL,
insuplc_admdvs VARCHAR(30) NULL
GO