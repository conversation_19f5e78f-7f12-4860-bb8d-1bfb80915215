use hisdb
go

INSERT INTO TB_Dic_HisDictionary(HisDictionaryCode, InputCode1,  HisDictionaryName, DictionaryCode, DictionaryTypeID,IsUse, HospitalId, CreatedBy, CreatedDate, IsDelete, lang)
select 11,'ZFBXCX','支付宝小程序',11,56,'1',a.HOSPITALCODE,9999,GETDATE(),'0',1 from Tb_Hospital a
             left join TB_Dic_HisDictionary b on a.HOSPITALCODE = b.HospitalId and b.DictionaryTypeID = 56 and b.HisDictionaryCode = 11
where b.HisDictionaryId is null

go

INSERT INTO TB_Dic_HisDictionary(HisDictionaryCode, InputCode1,  HisDictionaryName, DictionaryCode, DictionaryTypeID,IsUse, HospitalId, CreatedBy, CreatedDate, IsDelete, lang)
select 17,'WXXCX','微信小程序',17,56,'1',a.HOSPITALCODE,9999,GETDATE(),'0',1 from Tb_Hospital a
             left join TB_Dic_HisDictionary b on a.HOSPITALCODE = b.HospitalId and b.DictionaryTypeID = 56 and b
                 .HisDictionaryCode = 17
where b.HisDictionaryId is null

go

INSERT INTO TB_Dic_HisDictionary(HisDictionaryCode, InputCode1,  HisDictionaryName, DictionaryCode, DictionaryTypeID,IsUse, HospitalId, CreatedBy, CreatedDate, IsDelete, lang)
select 13,'WXGZH','微信公众号',13,56,'1',a.HOSPITALCODE,9999,GETDATE(),'0',1 from Tb_Hospital a
             left join TB_Dic_HisDictionary b on a.HOSPITALCODE = b.HospitalId and b.DictionaryTypeID = 56 and b.HisDictionaryCode = 13
where b.HisDictionaryId is null

go