/*
 Navicat Premium Data Transfer

 Source Server         : *********
 Source Server Type    : SQL Server
 Source Server Version : 15002000 (15.00.2000)
 Source Host           : *********:1435
 Source Catalog        : HISDB
 Source Schema         : dbo

 Target Server Type    : SQL Server
 Target Server Version : 15002000 (15.00.2000)
 File Encoding         : 65001

 Date: 30/05/2024 09:18:21
*/


-- ----------------------------
-- Table structure for Reg_Online_GjYiBao_uploadFeeRecord
-- ----------------------------
IF EXISTS (SELECT * FROM sys.all_objects WHERE object_id = OBJECT_ID(N'[dbo].[Reg_Online_GjYiBao_uploadFeeRecord]') AND type IN ('U'))
	DROP TABLE [dbo].[Reg_Online_GjYiBao_uploadFeeRecord]
GO

CREATE TABLE [dbo].[Reg_Online_GjYiBao_uploadFeeRecord] (
  [ID] bigint  NOT NULL,
  [PatName] varchar(200) COLLATE Chinese_PRC_CI_AS  NULL,
  [CardType] varchar(10) COLLATE Chinese_PRC_CI_AS  NULL,
  [CardNo] varchar(50) COLLATE Chinese_PRC_CI_AS  NULL,
  [ChargeNo] bigint  NULL,
  [RegNo] bigint  NULL,
  [Flag] int  NULL,
  [HospitalCode] varchar(50) COLLATE Chinese_PRC_CI_AS  NULL,
  [PayOrdId] varchar(50) COLLATE Chinese_PRC_CI_AS  NULL,
  [PayToken] varchar(50) COLLATE Chinese_PRC_CI_AS  NULL,
  [OrdStas] varchar(40) COLLATE Chinese_PRC_CI_AS  NULL,
  [FeeSumamt] decimal(16,2)  NULL,
  [OwnPayAmt] decimal(16,2)  NULL,
  [PsnAcctPay] decimal(16,2)  NULL,
  [FundPay] decimal(16,2)  NULL,
  [Deposit] decimal(16,2)  NULL,
  [CreatedDate] datetime  NULL,
  [UpdatedDate] datetime  NULL,
  [Payway] int  NULL
)
GO

ALTER TABLE [dbo].[Reg_Online_GjYiBao_uploadFeeRecord] SET (LOCK_ESCALATION = TABLE)
GO

EXEC sp_addextendedproperty
'MS_Description', N'就诊人姓名',
'SCHEMA', N'dbo',
'TABLE', N'Reg_Online_GjYiBao_uploadFeeRecord',
'COLUMN', N'PatName'
GO

EXEC sp_addextendedproperty
'MS_Description', N'证件类型',
'SCHEMA', N'dbo',
'TABLE', N'Reg_Online_GjYiBao_uploadFeeRecord',
'COLUMN', N'CardType'
GO

EXEC sp_addextendedproperty
'MS_Description', N'证件号码',
'SCHEMA', N'dbo',
'TABLE', N'Reg_Online_GjYiBao_uploadFeeRecord',
'COLUMN', N'CardNo'
GO

EXEC sp_addextendedproperty
'MS_Description', N'结算单号',
'SCHEMA', N'dbo',
'TABLE', N'Reg_Online_GjYiBao_uploadFeeRecord',
'COLUMN', N'ChargeNo'
GO

EXEC sp_addextendedproperty
'MS_Description', N'门诊序号',
'SCHEMA', N'dbo',
'TABLE', N'Reg_Online_GjYiBao_uploadFeeRecord',
'COLUMN', N'RegNo'
GO

EXEC sp_addextendedproperty
'MS_Description', N'0 挂号 1 缴费',
'SCHEMA', N'dbo',
'TABLE', N'Reg_Online_GjYiBao_uploadFeeRecord',
'COLUMN', N'Flag'
GO

EXEC sp_addextendedproperty
'MS_Description', N'医院编码',
'SCHEMA', N'dbo',
'TABLE', N'Reg_Online_GjYiBao_uploadFeeRecord',
'COLUMN', N'HospitalCode'
GO

EXEC sp_addextendedproperty
'MS_Description', N'医保-支付订单号',
'SCHEMA', N'dbo',
'TABLE', N'Reg_Online_GjYiBao_uploadFeeRecord',
'COLUMN', N'PayOrdId'
GO

EXEC sp_addextendedproperty
'MS_Description', N'医保-支付Token',
'SCHEMA', N'dbo',
'TABLE', N'Reg_Online_GjYiBao_uploadFeeRecord',
'COLUMN', N'PayToken'
GO

EXEC sp_addextendedproperty
'MS_Description', N'医保-订单状态',
'SCHEMA', N'dbo',
'TABLE', N'Reg_Online_GjYiBao_uploadFeeRecord',
'COLUMN', N'OrdStas'
GO

EXEC sp_addextendedproperty
'MS_Description', N'医保-费用总额',
'SCHEMA', N'dbo',
'TABLE', N'Reg_Online_GjYiBao_uploadFeeRecord',
'COLUMN', N'FeeSumamt'
GO

EXEC sp_addextendedproperty
'MS_Description', N'医保-现金支付',
'SCHEMA', N'dbo',
'TABLE', N'Reg_Online_GjYiBao_uploadFeeRecord',
'COLUMN', N'OwnPayAmt'
GO

EXEC sp_addextendedproperty
'MS_Description', N'医保-个人账户支出',
'SCHEMA', N'dbo',
'TABLE', N'Reg_Online_GjYiBao_uploadFeeRecord',
'COLUMN', N'PsnAcctPay'
GO

EXEC sp_addextendedproperty
'MS_Description', N'医保-医保基金支付',
'SCHEMA', N'dbo',
'TABLE', N'Reg_Online_GjYiBao_uploadFeeRecord',
'COLUMN', N'FundPay'
GO

EXEC sp_addextendedproperty
'MS_Description', N'医保-住院押金',
'SCHEMA', N'dbo',
'TABLE', N'Reg_Online_GjYiBao_uploadFeeRecord',
'COLUMN', N'Deposit'
GO

EXEC sp_addextendedproperty
'MS_Description', N'创建时间',
'SCHEMA', N'dbo',
'TABLE', N'Reg_Online_GjYiBao_uploadFeeRecord',
'COLUMN', N'CreatedDate'
GO

EXEC sp_addextendedproperty
'MS_Description', N'更新时间',
'SCHEMA', N'dbo',
'TABLE', N'Reg_Online_GjYiBao_uploadFeeRecord',
'COLUMN', N'UpdatedDate'
GO

EXEC sp_addextendedproperty
'MS_Description', N'支付渠道 11-支付宝小程序 17-微信小程序 13-微信公众号 ',
'SCHEMA', N'dbo',
'TABLE', N'Reg_Online_GjYiBao_uploadFeeRecord',
'COLUMN', N'Payway'
GO

EXEC sp_addextendedproperty
'MS_Description', N'线上医保明细上传和支付下单记录',
'SCHEMA', N'dbo',
'TABLE', N'Reg_Online_GjYiBao_uploadFeeRecord'
GO


-- ----------------------------
-- Primary Key structure for table Reg_Online_GjYiBao_uploadFeeRecord
-- ----------------------------
ALTER TABLE [dbo].[Reg_Online_GjYiBao_uploadFeeRecord] ADD CONSTRAINT [PK__Reg_Online_GjYiBao_uploadFeeRecord_3214EC2735F7B9FA] PRIMARY KEY CLUSTERED ([ID])
WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON)  
ON [PRIMARY]
GO

