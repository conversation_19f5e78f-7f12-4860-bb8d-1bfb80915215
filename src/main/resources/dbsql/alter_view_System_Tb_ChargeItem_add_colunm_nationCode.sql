use hisdb;
go

alter view dbo.System_Tb_ChargeItem 
AS
select a.ItemCode  ItemCode     
      ,NewItemCode        
      ,a.ItemName        
      ,a.InputCode1        
      ,a.InputCode2        
      ,a.InputCode3        
      ,ItemCategory        
      ,ChildItemCategory        
      ,ClinicExpensePrice ExpensePrice        
      ,ClinicNonExpensePrice NonExpensePrice        
      ,a.<PERSON>ty        
      ,a.ClinicUnit        
      ,WardUnit        
      ,a.<PERSON>        
      ,a.<PERSON>Unit        
      ,a.DrugGuage        
      ,a.<PERSON>e<PERSON>ept        
      ,a.Stopped        
      ,a.ABClass        
      ,a.ProductPlace        
      ,a.TradePrice        
      ,a.RetailPrice        
      ,a.CheckCode        
      ,Creator        
      ,CreateTime        
      ,Updater        
      ,UpdateTime        
      ,Stopper        
      ,StopTime        
      ,b.HospitalId         
    ,StandName   , pycode,  wbcode ,a.NationCode    
    from dbo.System_Tb_PubItems a   
 inner join System_Tb_ItemHospital b on a.GlobalId= b.GlobalId  
 where b.UseDeptRange = 1  
 union all
 select a.ItemCode ItemCode     
      ,NewItemCode        
      ,a.ItemName        
      ,a.InputCode1        
      ,a.InputCode2        
      ,a.InputCode3        
      ,ItemCategory        
      ,ChildItemCategory        
      ,ClinicExpensePrice ExpensePrice        
      ,ClinicNonExpensePrice NonExpensePrice        
      ,a.ClinicQty        
      ,a.ClinicUnit        
      ,WardUnit        
      ,a.Dosage        
      ,a.DosageUnit        
      ,a.DrugGuage        
      ,a.ExeDept        
      ,a.Stopped        
      ,a.ABClass        
      ,a.ProductPlace        
      ,a.TradePrice        
      ,a.RetailPrice        
      ,a.CheckCode        
      ,Creator        
      ,CreateTime        
      ,Updater        
      ,UpdateTime        
      ,Stopper        
      ,StopTime        
      ,b.HospitalId         
    ,StandName   , pycode,  wbcode ,a.NationCode    
    from dbo.System_Tb_PubItems a   
 inner join Drug_Tb_DrugInfoHospital b on a.GlobalId= b.GlobalId  
 where b.UseDeptRange = 1;

go