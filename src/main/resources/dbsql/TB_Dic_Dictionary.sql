use hisdb
go
if not exists (select 1 from  [TB_Dic_Dictionary] where  [DictionaryTypeID]=56 and DictionaryCode=11)
begin
INSERT INTO [dbo].[TB_Dic_Dictionary]([DictionaryCode], [DictionaryName], [Remark], [DictionaryTypeID], [lang], [IsUse] ,[InputCode]) VALUES (11, '支付宝小程序', '11', 56, 1, '1', 'ZFBXCX');
end

go
if not exists (select 1 from  [TB_Dic_Dictionary] where  [DictionaryTypeID]=56 and DictionaryCode=17)
begin
INSERT INTO [dbo].[TB_Dic_Dictionary]([DictionaryCode], [DictionaryName], [Remark], [DictionaryTypeID], [lang],
                                      [IsUse] ,[InputCode]) VALUES (17, '微信小程序', '17', 56, 1, '1', 'WXXCX');
end
go
if not exists (select 1 from  [TB_Dic_Dictionary] where  [DictionaryTypeID]=56 and DictionaryCode=13)
begin
INSERT INTO [dbo].[TB_Dic_Dictionary]([DictionaryCode], [DictionaryName], [Remark], [DictionaryTypeID], [lang], [IsUse] ,[InputCode]) VALUES (13, '微信公众号', '13', 56, 1, '1', 'WXGZH');
end
go
