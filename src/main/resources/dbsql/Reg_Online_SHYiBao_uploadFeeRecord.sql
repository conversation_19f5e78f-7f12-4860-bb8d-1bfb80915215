USE HISDB
GO

CREATE TABLE [dbo].[Reg_Online_SHYiBao_uploadFeeRecord] (
  [ID] bigint  NOT NULL,
  [Cardid] nvarchar(50) COLLATE Chinese_PRC_CI_AS  NULL,
  [Personname] nvarchar(100) COLLATE Chinese_PRC_CI_AS  NULL,
  [Personspectag] nvarchar(50) COLLATE Chinese_PRC_CI_AS  NULL,
  [Accountattr] nvarchar(50) COLLATE Chinese_PRC_CI_AS  NULL,
  [Jmjsbz] nvarchar(50) COLLATE Chinese_PRC_CI_AS  NULL,
  [Totalexpense] decimal(18,2)  NULL,
  [Zfdxjzfs] decimal(18,2)  NULL,
  [Tcdzhzfs] decimal(18,2)  NULL,
  [Tcdxjzfs] decimal(18,2)  NULL,
  [Tczfs] decimal(18,2)  NULL,
  [Ybjsfwfyze] decimal(18,2)  NULL,
  [Fybjsfwfyze] decimal(18,2)  NULL,
  [Jssqxh] nvarchar(50) COLLATE Chinese_PRC_CI_AS  NULL,
  [PatName] nvarchar(100) COLLATE Chinese_PRC_CI_AS  NULL,
  [CardType] nvarchar(50) COLLATE Chinese_PRC_CI_AS  NULL,
  [CardNo] nvarchar(50) COLLATE Chinese_PRC_CI_AS  NULL,
  [ChargeNo] bigint  NULL,
  [RegNo] bigint  NULL,
  [Flag] int  NULL,
  [HospitalCode] nvarchar(50) COLLATE Chinese_PRC_CI_AS  NULL,
  [OrderNo] nvarchar(100) COLLATE Chinese_PRC_CI_AS  NULL,
  [CreatedDate] datetime  NULL,
  [UpdatedDate] datetime  NULL,
  [Payway] int  NULL,
  [AuthCode] nvarchar(100) COLLATE Chinese_PRC_CI_AS  NULL,
  [UserName] nvarchar(100) COLLATE Chinese_PRC_CI_AS  NULL,
  [CityId] nvarchar(50) COLLATE Chinese_PRC_CI_AS  NULL,
  [PayAuthNo] nvarchar(100) COLLATE Chinese_PRC_CI_AS  NULL,
  [Longitude] nvarchar(50) COLLATE Chinese_PRC_CI_AS  NULL,
  [Latitude] nvarchar(50) COLLATE Chinese_PRC_CI_AS  NULL,
  [EcQrcode] nvarchar(100) COLLATE Chinese_PRC_CI_AS  NULL,
  [UserCardNo] nvarchar(50) COLLATE Chinese_PRC_CI_AS  NULL,
  [EcToken] nvarchar(100) COLLATE Chinese_PRC_CI_AS  NULL
)
GO

ALTER TABLE [dbo].[Reg_Online_SHYiBao_uploadFeeRecord] SET (LOCK_ESCALATION = TABLE)
GO

EXEC sp_addextendedproperty
'MS_Description', N'卡号',
'SCHEMA', N'dbo',
'TABLE', N'Reg_Online_SHYiBao_uploadFeeRecord',
'COLUMN', N'Cardid'
GO

EXEC sp_addextendedproperty
'MS_Description', N'姓名',
'SCHEMA', N'dbo',
'TABLE', N'Reg_Online_SHYiBao_uploadFeeRecord',
'COLUMN', N'Personname'
GO

EXEC sp_addextendedproperty
'MS_Description', N'特殊人员标识，0：普通，1：离休，2：伤残，3：干部保健定点',
'SCHEMA', N'dbo',
'TABLE', N'Reg_Online_SHYiBao_uploadFeeRecord',
'COLUMN', N'Personspectag'
GO

EXEC sp_addextendedproperty
'MS_Description', N'帐户标志，见字典表',
'SCHEMA', N'dbo',
'TABLE', N'Reg_Online_SHYiBao_uploadFeeRecord',
'COLUMN', N'Accountattr'
GO

EXEC sp_addextendedproperty
'MS_Description', N'减免结算标志，0：正常结算，1：医保减免结算，2：财政减免结算',
'SCHEMA', N'dbo',
'TABLE', N'Reg_Online_SHYiBao_uploadFeeRecord',
'COLUMN', N'Jmjsbz'
GO

EXEC sp_addextendedproperty
'MS_Description', N'交易费用总额，数字格式 A',
'SCHEMA', N'dbo',
'TABLE', N'Reg_Online_SHYiBao_uploadFeeRecord',
'COLUMN', N'Totalexpense'
GO

EXEC sp_addextendedproperty
'MS_Description', N'自负段现金支付数，数字格式 A',
'SCHEMA', N'dbo',
'TABLE', N'Reg_Online_SHYiBao_uploadFeeRecord',
'COLUMN', N'Zfdxjzfs'
GO

EXEC sp_addextendedproperty
'MS_Description', N'统筹段帐户支付数，数字格式 A',
'SCHEMA', N'dbo',
'TABLE', N'Reg_Online_SHYiBao_uploadFeeRecord',
'COLUMN', N'Tcdzhzfs'
GO

EXEC sp_addextendedproperty
'MS_Description', N'统筹段现金支付数，数字格式 A',
'SCHEMA', N'dbo',
'TABLE', N'Reg_Online_SHYiBao_uploadFeeRecord',
'COLUMN', N'Tcdxjzfs'
GO

EXEC sp_addextendedproperty
'MS_Description', N'统筹支付数，工伤病人返回工伤基金支付数，数字格式 A',
'SCHEMA', N'dbo',
'TABLE', N'Reg_Online_SHYiBao_uploadFeeRecord',
'COLUMN', N'Tczfs'
GO

EXEC sp_addextendedproperty
'MS_Description', N'医保结算范围费用总额，工伤病人返回工伤结算范围费用总额，数字格式 A',
'SCHEMA', N'dbo',
'TABLE', N'Reg_Online_SHYiBao_uploadFeeRecord',
'COLUMN', N'Ybjsfwfyze'
GO

EXEC sp_addextendedproperty
'MS_Description', N'非医保结算范围费用总额，工伤病人返回非工伤结算范围费用总额，数字格式 A',
'SCHEMA', N'dbo',
'TABLE', N'Reg_Online_SHYiBao_uploadFeeRecord',
'COLUMN', N'Fybjsfwfyze'
GO

EXEC sp_addextendedproperty
'MS_Description', N'计算申请序号',
'SCHEMA', N'dbo',
'TABLE', N'Reg_Online_SHYiBao_uploadFeeRecord',
'COLUMN', N'Jssqxh'
GO

EXEC sp_addextendedproperty
'MS_Description', N'就诊人姓名',
'SCHEMA', N'dbo',
'TABLE', N'Reg_Online_SHYiBao_uploadFeeRecord',
'COLUMN', N'PatName'
GO

EXEC sp_addextendedproperty
'MS_Description', N'证件类型',
'SCHEMA', N'dbo',
'TABLE', N'Reg_Online_SHYiBao_uploadFeeRecord',
'COLUMN', N'CardType'
GO

EXEC sp_addextendedproperty
'MS_Description', N'证件号码',
'SCHEMA', N'dbo',
'TABLE', N'Reg_Online_SHYiBao_uploadFeeRecord',
'COLUMN', N'CardNo'
GO

EXEC sp_addextendedproperty
'MS_Description', N'结算单号',
'SCHEMA', N'dbo',
'TABLE', N'Reg_Online_SHYiBao_uploadFeeRecord',
'COLUMN', N'ChargeNo'
GO

EXEC sp_addextendedproperty
'MS_Description', N'门诊序号',
'SCHEMA', N'dbo',
'TABLE', N'Reg_Online_SHYiBao_uploadFeeRecord',
'COLUMN', N'RegNo'
GO

EXEC sp_addextendedproperty
'MS_Description', N'标志位，0表示挂号，1表示缴费',
'SCHEMA', N'dbo',
'TABLE', N'Reg_Online_SHYiBao_uploadFeeRecord',
'COLUMN', N'Flag'
GO

EXEC sp_addextendedproperty
'MS_Description', N'医院编码',
'SCHEMA', N'dbo',
'TABLE', N'Reg_Online_SHYiBao_uploadFeeRecord',
'COLUMN', N'HospitalCode'
GO

EXEC sp_addextendedproperty
'MS_Description', N'医保支付订单号',
'SCHEMA', N'dbo',
'TABLE', N'Reg_Online_SHYiBao_uploadFeeRecord',
'COLUMN', N'OrderNo'
GO

EXEC sp_addextendedproperty
'MS_Description', N'创建时间',
'SCHEMA', N'dbo',
'TABLE', N'Reg_Online_SHYiBao_uploadFeeRecord',
'COLUMN', N'CreatedDate'
GO

EXEC sp_addextendedproperty
'MS_Description', N'更新时间',
'SCHEMA', N'dbo',
'TABLE', N'Reg_Online_SHYiBao_uploadFeeRecord',
'COLUMN', N'UpdatedDate'
GO

EXEC sp_addextendedproperty
'MS_Description', N'支付渠道，11-支付宝小程序，17-微信小程序，13-微信公众号',
'SCHEMA', N'dbo',
'TABLE', N'Reg_Online_SHYiBao_uploadFeeRecord',
'COLUMN', N'Payway'
GO

EXEC sp_addextendedproperty
'MS_Description', N'微信授权码',
'SCHEMA', N'dbo',
'TABLE', N'Reg_Online_SHYiBao_uploadFeeRecord',
'COLUMN', N'AuthCode'
GO

EXEC sp_addextendedproperty
'MS_Description', N'用户姓名',
'SCHEMA', N'dbo',
'TABLE', N'Reg_Online_SHYiBao_uploadFeeRecord',
'COLUMN', N'UserName'
GO

EXEC sp_addextendedproperty
'MS_Description', N'用户参保地代码，若未选择主参保地则为空',
'SCHEMA', N'dbo',
'TABLE', N'Reg_Online_SHYiBao_uploadFeeRecord',
'COLUMN', N'CityId'
GO

EXEC sp_addextendedproperty
'MS_Description', N'医保线上核验payAuthNo，医保线上支付功能时返回',
'SCHEMA', N'dbo',
'TABLE', N'Reg_Online_SHYiBao_uploadFeeRecord',
'COLUMN', N'PayAuthNo'
GO

EXEC sp_addextendedproperty
'MS_Description', N'经度',
'SCHEMA', N'dbo',
'TABLE', N'Reg_Online_SHYiBao_uploadFeeRecord',
'COLUMN', N'Longitude'
GO

EXEC sp_addextendedproperty
'MS_Description', N'纬度',
'SCHEMA', N'dbo',
'TABLE', N'Reg_Online_SHYiBao_uploadFeeRecord',
'COLUMN', N'Latitude'
GO

EXEC sp_addextendedproperty
'MS_Description', N'医保线上核验ecQrcode，医保线上支付功能时返回',
'SCHEMA', N'dbo',
'TABLE', N'Reg_Online_SHYiBao_uploadFeeRecord',
'COLUMN', N'EcQrcode'
GO

EXEC sp_addextendedproperty
'MS_Description', N'医保卡号',
'SCHEMA', N'dbo',
'TABLE', N'Reg_Online_SHYiBao_uploadFeeRecord',
'COLUMN', N'UserCardNo'
GO

EXEC sp_addextendedproperty
'MS_Description', N'医保线上ecToken',
'SCHEMA', N'dbo',
'TABLE', N'Reg_Online_SHYiBao_uploadFeeRecord',
'COLUMN', N'EcToken'
GO

EXEC sp_addextendedproperty
'MS_Description', N'记录医保支付相关费用和结算信息的表，包括支付方式、卡号、结算单号等信息',
'SCHEMA', N'dbo',
'TABLE', N'Reg_Online_SHYiBao_uploadFeeRecord'
GO


-- ----------------------------
-- Primary Key structure for table Reg_Online_SHYiBao_uploadFeeRecord
-- ----------------------------
ALTER TABLE [dbo].[Reg_Online_SHYiBao_uploadFeeRecord] ADD CONSTRAINT [PK_Reg_Online_SHYiBao_uploadFeeRecord] PRIMARY KEY CLUSTERED ([ID])
WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON)
ON [PRIMARY]
GO

