/*
 Navicat Premium Data Transfer

 Source Server         : *********
 Source Server Type    : SQL Server
 Source Server Version : 15002000 (15.00.2000)
 Source Host           : *********:1435
 Source Catalog        : HISDB
 Source Schema         : dbo

 Target Server Type    : SQL Server
 Target Server Version : 15002000 (15.00.2000)
 File Encoding         : 65001

 Date: 30/05/2024 09:18:02
*/


-- ----------------------------
-- Table structure for Reg_Online_GjYiBao_Interface_Log
-- ----------------------------
IF EXISTS (SELECT * FROM sys.all_objects WHERE object_id = OBJECT_ID(N'[dbo].[Reg_Online_GjYiBao_Interface_Log]') AND type IN ('U'))
	DROP TABLE [dbo].[Reg_Online_GjYiBao_Interface_Log]
GO

CREATE TABLE [dbo].[Reg_Online_GjYiBao_Interface_Log] (
  [ID] bigint  NOT NULL,
  [Name] varchar(200) COLLATE Chinese_PRC_CI_AS  NULL,
  [Url] varchar(200) COLLATE Chinese_PRC_CI_AS  NULL,
  [Input] text COLLATE Chinese_PRC_CI_AS  NULL,
  [Output] text COLLATE Chinese_PRC_CI_AS  NULL,
  [HospitalCode] varchar(50) COLLATE Chinese_PRC_CI_AS  NULL,
  [CreatedDate] datetime  NULL
)
GO

ALTER TABLE [dbo].[Reg_Online_GjYiBao_Interface_Log] SET (LOCK_ESCALATION = TABLE)
GO

EXEC sp_addextendedproperty
'MS_Description', N'接口名或编码',
'SCHEMA', N'dbo',
'TABLE', N'Reg_Online_GjYiBao_Interface_Log',
'COLUMN', N'Name'
GO

EXEC sp_addextendedproperty
'MS_Description', N'接口请求地址',
'SCHEMA', N'dbo',
'TABLE', N'Reg_Online_GjYiBao_Interface_Log',
'COLUMN', N'Url'
GO

EXEC sp_addextendedproperty
'MS_Description', N'入参',
'SCHEMA', N'dbo',
'TABLE', N'Reg_Online_GjYiBao_Interface_Log',
'COLUMN', N'Input'
GO

EXEC sp_addextendedproperty
'MS_Description', N'出参',
'SCHEMA', N'dbo',
'TABLE', N'Reg_Online_GjYiBao_Interface_Log',
'COLUMN', N'Output'
GO

EXEC sp_addextendedproperty
'MS_Description', N'医院编码',
'SCHEMA', N'dbo',
'TABLE', N'Reg_Online_GjYiBao_Interface_Log',
'COLUMN', N'HospitalCode'
GO

EXEC sp_addextendedproperty
'MS_Description', N'创建时间',
'SCHEMA', N'dbo',
'TABLE', N'Reg_Online_GjYiBao_Interface_Log',
'COLUMN', N'CreatedDate'
GO


-- ----------------------------
-- Primary Key structure for table Reg_Online_GjYiBao_Interface_Log
-- ----------------------------
ALTER TABLE [dbo].[Reg_Online_GjYiBao_Interface_Log] ADD CONSTRAINT [PK_Reg_Online_GjYiBao_Interface_Log_9989EC2735F7B9FA] PRIMARY KEY CLUSTERED ([ID])
WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON)  
ON [PRIMARY]
GO

