<?xml version="1.0" encoding="UTF-8"?>

<configuration scan="true">
    <shutdownHook class="ch.qos.logback.core.hook.DelayingShutdownHook" />
    <conversionRule conversionWord="runTime"  converterClass="com.sunhealth.ihhis.config.handler.LogRunTimeConverter" />
    <conversionRule conversionWord="requestId"  converterClass="com.sunhealth.ihhis.config.handler.LogRequestIdConverter" />

    <include resource="org/springframework/boot/logging/logback/base.xml" />

    <property name="CONSOLE_LOG_PATTERN"
              value="%d{yyyy-MM-dd HH:mm:ss} %clr(${LOG_LEVEL_PATTERN:-%5p}) %clr(${PID:- }){magenta} %clr(---){faint} %clr([%1.14X{principal:-}]) %clr([%15.15t]){faint} %clr(%-40.40logger{39}){cyan} %clr(:){faint} %requestId, %runTime, %m%n${LOG_EXCEPTION_CONVERSION_WORD:-%wEx}" />

    <property name="FILE_LOG_PATTERN"
              value="%d{yyyy-MM-dd HH:mm:ss.SSS} ${LOG_LEVEL_PATTERN:-%5p} ${PID:- } --- [%X{principal}] [%t] %-40.40logger{39} : %requestId, %runTime, %m%n${LOG_EXCEPTION_CONVERSION_WORD:-%wEx}" />

<!--    <springProperty scope="context" name="APP_NAME" source="spring.application.name" />-->
<!--    <springProperty scope="context" name="APP_HOME" source="app.home" />-->
    <property name="APP_NAME" value="rj_internet_interface" />
    <property name="APP_HOME" value="./ih_his_logs/" />

    <property name="MESSAGE_FORMAT"
      value="%contextName %2(%.-1level) %6.6mdc{sequence-number} %18.18mdc{time} %8.8mdc{application-type}:%-12.12mdc{application-instance} %-10.10mdc{work-unit} %-35.35logger{0} %4.4mdc{message-code} %message %throwable{short}%n"/>

    <!-- The FILE and ASYNC appenders are here as examples for a production configuration -->
    <appender name="LOGFILE" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <file>${APP_HOME}/log/${APP_NAME}.log</file>
        <rollingPolicy class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">
            <fileNamePattern>${APP_HOME}/log/${APP_NAME}.%d{yyyy-MM-dd}.log</fileNamePattern>
            <maxHistory>48</maxHistory>
        </rollingPolicy>
        <encoder>
            <charset>utf-8</charset>
            <Pattern>${FILE_LOG_PATTERN}</Pattern>
        </encoder>
        <filter class="ch.qos.logback.classic.filter.ThresholdFilter">
            <level>ALL</level>
        </filter>
    </appender>
    <appender name="ARCHIVE" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <file>${APP_HOME}/log/${APP_NAME}.log.gz</file>
        <rollingPolicy class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">
            <fileNamePattern>${APP_HOME}/log/%d{yyyy-MM-dd,aux}/${APP_NAME}.%d{yyyy-MM-dd_HH}.log.gz</fileNamePattern>
            <maxHistory>4320</maxHistory>
        </rollingPolicy>
        <encoder>
            <charset>utf-8</charset>
            <Pattern>${FILE_LOG_PATTERN}</Pattern>
        </encoder>
        <filter class="ch.qos.logback.classic.filter.ThresholdFilter">
            <level>ALL</level>
        </filter>
    </appender>


    <appender name="CONSOLE" class="ch.qos.logback.core.ConsoleAppender">
        <encoder>
            <pattern>${CONSOLE_LOG_PATTERN}</pattern>
        </encoder>
        <filter class="ch.qos.logback.classic.filter.ThresholdFilter">
            <level>ALL</level>
        </filter>
    </appender>
    <!--
        <appender name="ASYNC" class="ch.qos.logback.classic.AsyncAppender">
            <queueSize>512</queueSize>
            <appender-ref ref="FILE"/>
        </appender>
    -->
    <logger name="ch.qos.logback" level="ERROR" />
    <logger name="javax.activation" level="WARN"/>
    <logger name="javax.mail" level="WARN"/>
    <logger name="javax.xml.bind" level="WARN"/>
    <logger name="ch.qos.logback" level="WARN"/>
    <logger name="io.dropwizard" level="WARN"/>
    <logger name="com.codahale.metrics" level="WARN"/>
    <logger name="com.ryantenney" level="WARN"/>
    <logger name="com.sun" level="WARN"/>
    <logger name="com.zaxxer" level="WARN"/>
    <logger name="net.sf.ehcache" level="WARN"/>
    <logger name="org.apache" level="WARN"/>
    <logger name="org.quartz" level="WARN"/>
    <logger name="org.apache.catalina.startup.DigesterFactory" level="OFF"/>
    <logger name="org.bson" level="WARN"/>
    <logger name="org.hibernate.validator" level="WARN"/>
    <logger name="org.hibernate" level="WARN"/>
    <logger name="org.springframework" level="WARN"/>
    <logger name="org.springframework.web" level="WARN"/>
    <logger name="org.springframework.security" level="WARN"/>
    <logger name="org.springframework.cache" level="WARN"/>
    <logger name="org.springframework.boot.web.embedded.tomcat.TomcatWebServer" level="INFO"/>
    <logger name="org.thymeleaf" level="WARN"/>
    <logger name="org.xnio" level="WARN"/>
    <logger name="springfox" level="WARN"/>
    <logger name="sun.rmi" level="WARN"/>
    <logger name="liquibase" level="WARN"/>
    <logger name="sun.rmi.transport" level="WARN"/>
    
    <contextListener class="ch.qos.logback.classic.jul.LevelChangePropagator">
        <resetJUL>true</resetJUL>
    </contextListener>

    <root level="INFO">
        <appender-ref ref="LOGFILE"/>
        <appender-ref ref="ARCHIVE"/>
    </root>

</configuration>
