# his服务
## 技术框架
    - sprintboot:
    - mybatis
    - mybatisPlus
    - sqlServer
    - 
    ```
## 开发环境运行
复制configDemo到同级目录重命名为config，修改config/application.yml中的连接信息
## 部署

### 基础环境
* jdk8
* mssql>使用his的数据库，这个项目不进行数据库结构维护
* maven3.5以上版本

### 编译命令
```
    mvn clean package -DskipTests=true  -Denforcer.skip=true
```

### 创建目录和脚本
* sudo adduser quege
* mkdir -p /home/<USER>/rj_internet_interface
* mkdir -p /home/<USER>/bin
* sudo chmod 777 /home/<USER>/bin/start-rj_internet_interface.sh
* sudo chmod 777 /home/<USER>/bin/stop-rj_internet_interface.sh
* sudo chmod 777 /home/<USER>/bin/upgrade-rj_internet_interface.sh
* /home/<USER>/bin/start-rj_internet_interface.sh
```
#!/usr/bin/env bash
/bin/systemctl start rj_internet_interface
```
* /home/<USER>/bin/stop-rj_internet_interface.sh
```
#!/usr/bin/env bash
/bin/systemctl stop rj_internet_interface
```
* /home/<USER>/bin/upgrade-rj_internet_interface.sh
```
#!/usr/bin/env bash

VERSION=$1
APP_HOME=$2
SASD=rj_internet_interface-$VERSION
KEEP_OLD_CONF=rj_internet_interface-conf

function error_exit
{
	echo "$1" 1>&2
	exit 1
}

# stop old instance
echo "stopping old instance ..."
sudo /home/<USER>/bin/stop-rj_internet_interface.sh || error_exit "unable to stop ih his"

# because use same version info, need keep old config and remove old folder first
# step 1 keep the corrent config
echo "Upgrading ih his now ..."
cd $APP_HOME
if [ -d ${KEEP_OLD_CONF} ]; then
  rm -rf ${KEEP_OLD_CONF}/
  mkdir ${KEEP_OLD_CONF}
else
  mkdir ${KEEP_OLD_CONF}
fi
if [ -d ./current ]; then
	OLD=`/bin/readlink -f ./current`
	cp -rf current/conf ${KEEP_OLD_CONF}/
	rm -f current
fi
# step 2 remove old folders
if [ -d $OLD ]; then
	echo "deleting old directory $OLD"
	rm -rf $OLD
fi

#cp ./target/$IHAPID.tar.gz $APP_HOME
tar -zxf $SASD.tar.gz

ln -s $APP_HOME/$SASD $APP_HOME/current
# copy old conf back to current
cp -rf ${KEEP_OLD_CONF}/conf current/

echo "starting new instance ..."
sudo /home/<USER>/bin/start-rj_internet_interface.sh || error_exit "unable to start ih his"

# do some cleanup
rm $SASD.tar.gz
```

### 安装
* cp rj_internet_interface-[version].tar.gz /home/<USER>/rj_internet_interface
* tar -zxvf rj_internet_interface-[version].tar.gz
* ln -s /home/<USER>/rj_internet_interface/rj_internet_interface-[version] /home/<USER>/rj_internet_interface/current
* sudo touch /etc/default/rj_internet_interface
* sudo systemctl daemon-reload

### 配置
/home/<USER>/rj_internet_interface/current/conf/application.yml
```
spring:
  datasource:
    username: rjhis
    password: rjhis
    url: jdbc:sqlserver://*********:1435;DatabaseName=HISDB;trustServerCertificate=true
    driverclassname: com.microsoft.sqlserver.jdbc.SQLServerDriver
```

### 建立Service

* 在/etc/systemd/system目录下建立文件，比如说：rj_internet_interface.service
* 运行命令 `sudo systemctl daemon-reload`
* 运行命令 `sudo systemctl start rj_internet_interface`
```
[Unit]
Description=ih api his
After=network.target

[Service]
EnvironmentFile=/etc/default/rj_internet_interface
ExecStart=/home/<USER>/rj_internet_interface/current/bin/rj_internet_interface --start
PIDFile=/home/<USER>/rj_internet_interface/current/run/rj_internet_interface.pid
LimitNOFILE=49152
KillMode=process
Restart=on-failure
User=quege
Group=quege

[Install]
WantedBy=multi-user.target
```

### nginx 配置
```
server {
    server_name gzh.taihealth.cn;
    underscores_in_headers on;
    location / {
        proxy_pass http://localhost:8082;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header Host $http_host;
        proxy_set_header X-NginX-Proxy true;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection "upgrade";
    }
    listen 80; # managed by Certbot
    client_max_body_size 200m;

    listen 443 ssl;
    ssl_certificate /etc/nginx/ssh/8297620_gzh.taihealth.cn_nginx/8297620_gzh.taihealth.cn.pem;
    ssl_certificate_key /etc/nginx/ssh/8297620_gzh.taihealth.cn_nginx/8297620_gzh.taihealth.cn.key;
}
```

### 升级
我们可以通过QuickBuild (Continuous Integration)来升级系统，也可以手工来升级。基础是使用如下命令：
```bash
cp rj_internet_interface-${newVersion}.tar.gz to /home/<USER>/rj_internet_interface
/home/<USER>/bin/upgrade-rj_internet_interface.sh ${newVersion} /home/<USER>/rj_internet_interface
```
### 关于Too Many Open Files
由于Systemd忽略常规的设置，所以我们通过修改/etc/systemd/system/<serviceName>.service来增加：

```
LimitNOFILE=65536
```

详细的说明参见[stackexchange](https://unix.stackexchange.com/questions/345595/how-to-set-ulimits-on-service-with-systemd)

## Swagger.json
路径：target/classes/com/sunhealth/ihhis/controller/swagger.json


## 多数据源
### 因为要同时查询his和lis的数据，所以需要配置多数据源
1. 在application.yml中配置多数据源
```yaml
spring:
  datasource:
    type: com.alibaba.druid.pool.DruidDataSource
    druid:
      his:
        driver-class-name: com.microsoft.sqlserver.jdbc.SQLServerDriver
        url: *******************************************************************************
        username: sa
        password: Abc1234
      lis:
        driver-class-name: com.microsoft.sqlserver.jdbc.SQLServerDriver
        url: ***********************************************************************************
        username: sa
        password: Abc1234
```
2. 开发过程中dao层内容写在对应位置，his的dao写在com.sunhealth.ihhis.dao.his下，lis的dao写在com.sunhealth.ihhis.dao.lis下
3. 在service使用对应dao即可


## sql脚本
### 1. 通过sql脚本添加字典值以符合线上医保的业务需求
[sql1](src/main/resources/dbsql/TB_Dic_Dictionary.sql)
[sql2](src/main/resources/dbsql/TB_Dic_HisDictionary.sql)

### 2. 通过sql脚本添加数据表以符合线上医保的业务需求
[sql3](src/main/resources/dbsql/Reg_Online_GjYiBao_Interface_Log.sql)
[sql4](src/main/resources/dbsql/Reg_Online_GjYiBao_OrderInfo.sql)
[sql5](src/main/resources/dbsql/Reg_Online_GjYiBao_uploadFeeRecord.sql)

### 3. 通过sql脚本修改视图，满足线上医保的要求
[sql6](src/main/resources/dbsql/alter_view_System_Tb_ChargeItem_add_colunm_nationCode.sql)
 
## 支付方式
### ih侧 
* 1=微信小程序支付
* 2=支付宝支付
* 3=微信公众号支付

### his侧
* 17=微信小程序支付
* 11=支付宝支付
* 13=微信公众号支付

